<?xml version="1.0" encoding="utf-8"?>
<layout>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      android:background="@drawable/chat_bg_contact_title_bar">

      <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginStart="@dimen/dp_12"
        android:src="@drawable/common_ic_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/indicator_enterprise_type"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_50"
        android:layout_marginEnd="@dimen/dp_50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

      <ImageView
        android:id="@+id/iv_search"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_12"
        android:src="@drawable/ic_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager2.widget.ViewPager2
      android:id="@+id/vp_content"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />
  </LinearLayout>
</layout>