<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_44">

        <ImageView
            android:id="@+id/iv_close"
            style="@style/wrap_wrap"
            android:layout_marginStart="@dimen/dp_4"
            android:src="@drawable/common_ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.16sp.333333"
            android:text="@string/account_merge_title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_match">

        <TextView
            style="@style/Text.20sp.333333"
            android:layout_marginTop="@dimen/common_dp_60"
            android:gravity="center"
            android:text="@string/account_merge_tips"
            app:layout_constraintBottom_toTopOf="@id/tv_option_one"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_option_one"
            style="@style/Button.Basic"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/frame_10c198_radius_10"
            android:text="@string/account_merge_option_one_text"
            android:textColor="@color/cl_ff7405"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@id/tv_option_two"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_option_two"
            style="@style/Button.Basic"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_option_one" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>