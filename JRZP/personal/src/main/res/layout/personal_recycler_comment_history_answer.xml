<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="data"
      type="com.bxkj.personal.data.UserHistoryItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout
    style="@style/match_wrap"
    android:paddingTop="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_14">

    <ImageView
      android:id="@+id/iv_checked"
      style="@style/wrap_wrap"
      android:src="@drawable/common_bg_checkbox_selector"
      android:visibility="gone"
      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/common_dp_32"
      android:layout_height="@dimen/common_dp_32"
      android:layout_marginStart="@dimen/dp_8"
      bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgUrl="@{data.cUserPhoto}"
      app:layout_constraintStart_toEndOf="@id/iv_checked"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_goneMarginStart="@dimen/dp_12"
      app:shapeAppearance="@style/roundedCornerImageStyle" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{data.cUserName}"
      android:textStyle="bold"
      app:layout_constraintBottom_toTopOf="@id/tv_publish_time"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_publish_time"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:text="@{data.cCreateTime}"
      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.17sp.000000"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:lineSpacingExtra="@dimen/dp_3"
      android:text="@{data.cContent}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/iv_avatar"
      app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.15sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/dp_8"
      android:background="@drawable/frame_eaeaea"
      android:lineSpacingExtra="@dimen/dp_3"
      android:padding="@dimen/dp_10"
      android:text="@{@string/comment_history_answer_format(data.userName,data.questionTitle)}"
      android:textStyle="bold"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/iv_avatar"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <!--        <TextView-->
    <!--            android:id="@+id/tv_views"-->
    <!--            style="@style/common_Text.12sp.333333"-->
    <!--            android:layout_marginStart="@dimen/common_dp_50"-->
    <!--            android:layout_marginTop="@dimen/common_dp_16"-->
    <!--            android:drawableStart="@drawable/ic_user_notice_views"-->
    <!--            android:drawablePadding="@dimen/common_dp_4"-->
    <!--            android:text="@{String.valueOf(data.view)}"-->
    <!--            app:layout_constraintEnd_toStartOf="@id/tv_comment"-->
    <!--            app:layout_constraintHorizontal_chainStyle="spread_inside"-->
    <!--            app:layout_constraintStart_toStartOf="parent"-->
    <!--            app:layout_constraintTop_toBottomOf="@id/tv_title" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/tv_comment"-->
    <!--            style="@style/common_Text.12sp.333333"-->
    <!--            android:drawableStart="@drawable/ic_user_notice_comment"-->
    <!--            android:drawablePadding="@dimen/common_dp_4"-->
    <!--            android:text="@{String.valueOf(data.commentsCount)}"-->
    <!--            app:layout_constraintBaseline_toBaselineOf="@id/tv_views"-->
    <!--            app:layout_constraintEnd_toStartOf="@id/tv_like"-->
    <!--            app:layout_constraintStart_toEndOf="@id/tv_views" />-->

    <!--        <TextView-->
    <!--            android:id="@+id/tv_like"-->
    <!--            style="@style/common_Text.12sp.333333"-->
    <!--            android:layout_marginEnd="@dimen/common_dp_50"-->
    <!--            android:drawableStart="@{data.like?@drawable/ic_user_notice_like_sel:@drawable/ic_user_notice_like_nor}"-->
    <!--            android:drawablePadding="@dimen/common_dp_4"-->
    <!--            android:text="@{String.valueOf(data.likesCount)}"-->
    <!--            app:layout_constraintBaseline_toBaselineOf="@id/tv_comment"-->
    <!--            app:layout_constraintEnd_toEndOf="parent"-->
    <!--            app:layout_constraintStart_toEndOf="@id/tv_comment" />-->

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>