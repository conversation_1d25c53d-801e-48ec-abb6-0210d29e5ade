<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_city_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_22"
        android:textColor="@color/common_item_text_selector"
        android:textSize="@dimen/dp_14" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginStart="@dimen/dp_22"
        android:background="@color/common_e8e8e8" />
</LinearLayout>