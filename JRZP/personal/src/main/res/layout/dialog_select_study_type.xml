<?xml version="1.0" encoding="utf-8"?>
<LinearLayout style="@style/match_wrap"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/common_bg_dialog"
    android:orientation="vertical"
    android:paddingBottom="@dimen/dp_36"
    android:paddingTop="@dimen/dp_8">

    <ImageView
        android:id="@+id/iv_close"
        style="@style/wrap_wrap"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_12"
        android:src="@drawable/ic_big_close" />

    <LinearLayout
        style="@style/match_wrap"
        android:layout_marginTop="@dimen/dp_24"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_type_one"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:drawablePadding="@dimen/dp_12"
            android:drawableTop="@drawable/ic_study_type_xl"
            android:gravity="center_horizontal"
            android:text="@string/home_post_study_type_one" />

        <View
            style="@style/Line.Vertical"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_type_two"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:drawablePadding="@dimen/dp_12"
            android:drawableTop="@drawable/ic_study_type_jn"
            android:gravity="center_horizontal"
            android:text="@string/home_post_study_type_two" />

        <View
            style="@style/Line.Vertical"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_type_three"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:drawablePadding="@dimen/dp_12"
            android:drawableTop="@drawable/ic_study_type_gk"
            android:gravity="center_horizontal"
            android:text="@string/home_post_study_type_three" />

    </LinearLayout>


</LinearLayout>