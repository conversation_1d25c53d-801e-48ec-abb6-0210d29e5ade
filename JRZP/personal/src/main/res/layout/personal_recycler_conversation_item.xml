<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.common.data.ContactBodyBean" />
    </data>

    <com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuLayout
        style="@style/match_wrap"
        app:contentViewId="@id/cl_content"
        app:rightViewId="@id/ll_right_options">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            style="@style/match_wrap">

            <FrameLayout
                android:id="@+id/fl_avatar"
                android:layout_width="@dimen/common_dp_44"
                android:layout_height="@dimen/common_dp_44"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginTop="@dimen/dp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:layout_width="@dimen/conversation_msg_avatar_size"
                    android:layout_height="@dimen/conversation_msg_avatar_size"
                    android:layout_gravity="bottom"
                    bind:imgUrl="@{data.CUserPhoto}" />

                <com.bxkj.common.widget.badge.BadgeTextView
                    style="@style/wrap_wrap"
                    android:layout_gravity="end"
                    android:text="@{String.valueOf(data.unReadCount)}"
                    android:textSize="@dimen/common_sp_10" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_sender_name"
                style="@style/Text.16sp.333333"
                android:layout_width="@dimen/dp_0"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginTop="@dimen/dp_16"
                android:ellipsize="end"
                android:lines="1"
                android:text="@{data.comName}"
                app:layout_constraintEnd_toStartOf="@id/tv_time"
                app:layout_constraintStart_toEndOf="@id/fl_avatar"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_msg_read_status"
                style="@style/wrap_wrap"
                android:src="@drawable/ic_msg_read_tag"
                android:visibility="@{data.newIslook==1?View.VISIBLE:View.GONE}"
                app:layout_constraintBottom_toBottomOf="@id/tv_content"
                app:layout_constraintStart_toEndOf="@id/fl_avatar"
                app:layout_constraintStart_toStartOf="@id/tv_sender_name"
                app:layout_constraintTop_toTopOf="@id/tv_content" />

            <TextView
                android:id="@+id/tv_content"
                style="@style/Text.12sp.999999"
                android:layout_width="@dimen/dp_0"
                android:layout_marginBottom="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginTop="@dimen/dp_2"
                android:ellipsize="end"
                android:lines="1"
                android:text="@{data.newContent}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_msg_state"
                app:layout_constraintStart_toEndOf="@id/iv_msg_read_status"
                app:layout_constraintTop_toBottomOf="@id/tv_sender_name"
                app:layout_goneMarginStart="@dimen/dp_0" />

            <TextView
                android:id="@+id/tv_time"
                style="@style/Text.12sp.999999"
                android:layout_marginEnd="@dimen/dp_8"
                android:text="@{data.lastMsgTimeDiff}"
                app:layout_constraintBottom_toBottomOf="@id/tv_sender_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_sender_name" />

            <ImageView
                android:id="@+id/tv_msg_state"
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/dp_8"
                app:layout_constraintBottom_toBottomOf="@id/tv_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_content" />

            <View
                style="@style/Line.Horizontal"
                android:layout_width="@dimen/dp_0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_sender_name" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_right_options"
            style="@style/wrap_match"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_up"
                style="@style/Text.14sp.FFFFFF"
                android:layout_width="@dimen/dp_70"
                android:layout_height="match_parent"
                android:background="@drawable/bg_dddddd"
                android:gravity="center"
                android:text="@{data.onTop?@string/personal_conversation_cancel_up:@string/personal_conversation_up}" />

            <TextView
                android:id="@+id/tv_delete"
                style="@style/Text.14sp.FFFFFF"
                android:layout_width="@dimen/dp_70"
                android:layout_height="match_parent"
                android:background="@drawable/bg_ff7405"
                android:gravity="center"
                android:text="@string/common_delete" />
        </LinearLayout>

    </com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuLayout>

</layout>