<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap"
  android:orientation="vertical"
  android:paddingTop="@dimen/dp_24">

  <LinearLayout
    style="@style/match_wrap"
    android:orientation="horizontal">

    <TextView
      android:id="@+id/tv_qa"
      style="@style/Text.14sp.333333.Bold"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_bbs_qa"
      android:drawablePadding="@dimen/dp_16"
      android:gravity="center"
      android:text="@string/personal_fragment_bbs_qa"
      app:layout_constraintEnd_toStartOf="@id/tv_study"
      app:layout_constraintHorizontal_chainStyle="spread_inside"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_study"
      style="@style/Text.14sp.333333.Bold"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_bbs_study"
      android:drawablePadding="@dimen/dp_16"
      android:gravity="center"
      android:text="@string/personal_fragment_bbs_study"
      app:layout_constraintEnd_toStartOf="@id/tv_fine_article"
      app:layout_constraintStart_toEndOf="@id/tv_qa"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_fine_article"
      style="@style/Text.14sp.333333.Bold"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_bbs_good_news"
      android:drawablePadding="@dimen/dp_16"
      android:gravity="center"
      android:text="@string/personal_fragment_bbs_good_news"
      app:layout_constraintEnd_toStartOf="@id/tv_skill"
      app:layout_constraintStart_toEndOf="@id/tv_study"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_skill"
      style="@style/Text.14sp.333333.Bold"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_bbs_skill"
      android:drawablePadding="@dimen/dp_16"
      android:gravity="center"
      android:text="@string/personal_fragment_bbs_skill"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_fine_article"
      app:layout_constraintTop_toTopOf="parent" />
  </LinearLayout>

  <View
      android:layout_width="match_parent"
    android:layout_height="@dimen/dp_6"
    android:layout_marginTop="@dimen/dp_12"
    android:background="@drawable/bg_f8f8f8"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tv_qa" />

  <LinearLayout
    android:id="@+id/ll_hot_discuss"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_24"
    android:paddingTop="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_24"
    android:paddingBottom="@dimen/dp_12">

    <TextView
      style="@style/Text.16sp.333333.Bold"
      android:text="@string/personal_fragment_bbs_hot_list" />

    <TextView
      style="@style/Text.10sp.FE6600"
      android:layout_marginStart="@dimen/dp_4"
      android:text="HOT" />

    <Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      style="@style/Text.10sp.888888"
      android:drawableEnd="@drawable/personal_ic_bbs_more_arrow"
      android:drawablePadding="@dimen/dp_2"
      android:text="@string/more" />

  </LinearLayout>

  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/recycler_hot_news"
    style="@style/match_wrap"
    android:layout_marginBottom="12dp" />

</LinearLayout>