<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="@dimen/dp_52"
  android:background="@drawable/bg_ffffff"
  android:paddingStart="@dimen/dp_12">

  <TextView
    android:id="@+id/tv_resume_name"
    style="@style/Text.15sp.333333"
    android:layout_width="@dimen/common_dp_0"
    android:ellipsize="end"
    android:lines="1"
    android:textColor="@color/cl_333333_to_b5b5b5_selector"
    app:layout_constraintBottom_toTopOf="@id/v_bottom_line"
    app:layout_constraintEnd_toStartOf="@id/barrier"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <androidx.constraintlayout.widget.Barrier
    android:id="@+id/barrier"
    android:layout_width="@dimen/common_dp_0"
    android:layout_height="@dimen/common_dp_0"
    android:layout_marginEnd="@dimen/common_dp_12"
    app:barrierDirection="left"
    app:constraint_referenced_ids="tv_delivered,iv_checked" />

  <TextView
    android:id="@+id/tv_delivered"
    style="@style/Text.15sp.b5b5b5"
    android:layout_marginEnd="@dimen/dp_22"
    android:text="@string/personal_resume_delivered"
    android:visibility="gone"
    app:layout_constraintBottom_toTopOf="@id/v_bottom_line"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <ImageView
    android:id="@+id/iv_checked"
    style="@style/wrap_wrap"
    android:layout_marginEnd="@dimen/dp_22"
    android:src="@drawable/personal_ic_resume_checked"
    android:visibility="gone"
    app:layout_constraintBottom_toTopOf="@id/v_bottom_line"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <View
    android:id="@+id/v_bottom_line"
    style="@style/Line.Horizontal"
    android:background="@color/common_e8e8e8"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>