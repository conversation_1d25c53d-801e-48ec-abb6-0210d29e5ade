<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.addquestion.AddQuestionViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            style="@style/match_wrap"
            app:optionEnable="@{viewModel.questionTitle.length()>0}"
            app:right_text="@string/add_question_post"
            app:title="@string/add_question_title" />

        <!--        <androidx.core.widget.NestedScrollView-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="0dp"-->
        <!--            android:layout_weight="1">-->

        <LinearLayout
            style="@style/match_match"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_title"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_54"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@null"
                android:hint="@string/add_question_title_hint"
                android:text="@={viewModel.questionTitle}"
                android:textColorHint="@color/cl_999999"
                android:textSize="22sp" />

            <View
                style="@style/Line.Horizontal"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12" />

            <EditText
                android:id="@+id/et_content"
                style="@style/Text.15sp.333333"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="top|start"
                android:hint="@string/add_question_content_hint"
                android:lineSpacingExtra="@dimen/dp_3"
                android:paddingStart="@dimen/dp_12"
                android:paddingTop="@dimen/dp_16"
                android:paddingEnd="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_16"
                android:text="@={viewModel.questionContent}" />

        </LinearLayout>

        <!--        </androidx.core.widget.NestedScrollView>-->

    </LinearLayout>
</layout>
