<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.conversation.GeekChatViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44">

      <ImageView
        android:id="@+id/iv_back"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_8"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_ic_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_friend_name"
        style="@style/Text.18sp.333333.Bold"
        android:layout_marginStart="48dp"
        android:layout_marginEnd="48dp"
        android:ellipsize="end"
        android:lines="1"
        android:onClick="@{()->viewModel.toUserHome()}"
        android:text="@{viewModel.pageTitle}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tv_company_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

      <TextView
        android:id="@+id/tv_company_name"
        style="@style/Text.12sp.666666"
        android:layout_marginStart="48dp"
        android:layout_marginEnd="48dp"
        android:ellipsize="end"
        android:lines="1"
        android:onClick="@{()->viewModel.toUserHome()}"
        android:text="@{viewModel.pageSubTitle}"
        android:visibility="@{viewModel.pageSubTitle.length()==0?View.GONE:View.VISIBLE}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_friend_name" />

      <ImageView
        android:id="@+id/iv_header_more_options"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_12"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/ic_more_options"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
      android:id="@+id/ll_top_options_bar"
      android:layout_width="match_parent"
      android:layout_height="52dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:visibility="@{viewModel.conversationInfo.unsuitable?View.GONE:View.VISIBLE}">

      <TextView
        android:id="@+id/tv_invite_send_resume"
        style="@style/common_Text.10sp.999999"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/chat_ic_option_bar_call_phone"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.callPhonePreCheck()}"
        android:text="@string/chat_c_call_phone" />

      <TextView
        android:id="@+id/tv_exchange_wechat"
        style="@style/common_Text.10sp.999999"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/chat_ic_option_bar_wechat"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.requestExchangeWechat()}"
        android:text="@string/chat_c_wechat" />

      <TextView
        android:id="@+id/tv_invite_interview"
        style="@style/common_Text.10sp.999999"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/chat_ic_option_bar_send_resume"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.sendResume()}"
        android:text="@string/chat_c_send_resume" />

      <TextView
        android:id="@+id/tv_mark"
        style="@style/common_Text.10sp.999999"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/chat_ic_option_bar_unsuitable"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:onClick="@{onClickListener}"
        android:text="@string/chat_c_unsuitable" />

    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:background="@drawable/bg_fff8f2"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_8"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_8"
      android:visibility="@{viewModel.conversationInfo.unsuitable?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.12sp.333333"
        android:layout_weight="1"
        android:text="@string/chat_mark_un_like" />

      <TextView
        style="@style/Text.14sp.FFFFFF"
        android:background="@drawable/bg_ff7405_radius_4"
        android:onClick="@{()->viewModel.cancelMark()}"
        android:paddingStart="@dimen/dp_6"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_2"
        android:text="@string/common_cancel" />

    </LinearLayout>

    <FrameLayout
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1">

      <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/srl_group"
        style="@style/match_match"
        android:background="@drawable/bg_f4f4f4"
        app:srlEnableLoadMore="false">

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_conversation_list"
          style="@style/match_wrap"
          android:paddingStart="@dimen/dp_14"
          android:paddingEnd="@dimen/dp_14" />

      </com.scwang.smartrefresh.layout.SmartRefreshLayout>

      <TextView
        style="@style/Text.16sp.FFFFFF"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_badge"
        android:gravity="center"
        android:minWidth="@dimen/dp_36"
        android:onClick="@{()->viewModel.refreshMsgList(false)}"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:text="@{String.valueOf(viewModel.unreadMsgCount)}"
        android:visibility="@{viewModel.unreadMsgCount==0?View.GONE:View.VISIBLE}" />
    </FrameLayout>

    <LinearLayout
      android:id="@+id/ll_edit_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_52"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_show_quick_msg"
        style="@style/common_Text.16sp"
        android:layout_marginStart="@dimen/dp_12"
        android:background="@drawable/common_bg_basic_btn_selector"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/chat_quick_msg"
        android:textColor="@color/enterprise_button_text_selector"
        android:visibility="@{viewModel.showQuickMsg?View.GONE:View.VISIBLE}" />

      <ImageView
        android:id="@+id/iv_close_quick_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:onClick="@{onClickListener}"
        android:src="@drawable/chat_ic_quick_msg"
        android:visibility="@{viewModel.showQuickMsg?View.VISIBLE:View.GONE}" />

      <EditText
        android:id="@+id/et_msg"
        style="@style/common_Text.16sp.333333"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_weight="1"
        android:background="@null"
        android:hint="@string/chat_msg_hint"
        android:singleLine="true"
        android:text="@={viewModel.msgContent}" />

      <TextView
        style="@style/Text.16sp"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/common_bg_basic_btn_selector"
        android:enabled="@{!CheckUtils.isNullOrEmpty(viewModel.msgContent)}"
        android:onClick="@{()->viewModel.sendNormalMsg()}"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_6"
        android:text="@string/chat_send"
        android:textColor="@color/common_button_text_selector"
        android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.msgContent)?View.GONE:View.VISIBLE}" />

      <ImageView
        android:id="@+id/iv_open_panel_options_layout"
        android:layout_width="@dimen/common_dp_28"
        android:layout_height="@dimen/common_dp_28"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:onClick="@{onClickListener}"
        android:src="@drawable/chat_ic_open_panel_layout"
        android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.msgContent)?View.VISIBLE:View.GONE}" />

    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <com.bxkj.jrzp.support.chat.widget.keyboardpanel.KeyboardPanelContainerLayout
      android:id="@+id/keybrd_panel_cntnr"
      android:layout_width="match_parent"
      android:layout_height="300dp">

      <com.bxkj.jrzp.support.chat.widget.keyboardpanel.KeyboardPanelOptionsLayout
        android:id="@+id/sub_panel_options_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16" />

      <LinearLayout
        android:id="@+id/sub_panel_quick_msg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
          android:id="@+id/include_quick_msg"
          layout="@layout/include_mvvm_refresh_layout"
          app:listViewModel="@{viewModel.quickMsgListViewModel}" />

        <View style="@style/Line.Horizontal.Light" />

        <LinearLayout
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:orientation="horizontal">

          <FrameLayout
            android:id="@+id/fl_quick_msg_add"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="@{onClickListener}">

            <TextView
              style="@style/Text.16sp.333333"
              android:layout_gravity="center"
              android:drawableStart="@drawable/chat_ic_quick_msg_add"
              android:drawablePadding="@dimen/dp_4"
              android:gravity="center"
              android:paddingTop="@dimen/dp_8"
              android:paddingBottom="@dimen/dp_8"
              android:text="@string/chat_quick_msg_add" />

          </FrameLayout>

          <FrameLayout
            android:id="@+id/fl_quick_msg_management"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="@{onClickListener}">

            <TextView
              style="@style/Text.16sp.333333"
              android:layout_gravity="center"
              android:drawableStart="@drawable/chat_ic_quick_msg_management"
              android:drawablePadding="@dimen/dp_4"
              android:gravity="center"
              android:paddingTop="@dimen/dp_8"
              android:paddingBottom="@dimen/dp_8"
              android:text="@string/chat_quick_msg_management" />
          </FrameLayout>

        </LinearLayout>
      </LinearLayout>
    </com.bxkj.jrzp.support.chat.widget.keyboardpanel.KeyboardPanelContainerLayout>

  </LinearLayout>
</layout>