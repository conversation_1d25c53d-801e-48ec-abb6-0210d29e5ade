<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_wrap"
  android:background="@drawable/common_bg_dialog"
  android:orientation="vertical"
  android:paddingTop="@dimen/dp_8"
  android:paddingBottom="@dimen/dp_36">

  <ImageView
    android:id="@+id/iv_close"
    style="@style/wrap_wrap"
    android:layout_gravity="end"
    android:layout_marginEnd="@dimen/dp_12"
    android:src="@drawable/ic_big_close" />

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_24"
    android:orientation="horizontal">

    <TextView
      android:id="@+id/tv_type_live"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_start_live"
      android:drawablePadding="@dimen/dp_12"
      android:gravity="center_horizontal"
      android:text="@string/home_live_type_now" />

    <View
      style="@style/Line.Vertical"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_16" />

    <TextView
      android:id="@+id/tv_type_notice"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_weight="1"
      android:drawableTop="@drawable/personal_ic_live_notice"
      android:drawablePadding="@dimen/dp_12"
      android:gravity="center_horizontal"
      android:text="@string/home_live_type_notice" />

  </LinearLayout>


</LinearLayout>