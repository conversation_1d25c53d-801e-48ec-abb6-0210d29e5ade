<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_match"
  android:animateLayoutChanges="true"
  android:background="@drawable/bg_ffffff"
  android:focusable="true"
  android:focusableInTouchMode="true"
  android:orientation="vertical">

  <include layout="@layout/common_include_title_bar" />

  <ScrollView style="@style/match_wrap">

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="vertical">

      <com.bxkj.common.widget.MyEditText
        android:id="@+id/et_search_job"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:background="@drawable/common_bg_rounded_rectangle_f4f4f4"
        android:drawableStart="@drawable/common_ic_search"
        android:drawablePadding="@dimen/dp_10"
        android:hint="@string/personal_search_position"
        android:imeOptions="actionDone"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:singleLine="true"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5"
        android:textCursorDrawable="@drawable/common_ic_custom_cursor"
        android:textSize="@dimen/sp_14" />

      <LinearLayout
        android:id="@+id/ll_select_area"
        style="@style/Layout.InfoItem"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_30">

        <TextView
          android:id="@+id/tv_location"
          style="@style/Text.15sp.888888"
          android:drawableStart="@drawable/personal_ic_search_job_location"
          android:drawablePadding="@dimen/dp_8" />

        <TextView
          android:id="@+id/tv_area"
          style="@style/Text.InfoItem"
          android:hint="@string/personal_please_select_area" />
      </LinearLayout>

      <View
        style="@style/Line.Horizontal"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30" />

      <LinearLayout
        android:id="@+id/ll_more_options"
        style="@style/match_wrap"
        android:orientation="vertical"
        android:visibility="gone">

        <!--<TextView-->
        <!--android:id="@+id/tv_position_tag"-->
        <!--style="@style/personal_Text.SearchJobOptions"-->
        <!--android:drawableStart="@drawable/personal_ic_search_job_position_tag"-->
        <!--android:hint="@string/personal_select_position_tag" />-->

        <!--<View-->
        <!--style="@style/common_Line.Horizontal"-->
        <!--android:layout_marginEnd="@dimen/common_dp_30"-->
        <!--android:layout_marginStart="@dimen/common_dp_30" />-->

        <TextView
          android:id="@+id/tv_expect_industry"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_expect_industry"
          android:hint="@string/personal_select_expect_industry" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_expect_salary"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_expect_salary"
          android:hint="@string/personal_select_expect_salary" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_work_exp"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_work_exp"
          android:hint="@string/personal_select_work_exp" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_education"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_education"
          android:hint="@string/personal_select_education" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_working_nature"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_work_nature"
          android:hint="@string/personal_select_working_nature" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_publish_date"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_publish_date"
          android:hint="@string/personal_select_publish_date" />


        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />

        <TextView
          android:id="@+id/tv_company_nature"
          style="@style/Text.SearchJobOptions"
          android:drawableStart="@drawable/personal_ic_search_job_company_nature"
          android:hint="@string/personal_select_company_nature" />

        <View
          style="@style/Line.Horizontal"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginEnd="@dimen/dp_30" />
      </LinearLayout>

      <TextView
        android:id="@+id/tv_expand_or_collapse"
        style="@style/Text.12sp.888888"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_30"
        android:drawableEnd="@drawable/personal_ic_search_job_expand_options"
        android:drawablePadding="@dimen/common_dp_9"
        android:text="@string/personal_select_more_options" />

      <TextView
        android:id="@+id/tv_search_job"
        style="@style/Button.Basic"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_30"
        android:layout_marginEnd="@dimen/dp_30"
        android:text="@string/common_search_position"
        android:textSize="@dimen/dp_16" />

      <View
        style="@style/Line.Horizontal"
        android:layout_height="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_30" />

      <RelativeLayout
        android:id="@+id/rl_search_tool"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48">

        <TextView
          style="@style/Text.15sp.333333"
          android:layout_centerVertical="true"
          android:layout_marginStart="@dimen/dp_12"
          android:gravity="center_vertical"
          android:text="@string/personal_record"
          android:textColor="@color/cl_333333"
          android:textSize="@dimen/sp_14" />

        <ImageView
          android:id="@+id/iv_clear_search_record"
          style="@style/wrap_wrap"
          android:layout_alignParentEnd="true"
          android:layout_centerVertical="true"
          android:layout_marginEnd="@dimen/dp_12"
          android:src="@drawable/personal_ic_delete" />

        <View
          style="@style/Line.Horizontal"
          android:layout_alignParentBottom="true"
          android:layout_marginStart="@dimen/dp_12" />
      </RelativeLayout>

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_search_record"
        style="@style/match_wrap" />
    </LinearLayout>

  </ScrollView>


</LinearLayout>