<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.fragment.applyrecordlist.ResumeDeliveryRecordViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <RadioGroup
      android:id="@+id/rg_delivery_state"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@drawable/bg_ffffff"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_12"
      android:paddingTop="@dimen/dp_8"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_8">

      <RadioButton
        android:id="@+id/rb_state_1"
        style="@style/Text.12sp"
        android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
        android:button="@null"
        android:checked="true"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/c_resume_delivery_record_all"
        android:textColor="@color/cl_888888_to_fe6600_selector" />

      <RadioButton
        android:id="@+id/rb_state_2"
        style="@style/Text.12sp"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
        android:button="@null"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/c_resume_delivery_record_viewed"
        android:textColor="@color/cl_888888_to_fe6600_selector" />

      <RadioButton
        android:id="@+id/rb_state_3"
        style="@style/Text.12sp"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
        android:button="@null"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/c_resume_delivery_record_inappropriate"
        android:textColor="@color/cl_888888_to_fe6600_selector" />

    </RadioGroup>

    <include
      android:id="@+id/include_job_list"
      layout="@layout/include_mvvm_refresh_layout"
      bind:listViewModel="@{viewModel.listViewModel}" />

  </LinearLayout>
</layout>