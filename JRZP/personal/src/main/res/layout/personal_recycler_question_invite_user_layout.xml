<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.InviteUserItemData.Group" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap">

        <TextView
            android:id="@+id/invite_tag"
            style="@style/Text.15sp.333333"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginTop="@dimen/dp_12"
            android:text="@string/question_details_invite_tag"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_see_all"
            style="@style/Text.14sp.333333"
            android:layout_marginEnd="@dimen/dp_14"
            android:text="@string/question_details_invite_see_all"
            app:layout_constraintBottom_toBottomOf="@id/invite_tag"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/invite_tag" />

        <View
            android:id="@+id/v_line"
            style="@style/Line.Horizontal"
            android:layout_marginTop="@dimen/dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/invite_tag" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_invite_users"
            style="@style/match_wrap"
            bind:items="@{data.items}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_line" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>