<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.addshieldcompany.AddShieldCompanyViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <LinearLayout
      android:id="@+id/ll_search_bar"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:gravity="center_vertical"
      android:minHeight="@dimen/dp_52">

      <ImageView
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_18"
        android:src="@drawable/ic_search" />

      <com.bxkj.common.widget.MyEditText
        style="@style/Text.15sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_8"
        android:layout_weight="1"
        android:background="@null"
        android:hint="@string/shield_company_hint"
        android:text="@={viewModel.searchKeyword}" />

      <TextView
        android:id="@+id/tv_cancel"
        style="@style/Text.15sp.333333"
        android:layout_height="match_parent"
        android:gravity="center"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20"
        android:text="@string/shield_company_add_cancel" />

    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1"
      android:orientation="vertical"
      android:visibility="@{viewModel.searchKeyword.length()&gt;0?View.GONE:View.VISIBLE}">

      <TextView
        style="@style/Text.18sp.333333.Bold"
        android:layout_margin="@dimen/dp_20"
        android:text="@string/shield_company_search_tips" />

      <TextView
        style="@style/Text.15sp.333333"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_20"
        android:text="@string/shield_company_search_tips_content" />
    </LinearLayout>

    <LinearLayout
      android:id="@+id/ll_search_result"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1"
      android:orientation="vertical"
      android:visibility="@{viewModel.searchKeyword.length()&gt;0?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_width="match_parent"
        android:paddingStart="@dimen/dp_20"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_12"
        android:text="@{viewModel.searchLoading?@string/shield_company_search_loading:@string/shield_company_recommend_format(viewModel.searchKeyword,viewModel.searchResultCount)}" />

      <View style="@style/Line.Horizontal" />

      <include
        layout="@layout/include_mvvm_refresh_layout"
        app:listViewModel="@{viewModel.searchResultListViewModel}" />
    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingTop="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12">

      <TextView
        android:id="@+id/tv_all_selected"
        style="@style/Text.16sp.ff7647"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_weight="1"
        android:background="@drawable/frame_ff865d_radius_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.switchSelectAllState()}"
        android:text="@{viewModel.allSelect?@string/shield_company_add_cancel:@string/shield_company_select_all}" />

      <TextView
        style="@style/Button.Basic"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_weight="3"
        android:enabled="@{viewModel.selectedItems.size&gt;0}"
        android:onClick="@{()->viewModel.addShieldCompany()}"
        android:text="@{@string/shield_company_select_count_format(viewModel.selectedItems.size)}" />

    </LinearLayout>
  </LinearLayout>
</layout>