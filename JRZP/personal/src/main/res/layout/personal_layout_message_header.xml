<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.UnreadMsgGroupData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <TextView
      android:id="@+id/tv_notice_permission"
      style="@style/Text.Tips"
      android:layout_gravity="center"
      android:drawableStart="@drawable/ic_payment_notice"
      android:gravity="center_vertical"
      android:text="@{HtmlUtils.fromHtml(@string/message_conversation_no_notice_permission_tips)}"
      android:textSize="@dimen/common_sp_14"
      android:visibility="gone"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />
    <!--    android:visibility="@{data.hasNoticePermission?View.GONE:View.VISIBLE}"-->

    <FrameLayout
      android:id="@+id/fl_notice"
      style="@style/Text.MessageGroupItem"
      android:layout_width="@dimen/dp_0"
      app:layout_constraintEnd_toStartOf="@id/fl_sub"
      app:layout_constraintHorizontal_weight="1"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_notice_permission">

      <TextView
        android:id="@+id/tv_notice"
        style="@style/Text.14sp.333333"
        android:layout_gravity="center"
        android:drawableTop="@drawable/ic_message_notice"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/message_notice" />

      <com.bxkj.common.widget.badge.BadgeTextView
        style="@style/wrap_wrap"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{String.valueOf(data.unreadMsgNoticeCount)}" />

    </FrameLayout>

    <FrameLayout
      android:id="@+id/fl_sub"
      style="@style/Text.MessageGroupItem"
      android:layout_width="@dimen/dp_0"
      app:layout_constraintEnd_toStartOf="@id/fl_see"
      app:layout_constraintHorizontal_weight="1"
      app:layout_constraintStart_toEndOf="@id/fl_notice"
      app:layout_constraintTop_toBottomOf="@id/tv_notice_permission">

      <TextView
        android:id="@+id/tv_sub"
        style="@style/Text.14sp.333333"
        android:layout_gravity="center"
        android:drawableTop="@drawable/ic_message_sub"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/message_hot_job" />

      <com.bxkj.common.widget.badge.BadgeTextView
        style="@style/wrap_wrap"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{String.valueOf(data.unreadSubMsgCount)}" />

    </FrameLayout>

    <FrameLayout
      android:id="@+id/fl_see"
      style="@style/Text.MessageGroupItem"
      android:layout_width="@dimen/dp_0"
      app:layout_constraintEnd_toStartOf="@id/fl_system"
      app:layout_constraintHorizontal_weight="1"
      app:layout_constraintStart_toEndOf="@id/fl_sub"
      app:layout_constraintTop_toBottomOf="@id/tv_notice_permission">

      <TextView
        android:id="@+id/tv_see"
        style="@style/Text.14sp.333333"
        android:layout_gravity="center"
        android:drawableTop="@drawable/ic_message_see"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/message_see" />

      <com.bxkj.common.widget.badge.BadgeTextView
        style="@style/wrap_wrap"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{String.valueOf(data.unreadViewMeCount)}" />

    </FrameLayout>


    <FrameLayout
      android:id="@+id/fl_system"
      style="@style/Text.MessageGroupItem"
      android:layout_width="@dimen/dp_0"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_weight="1"
      app:layout_constraintStart_toEndOf="@id/fl_see"
      app:layout_constraintTop_toBottomOf="@id/tv_notice_permission">

      <TextView
        android:id="@+id/tv_system"
        style="@style/Text.14sp.333333"
        android:layout_gravity="center"
        android:drawableTop="@drawable/ic_message_system"
        android:drawablePadding="@dimen/dp_12"
        android:text="@string/message_system" />

      <com.bxkj.common.widget.badge.BadgeTextView
        style="@style/wrap_wrap"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{String.valueOf(data.unreadSystemMsgCount)}" />

    </FrameLayout>

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>