<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.personal.data.QARankItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_8">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            app:civ_border_color="@color/common_f4f4f4"
            app:civ_border_width="@dimen/dp_1"
            bind:imgUrl="@{data.userPhoto}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_nick_name"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_16"
            android:text="@{data.userName}"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/tv_follow"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_avatar" />

        <TextView
            android:id="@+id/tv_notice_count"
            style="@style/Text.12sp.888888"
            android:text="@{@string/qa_rank_answer_count_format(data.count)}"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintStart_toStartOf="@id/tv_nick_name"
            app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />


        <TextView
            android:id="@+id/tv_follow"
            style="@style/Text.12sp"
            android:layout_width="@dimen/common_dp_60"
            android:background="@{!data.followUser?@drawable/bg_fe6600_round:@drawable/frame_10c198_round}"
            android:gravity="center"
            android:paddingTop="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_4"
            android:text="@{!data.followUser?@string/shuangxuan_follow:@string/shuangxuan_followed}"
            android:textColor="@{!data.followUser?@color/common_white:@color/cl_ff7405}"
            android:visibility="@{data.isSelf()?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_avatar" />

        <View
            style="@style/Line.Horizontal"
            android:layout_marginTop="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>