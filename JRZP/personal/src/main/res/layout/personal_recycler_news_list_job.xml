<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.NewsItemData" />
    </data>

    <!--资讯列表直招类-->
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingStart="@dimen/dp_14"
        android:paddingTop="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_14">

        <TextView
            android:id="@+id/tv_job_names"
            style="@style/Text.17sp.000000"
            android:layout_width="@dimen/dp_0"
            android:lineSpacingExtra="@dimen/dp_3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_company"
            style="@style/Text.12sp.999999"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.companyName}"
            app:layout_constraintEnd_toStartOf="@id/tv_tag"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_job_names" />

        <TextView
            android:id="@+id/tv_tag"
            style="@style/Text.10sp.FFFFFF"
            android:background="@color/cl_ff7405"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            android:text="@string/home_news_job_item_tag"
            app:layout_constraintBottom_toBottomOf="@id/tv_company"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_company" />

        <View
            style="@style/Line.Horizontal"
            android:layout_marginTop="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_company" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
