<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data></data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <LinearLayout
      android:id="@+id/ll_default_resume"
      style="@style/Layout.InfoItem">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/personal_resume_default" />

      <TextView
        android:id="@+id/tv_resume_default"
        style="@style/Text.InfoItem" />
    </LinearLayout>

    <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout
      android:id="@+id/ll_resume_show"
      style="@style/Layout.InfoItem">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/personal_resume_show" />

      <TextView
        android:id="@+id/tv_resume_show"
        style="@style/Text.InfoItem" />
    </LinearLayout>

    <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout
      android:id="@+id/ll_auto_refresh"
      style="@style/Layout.InfoItem">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/personal_resume_auto_refresh" />

      <TextView
        android:id="@+id/tv_auto_refresh"
        style="@style/Text.InfoItem" />
    </LinearLayout>

    <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />


  </LinearLayout>
</layout>
