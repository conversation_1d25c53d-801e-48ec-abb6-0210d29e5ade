<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:background="@drawable/bg_bottom_sheet"
        android:orientation="vertical">

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginBottom="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_18"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingEnd="@dimen/dp_16"
            android:paddingStart="@dimen/dp_16">

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Text.16sp.999999"
                android:text="@string/common_cancel" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text.DialogTitle"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/personal_job_intention_salary" />

            <TextView
                android:id="@+id/tv_confirm"
                style="@style/Text.16sp.ff7647"
                android:text="@string/confirm" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <com.contrarywind.view.WheelView
                android:id="@+id/wheel_min_salary"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <View
                android:layout_width="12dp"
                android:layout_height="1dp"
                android:background="@drawable/bg_f4f4f4" />

            <com.contrarywind.view.WheelView
                android:id="@+id/wheel_max_salary"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>
</layout>