<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_wrap"
  android:orientation="vertical"
  android:paddingStart="@dimen/dp_12"
  android:paddingEnd="@dimen/dp_12">

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_original_position_short" />

    <TextView
      android:id="@+id/tv_original_position"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_expect_position" />

    <TextView
      android:id="@+id/tv_expect_position"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_specific_position" />

    <TextView
      android:id="@+id/tv_specific_position"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_working_nature" />

    <TextView
      android:id="@+id/tv_working_nature"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_expected_salary" />

    <TextView
      android:id="@+id/tv_expected_salary"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_working_place" />

    <TextView
      android:id="@+id/tv_work_place"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_work_exp" />

    <TextView
      android:id="@+id/tv_work_exp"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_16"
    android:orientation="horizontal">

    <TextView
      style="@style/Text.15sp.333333"
      android:text="@string/personal_duty_time" />

    <TextView
      android:id="@+id/tv_duty_time"
      style="@style/Text.15sp.888888"
      android:layout_marginStart="@dimen/dp_16" />

  </LinearLayout>
</LinearLayout>