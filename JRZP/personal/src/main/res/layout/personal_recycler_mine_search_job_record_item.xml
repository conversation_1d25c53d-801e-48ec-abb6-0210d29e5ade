<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48">

    <TextView
        android:id="@+id/tv_job_name"
        style="@style/Text.15sp.888888.SingleLine"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_toStartOf="@id/tv_job_count"
        android:drawablePadding="@dimen/dp_10"
        android:drawableStart="@drawable/personal_ic_search_record" />

    <TextView
        android:id="@+id/tv_job_count"
        style="@style/Text.15sp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_12"
        android:text="fadfad"
        android:textColor="@color/cl_ff7405" />

    <View
        style="@style/Line.Horizontal"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dp_12" />
</RelativeLayout>