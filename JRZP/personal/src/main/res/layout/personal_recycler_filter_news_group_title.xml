<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/common_dp_44">

    <TextView
        android:id="@+id/tv_group_title"
        style="@style/Text.15sp.888888"
        android:layout_marginStart="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_more"
        style="@style/Text.12sp.FF7647"
        android:layout_marginEnd="@dimen/dp_12"
        android:drawableEnd="@drawable/common_ic_next"
        android:drawablePadding="@dimen/common_dp_5"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>