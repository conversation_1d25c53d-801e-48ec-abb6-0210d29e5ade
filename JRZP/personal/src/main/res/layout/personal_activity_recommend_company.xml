<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.recommendcompany.RecommendCompanyViewModel" />
  </data>

  <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match">

    <com.google.android.material.appbar.AppBarLayout
      android:id="@+id/app_bar_layout"
      style="@style/match_wrap"
      android:minHeight="@dimen/common_dp_42">

      <com.google.android.material.appbar.CollapsingToolbarLayout
        android:id="@+id/ctl_header"
        style="@style/match_wrap"
        app:layout_scrollFlags="scroll|exitUntilCollapsed">

        <FrameLayout style="@style/match_wrap">

          <ImageView
            android:id="@+id/iv_header_bg_blur"
            style="@style/match_wrap"
            android:adjustViewBounds="true" />

          <ImageView
            android:id="@+id/iv_header_bg"
            style="@style/match_wrap"
            android:adjustViewBounds="true"
            android:src="@drawable/personal_img_recommend_company_header" />
        </FrameLayout>

        <FrameLayout
          android:id="@+id/fl_title_bar"
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_48"
          app:layout_collapseMode="pin">

          <ImageView
            android:id="@+id/iv_back"
            style="@style/wrap_wrap"
            android:layout_gravity="center_vertical"
            android:src="@drawable/common_ic_white_back" />

          <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.FFFFFF"
            android:layout_gravity="center"
            android:text="@string/personal_recommend_company_title" />
        </FrameLayout>
      </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <com.sanjindev.pagestatelayout.PageStateLayout
      android:id="@+id/psl_content"
      style="@style/match_match"
      android:background="@drawable/personal_recommend_company_bg"

      app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_recommend_company"
        style="@style/match_match"
        bind:items="@{viewModel.recommendCompanyList}" />
    </com.sanjindev.pagestatelayout.PageStateLayout>

  </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>