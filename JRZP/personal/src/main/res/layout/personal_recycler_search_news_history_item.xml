<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.support.db.entry.SearchRecord" />
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap">

        <TextView
            android:id="@+id/tv_item"
            style="@style/Text.18sp.333333"
            android:layout_alignParentStart="true"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_toStartOf="@id/iv_delete"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.keyword}" />

        <ImageView
            android:id="@+id/iv_delete"
            style="@style/wrap_wrap"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp_12"
            android:src="@drawable/common_ic_close" />

        <View
            android:id="@+id/v_split"
            style="@style/Line.Vertical"
            android:layout_height="@dimen/dp_12"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true" />

    </RelativeLayout>
</layout>