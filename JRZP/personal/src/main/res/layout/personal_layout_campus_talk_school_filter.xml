<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:background="@drawable/bg_ffffff"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_city"
            style="@style/Text.14sp.666666"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/ic_expand_arrow"
            android:gravity="center_vertical"
            android:padding="12dp" />

        <View style="@style/Line.Horizontal.Light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_city_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_12"
                android:visibility="gone" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scroll_school_content"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_school_all"
                        style="@style/Text.14sp.333333"
                        android:layout_width="match_parent"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_12"
                        android:background="@drawable/bg_f4f4f4_to_fe6600_radius_2"
                        android:gravity="center"
                        android:padding="@dimen/dp_8"
                        android:textColor="@color/common_333333_to_ffffff_selector" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_school_list"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginTop="@dimen/dp_8" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingBottom="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_12"
                android:paddingStart="@dimen/dp_12"
                android:paddingTop="@dimen/dp_2">

                <TextView
                    android:id="@+id/tv_reset"
                    style="@style/Text.14sp.666666"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/frame_666666_radius_6"
                    android:gravity="center"
                    android:padding="@dimen/dp_12"
                    android:text="@string/common_reset"
                    android:textColor="@color/common_333333_to_ffffff_selector" />

                <TextView
                    android:id="@+id/tv_confirm"
                    style="@style/Text.14sp.FFFFFF"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_weight="1"
                    android:background="@drawable/bg_fe6600_radius_8"
                    android:gravity="center"
                    android:padding="@dimen/dp_12"
                    android:text="@string/common_confirm" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</layout>