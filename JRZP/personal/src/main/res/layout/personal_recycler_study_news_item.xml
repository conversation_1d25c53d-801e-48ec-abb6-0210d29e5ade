<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="data"
            type="com.bxkj.personal.data.StudyNewsItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap"
        android:paddingStart="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_14">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{data.title}"
            app:layout_constraintEnd_toStartOf="@id/iv_photo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <TextView
            android:id="@+id/tv_type"
            style="@style/Text.12sp.888888"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:text="@{@string/study_news_item_type_format(data.typeName,data.typeName2)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_comment_count"
            style="@style/Text.12sp.888888"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@{@string/study_news_item_comment_format(data.commentsCount)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_type"
            app:layout_constraintStart_toEndOf="@id/tv_type" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.12sp.888888"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@{data.timeDiff}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_comment_count"
            app:layout_constraintStart_toEndOf="@id/tv_comment_count" />

        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="114dp"
            android:layout_height="76dp"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:visibility="@{CheckUtils.isNullOrEmpty(data.pic)?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            style="@style/Line.Horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>