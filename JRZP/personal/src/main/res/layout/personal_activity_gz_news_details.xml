<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.gznews.GzNewsDetailsViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44">

      <ImageView
        android:id="@+id/iv_left"
        style="@style/wrap_wrap"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_ic_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <LinearLayout
        android:id="@+id/ll_title_info"
        style="@style/wrap_wrap"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
          style="@style/wrap_wrap"
          android:layout_height="@dimen/dp_22"
          android:adjustViewBounds="true"
          android:src="@drawable/ic_title_bar_logo" />

        <TextView
          style="@style/wrap_wrap"
          android:layout_marginStart="@dimen/dp_4"
          android:layout_marginEnd="@dimen/dp_4"
          android:text="@string/common_point"
          android:textColor="@color/common_black"
          android:textSize="@dimen/dp_20"
          android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.newsType)?View.GONE:View.VISIBLE}" />

        <TextView
          android:id="@+id/tv_news_type"
          style="@style/Text.16sp.000000"
          android:onClick="@{()->viewModel.toTypeNews()}"
          android:text="@{viewModel.newsType}"
          android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.newsType)?View.GONE:View.VISIBLE}" />
      </LinearLayout>

      <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title_bar_user_info"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_8"
        android:onClick="@{()->viewModel.toAuthorHome()}"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_left"
        app:layout_constraintTop_toTopOf="parent">

        <de.hdodenhof.circleimageview.CircleImageView
          android:id="@+id/iv_title_avatar"
          android:layout_width="@dimen/common_dp_32"
          android:layout_height="@dimen/common_dp_32"
          app:civ_border_color="@color/common_f4f4f4"
          app:civ_border_width="@dimen/dp_1"
          bind:imgUrl="@{viewModel.newsDetails.dwLogo}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_title_nike_name"
          style="@style/Text.14sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:text="@{viewModel.newsDetails.dwName}"
          android:textStyle="bold"
          app:layout_constraintBottom_toTopOf="@id/tv_fans_count"
          app:layout_constraintStart_toEndOf="@id/iv_title_avatar"
          app:layout_constraintTop_toTopOf="@id/iv_title_avatar" />

        <TextView
          android:id="@+id/tv_fans_count"
          style="@style/Text.11sp.333333"
          android:text="@{viewModel.newsDetails.createTime}"
          app:layout_constraintBottom_toBottomOf="@id/iv_title_avatar"
          app:layout_constraintStart_toStartOf="@id/tv_title_nike_name"
          app:layout_constraintTop_toBottomOf="@id/tv_title_nike_name" />
      </androidx.constraintlayout.widget.ConstraintLayout>

      <ImageView
        android:id="@+id/iv_search"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_8"
        android:onClick="@{onClickListener}"
        android:src="@drawable/ic_news_details_search"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_more"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_more"
        style="@style/wrap_wrap"
        android:minWidth="@dimen/dp_36"
        android:minHeight="@dimen/dp_36"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/ic_more_options"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <View
        style="@style/Line.Horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
      android:id="@+id/refresh_content"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      app:enableLoadMore="@{viewModel.commentListViewModel.refreshLayoutViewModel.enableLoadMore}"
      app:enableRefresh="@{viewModel.commentListViewModel.refreshLayoutViewModel.enableRefresh}"
      app:loadFinish="@{viewModel.commentListViewModel.refreshLayoutViewModel.loadFinish}"
      app:noMoreData="@{viewModel.commentListViewModel.refreshLayoutViewModel.noMoreData}"
      app:onRefreshAndLoadMoreListener="@{viewModel.commentListViewModel.refreshLayoutViewModel}"
      app:srlEnableLoadMoreWhenContentNotFull="false">

      <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_content"
        style="@style/match_match">

        <LinearLayout
          style="@style/match_wrap"
          android:orientation="vertical">

          <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_top_layout"
            style="@style/match_wrap"
            android:paddingStart="@dimen/dp_14"
            android:paddingTop="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_14"
            android:paddingBottom="@dimen/dp_24">

            <TextView
              android:id="@+id/tv_title"
              style="@style/Text.22sp.333333"
              android:layout_width="@dimen/dp_0"
              android:text="@{viewModel.newsDetails.title}"
              android:textStyle="bold"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
              android:id="@+id/cl_user_info"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_24"
              android:visibility="@{viewModel.newsDetails.dwID&gt;0?View.VISIBLE:View.GONE}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/tv_title">

              <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/iv_author_avatar"
                android:layout_width="@dimen/common_dp_32"
                android:layout_height="@dimen/common_dp_32"
                android:onClick="@{()->viewModel.toAuthorHome()}"
                bind:imgUrl="@{viewModel.newsDetails.dwLogo}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

              <TextView
                android:id="@+id/tv_nick_name"
                style="@style/Text.14sp.333333"
                android:layout_marginStart="@dimen/dp_8"
                android:onClick="@{()->viewModel.toAuthorHome()}"
                android:text="@{viewModel.newsDetails.dwName}"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tv_publish_time"
                app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
                app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

              <TextView
                android:id="@+id/tv_publish_time"
                style="@style/Text.12sp.999999"
                android:text="@{viewModel.newsDetails.createTime}"
                app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                app:layout_constraintStart_toStartOf="@id/tv_nick_name"
                app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

              <TextView
                android:id="@+id/tv_follow"
                style="@style/Text.12sp"
                android:layout_width="@dimen/common_dp_60"
                android:layout_height="@dimen/dp_24"
                android:background="@{viewModel.newsDetails.isFollowUser||viewModel.newsDetails.isFollowDW?@drawable/frame_eaeaea_round:@drawable/bg_10c198_round}"
                android:gravity="center"
                android:onClick="@{()->viewModel.addOrRemoveFollow()}"
                android:text="@{viewModel.newsDetails.isFollowUser||viewModel.newsDetails.isFollowDW?@string/news_details_followed:@string/news_details_follow}"
                android:textColor="@{viewModel.newsDetails.isFollowUser||viewModel.newsDetails.isFollowDW?@color/cl_333333:@color/common_white}"
                app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
              android:id="@+id/iv_ad_top"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_12"
              android:adjustViewBounds="true"
              android:onClick="@{()->viewModel.openAdUrl()}"
              android:visibility="@{viewModel.newsAdInfo==null?View.GONE:View.VISIBLE}"
              bind:imgUrl="@{viewModel.newsAdInfo.webPic1}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/cl_user_info" />

            <com.bxkj.common.widget.YUIWebView
              android:id="@+id/web_content"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_16"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/iv_ad_top" />

            <TextView
              android:id="@+id/tv_editor"
              style="@style/Text.12sp.888888"
              android:layout_width="@dimen/dp_0"
              android:layout_marginTop="@dimen/dp_8"
              android:text="@{@string/news_details_editor_format(viewModel.newsDetails.bianji)}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/web_content" />

            <TextView
              android:id="@+id/tv_read_num"
              style="@style/Text.12sp.999999"
              android:layout_marginTop="@dimen/dp_16"
              android:text="@{@string/news_details_read_num_format(viewModel.newsDetails.count)}"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/tv_editor" />

            <ImageView
              android:id="@+id/iv_ad_bottom"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_12"
              android:adjustViewBounds="true"
              android:onClick="@{()->viewModel.openAdUrl()}"
              android:visibility="@{viewModel.newsAdInfo==null?View.GONE:View.VISIBLE}"
              bind:imgUrl="@{viewModel.newsAdInfo.webPic2}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/tv_editor" />

          </androidx.constraintlayout.widget.ConstraintLayout>

          <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_recommend_news"
            style="@style/match_wrap"
            android:overScrollMode="never"
            bind:items="@{viewModel.recommendNewsList}" />

          <View style="@style/Line.Horizontal" />

          <LinearLayout
            android:id="@+id/ll_comment_like_info"
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_18"
            android:orientation="horizontal">

            <TextView
              style="@style/Text.13sp.333333"
              android:layout_marginStart="@dimen/dp_14"
              android:layout_weight="1"
              android:text="@{@string/news_details_comment_count_format(viewModel.newsDetails.commentsCount)}"
              android:textStyle="bold" />

            <TextView
              style="@style/Text.13sp.333333"
              android:layout_marginEnd="@dimen/dp_14"
              android:text="@{@string/news_details_like_count_format(viewModel.newsDetails.likesCount)}"
              android:textStyle="bold" />
          </LinearLayout>

          <include
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.commentListViewModel}" />

        </LinearLayout>
      </androidx.core.widget.NestedScrollView>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_comment"
        style="@style/Text.12sp.888888"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_6"
        android:layout_weight="1"
        android:background="@drawable/bg_news_details_comment"
        android:drawableStart="@drawable/ic_news_details_comment_edit"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="center_vertical"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_8"
        android:text="@string/news_details_comment_hint" />

      <FrameLayout
        android:id="@+id/fl_comment"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{onClickListener}">

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_8"
          android:src="@drawable/ic_news_details_comment" />

        <TextView
          style="@style/wrap_wrap"
          android:layout_gravity="end"
          android:layout_marginTop="@dimen/dp_6"
          android:background="@drawable/bg_fe6600_round"
          android:paddingStart="@dimen/dp_4"
          android:paddingEnd="@dimen/dp_4"
          android:text="@{String.valueOf(viewModel.newsDetails.commentsCount)}"
          android:textColor="@color/common_white"
          android:textSize="8sp" />

      </FrameLayout>

      <ImageView
        android:id="@+id/iv_collect"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{()->viewModel.addOrRemoveCollection()}"
        android:src="@{viewModel.newsDetails.isStow?@drawable/ic_news_details_collect_selected:@drawable/ic_news_details_collect}" />

      <ImageView
        android:id="@+id/iv_like"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{()->viewModel.addOrRemoveLike()}"
        android:src="@{viewModel.newsDetails.isLike?@drawable/ic_moment_like_selected:@drawable/ic_moment_like_normal}" />

      <ImageView
        android:id="@+id/iv_share"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_16"
        android:onClick="@{onClickListener}"
        android:src="@drawable/ic_news_details_share" />

    </LinearLayout>

  </LinearLayout>
</layout>