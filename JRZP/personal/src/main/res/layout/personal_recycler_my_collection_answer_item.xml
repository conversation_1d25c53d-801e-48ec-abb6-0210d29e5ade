<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.UserHistoryItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingTop="@dimen/dp_14"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_14">

        <ImageView
            android:id="@+id/iv_checked"
            style="@style/wrap_wrap"
            android:src="@drawable/common_bg_checkbox_selector"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/common_dp_32"
            android:layout_height="@dimen/common_dp_32"
            android:layout_marginStart="@dimen/dp_8"
            app:civ_border_color="@color/common_f4f4f4"
            app:civ_border_width="@dimen/dp_1"
            bind:imgUrl="@{data.userPhoto}"
            app:layout_constraintStart_toEndOf="@id/iv_checked"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/dp_12" />

        <TextView
            android:id="@+id/tv_nick_name"
            style="@style/Text.14sp.333333"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@{data.userName}"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/tv_time"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_avatar" />

        <TextView
            android:id="@+id/tv_time"
            style="@style/Text.12sp.999999"
            android:text="@{data.createTime}"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
            app:layout_constraintStart_toStartOf="@id/tv_nick_name"
            app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

        <TextView
            android:id="@+id/tv_answer_title"
            style="@style/Text.17sp.000000"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@{@string/user_question_answer_format(data.questionTitle)}"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@id/iv_avatar"
            app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

        <TextView
            android:id="@+id/tv_content"
            style="@style/Text.17sp.000000"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_8"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/dp_4"
            android:maxLines="5"
            android:text="@{data.content}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/iv_avatar"
            app:layout_constraintTop_toBottomOf="@id/tv_answer_title" />

        <TextView
            android:id="@+id/tv_views"
            style="@style/Text.12sp.333333"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableStart="@drawable/ic_user_notice_views"
            android:drawablePadding="@dimen/dp_4"
            android:text="@{String.valueOf(data.view)}"
            app:layout_constraintEnd_toStartOf="@id/tv_comment"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_content" />

        <TextView
            android:id="@+id/tv_comment"
            style="@style/Text.12sp.333333"
            android:drawableStart="@drawable/ic_user_notice_comment"
            android:drawablePadding="@dimen/dp_4"
            android:text="@{String.valueOf(data.commentsCount)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_views"
            app:layout_constraintEnd_toStartOf="@id/tv_like"
            app:layout_constraintStart_toEndOf="@id/tv_views" />

        <TextView
            android:id="@+id/tv_like"
            style="@style/Text.12sp.333333"
            android:layout_marginEnd="@dimen/dp_50"
            android:drawableStart="@{data.like?@drawable/ic_user_notice_like_sel:@drawable/ic_user_notice_like_nor}"
            android:drawablePadding="@dimen/dp_4"
            android:text="@{String.valueOf(data.likesCount)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_comment"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_comment" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>