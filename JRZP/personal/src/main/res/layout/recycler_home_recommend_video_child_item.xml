<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.video.data.OnlineVideoData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="248dp"
    android:layout_height="336dp"
    android:layout_marginStart="@dimen/dp_14">

    <ImageView
      android:layout_width="0dp"
      android:layout_height="@dimen/dp_0"
      android:background="@drawable/bg_000000_radius_4"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      bind:loadRadiusCenterImg="@{data.pic}" />

    <androidx.constraintlayout.widget.Guideline
      android:id="@+id/guideline"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      app:layout_constraintGuide_percent="0.7" />

    <View
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:background="@drawable/shadow_bottom"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/guideline" />

    <TextView
      android:id="@+id/tv_company"
      style="@style/Text.12sp.FFFFFF"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.companyName}"
      app:layout_constraintBottom_toTopOf="@id/tv_title"
      app:layout_constraintEnd_toStartOf="@id/tv_tag"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_goneMarginEnd="@dimen/dp_0" />

    <TextView
      android:id="@+id/tv_tag"
      style="@style/Text.10sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_12"
      android:background="@color/cl_ff7405"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_2"
      android:text="@{data.subTypeName}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.subTypeName)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toTopOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.18sp.FFFFFF.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12"
      android:layout_marginBottom="@dimen/dp_12"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.showTitle}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>