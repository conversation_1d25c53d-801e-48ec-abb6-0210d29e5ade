<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <ScrollView
        style="@style/match_wrap"
        android:overScrollMode="never">

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_30"
            android:paddingTop="@dimen/dp_30"
            android:paddingEnd="@dimen/dp_30">

            <LinearLayout
                style="@style/wrap_wrap"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View style="@style/View.RedTextStartTag" />

                <TextView
                    style="@style/Text.16sp.333333"
                    android:layout_marginStart="@dimen/dp_10"
                    android:text="@string/account_details_information" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_tips"
                style="@style/wrap_wrap"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/common_dp_5"
                android:text="@string/account_details_information_tips"
                android:textColor="@color/common_888888"
                android:textSize="12sp" />

            <LinearLayout
                android:id="@+id/ll_graduation_time"
                style="@style/Layout.UserInfoItem"
                android:layout_marginTop="@dimen/dp_10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_graduation_time" />

                <TextView
                    android:id="@+id/tv_graduation_time"
                    style="@style/Text.InfoItem" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_QQ" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_QQ"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:drawablePadding="@dimen/dp_4"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionNext"
                    android:inputType="number" />
            </LinearLayout>


            <View style="@style/Line.Horizontal" />

            <LinearLayout
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_contract_address" />


                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_contract_address"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionNext" />
            </LinearLayout>


            <View style="@style/Line.Horizontal" />

            <LinearLayout
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_postal_code" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_postal_code"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionNext"
                    android:inputType="number" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_permanent_residence"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_permanent_residence" />

                <TextView
                    android:id="@+id/tv_permanent_residence"
                    style="@style/Text.InfoItem" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_id_type"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_id_type" />

                <TextView
                    android:id="@+id/tv_id_type"
                    style="@style/Text.InfoItem" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_id_number" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_id_number"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionNext"
                    android:inputType="number" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_marital_status"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_marital_status" />

                <TextView
                    android:id="@+id/tv_marital_status"
                    style="@style/Text.InfoItem" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_height" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_height"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionNext"
                    android:inputType="number" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_nationality"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_nationality" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_nationality"
                    style="@style/EditText.Basic"
                    android:layout_marginStart="@dimen/dp_10"
                    android:gravity="end|center_vertical"
                    android:imeOptions="actionDone" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_nation"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_nation" />

                <TextView
                    android:id="@+id/tv_nation"
                    style="@style/Text.InfoItem" />
            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <LinearLayout
                android:id="@+id/ll_political_status"
                style="@style/Layout.UserInfoItem"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/account_political_status" />

                <TextView
                    android:id="@+id/tv_political_status"
                    style="@style/Text.InfoItem" />

            </LinearLayout>

            <View style="@style/Line.Horizontal" />

            <Button
                android:id="@+id/btn_confirm"
                style="@style/Button.Basic"
                android:layout_marginTop="@dimen/common_dp_44"
                android:layout_marginBottom="@dimen/dp_30"
                android:text="@string/common_complete" />

        </LinearLayout>
    </ScrollView>


</LinearLayout>