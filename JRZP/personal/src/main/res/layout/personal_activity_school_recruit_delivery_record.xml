<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordManagementViewModel" />

  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:rightText="@{viewModel.showSelectAll?@string/school_recruit_delivery_record_cancel:@string/school_recruit_delivery_record_multi}"
      app:show_right_text_bg="false"
      app:title="@string/school_recruit_delivery_record_title" />

    <net.lucode.hackware.magicindicator.MagicIndicator
      android:id="@+id/indicator_type"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_40" />

    <View style="@style/Line.Horizontal.Light" />

    <androidx.viewpager2.widget.ViewPager2
      android:id="@+id/vp_record_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

  </LinearLayout>
</layout>