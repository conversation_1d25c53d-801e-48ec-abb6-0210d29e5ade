<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.parttimejobtypelist.PartTimeJobTypeListViewModel" />
  </data>

  <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
      android:id="@+id/app_bar_layout"
      android:layout_width="match_parent"
      android:layout_height="wrap_content">

      <com.google.android.material.appbar.CollapsingToolbarLayout
        android:id="@+id/clps_tlb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/img_part_time_job_type_day_bg"
        app:collapsedTitleGravity="start|center_vertical"
        app:collapsedTitleTextAppearance="@style/RecommendJobHeaderTitleCollapsed"
        app:expandedTitleMarginBottom="@dimen/dp_52"
        app:expandedTitleMarginStart="@dimen/dp_32"
        app:expandedTitleTextAppearance="@style/RecommendJobHeaderTitleExpanded"
        app:layout_scrollFlags="scroll|exitUntilCollapsed"
        app:scrimAnimationDuration="500"
        app:titleCollapseMode="scale">

        <View
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_8"
          android:layout_gravity="bottom"
          android:background="@drawable/bg_fff8f2_top_round" />

        <TextView
          android:id="@+id/tv_header_tips"
          style="@style/Text.14sp.FFFFFF"
          android:layout_gravity="bottom"
          android:layout_marginStart="@dimen/dp_32"
          android:layout_marginBottom="@dimen/dp_24"
          android:text="@string/personal_part_time_job_type_three_tips" />

        <View
          android:id="@+id/v_top_bg"
          style="@style/match_match"
          android:background="@drawable/bg_ff7405" />


        <androidx.appcompat.widget.Toolbar
          android:id="@+id/tool_bar"
          android:layout_width="match_parent"
          android:layout_height="?android:attr/actionBarSize"
          app:layout_collapseMode="pin"
          app:navigationIcon="@drawable/ic_back_white"
          app:title="@string/personal_part_time_job_type_three" />

      </com.google.android.material.appbar.CollapsingToolbarLayout>

      <FrameLayout
        android:id="@+id/fl_filter_option"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_fff8f2"
        app:layout_scrollFlags="scroll|exitUntilCollapsed">

        <TextView
          android:id="@+id/tv_filter"
          style="@style/Text.12sp.ec535b"
          android:layout_marginStart="14dp"
          android:layout_marginTop="@dimen/dp_8"
          android:layout_marginEnd="14dp"
          android:layout_marginBottom="@dimen/dp_8"
          android:background="@drawable/bg_ffffff_radius_4"
          android:paddingStart="@dimen/dp_12"
          android:paddingTop="@dimen/dp_4"
          android:paddingEnd="@dimen/dp_12"
          android:paddingBottom="@dimen/dp_4" />

        <ImageView
          android:layout_width="4dp"
          android:layout_height="4dp"
          android:layout_gravity="end|bottom"
          android:scaleType="centerInside"
          android:src="@drawable/personal_ic_part_time_job_filter_more" />
      </FrameLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
      android:id="@+id/fl_content"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

    </FrameLayout>

  </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>