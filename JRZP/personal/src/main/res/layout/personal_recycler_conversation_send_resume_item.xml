<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginBottom="@dimen/dp_8"
    android:orientation="vertical">

    <include layout="@layout/layout_conversation_time_tag" />

    <androidx.constraintlayout.widget.ConstraintLayout
      style="@style/match_wrap"
      android:visibility="@{data.sender?View.VISIBLE:View.GONE}">

      <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/iv_sender_avatar"
        android:layout_width="@dimen/conversation_msg_avatar_size"
        android:layout_height="@dimen/conversation_msg_avatar_size"
        bind:imgUrl="@{data.myAvatar}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <LinearLayout
        android:id="@+id/ll_send_resume"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="@drawable/bg_sender_white"
        android:drawableStart="@drawable/ic_conversation_resume"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_sender_avatar"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
          style="@style/wrap_wrap"
          android:src="@drawable/ic_conversation_resume" />

        <TextView
          style="@style/Text.16sp.333333"
          android:layout_marginStart="@dimen/dp_10"
          android:ellipsize="end"
          android:lines="1"
          android:text="@string/chat_online_resume" />
      </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
      style="@style/Text.14sp.666666"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="@dimen/dp_8"
      android:background="@drawable/bg_ebeced_radius_4"
      android:paddingStart="@dimen/dp_6"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_6"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/chat_send_resume_success"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/ll_send_resume" />

  </LinearLayout>

</layout>