<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingEnd="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_date_tag"
        android:layout_width="@dimen/common_dp_5"
        android:layout_height="@dimen/common_dp_5"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_22"
        android:src="@drawable/personal_ic_work_exp_date_tag"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/Line.Vertical"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_date_tag"
        app:layout_constraintStart_toStartOf="@id/iv_date_tag"
        app:layout_constraintTop_toBottomOf="@id/iv_date_tag" />

    <ImageView
        android:id="@+id/iv_edit"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:src="@drawable/personal_ic_recommend_job_edit"
        app:layout_constraintBottom_toBottomOf="@id/iv_date_tag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_date_tag" />

    <TextView
        android:id="@+id/tv_edu_time"
        style="@style/Text.15sp.888888"
        android:layout_marginStart="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="@id/iv_date_tag"
        app:layout_constraintStart_toEndOf="@id/iv_date_tag"
        app:layout_constraintTop_toTopOf="@id/iv_date_tag" />

    <TextView
        android:id="@+id/tv_edu_about"
        style="@style/Text.15sp.333333"
        android:layout_marginTop="@dimen/common_dp_5"
        android:ellipsize="end"
        android:lines="1"
        app:layout_constraintStart_toStartOf="@id/tv_edu_time"
        app:layout_constraintTop_toBottomOf="@id/tv_edu_time" />

</androidx.constraintlayout.widget.ConstraintLayout>