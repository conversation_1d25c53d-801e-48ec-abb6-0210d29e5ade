<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.fragment.inviteuser.InviteUserViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_back" />

            <TextView
                android:id="@+id/tv_search"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_32"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@drawable/bg_f4f4f4_radius_2"
                android:drawableStart="@drawable/common_ic_search"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center_vertical"
                android:hint="@string/invite_user_search_hint"
                android:onClick="@{onClickListener}"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_12"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Light" />

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/indicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:layout_marginStart="100dp"
            android:layout_marginEnd="100dp" />

        <View style="@style/Line.Horizontal.Light" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_content"
            style="@style/match_match" />
    </LinearLayout>
</layout>