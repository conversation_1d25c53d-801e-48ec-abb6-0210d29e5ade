<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap"
  android:background="@drawable/bg_ffffff"
  android:paddingStart="@dimen/dp_12"
  android:paddingTop="@dimen/dp_16"
  android:paddingEnd="@dimen/dp_12"
  android:paddingBottom="@dimen/dp_16">

  <TextView
    android:id="@+id/tv_job_name"
    style="@style/Text.18sp.333333.Bold"
    android:layout_width="@dimen/dp_0"
    android:layout_marginEnd="@dimen/dp_16"
    android:ellipsize="end"
    android:lines="1"
    app:layout_constrainedWidth="true"
    app:layout_constraintEnd_toStartOf="@id/tv_job_wages"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_job_wages"
    style="@style/Text.17sp"
    android:textColor="@color/cl_ff7405"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_type"
    style="@style/Text.12sp.888888"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:layout_marginTop="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_constraintStart_toStartOf="parent" />

  <TextView
    android:id="@+id/tv_job_area"
    style="@style/Text.12sp.888888"
    android:layout_marginStart="@dimen/dp_8"
    android:layout_marginTop="@dimen/dp_8"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    app:layout_constraintStart_toEndOf="@id/tv_type"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_goneMarginStart="0dp" />

  <TextView
    android:id="@+id/tv_job_degree"
    style="@style/Text.12sp.888888"
    android:layout_marginStart="@dimen/dp_8"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:layout_marginTop="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_constraintStart_toEndOf="@id/tv_job_area"
    app:layout_goneMarginStart="@dimen/dp_0" />

  <TextView
    android:id="@+id/tv_job_exp"
    style="@style/Text.12sp.888888"
    android:layout_marginStart="@dimen/dp_8"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:layout_marginTop="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_constraintStart_toEndOf="@id/tv_job_degree" />

  <TextView
    android:id="@+id/tv_identity"
    style="@style/Text.12sp.888888"
    android:layout_marginStart="@dimen/dp_8"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:layout_marginTop="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_constraintStart_toEndOf="@id/tv_job_exp" />

  <TextView
    android:id="@+id/tv_partner"
    style="@style/Text.12sp.888888"
    android:layout_marginStart="@dimen/dp_8"
    android:background="@drawable/bg_f4f4f4_radius_4"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:layout_marginTop="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_constraintStart_toEndOf="@id/tv_identity" />

  <TextView
    android:id="@+id/tv_application_state"
    style="@style/Text.12sp.888888"
    android:visibility="gone"
    app:layout_constraintBaseline_toBaselineOf="@id/tv_job_exp"
    app:layout_constraintEnd_toEndOf="parent" />

  <ImageView
    android:id="@+id/iv_video_cover"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    android:layout_marginTop="@dimen/dp_8"
    android:layout_marginBottom="@dimen/dp_8"
    android:scaleType="centerCrop"
    app:layout_constraintBottom_toTopOf="@id/barrier_job_bottom_info"
    app:layout_constraintDimensionRatio="1:1"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintEnd_toStartOf="@id/tv_application_state"
    app:layout_constraintTop_toBottomOf="@id/tv_job_name"
    app:layout_goneMarginEnd="@dimen/dp_0" />

  <TextView
    android:id="@+id/tv_date"
    style="@style/Text.14sp.888888"
    android:layout_marginTop="@dimen/dp_8"
    android:drawableStart="@drawable/ic_job_fair_start_date"
    android:drawablePadding="@dimen/dp_4"
    android:text="@{data.formatPublishDate}"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tv_job_exp" />

  <TextView
    android:id="@+id/tv_job_publish_enterprise"
    style="@style/Text.14sp.333333"
    android:layout_width="@dimen/dp_0"
    android:layout_marginTop="@dimen/dp_10"
    android:layout_marginEnd="@dimen/dp_16"
    android:ellipsize="end"
    android:lines="1"
    app:layout_constraintEnd_toStartOf="@id/barrier_job_right_info"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tv_date"
    app:layout_goneMarginTop="@dimen/dp_10" />

  <androidx.constraintlayout.widget.Barrier
    android:id="@+id/barrier_job_right_info"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    app:barrierDirection="left"
    app:constraint_referenced_ids="tv_application,tv_distance,iv_video_cover" />

  <androidx.constraintlayout.widget.Barrier
    android:id="@+id/barrier_job_bottom_info"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    app:barrierDirection="top"
    app:constraint_referenced_ids="tv_distance,tv_application" />

  <TextView
    android:id="@+id/tv_application"
    style="@style/common_Text.12sp.ffffff"
    android:layout_width="@dimen/common_dp_64"
    android:layout_height="@dimen/dp_24"
    android:background="@drawable/bg_fe6600_round"
    android:gravity="center"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="@id/tv_job_publish_enterprise"
    app:layout_constraintEnd_toStartOf="@id/tv_distance"
    app:layout_constraintTop_toTopOf="@id/tv_job_publish_enterprise"
    app:layout_goneMarginEnd="@dimen/dp_0" />

  <TextView
    android:id="@+id/tv_distance"
    style="@style/Text.14sp.333333"
    android:paddingStart="@dimen/dp_4"
    android:paddingTop="@dimen/dp_2"
    android:paddingEnd="@dimen/dp_4"
    android:paddingBottom="@dimen/dp_2"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="@id/tv_job_publish_enterprise"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="@id/tv_job_publish_enterprise" />

</androidx.constraintlayout.widget.ConstraintLayout>
