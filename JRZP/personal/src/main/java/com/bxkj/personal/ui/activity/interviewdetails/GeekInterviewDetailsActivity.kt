package com.bxkj.personal.ui.activity.interviewdetails

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.SystemUtil
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityGeekInterviewDetailsBinding

/**
 * @Description:面试详情
 */
@Route(path = GeekInterviewDetailsNavigation.PATH)
class GeekInterviewDetailsActivity :
    BaseDBActivity<PersonalActivityGeekInterviewDetailsBinding, GeekInterviewDetailsViewModel>() {

    companion object {

        fun newIntent(context: Context, jobID: Int, resumeID: Int): Intent {
            return Intent(context, GeekInterviewDetailsActivity::class.java).apply {
                putExtra(GeekInterviewDetailsNavigation.EXTRA_JOB_ID, jobID)
                putExtra(GeekInterviewDetailsNavigation.EXTRA_RESUME_ID, resumeID)
            }
        }
    }

    override fun getViewModelClass(): Class<GeekInterviewDetailsViewModel> =
        GeekInterviewDetailsViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_geek_interview_details

    override fun initPage(savedInstanceState: Bundle?) {
        statusBarManager.titleBar(viewBinding.clTitleBar).statusBarDarkFont(false).init()

        viewBinding.viewModel = viewModel

        subscribeViewModelEvent()

        viewModel.start(
            intent.getIntExtra(GeekInterviewDetailsNavigation.EXTRA_JOB_ID, 0),
            intent.getIntExtra(GeekInterviewDetailsNavigation.EXTRA_RESUME_ID, 0)
        )
    }

    private fun subscribeViewModelEvent() {
        viewModel.callHRCommand.observe(this, EventObserver {
            SystemUtil.callPhone(this, it)
        })

        viewModel.toChatCommand.observe(this, EventObserver {

        })

        viewModel.openLocalMapCommand.observe(this, EventObserver {
            SystemUtil.openLocalMapShowLocation(this, "", "", it.getConvertAddress())
        })

        viewModel.interviewStateChangeEvent.observe(this, EventObserver {
            setResult(RESULT_OK)
        })
    }
}