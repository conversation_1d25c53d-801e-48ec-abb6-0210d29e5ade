package com.bxkj.personal.di.module;

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.di.ViewModelKey;
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyViewModel;
import com.bxkj.personal.ui.activity.aboutus.AboutUsV2ViewModel;
import com.bxkj.personal.ui.activity.addanswer.AddAnswerViewModel;
import com.bxkj.personal.ui.activity.addquestion.AddQuestionViewModel;
import com.bxkj.personal.ui.activity.addshieldcompany.AddShieldCompanyViewModel;
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsViewModel;
import com.bxkj.personal.ui.activity.arealist.AreaListViewModel;
import com.bxkj.personal.ui.activity.bindmobile.BindMobileViewModel;
import com.bxkj.personal.ui.activity.campusrecruitdetails.CampusRecruitDetailsViewModel;
import com.bxkj.personal.ui.activity.campusrecruitdetails.HotCampusRecruitListViewModel;
import com.bxkj.personal.ui.activity.campusrecruitdetails.RecruitBrochureViewModel;
import com.bxkj.personal.ui.activity.campustalkdetails.CampusTalkDetailsViewModel;
import com.bxkj.personal.ui.activity.companydetails.CompanyDetailsViewModel;
import com.bxkj.personal.ui.activity.conversation.GeekChatViewModel;
import com.bxkj.personal.ui.activity.conversationreport.CommonReportViewModel;
import com.bxkj.personal.ui.activity.editrichtext.EditRichTextViewModel;
import com.bxkj.personal.ui.activity.fans.FansViewModel;
import com.bxkj.personal.ui.activity.findjobbymap.FindJobOnMapViewModel;
import com.bxkj.personal.ui.activity.finearticle.FineArticleDetailsViewModel;
import com.bxkj.personal.ui.activity.finearticle.FineArticleViewModel;
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsViewModel;
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeViewModel;
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussRankViewModel;
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussViewModel;
import com.bxkj.personal.ui.activity.integralrecharge.IntegralRechargeViewModel;
import com.bxkj.personal.ui.activity.interviewdetails.GeekInterviewDetailsViewModel;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsViewModel;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsViewModelV2;
import com.bxkj.personal.ui.activity.jobintention.JobIntentionViewModel;
import com.bxkj.personal.ui.activity.lastjob.LatestJobViewModel;
import com.bxkj.personal.ui.activity.main.MainViewModel;
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoViewModel;
import com.bxkj.personal.ui.activity.momentdetails.MomentDetailsViewModel;
import com.bxkj.personal.ui.activity.msgnotification.MsgNotificationViewModel;
import com.bxkj.personal.ui.activity.msgnotificationcontent.MsgNotificationContentViewModel;
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowViewModel;
import com.bxkj.personal.ui.activity.myhistory.MyHistoryViewModel;
import com.bxkj.personal.ui.activity.myresume.MyResumeDetailsViewModel;
import com.bxkj.personal.ui.activity.myresume.SelfEvaluationViewModel;
import com.bxkj.personal.ui.activity.myresume.workexp.EditWorkExpViewModel;
import com.bxkj.personal.ui.activity.myresumelist.MyResumeListViewModel;
import com.bxkj.personal.ui.activity.onlinecampustalkdetails.OnlineCampusTalkDetailsViewModel;
import com.bxkj.personal.ui.activity.paiduser.PaidUserViewModel;
import com.bxkj.personal.ui.activity.parttimejob.PartTimeJobViewModel;
import com.bxkj.personal.ui.activity.parttimejobtypelist.PartTimeJobSearchResultViewModel;
import com.bxkj.personal.ui.activity.parttimejobtypelist.PartTimeJobTypeListViewModel;
import com.bxkj.personal.ui.activity.parttimeworkbench.PartTimeWorkbenchViewModel;
import com.bxkj.personal.ui.activity.parttimeworkbench.customerlist.CustomerListViewModel;
import com.bxkj.personal.ui.activity.parttimeworkbench.incomeanalyzer.IncomeAnalyzerViewModel;
import com.bxkj.personal.ui.activity.parttimeworkbench.myprofit.MyProfitViewModel;
import com.bxkj.personal.ui.activity.parttimeworkbench.workbench.WorkBenchViewModel;
import com.bxkj.personal.ui.activity.paymentorder.PaymentOrderViewModel;
import com.bxkj.personal.ui.activity.paymentresult.PaymentResultViewModel;
import com.bxkj.personal.ui.activity.personalmember.PersonalMemberViewModel;
import com.bxkj.personal.ui.activity.postnews.PostNewsViewModel;
import com.bxkj.personal.ui.activity.postnotice.PostNoticeViewModel;
import com.bxkj.personal.ui.activity.postvideo.PostVideoViewModel;
import com.bxkj.personal.ui.activity.privacysetting.PrivacySettingViewModel;
import com.bxkj.personal.ui.activity.qaranklist.QARankListViewModel;
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsViewModel;
import com.bxkj.personal.ui.activity.quickanswer.QuickAnswerViewModel;
import com.bxkj.personal.ui.activity.recommendcompany.RecommendCompanyViewModel;
import com.bxkj.personal.ui.activity.recommendjob.RecommendJobViewModel;
import com.bxkj.personal.ui.activity.reportreason.ReportReasonViewModel;
import com.bxkj.personal.ui.activity.resumeopenstatesetting.ResumeOpenStateSettingViewModel;
import com.bxkj.personal.ui.activity.resumetop.ResumeTopViewModel;
import com.bxkj.personal.ui.activity.resumetop.ResumeTopViewModelV2;
import com.bxkj.personal.ui.activity.scanloginconfirm.ScanLoginConfirmViewModel;
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordManagementViewModel;
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordViewModel;
import com.bxkj.personal.ui.activity.schoolrecruitdetails.SchoolRecruitDetailsViewModel;
import com.bxkj.personal.ui.activity.searchanswer.SearchAnswerViewModel;
import com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultViewModel;
import com.bxkj.personal.ui.activity.searchnews.SearchNewsViewModel;
import com.bxkj.personal.ui.activity.searchnews.SearchVideoViewModel;
import com.bxkj.personal.ui.activity.searchquestion.SearchQuestionViewModel;
import com.bxkj.personal.ui.activity.seenmybusiness.ViewedMeCompanyViewModel;
import com.bxkj.personal.ui.activity.selectdefaultavatar.SelectDefaultAvatarViewModel;
import com.bxkj.personal.ui.activity.selectdepartment.SelectDepartmentViewModel;
import com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolViewModel;
import com.bxkj.personal.ui.activity.service.ServiceViewModel;
import com.bxkj.personal.ui.activity.setting.SettingViewModel;
import com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyViewModel;
import com.bxkj.personal.ui.activity.signupuser.SignUpUserViewModel;
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsViewModel;
import com.bxkj.personal.ui.activity.sysrecomendsetting.SysRecommendViewModel;
import com.bxkj.personal.ui.activity.takecover.TakeCoverViewModel;
import com.bxkj.personal.ui.activity.typenews.TypeNewsViewModel;
import com.bxkj.personal.ui.activity.uploadattechmentresume.AttachmentResumeViewModel;
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarViewModel;
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoViewModel;
import com.bxkj.personal.ui.activity.usersetting.UserSettingViewModel;
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsViewModel;
import com.bxkj.personal.ui.activity.videosignupmsg.VideoSignUpMsgViewModel;
import com.bxkj.personal.ui.activity.web.WebViewModel;
import com.bxkj.personal.ui.fragment.applyrecordlist.ResumeDeliveryRecordViewModel;
import com.bxkj.personal.ui.fragment.bbs.BBSViewModel;
import com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitListViewModel;
import com.bxkj.personal.ui.fragment.campustalk.CampusTalkViewModel;
import com.bxkj.personal.ui.fragment.campustalk.RelateCampusTalkViewModel;
import com.bxkj.personal.ui.fragment.collection.CollectionViewModel;
import com.bxkj.personal.ui.fragment.comment.CommentViewModel;
import com.bxkj.personal.ui.fragment.home.GovRecruitmentViewModel;
import com.bxkj.personal.ui.fragment.homenews.RecruitmentNewsViewModel;
import com.bxkj.personal.ui.fragment.homesubjob.HomeSubJobViewModel;
import com.bxkj.personal.ui.fragment.homev3.GeekHomeViewModelV3;
import com.bxkj.personal.ui.fragment.inviteuser.InviteUserViewModel;
import com.bxkj.personal.ui.fragment.joblist.LatestJobListViewModel;
import com.bxkj.personal.ui.fragment.joblist.NearbyJobListViewModel;
import com.bxkj.personal.ui.fragment.like.LikeViewModel;
import com.bxkj.personal.ui.fragment.message.GeekContactViewModel;
import com.bxkj.personal.ui.fragment.onlinecampustalk.OnlineCampusTalkViewModel;
import com.bxkj.personal.ui.fragment.orderhistory.OrderHistoryViewModel;
import com.bxkj.personal.ui.fragment.parttimejoblist.PartTimeJobListViewModel;
import com.bxkj.personal.ui.fragment.qa.QAViewModel;
import com.bxkj.personal.ui.fragment.question.QuestionViewModel;
import com.bxkj.personal.ui.fragment.questionrecommend.QuestionRecommendViewModel;
import com.bxkj.personal.ui.fragment.schoolmate.SchoolMateViewModel;
import com.bxkj.personal.ui.fragment.signupuser.SignUpUserChildViewModel;
import com.bxkj.personal.ui.fragment.study.StudyViewModel;
import com.bxkj.personal.ui.fragment.top500recruit.Top500RecruitViewModel;
import com.bxkj.personal.ui.fragment.usernotice.UserNoticeViewModel;
import com.bxkj.personal.ui.fragment.uservideo.UserVideoViewModel;
import com.bxkj.personal.ui.fragment.videogroup.VideoContainerViewModel;
import com.bxkj.personal.weight.salaryselect.SalarySelectViewModel;
import dagger.Binds;
import dagger.Module;
import dagger.multibindings.IntoMap;

/**
 * @date 2019/4/4
 */
@Module
public abstract class PersonalViewModelModule {

  @Binds
  @IntoMap
  @ViewModelKey(IncomeAnalyzerViewModel.class)
  abstract BaseViewModel bindIncomeAnalyzerViewModel(
    IncomeAnalyzerViewModel incomeAnalyzerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PartTimeWorkbenchViewModel.class)
  abstract BaseViewModel bindPartTimeWorkbenchViewModel(
    PartTimeWorkbenchViewModel partTimeWorkbenchViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyProfitViewModel.class)
  abstract BaseViewModel bindMyProfitViewModel(MyProfitViewModel myProfitViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CustomerListViewModel.class)
  abstract BaseViewModel bindEnterpriseListViewModel(
    CustomerListViewModel customerListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(WorkBenchViewModel.class)
  abstract BaseViewModel bindWorkbenchViewModel(
    WorkBenchViewModel workBenchViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobDetailsViewModelV2.class)
  abstract BaseViewModel bindJobDetailsViewModelV2(JobDetailsViewModelV2 jobDetailsViewModelV2);

  @Binds
  @IntoMap
  @ViewModelKey(EditWorkExpViewModel.class)
  abstract BaseViewModel bindEditWorkExpViewModel(EditWorkExpViewModel editWorkExpViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FindJobOnMapViewModel.class)
  abstract BaseViewModel bindFindJobOnMapViewModel(FindJobOnMapViewModel findJobOnMapViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PersonalMemberViewModel.class)
  abstract BaseViewModel bindPersonalMemberViewModel(
    PersonalMemberViewModel personalViewModelModule);

  @Binds
  @IntoMap
  @ViewModelKey(ResumeTopViewModelV2.class)
  abstract BaseViewModel bindResumeTopViewModelV2(ResumeTopViewModelV2 resumeTopViewModelV2);

  @Binds
  @IntoMap
  @ViewModelKey(ResumeDeliveryRecordViewModel.class)
  abstract BaseViewModel bindResumeDeliveryRecordViewModel(
    ResumeDeliveryRecordViewModel resumeDeliveryRecordViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AttachmentResumeViewModel.class)
  abstract BaseViewModel bindAttachmentResumeViewModel(
    AttachmentResumeViewModel attachmentResumeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ResumeOpenStateSettingViewModel.class)
  abstract BaseViewModel bindResumeOpenStateSettingViewModel(
    ResumeOpenStateSettingViewModel resumeOpenStateSettingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GeekContactViewModel.class)
  abstract BaseViewModel bindGeekContactViewModel(GeekContactViewModel geekContactViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RelateCampusTalkViewModel.class)
  abstract BaseViewModel bindRelateCampusTalkViewModel(
    RelateCampusTalkViewModel relateCampusTalkViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(OnlineCampusTalkDetailsViewModel.class)
  abstract BaseViewModel bindOnlineCampusTalkDetailsViewModel(
    OnlineCampusTalkDetailsViewModel onlineCampusTalkDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(OnlineCampusTalkViewModel.class)
  abstract BaseViewModel bindOnlineCampusTalkViewModel(
    OnlineCampusTalkViewModel onlineCampusTalkViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CampusTalkDetailsViewModel.class)
  abstract BaseViewModel bindCampusTalkDetailsViewModel(
    CampusTalkDetailsViewModel campusTalkDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HotCampusRecruitListViewModel.class)
  abstract BaseViewModel bindHotCampusRecruitListViewModel(
    HotCampusRecruitListViewModel hotCampusRecruitListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecruitBrochureViewModel.class)
  abstract BaseViewModel bindRecruitBrochureViewModel(
    RecruitBrochureViewModel recruitBrochureViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CampusRecruitDetailsViewModel.class)
  abstract BaseViewModel bindCampusRecruitDetailsViewModel(
    CampusRecruitDetailsViewModel campusRecruitDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SalarySelectViewModel.class)
  abstract BaseViewModel bindSalarySelectViewModel(SalarySelectViewModel salarySelectViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ViewedMeCompanyViewModel.class)
  abstract BaseViewModel bindSeenMeBusinessViewModel(
    ViewedMeCompanyViewModel viewedMeCompanyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(LatestJobViewModel.class)
  abstract BaseViewModel bindLastJobViewModel(LatestJobViewModel latestJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GeekInterviewDetailsViewModel.class)
  abstract BaseViewModel bindGeekInterviewDetailsViewModel(
    GeekInterviewDetailsViewModel geekInterviewDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PartTimeJobSearchResultViewModel.class)
  abstract BaseViewModel bindPartTimeJobTypeListViewModel(
    PartTimeJobSearchResultViewModel partTimeJobSearchResultViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PartTimeJobTypeListViewModel.class)
  abstract BaseViewModel bindPartTimeTypeListViewModel(
    PartTimeJobTypeListViewModel partTimeJobTypeListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PartTimeJobListViewModel.class)
  abstract BaseViewModel bindPartTimeJobListViewModel(
    PartTimeJobListViewModel partTimeJobListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PartTimeJobViewModel.class)
  abstract BaseViewModel bindPartTimeViewModel(PartTimeJobViewModel partTimeJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CampusTalkViewModel.class)
  abstract BaseViewModel bindCampusTalkViewModel(CampusTalkViewModel campusTalkViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(Top500RecruitViewModel.class)
  abstract BaseViewModel bindTop500RecruitViewModel(Top500RecruitViewModel top500RecruitViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CampusRecruitListViewModel.class)
  abstract BaseViewModel bindCampusListViewModel(
    CampusRecruitListViewModel campusRecruitListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchJobResultViewModel.class)
  abstract BaseViewModel bindSearchJobResultViewModel(
    SearchJobResultViewModel searchJobResultViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(VideoContainerViewModel.class)
  abstract BaseViewModel bindVideoContainerViewModel(
    VideoContainerViewModel videoContainerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HotDiscussViewModel.class)
  abstract BaseViewModel bindHotDiscussViewModel(HotDiscussViewModel hotDiscussViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(NearbyJobListViewModel.class)
  abstract BaseViewModel bindNearbyJobListViewModel(NearbyJobListViewModel nearbyJobListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelfEvaluationViewModel.class)
  abstract BaseViewModel bindSelfEvaluationViewModel(
    SelfEvaluationViewModel selfEvaluationViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyResumeDetailsViewModel.class)
  abstract BaseViewModel bindMyResumeDetailsViewModel(
    MyResumeDetailsViewModel myResumeDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobIntentionViewModel.class)
  abstract BaseViewModel bindJobIntentionViewModel(JobIntentionViewModel jobIntentionViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MicroResumeInfoViewModel.class)
  abstract BaseViewModel bindMicroResumeInfoViewModel(
    MicroResumeInfoViewModel microResumeInfoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HotDiscussRankViewModel.class)
  abstract BaseViewModel bindHotDiscussRankViewModel(
    HotDiscussRankViewModel hotDiscussRankViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FineArticleDetailsViewModel.class)
  abstract BaseViewModel bindFineArticleDetailsViewModel(
    FineArticleDetailsViewModel fineArticleDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FineArticleViewModel.class)
  abstract BaseViewModel bindFineArticleViewModel(FineArticleViewModel fineArticleViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecommendJobViewModel.class)
  abstract BaseViewModel bindRecommendJobViewModel(RecommendJobViewModel recommendJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecommendCompanyViewModel.class)
  abstract BaseViewModel bindRecommendCompanyViewModel(
    RecommendCompanyViewModel recommendCompanyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BBSViewModel.class)
  abstract BaseViewModel bindBBSViewModel(BBSViewModel bbsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(LatestJobListViewModel.class)
  abstract BaseViewModel bindJobListViewModel(LatestJobListViewModel latestJobListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GeekHomeViewModelV3.class)
  abstract BaseViewModel bindHomeViewModelV3(GeekHomeViewModelV3 geekHomeViewModelV3);

  @Binds
  @IntoMap
  @ViewModelKey(DeliveryRecordManagementViewModel.class)
  abstract BaseViewModel bindDeliveryRecordManagementViewModel(
    DeliveryRecordManagementViewModel deliveryRecordManagementViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchVideoViewModel.class)
  abstract BaseViewModel bindSearchVideoViewModel(SearchVideoViewModel searchVideoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MainViewModel.class)
  abstract BaseViewModel bindMainViewModel(MainViewModel mainViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SettingViewModel.class)
  abstract BaseViewModel bindSettingViewModel(SettingViewModel settingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(WebViewModel.class)
  abstract BaseViewModel bindWebViewModel(WebViewModel webViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ServiceViewModel.class)
  abstract BaseViewModel bindServiceViewModel(ServiceViewModel serviceViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ScanLoginConfirmViewModel.class)
  abstract BaseViewModel bindScanLoginConfirmViewModel(
    ScanLoginConfirmViewModel scanLoginConfirmViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CompanyDetailsViewModel.class)
  abstract BaseViewModel bindCompanyDetailsViewModel(
    CompanyDetailsViewModel companyDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(JobDetailsViewModel.class)
  abstract BaseViewModel bindJobDetailsViewModel(JobDetailsViewModel jobDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolRecruitDetailsViewModel.class)
  abstract BaseViewModel bindSchoolRecruitmentDetailsViewModel(
    SchoolRecruitDetailsViewModel schoolRecruitDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CommentReplyViewModel.class)
  abstract BaseViewModel bindCommendReplyViewModel(CommentReplyViewModel commentReplyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelectDepartmentViewModel.class)
  abstract BaseViewModel bindSelectDepartmentViewModel(
    SelectDepartmentViewModel selectDepartmentViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PostNewsViewModel.class)
  abstract BaseViewModel bindReleaseNewsViewModel(PostNewsViewModel postNewsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(IntegralRechargeViewModel.class)
  abstract BaseViewModel bindIntegralRechargeViewModel(
    IntegralRechargeViewModel integralRechargeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PaymentOrderViewModel.class)
  abstract BaseViewModel bindPaymentOrderViewModel(PaymentOrderViewModel paymentOrderViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SchoolMateViewModel.class)
  abstract BaseViewModel bindSchoolMateViewModel(SchoolMateViewModel schoolMateViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PaymentResultViewModel.class)
  abstract BaseViewModel bindPaymentResultViewModel(PaymentResultViewModel paymentResultViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(UserBasicInfoViewModel.class)
  abstract BaseViewModel bindUserBasicInfoViewModel(UserBasicInfoViewModel basicInfoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyFollowViewModel.class)
  abstract BaseViewModel bindMyFollowViewModel(MyFollowViewModel myFollowViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(FansViewModel.class)
  abstract BaseViewModel bindFansViewModel(FansViewModel fansViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PaidUserViewModel.class)
  abstract BaseViewModel bindPaidUserViewModel(PaidUserViewModel paidUserViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(UploadAvatarViewModel.class)
  abstract BaseViewModel bindUploadAvatarViewModel(UploadAvatarViewModel uploadAvatarViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelectDefaultAvatarViewModel.class)
  abstract BaseViewModel bindSelectDefaultAvatarViewModel(
    SelectDefaultAvatarViewModel selectDefaultAvatarViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(BindMobileViewModel.class)
  abstract BaseViewModel bindBindMobielViewModel(BindMobileViewModel bindMobileViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GovRecruitmentViewModel.class)
  abstract BaseViewModel bindNewNomeViewModel(GovRecruitmentViewModel govRecruitmentViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(RecruitmentNewsViewModel.class)
  abstract BaseViewModel bindHomeNewsViewModel(RecruitmentNewsViewModel recruitmentNewsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GzNewsDetailsViewModel.class)
  abstract BaseViewModel bindGzNewsDetailsViewModel(GzNewsDetailsViewModel gzNewsDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(VideoDetailsViewModel.class)
  abstract BaseViewModel bindVideoDetailsViewModel(VideoDetailsViewModel videoDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PostVideoViewModel.class)
  abstract BaseViewModel bindPostVideoViewModel(PostVideoViewModel postVideoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(TakeCoverViewModel.class)
  abstract BaseViewModel bindTakeCoverViewModel(TakeCoverViewModel takeCoverViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PostNoticeViewModel.class)
  abstract BaseViewModel bindPostNoticeViewModel(PostNoticeViewModel postNoticeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GzUserHomeViewModel.class)
  abstract BaseViewModel bindGzUserHomeViewModel(GzUserHomeViewModel gzUserHomeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(UserNoticeViewModel.class)
  abstract BaseViewModel bindUserNoticeViewModel(UserNoticeViewModel userNoticeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(UserVideoViewModel.class)
  abstract BaseViewModel bindUserVideoViewModel(UserVideoViewModel userVideoViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QuestionDetailsViewModel.class)
  abstract BaseViewModel bindQuestionDetailsViewModel(
    QuestionDetailsViewModel questionDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AddAnswerViewModel.class)
  abstract BaseViewModel bindAddAnswerViewModel(AddAnswerViewModel addAnswerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AddQuestionViewModel.class)
  abstract BaseViewModel bindAddQuestionViewModel(AddQuestionViewModel addQuestionViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AnswerDetailsViewModel.class)
  abstract BaseViewModel bindAnswerDetailsViewModel(AnswerDetailsViewModel answerDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QuestionViewModel.class)
  abstract BaseViewModel bindQuestionViewModel(QuestionViewModel questionViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyHistoryViewModel.class)
  abstract BaseViewModel bindMyViewModel(MyHistoryViewModel myHistoryViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CollectionViewModel.class)
  abstract BaseViewModel bindCollectionViewModel(CollectionViewModel collectionViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CommentViewModel.class)
  abstract BaseViewModel bindCommentViewModel(CommentViewModel commentViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(LikeViewModel.class)
  abstract BaseViewModel bindLikeViewModel(LikeViewModel likeViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MyResumeListViewModel.class)
  abstract BaseViewModel bindMyResumeListViewModel(MyResumeListViewModel myResumeListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ResumeTopViewModel.class)
  abstract BaseViewModel bindResumeTopViewModel(ResumeTopViewModel resumeTopViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(OrderHistoryViewModel.class)
  abstract BaseViewModel bindOrderHistoryViewModel(OrderHistoryViewModel orderHistoryViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchNewsViewModel.class)
  abstract BaseViewModel bindSearchNewsViewModel(SearchNewsViewModel searchNewsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(InviteUserViewModel.class)
  abstract BaseViewModel bindInviteUserViewModel(InviteUserViewModel inviteUserViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchAnswerViewModel.class)
  abstract BaseViewModel bindSearchAnswerViewModel(SearchAnswerViewModel searchAnswerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MsgNotificationViewModel.class)
  abstract BaseViewModel bindMsgNotificationViewModel(
    MsgNotificationViewModel msgNotificationViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MsgNotificationContentViewModel.class)
  abstract BaseViewModel bindMsgNotificationContentViewModel(
    MsgNotificationContentViewModel notificationContentViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QuestionRecommendViewModel.class)
  abstract BaseViewModel bindQuestionRecommendViewModel(
    QuestionRecommendViewModel questionRecommendViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SearchQuestionViewModel.class)
  abstract BaseViewModel bindSearchQuestionViewModel(
    SearchQuestionViewModel searchQuestionViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(TypeNewsViewModel.class)
  abstract BaseViewModel bindTypeNewsViewModel(TypeNewsViewModel typeNewsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(HomeSubJobViewModel.class)
  abstract BaseViewModel bindHomeSubJobViewModel(HomeSubJobViewModel homeSubJobViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SignUpUserViewModel.class)
  abstract BaseViewModel bindSignUpUserViewModel(SignUpUserViewModel signUpUserViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SignUpUserChildViewModel.class)
  abstract BaseViewModel bindSignUpChildUserViewModel(
    SignUpUserChildViewModel signUpUserChildViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(VideoSignUpMsgViewModel.class)
  abstract BaseViewModel bindVideoSignUpMsgViewModel(
    VideoSignUpMsgViewModel videoSignUpMsgViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QAViewModel.class)
  abstract BaseViewModel bindQAViewModel(QAViewModel qaViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QARankListViewModel.class)
  abstract BaseViewModel bindQARankListViewModel(QARankListViewModel qaRankListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(StudyViewModel.class)
  abstract BaseViewModel bindLearnViewModel(StudyViewModel studyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(StudyNewsDetailsViewModel.class)
  abstract BaseViewModel bindStudyNewsDetailsViewModel(
    StudyNewsDetailsViewModel studyNewsDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(QuickAnswerViewModel.class)
  abstract BaseViewModel bindQuickAnswerViewModel(QuickAnswerViewModel quickAnswerViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AreaListViewModel.class)
  abstract BaseViewModel bindAreaListViewModel(AreaListViewModel areaListViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ShieldCompanyViewModel.class)
  abstract BaseViewModel bindShieldCompanyViewModel(ShieldCompanyViewModel shieldCompanyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AddShieldCompanyViewModel.class)
  abstract BaseViewModel bindAddShieldCompanyViewModel(
    AddShieldCompanyViewModel addShieldCompanyViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(UserSettingViewModel.class)
  abstract BaseViewModel bindUserSettingViewModel(UserSettingViewModel userSettingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(GeekChatViewModel.class)
  abstract BaseViewModel bindChatViewModel(GeekChatViewModel geekChatViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(CommonReportViewModel.class)
  abstract BaseViewModel bindConversationReportViewModel(
    CommonReportViewModel commonReportViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(ReportReasonViewModel.class)
  abstract BaseViewModel bindReportReasonViewModel(ReportReasonViewModel reportReasonViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(PrivacySettingViewModel.class)
  abstract BaseViewModel bindPrivacySettingViewModel(
    PrivacySettingViewModel privacySettingViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(MomentDetailsViewModel.class)
  abstract BaseViewModel bindMomentDetailsViewModel(MomentDetailsViewModel momentDetailsViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SelectRelateSchoolViewModel.class)
  abstract BaseViewModel bindSelectRelateSchoolViewModel(
    SelectRelateSchoolViewModel selectRelateSchoolViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(EditRichTextViewModel.class)
  abstract BaseViewModel bindRichTextViewModel(EditRichTextViewModel editRichTextViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(SysRecommendViewModel.class)
  abstract BaseViewModel bindSysRecommendViewModel(SysRecommendViewModel sysRecommendViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(AboutUsV2ViewModel.class)
  abstract BaseViewModel bindAboutUsV2ViewModel(AboutUsV2ViewModel aboutUsV2ViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(DeliveryRecordViewModel.class)
  abstract BaseViewModel bindSchoolRecruitDeliveryViewModel(
    DeliveryRecordViewModel schoolRecruitDeliveryRecordViewModel);
}
