package com.bxkj.personal.ui.activity.fans

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.FansItemData
import com.bxkj.personal.databinding.PersonalActivityFansBinding

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.fans
 * @Description: 粉丝
 * <AUTHOR>
 * @date 2019/12/10
 * @version V1.0
 */
@Route(path = FansNavigation.PATH)
class FansActivity : BaseDBActivity<PersonalActivityFansBinding, FansViewModel>() {

  companion object {

    fun newIntent(context: Context, queryID: Int, queryType: Int): Intent {
      val intent = Intent(context, FansActivity::class.java)
      intent.putExtra(FansNavigation.EXTRA_QUERY_ID, queryID)
      intent.putExtra(FansNavigation.EXTRA_QUERY_TYPE, queryType)
      return intent
    }
  }

  override fun getViewModelClass(): Class<FansViewModel> = FansViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_fans

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    setupFansListAdapter()
    viewModel.start(intent)
  }

  private fun setupFansListAdapter() {
    val fansListAdapter =
      SimpleDBListAdapter<FansItemData>(
        this,
        layout.personal_recycler_fans_list_item
      )
    fansListAdapter.setOnItemClickListener(object :
      SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        if (v.id == R.id.tv_follow) {
          viewModel.addOrRemoveFollow(fansListAdapter.data[position])
        } else {
          UserHomeNavigation.navigate(
            fansListAdapter.data[position].id
          ).start()
        }
      }
    }, R.id.tv_follow)
    val recyclerFansList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerFansList.layoutManager = LinearLayoutManager(this)
    viewModel.listViewModel.setAdapter(fansListAdapter)
  }

}