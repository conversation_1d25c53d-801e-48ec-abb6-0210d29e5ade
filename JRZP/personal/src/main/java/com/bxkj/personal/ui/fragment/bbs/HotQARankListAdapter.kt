package com.bxkj.personal.ui.fragment.bbs

import android.graphics.Color
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.personal.data.DiscussData

/**
 *
 * @author: sanjin
 * @date: 2022/8/31
 */
class HotQARankListAdapter : SimpleDiffListAdapter<DiscussData>(
    R.layout.personal_recycler_hot_qa_item,
    DiscussData.DiffCallback()
) {

    override fun bind(holder: SuperViewHolder, item: DiscussData, position: Int) {
        super.bind(holder, item, position)
        holder.findViewById<ImageView>(R.id.iv_tag).apply {
            val imageResource =
                if (item.no > 1) R.drawable.personal_ic_hot_discuss_new else R.drawable.personal_ic_hot_discuss
            setImageResource(imageResource)
        }
        holder.findViewById<TextView>(R.id.tv_rank).apply {
            text = (item.no + 1).toString()
            val rankTextColor = when (item.no) {
                0 -> {
                    "#FB2C2C"
                }
                1 -> {
                    "#FB7F2C"
                }
                2 -> {
                    "#6AD131"
                }
                else -> {
                    "#2CD8FB"
                }
            }
            setTextColor(Color.parseColor(rankTextColor))
        }
    }
}