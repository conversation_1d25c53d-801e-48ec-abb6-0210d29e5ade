package com.bxkj.personal.ui.activity.gzuserhome

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import cn.jzvd.Jzvd
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.indicator.ScaleIndicatorAdapter
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.user.mine.data.UserHomeData
import com.bxkj.personal.R
import com.bxkj.personal.data.MomentPhotoItemData
import com.bxkj.personal.databinding.PersonalActivityGzUserHomeBinding
import com.bxkj.personal.ui.activity.companydetails.companyjobs.CompanyJobsFragment
import com.bxkj.personal.ui.activity.fans.FansActivity
import com.bxkj.personal.ui.activity.fans.FansNavigation
import com.bxkj.personal.ui.activity.gallery.GalleryActivity
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowActivity
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoActivity
import com.bxkj.personal.ui.fragment.question.QuestionFragment
import com.bxkj.personal.ui.fragment.usernotice.UserNoticeFragment
import com.bxkj.personal.ui.fragment.uservideo.UserVideoFragment
import com.bxkj.personal.weight.pagemoreoptions.PageMoreOptionsPopup
import com.therouter.router.Route
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.gzuserhome
 * @Description: 用户个人中心
 * <AUTHOR>
 * @date 2020/1/7
 * @version V1.0
 */
@Route(path = GzUserHomeNavigation.PATH)
class GzUserHomeActivity : BaseDBActivity<PersonalActivityGzUserHomeBinding, GzUserHomeViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_QUERY_USER_ID = "QUERY_USER_ID"
    const val EXTRA_QUERY_ENTERPRISE_ID = "QUERY_ENTERPRISE_ID"
    const val EXTRA_JUMP_TO_NOTICE = "JUMP_TO_NOTICE"
    const val EXTRA_AFFECT_POSITION = "AFFECT_POSITION"
    const val EXTRA_FOLLOW_STATUS = "FOLLOW_STATUS"
    const val EXTRA_TO_QUESTION_PAGE = "TO_QUESTION_PAGE"
    const val EXTRA_TARGET_PAGE_INDEX = "TARGET_PAGE_INDEX"
    const val EXTRA_IS_ORG = "IS_ENTERPRISE"

    const val RESULT_FOLLOW_STATUS_CHANGE = Activity.RESULT_FIRST_USER + 1

    const val TO_USER_BASIC_INFO_CODE = 1

    fun newIntent(
      context: Context, queryUserId: Int, queryEnterpriseId: Int? = CommonApiConstants.NO_ID,
      jumpToNotice: Boolean? = false, affectPosition: Int? = 0, toQuestionPage: Boolean? = false,
      targetPageIndex: Int = CommonApiConstants.NO_ID, isOrg: Boolean? = false
    ): Intent {
      return Intent(context, GzUserHomeActivity::class.java)
        .apply {
          putExtra(EXTRA_QUERY_USER_ID, queryUserId)
          putExtra(EXTRA_QUERY_ENTERPRISE_ID, queryEnterpriseId)
          putExtra(EXTRA_JUMP_TO_NOTICE, jumpToNotice)
          putExtra(EXTRA_AFFECT_POSITION, affectPosition)
          putExtra(EXTRA_TO_QUESTION_PAGE, toQuestionPage)
          putExtra(EXTRA_TARGET_PAGE_INDEX, targetPageIndex)
          putExtra(EXTRA_IS_ORG, isOrg)
        }
    }
  }

  private var mSharePagePopup: PageMoreOptionsPopup? = null
  private var mContentPageAdapter: CommonPagerAdapter? = null

  override fun getViewModelClass(): Class<GzUserHomeViewModel> = GzUserHomeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_gz_user_home

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    viewModel.start(intent)

    subscribeViewModelEvent()
    subscribeFollowStatusChange()
    subscribeShareBackAction()

    if (intent.getBooleanExtra(EXTRA_JUMP_TO_NOTICE, false)) {
      viewBinding.appBarLayout.setExpanded(false)
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.iv_right_option_three -> {
          setupShareInfo()
        }

        R.id.ll_notice -> {
          viewBinding.appBarLayout.setExpanded(false)
        }

        R.id.ll_follow -> {
          startActivity(
            MyFollowActivity.newIntent(
              this,
              if (viewModel.isEnterprise()) viewModel.getQueryEnterpriseId() else viewModel.getQueryUserId(),
              viewModel.isEnterprise()
            )
          )
        }

        R.id.ll_fans -> {
          startActivity(
            FansActivity.newIntent(
              this,
              if (viewModel.isEnterprise()) viewModel.getQueryEnterpriseId() else viewModel.getQueryUserId(),
              if (viewModel.isEnterprise()) FansNavigation.QUERY_TYPE_ENTERPRISE else FansNavigation.QUERY_TYPE_PERSONAL
            )
          )
        }

        R.id.tv_edit -> {
          startActivityForResult(UserBasicInfoActivity.newIntent(this), TO_USER_BASIC_INFO_CODE)
        }
      }
    }
  }

  private fun setupShareInfo() {
    var shareType = 1 //资讯1 问答2 视频3 机构4
    val currentFragment = mContentPageAdapter?.getItem(viewBinding.vpContent.currentItem)
    if (currentFragment != null) {
      when (currentFragment) {
        is UserNoticeFragment -> { //公告页
          shareType = 1
        }

        is UserVideoFragment -> { //视频页
          shareType = 3
        }

        is QuestionFragment -> {  //问答页
          shareType = 2
        }

        is CompanyJobsFragment -> { //职位页
          shareType = 6
        }
      }
      viewModel.getShareInfo(shareType, viewBinding.vpContent.currentItem)
    }
  }

  private fun subscribeFollowStatusChange() {
    viewModel.followStatus.observe(this, Observer {
      setResult(RESULT_FOLLOW_STATUS_CHANGE, intent.apply {
        putExtra(EXTRA_FOLLOW_STATUS, it)
      })
    })
  }

  private fun subscribeShareBackAction() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_SHARE_BACK) {
            mSharePagePopup?.dismiss()
          }
        })
  }

  private fun subscribeViewModelEvent() {
    viewModel.newsTabItems.observe(this) {
      setupChildPage(it)
    }

    viewModel.toEditCompanyInfoCommand.observe(this) {
      BusinessBasicInfoNavigation.navigate(true).start()
    }

    viewModel.toCertificateCompanyInfoCommand.observe(this) {
      BusinessBasicInfoNavigation.navigate(true).start()
    }

    viewModel.viewUserAvatarCommand.observe(this) {
      startActivity(
        GalleryActivity.newIntent(
          this, arrayListOf(MomentPhotoItemData.fromUrl(it)), 0
        )
      )
    }

    viewModel.notCertifiedEvent.observe(this) {
      BusinessBasicInfoNavigation.navigate(true).start()
    }

    viewModel.certificationEvent.observe(this) {
      TipsDialog()
        .setTitle(getString(R.string.tips))
        .setContent(getString(R.string.certification_tips))
        .show(supportFragmentManager)
    }

    viewModel.certificationFailedEvent.observe(this) {
      ActionDialog.Builder()
        .setTitle(getString(R.string.tips))
        .setContent(getString(R.string.certification_failed_format, it))
        .setOnConfirmClickListener {
          BusinessBasicInfoNavigation.navigate(true).start()
        }
        .build()
        .show(supportFragmentManager)
    }

    viewModel.shareUserHomeCommand.observe(this) {
      mSharePagePopup = PageMoreOptionsPopup(
        this,
        it.title,
        it.content,
        it.shareUrl,
        showCollection = false,
        shareMomentTitle = it.title2,
        sharePhotoUrl = it.sharePic
      )
      mSharePagePopup?.showBottom()
    }
  }

  private fun setupChildPage(newsTabs: List<NewsTabItem>) {
    val childPageList = ArrayList<Fragment>()
    val titles = arrayOfNulls<String?>(newsTabs.size)
    for (i in newsTabs.indices) {
      val tabItem = newsTabs[i]
      titles[i] = tabItem.title
      when (tabItem.tabType) {
        TAB_TYPE_NEWS -> {
          childPageList.add(UserNoticeFragment.newInstance(viewModel.getQueryEnterpriseId()))
        }

        TAB_TYPE_VIDEO -> {
          childPageList.add(UserVideoFragment.newInstance(viewModel.getQueryUserId()))
        }

        TAB_TYPE_QA -> {
          childPageList.add(QuestionFragment.newInstance(viewModel.getQueryUserId()))
        }

        TAB_TYPE_JOB -> {
          childPageList.add(CompanyJobsFragment.newInstance(tabItem.linkId))
        }
      }
    }
    mContentPageAdapter =
      CommonPagerAdapter(
        supportFragmentManager,
        childPageList
      )
    viewBinding.vpContent.adapter = mContentPageAdapter
    viewBinding.vpContent.offscreenPageLimit = childPageList.size
    setupIndicator(titles)
  }

  private fun showSharePopup(userInfo: UserHomeData) {
    val shareUrl = getString(
      R.string.user_home_share_url_format,
      userInfo.userID,
      userInfo.dwID,
      viewBinding.vpContent.currentItem
    )
    //朋友标题
    var shareTitle = getString(R.string.user_home_share_title_format, userInfo.name)
    //朋友内容
    var shareDesc = if (userInfo.dwID == 0) {
      getString(R.string.user_home_share_user_desc_format, userInfo.actionCount, userInfo.fansCount)
    } else {
      getString(R.string.user_home_share_company_desc_format, userInfo.dwName)
    }
    //朋友圈标题
    var shareMomentTitle = getString(R.string.user_home_share_title_format, userInfo.name)
    //朋友圈内容
    var shareMomentDesc = if (userInfo.dwID == 0) {
      getString(R.string.user_home_share_user_desc_format, userInfo.actionCount, userInfo.fansCount)
    } else {
      getString(R.string.user_home_share_company_desc_format, userInfo.dwName)
    }
    val currentFragment = mContentPageAdapter?.getItem(viewBinding.vpContent.currentItem)
    if (currentFragment != null) {
      when (currentFragment) {
        is UserNoticeFragment -> { //公告页
          currentFragment.viewModel.getShareItem()?.let {
            shareTitle = getString(R.string.user_home_share_notice_title_format, userInfo.name)
            shareMomentTitle =
              getString(R.string.user_home_share_notice_title_format, userInfo.name)
            shareDesc = it.title
          }
        }

        is UserVideoFragment -> { //视频页
          currentFragment.viewModel.getShareItem()?.let {
            shareTitle = getString(
              R.string.user_home_share_video_title_format,
              userInfo.name
            )
            shareDesc = it.realShareContent
            shareMomentTitle = getString(
              R.string.user_home_share_video_title_format,
              userInfo.name
            )
            shareMomentDesc = it.realShareContent
          }
        }

        is QuestionFragment -> {  //问答页
          currentFragment.viewModel.getShareItem()?.let {
            var finalTitle = it.title
            if (it.type != 1) { //回答
              finalTitle = it.toWendaTitle
            }
            shareTitle =
              getString(R.string.user_home_share_qa_title_format, userInfo.name)
            shareDesc = finalTitle
            shareMomentTitle =
              getString(R.string.user_home_share_qa_title_format, userInfo.name)
            shareMomentDesc = finalTitle
          }
        }
      }
    }

    mSharePagePopup = PageMoreOptionsPopup(
      this,
      shareTitle,
      shareDesc,
      shareUrl,
      showCollection = false,
      sharePhotoUrl = userInfo.photo.replace("https", "http"),
      shareMomentTitle = shareMomentTitle,
      shareMomentDesc = shareMomentDesc
    )
    mSharePagePopup?.showBottom()
  }

  private fun setupIndicator(array: Array<String?>) {
    val commonNavigator = CommonNavigator(this)
    commonNavigator.isAdjustMode = true
    commonNavigator.adapter = object : ScaleIndicatorAdapter(array) {
      override fun getTitleView(context: Context, index: Int): IPagerTitleView {
        val pageTitleView =
          ScalePagerTitleView(context)
        pageTitleView.textSize = 14f
        pageTitleView.normalColor = ContextCompat.getColor(context, R.color.cl_333333)
        pageTitleView.selectedColor = ContextCompat.getColor(context, R.color.cl_333333)
        pageTitleView.text = getTitles()[index]
        pageTitleView.setOnClickListener { view ->
          getOnTabClickListener()?.onTabClicked(view, index)
        }
        return pageTitleView
      }
    }.apply {
      setOnTabClickListener(object :
        OnTabClickListener {
        override fun onTabClicked(v: View, index: Int) {
          viewBinding.vpContent.currentItem = index
        }
      })
    }
    viewBinding.indicatorNewsType.navigator = commonNavigator
    ViewPagerHelper.bind(viewBinding.indicatorNewsType, viewBinding.vpContent)

    handleTargetPageIndexParams()
  }

  private fun handleTargetPageIndexParams() {
    viewBinding.vpContent.adapter?.let {
      //是否需要跳到问答页
      if (intent.getBooleanExtra(EXTRA_TO_QUESTION_PAGE, false)) {
        viewBinding.vpContent.currentItem = it.count - 1
      }

      //目标pageindex
      val targetPageIndex = intent.getIntExtra(EXTRA_TARGET_PAGE_INDEX, CommonApiConstants.NO_ID)
      if (targetPageIndex != 0 && targetPageIndex < it.count) {
        viewBinding.vpContent.currentItem = targetPageIndex
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onBackPressed() {
    if (Jzvd.backPress()) {
      return
    }
    super.onBackPressed()
  }
}