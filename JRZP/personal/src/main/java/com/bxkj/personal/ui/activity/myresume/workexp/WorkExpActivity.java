package com.bxkj.personal.ui.activity.myresume.workexp;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.picker.PickerUtils;
import com.bxkj.common.widget.MyEditText;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.personal.R;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.ui.activity.editinfo.EditInfoActivity;
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import javax.inject.Inject;

/**
 * 工作经历页面
 */
public class WorkExpActivity extends BaseDaggerActivity implements WorkExpContract.View {

  private static final String START_TYPE = "startType";
  private static final String RESUME_ID = "resumeId";
  private static final String WORK_EXP_ID = "workExpId";

  private static final int TO_EDIT_WORK_DESC_CODE = 0x01;

  public static final int CREATE_TYPE = 0x01;
  public static final int UPDATE_TYPE = 0x02;

  @Inject
  WorkExpPresenter mWorkExpPresenter;

  private TextView tvStartTime;
  private TextView tvEndTime;
  private MyEditText etCompanyName;
  private MyEditText etPositionName;
  private TextView tvWorkDesc;
  private TextView tvDeleteThisWorkExp;

  private WorkExpData mWorkExpData;

  private TimePickerView mStartDatePicker;
  private TimePickerView mEndDatePicker;

  private int mPageType;
  private int mResumeId;
  private int mWorkExpId;

  public static Intent newIntent(Activity activity, int startType, int resumeId, int workExpId) {
    Intent intent = new Intent(activity, WorkExpActivity.class);
    intent.putExtra(START_TYPE, startType);
    intent.putExtra(RESUME_ID, resumeId);
    intent.putExtra(WORK_EXP_ID, workExpId);
    return intent;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mWorkExpPresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_work_exp;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_work_exp))
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> submitInfo());
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mPageType = getIntent().getIntExtra(START_TYPE, CommonApiConstants.NO_DATA);
    if (mPageType == UPDATE_TYPE) {
      mWorkExpId = getIntent().getIntExtra(WORK_EXP_ID, CommonApiConstants.NO_ID);
      mWorkExpPresenter.getWorkExpDetails(getMUserID(), mWorkExpId);
    } else {
      mResumeId = getIntent().getIntExtra(RESUME_ID, CommonApiConstants.NO_ID);
      tvDeleteThisWorkExp.setVisibility(View.GONE);
      mWorkExpData = new WorkExpData();
    }

    initStartAndEndDatePicker();
  }

  /**
   * 初始化开始和结束时间选择器
   */
  private void initStartAndEndDatePicker() {
    Calendar startCalendar = Calendar.getInstance();
    startCalendar.set(1900, 0, 1);
    mStartDatePicker = PickerUtils.applyMyConfig(new TimePickerBuilder(this, (date, v) -> {
        String startDate = new SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(date);
        tvStartTime.setText(startDate);
        mWorkExpData.setDate1(startDate);
      })
        .setDate(Calendar.getInstance())
        .setType(new boolean[] { true, true, false, false, false, false })
        .setRangDate(startCalendar, Calendar.getInstance()))
      .build();

    mEndDatePicker = PickerUtils.applyMyConfig(new TimePickerBuilder(this, (date, v) -> {
        String endDate = new SimpleDateFormat("yyyy-MM", Locale.getDefault()).format(date);
        tvEndTime.setText(endDate);
        mWorkExpData.setDate2(endDate);
      }).setLayoutRes(R.layout.layout_my_time_picker, v -> {
          final TextView tvSubmit = v.findViewById(R.id.tv_finish);
          final TextView tvCancel = v.findViewById(R.id.tv_cancel);
          final TextView tvNow = v.findViewById(R.id.tv_now);
          tvSubmit.setOnClickListener(view -> {
              mEndDatePicker.dismiss();
              mEndDatePicker.returnData();
            }
          );
          tvCancel.setOnClickListener(view -> mEndDatePicker.dismiss());
          tvNow.setOnClickListener(view -> {
            mEndDatePicker.dismiss();
            tvEndTime.setText(getString(R.string.up_to_now));
            mWorkExpData.setDate2(getString(R.string.up_to_now));
          });
        })
        .setDate(Calendar.getInstance())
        .setType(new boolean[] { true, true, false, false, false, false })
        .setRangDate(startCalendar, Calendar.getInstance()))
      .build();
  }

  private void submitInfo() {
    if (mWorkExpData == null) {
      showToast("数据保存失败，请返回重试");
      return;
    }
    mWorkExpData.setConame(etCompanyName.getText().toString());
    mWorkExpData.setJob(etPositionName.getText().toString());
    mWorkExpData.setDes(tvWorkDesc.getText().toString());
    if (mPageType == UPDATE_TYPE) {
      mWorkExpPresenter.updateWorkExp(getMUserID(), mWorkExpId, mWorkExpData);
    } else {
      mWorkExpPresenter.addWorkExp(getMUserID(), mResumeId, mWorkExpData);
    }
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_start_time) {
      mStartDatePicker.show();
    } else if (view.getId() == R.id.tv_end_time) {
      mEndDatePicker.show();
    } else if (view.getId() == R.id.tv_work_desc) {
      startActivityForResult(
        EditInfoActivity.newIntent(this, getString(R.string.personal_work_desc),
          getString(R.string.personal_input_work_desc_hint), tvWorkDesc.getText().toString(),
          2000), TO_EDIT_WORK_DESC_CODE);
    } else {
      new ActionDialog.Builder()
        .setTitle(getString(R.string.common_confirm_delete_information))
        .setContent(getString(R.string.common_unrecoverable_information_after_deletion))
        .setOnConfirmClickListener(
          (dialog) -> mWorkExpPresenter.deleteWorkExp(getMUserID(), mWorkExpId))
        .build()
        .show(getSupportFragmentManager(), ActionDialog.TAG);
    }
  }

  @Override
  public void getWorkExpDetailsSuccess(WorkExpData workExpData) {
    mWorkExpData = workExpData;
    tvStartTime.setText(workExpData.getDate1());
    tvEndTime.setText(workExpData.getDate2());
    etCompanyName.setText(workExpData.getConame());
    etPositionName.setText(workExpData.getJob());
    tvWorkDesc.setText(workExpData.getDes());
  }

  @Override
  public void addWorkExpSuccess() {
    showToast(getString(R.string.common_save_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void updateWorkExpSuccess() {
    showToast(getString(R.string.common_save_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void deleteWorkExpSuccess() {
    showToast(getString(R.string.common_delete_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK && data != null) {
      String resultText = data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT);
      if (requestCode == TO_EDIT_WORK_DESC_CODE) {
        tvWorkDesc.setText(resultText);
      }
    }
  }

  private void bindView(View bindSource) {
    tvStartTime = bindSource.findViewById(R.id.tv_start_time);
    tvEndTime = bindSource.findViewById(R.id.tv_end_time);
    etCompanyName = bindSource.findViewById(R.id.et_company_name);
    etPositionName = bindSource.findViewById(R.id.et_position_name);
    tvWorkDesc = bindSource.findViewById(R.id.tv_work_desc);
    tvDeleteThisWorkExp = bindSource.findViewById(R.id.tv_delete_this);
    bindSource.findViewById(R.id.tv_start_time).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_end_time).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_desc).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_delete_this).setOnClickListener(v -> onViewClicked(v));
  }
}
