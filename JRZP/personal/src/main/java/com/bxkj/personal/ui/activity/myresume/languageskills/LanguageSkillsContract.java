package com.bxkj.personal.ui.activity.myresume.languageskills;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.LanguageSkillsItemData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.languageskills
 * @Description: LanguageSkills
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface LanguageSkillsContract {
    interface View extends BaseView {
        void getLanguageSkillsDetailsSuccess(LanguageSkillsItemData languageSkillsItemData);

        void addLanguageSkillsSuccess();

        void updateLanguageSkills();

        void deleteLanguageSkills();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getLanguageSkillsDetails(int userId, int skillId);

        public abstract void addLanguageSkills(int userId, int resumeId, LanguageSkillsItemData languageSkillsItemData);

        public abstract void updateLanguageSkills(int userId, int skillId, LanguageSkillsItemData languageSkillsItemData);

        public abstract void deleteLanguageSkills(int userId, int skillId);
    }
}
