package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.databinding.library.baseAdapters.BR;

import com.bxkj.common.util.UserUtils;

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
public class QARankItemData extends BaseObservable {
  private int userID;
  private String userName;
  private String userPhoto;
  private int count;
  private boolean isFollowUser;
  private int rzType;

  public int getUserID() {
    return userID;
  }

  public void setUserID(int userID) {
    this.userID = userID;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getUserPhoto() {
    return userPhoto;
  }

  public void setUserPhoto(String userPhoto) {
    this.userPhoto = userPhoto;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  @Bindable
  public boolean isFollowUser() {
    return isFollowUser;
  }

  public void setFollowUser(boolean followUser) {
    isFollowUser = followUser;
    notifyPropertyChanged(BR.followUser);
  }

  public boolean isSelf() {
    return (UserUtils.logged() && userID == UserUtils.getUserId());
  }

  public int getRzType() {
    return rzType;
  }

  public void setRzType(int rzType) {
    this.rzType = rzType;
  }
}
