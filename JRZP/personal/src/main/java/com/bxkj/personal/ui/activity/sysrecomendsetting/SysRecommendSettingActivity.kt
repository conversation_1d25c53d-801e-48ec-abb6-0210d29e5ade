package com.bxkj.personal.ui.activity.sysrecomendsetting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivitySysRecommendSettingBinding

class SysRecommendSettingActivity :
    BaseDBActivity<PersonalActivitySysRecommendSettingBinding, SysRecommendViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, SysRecommendSettingActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<SysRecommendViewModel> =
        SysRecommendViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_sys_recommend_setting

    override fun initPage(savedInstanceState: Bundle?) {
        setupOpenHomePushSwitchListener()
        subscribeViewModelEvent()
        viewModel.start()
    }

    private fun subscribeViewModelEvent() {
        viewModel.openPersonalizedPush.observe(this, Observer {
            viewBinding.switchPrecisePush.toggleSwitch(it)
        })

        viewModel.switchSuccessEvent.observe(this, Observer {
            postSysRecommendSwitchEvent()
        })
    }

    private fun postSysRecommendSwitchEvent() {
        RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_SWITCH_SYS_RECOMMEND))
    }

    private fun setupOpenHomePushSwitchListener() {
        viewBinding.switchPrecisePush.setOnClickListener {
            viewModel.switchHomePushStatus(viewBinding.switchPrecisePush.isOpened)
        }
    }
}