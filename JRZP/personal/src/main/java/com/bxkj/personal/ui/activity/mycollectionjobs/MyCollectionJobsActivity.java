package com.bxkj.personal.ui.activity.mycollectionjobs;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.bxkj.common.base.BaseListActivity;
import com.therouter.router.Route;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.recyclerutil.RecycleViewDivider;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.personal.R;
import com.bxkj.personal.data.MyCollectionJobData;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;
import com.bxkj.personal.ui.activity.selectresume.SendResumeMethod;
import com.bxkj.personal.ui.activity.selectresume.SendResumeNavigation;

import java.util.List;

import javax.inject.Inject;

/**
 * @TODO: TODO
 * @date 2018/5/8
 */
@Route(path = MyCollectionJobsNavigation.PATH)
public class MyCollectionJobsActivity extends BaseListActivity
  implements MyCollectionJobsContract.View {

  @Inject
  MyCollectionJobsPresenter mMyCollectionJobsPresenter;

  private MyCollectionJobsAdapter mMyCollectionJobsAdapter;

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_only_list;
  }

  @Override
  protected void initPresenter() {
    mMyCollectionJobsPresenter.attachView(this);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_collection_job));
  }

  @Override
  protected void initPage() {
    super.initPage();
    mMyCollectionJobsAdapter =
      new MyCollectionJobsAdapter(this, null, R.layout.personal_recycler_my_collection_jobs_item);
    mMyCollectionJobsAdapter.setOnItemClickListener((view, position) -> {
      MyCollectionJobData myCollectionJobData = mMyCollectionJobsAdapter.getData().get(position);
      if (view.getId() == R.id.tv_cancel_collection) {
        new ActionDialog.Builder()
          .setTitle(getString(R.string.personal_confirm_cancel_collection))
          .setOnConfirmClickListener(
            (dialog) -> mMyCollectionJobsPresenter.uncollectionJob(getMUserID(),
              myCollectionJobData.getRelid(), position))
          .build()
          .show(getSupportFragmentManager(), ActionDialog.TAG);
      } else if (view.getId() == R.id.tv_submit_resume) {
        SendResumeNavigation.Companion.navigate(myCollectionJobData.getRelid(),
          myCollectionJobData.getComid(),
          SendResumeMethod.SEND).start();
      } else {
        startActivity(JobDetailsActivityV2.Companion.newIntent(this, myCollectionJobData.getRelid()));
      }
    });

    getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
    getRecyclerView().addItemDecoration(
      new RecycleViewDivider(this, LinearLayoutManager.HORIZONTAL, DensityUtils.dp2px(this, 8),
        getMColor(R.color.common_f4f4f4)));
    getRecyclerView().setAdapter(mMyCollectionJobsAdapter);

    getRefreshLayoutManager().refreshPage();
  }

  @Override
  protected void loadData() {
    mMyCollectionJobsPresenter.getUserCollectionJobs(getMUserID(),
      getRefreshLayoutManager().getCurrentPage(), CommonApiConstants.DEFAULT_PAGE_SIZE);
  }

  @Override
  public void getUserCollectionJobsSuccess(List<MyCollectionJobData> userCollectionJobsList) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    if (getRefreshLayoutManager().currentFirstPage()) {
      mMyCollectionJobsAdapter.reset(userCollectionJobsList);
    } else {
      mMyCollectionJobsAdapter.addAll(userCollectionJobsList);
    }
  }

  @Override
  public void uncollectionJobSuccess(int position) {
    if (mMyCollectionJobsAdapter.removeAt(position) == 0) {
      getRefreshLayoutManager().refreshPage();
    }
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mMyCollectionJobsPresenter.detachView();
  }
}
