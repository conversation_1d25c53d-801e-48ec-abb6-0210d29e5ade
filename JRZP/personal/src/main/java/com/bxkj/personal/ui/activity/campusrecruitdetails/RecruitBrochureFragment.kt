package com.bxkj.personal.ui.activity.campusrecruitdetails

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.data.CampusRecruitData
import com.bxkj.personal.data.CampusRecruitJobBean
import com.bxkj.personal.databinding.PersonalFragmentRecruitBrochureBinding
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation

/**
 * Description: 校招详情-招聘简章
 * Author:45457
 **/
class RecruitBrochureFragment : BaseDBFragment<PersonalFragmentRecruitBrochureBinding, RecruitBrochureViewModel>() {

    companion object {

        private const val EXTRA_CAMPUS_RECRUIT_DATA = "CAMPUS_RECRUIT_DATA"
        private const val EXTRA_INFO_TYPE = "INFO_TYPE"

        fun newInstance(campusRecruitData: CampusRecruitData, infoType: Int = 1): RecruitBrochureFragment {
            return RecruitBrochureFragment().apply {
                arguments = bundleOf(
                    EXTRA_CAMPUS_RECRUIT_DATA to campusRecruitData,
                    EXTRA_INFO_TYPE to infoType
                )
            }
        }
    }

    private val _campusRecruitData: CampusRecruitData? by lazy {
        arguments?.getParcelable(EXTRA_CAMPUS_RECRUIT_DATA)
    }

    override fun getViewModelClass(): Class<RecruitBrochureViewModel> = RecruitBrochureViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_fragment_recruit_brochure

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupCampusRecruitJobList()
        subscribeViewModelEvent()

        viewModel.start(
            arguments?.getParcelable(EXTRA_CAMPUS_RECRUIT_DATA)!!,
            arguments?.getInt(EXTRA_INFO_TYPE, 1).getOrDefault(1)
        )

        viewBinding.webBrochure.loadRichText(_campusRecruitData?.content)

        viewBinding.tvViewJobList.setOnClickListener {
            viewBinding.scrollContent.smoothScrollTo(0, viewBinding.recyclerJobList.top)
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.toCreateResumeCommand.observe(this) {
            MicroResumeInfoNavigation.create().start()
        }
    }

    private fun setupCampusRecruitJobList() {
        viewBinding.recyclerJobList.apply {
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_4))
                    .drawHeader(true)
                    .drawFoot(true)
                    .build()
            )
            adapter = SimpleDiffListAdapter(
                R.layout.personal_recycler_campus_reruit_item,
                CampusRecruitJobBean.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        if (v.id == R.id.tv_apply) {
                            getData()?.get(position)?.let {
                                viewModel.sendResume(it)
                            }
                        }
                    }
                }, R.id.tv_apply)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        viewBinding.webBrochure.onPause()
    }

    override fun onResume() {
        super.onResume()
        viewBinding.webBrochure.onResume()
    }

    override fun onDestroy() {
        viewBinding.webBrochure.destroy()
        super.onDestroy()
    }
}