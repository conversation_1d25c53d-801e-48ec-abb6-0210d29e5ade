package com.bxkj.personal.ui.fragment.signupuser

import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.R
import com.bxkj.personal.data.SignUpUserItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/3/27
 * @version: V1.0
 */
class SignUpUserChildViewModel @Inject constructor(private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val signUpListViewModel = RefreshListViewModel()
    val outSuccessEvent = LiveEvent<Void>()
    val passedSuccessEvent = LiveEvent<Void>()

    private var mUserType: Int = CommonApiConstants.NO_DATA
    private var mVideoId: Int = CommonApiConstants.NO_DATA

    init {
        signUpListViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getSignUpUserList(mVideoId, mUserType, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<SignUpUserItemData>> {
                override fun onSuccess(data: List<SignUpUserItemData>?) {
                    signUpListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30007) {
                        signUpListViewModel.noMoreData()
                    } else {
                        signUpListViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start(videoId: Int?, userType: Int?) {
        mVideoId = videoId ?: CommonApiConstants.NO_DATA
        mUserType = userType ?: CommonApiConstants.NO_DATA
        signUpListViewModel.refresh()
    }

    fun outUser(signUpUserItemData: SignUpUserItemData) {
        showLoading()
        mAccountRepo.updateSignUpUserStatus(signUpUserItemData.id, SignUpUserFragment.SIGN_UP_TYPE_OUT
                , object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                showToast(R.string.sign_up_user_out_tips)
                removeAndRefresh(signUpUserItemData)
                outSuccessEvent.call()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun passedUser(signUpUserItemData: SignUpUserItemData) {
        showLoading()
        mAccountRepo.updateSignUpUserStatus(signUpUserItemData.id, SignUpUserFragment.SIGN_UP_TYPE_PASSED
                , object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                showToast(R.string.sign_up_user_passed_tips)
                removeAndRefresh(signUpUserItemData)
                passedSuccessEvent.call()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    private fun removeAndRefresh(signUpUserItemData: SignUpUserItemData) {
        signUpListViewModel.remove(signUpUserItemData)
        if (signUpListViewModel.childCount == 0) {
            signUpListViewModel.refresh()
        }
    }

}