package com.bxkj.personal.data;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.data.WelfareItemData;
import com.bxkj.video.data.VideoData;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/4/19
 */

public class JobDetailsData {

  /**
   * id : 1268470 uid : 0 name : 机修工 edate1 : null edate2 : null money : 3000-5000元 number : 0 count
   * : 0 des : 设备维护维修 lxr : phone : qq : wtName : 2年以上 quaName : 高中 comName : null shiName : 宁波
   * quName : null islook : 0 com : {"id":0,"name":"宁波蜗牛锻造有限公司","address":null,"phone":"18329116516","lxr":"刘先生","qq":"","Info":null,"proName":"中外合资（合资·合作）","tradeName":"汽车·摩托车","sizeName":"500-999人","coordinate":null}
   */

  private int id;
  private int uid;
  private String name;
  private int type2;
  private Object edate1;
  private Object edate2;
  private String money;
  private int number;
  private int count;
  private String des;
  private String lxr;
  private String phone;
  private String qq;
  private String wtName;
  private String quaName;
  private String comName;
  private String shiName;
  private String xianName;
  private String jieName;
  private String quName;
  private int islook;
  private String userPhoto;
  private ComBean com;
  private List<VideoData> jobLinkVideos;
  private List<WelfareItemData> jobWelfareList;
  private int Isphone;

  public int getIsphone() {
    return Isphone;
  }

  public void setIsphone(int isphone) {
    Isphone = isphone;
  }

  @SerializedName("jnName")
  private String nature;

  @SerializedName("jnName2")
  private String partner;

  private int jnid;
  private int wtid;

  @SerializedName("moneyUnitName")
  private String salaryUnit;

  @SerializedName("moneyJiesuanName")
  private String salaryPaymentCycle;

  @SerializedName("shenfenName")
  private String identityRequire;

  public String getPartner() {
    return partner;
  }

  public void setPartner(String partner) {
    this.partner = partner;
  }

  public List<WelfareItemData> getJobWelfareList() {
    return jobWelfareList;
  }

  public void setJobWelfareList(List<WelfareItemData> jobWelfareList) {
    this.jobWelfareList = jobWelfareList;
  }

  public String getSalaryUnit() {
    return salaryUnit;
  }

  public void setSalaryUnit(String salaryUnit) {
    this.salaryUnit = salaryUnit;
  }

  public String getSalaryPaymentCycle() {
    return salaryPaymentCycle;
  }

  public void setSalaryPaymentCycle(String salaryPaymentCycle) {
    this.salaryPaymentCycle = salaryPaymentCycle;
  }

  public String getIdentityRequire() {
    return identityRequire;
  }

  public void setIdentityRequire(String identityRequire) {
    this.identityRequire = identityRequire;
  }

  public String getIdentityRequireText() {
    if (!CheckUtils.isNullOrEmpty(partner)) {
      return partner;
    }
    return identityRequire;
  }

  public Boolean isNormalRecruitment() {
    return jnid != 7 && jnid != 9 || (jnid == 7 && wtid != 2 && wtid != 3);
  }

  public int getWtid() {
    return wtid;
  }

  public void setWtid(int wtid) {
    this.wtid = wtid;
  }

  public String getNature() {
    return nature;
  }

  public void setNature(String nature) {
    this.nature = nature;
  }

  public int getJnid() {
    return jnid;
  }

  public void setJnid(int jnid) {
    this.jnid = jnid;
  }

  public List<VideoData> getJobLinkVideos() {
    return jobLinkVideos;
  }

  public void setJobLinkVideos(List<VideoData> jobLinkVideos) {
    this.jobLinkVideos = jobLinkVideos;
  }

  public String getUserPhoto() {
    return userPhoto;
  }

  public void setUserPhoto(String userPhoto) {
    this.userPhoto = userPhoto;
  }

  public String getXianName() {
    return xianName;
  }

  public void setXianName(String xianName) {
    this.xianName = xianName;
  }

  public String getJieName() {
    return jieName;
  }

  public void setJieName(String jieName) {
    this.jieName = jieName;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public int getType2() {
    return type2;
  }

  public void setType2(int type2) {
    this.type2 = type2;
  }

  public Object getEdate1() {
    return edate1;
  }

  public void setEdate1(Object edate1) {
    this.edate1 = edate1;
  }

  public Object getEdate2() {
    return edate2;
  }

  public void setEdate2(Object edate2) {
    this.edate2 = edate2;
  }

  public String getMoney() {
    return money;
  }

  public void setMoney(String money) {
    this.money = money;
  }

  public int getNumber() {
    return number;
  }

  public void setNumber(int number) {
    this.number = number;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public String getDes() {
    return (CheckUtils.isNullOrEmpty(des) ? name : des).replaceAll("\n", "<br>");
  }

  public void setDes(String des) {
    this.des = des;
  }

  public String getLxr() {
    return lxr == null ? "" : lxr;
  }

  public void setLxr(String lxr) {
    this.lxr = lxr;
  }

  public String getPhone() {
    return phone == null ? "" : phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getQq() {
    return qq == null ? "" : qq;
  }

  public void setQq(String qq) {
    this.qq = qq;
  }

  public String getWtName() {
    return CheckUtils.isNullOrEmpty(wtName) ? "经验不限" : wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public String getQuaName() {
    return CheckUtils.isNullOrEmpty(quaName) ? "学历不限" : quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public String getComName() {
    return comName;
  }

  public void setComName(String comName) {
    this.comName = comName;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getQuName() {
    return quName;
  }

  public void setQuName(String quName) {
    this.quName = quName;
  }

  public int getIslook() {
    return islook;
  }

  public void setIslook(int islook) {
    this.islook = islook;
  }

  public String getCovertSalary() {
    if (!CheckUtils.isNullOrEmpty(getMoney()) && getMoney().equals("面议")) {
      return getMoney();
    } else {
      final StringBuilder salaryBuilder = new StringBuilder();
      salaryBuilder.append(getMoney());
      if (!CheckUtils.isNullOrEmpty(salaryUnit)) {
        salaryBuilder.append("/").append(salaryUnit);
      }
      if (!CheckUtils.isNullOrEmpty(salaryPaymentCycle)) {
        salaryBuilder.append("/").append(salaryPaymentCycle);
      }
      return salaryBuilder.toString();
    }
  }

  public String[] getWelfareTextArray() {
    if (CheckUtils.isNullOrEmpty(jobWelfareList)) {
      return new String[0];
    }
    String[] welfareTextArray = new String[jobWelfareList.size()];
    for (int i = 0; i < jobWelfareList.size(); i++) {
      welfareTextArray[i] = jobWelfareList.get(i).getName();
    }
    return welfareTextArray;
  }

  public String getFixedAddress() {
    return CheckUtils.isNullOrEmpty(getCom().getAddress()) ? getCom().getName()
      : getCom().getAddress();
  }

  public ComBean getCom() {
    return com == null ? new ComBean() : com;
  }

  public void setCom(ComBean com) {
    this.com = com;
  }

  public static class ComBean {

    /**
     * id : 0 name : 宁波蜗牛锻造有限公司 address : null phone : 18329116516 lxr : 刘先生 qq : Info : null
     * proName : 中外合资（合资·合作） tradeName : 汽车·摩托车 sizeName : 500-999人 coordinate : null
     */

    private int id;
    private String name;
    private String address;
    private String phone;
    private String lxr;
    private String qq;
    private String Info;
    private String proName;
    private String tradeName;
    private String sizeName;
    private String coordinate;
    private String about;
    private String logo;

    public String getLogo() {
      return logo;
    }

    public void setLogo(String logo) {
      this.logo = logo;
    }

    public String getAbout() {
      StringBuilder companyAbout = new StringBuilder();
      if (!CheckUtils.isNullOrEmpty(tradeName)) {
        companyAbout.append(tradeName).append("|");
      }
      if (!CheckUtils.isNullOrEmpty(proName)) {
        companyAbout.append(proName).append("|");
      }
      if (!CheckUtils.isNullOrEmpty(sizeName)) {
        companyAbout.append(sizeName);
      }
      return CheckUtils.isNullOrEmpty(companyAbout) ? "无公司介绍" : companyAbout.toString();
    }

    public void setAbout(String about) {
      this.about = about;
    }

    public int getId() {
      return id;
    }

    public void setId(int id) {
      this.id = id;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getAddress() {
      return address;
    }

    public void setAddress(String address) {
      this.address = address;
    }

    public String getPhone() {
      return phone == null ? "" : phone;
    }

    public void setPhone(String phone) {
      this.phone = phone;
    }

    public String getLxr() {
      return lxr == null ? "" : lxr;
    }

    public void setLxr(String lxr) {
      this.lxr = lxr;
    }

    public String getQq() {
      return qq;
    }

    public void setQq(String qq) {
      this.qq = qq;
    }

    public String getInfo() {
      return Info;
    }

    public void setInfo(String Info) {
      this.Info = Info;
    }

    public String getProName() {
      return proName;
    }

    public void setProName(String proName) {
      this.proName = proName;
    }

    public String getTradeName() {
      return tradeName;
    }

    public void setTradeName(String tradeName) {
      this.tradeName = tradeName;
    }

    public String getSizeName() {
      return sizeName;
    }

    public void setSizeName(String sizeName) {
      this.sizeName = sizeName;
    }

    public String getCoordinate() {
      return coordinate;
    }

    public void setCoordinate(String coordinate) {
      this.coordinate = coordinate;
    }
  }
}
