package com.bxkj.personal.ui.activity.applicationrecord;

import android.content.Context;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.personal.R;
import com.bxkj.personal.data.ResumeDeliveryRecordBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.submitrecord
 * @Description:
 * @TODO: TODO
 * @date 2018/5/8
 */

public class ApplicationRecordAdapter extends SuperAdapter<ResumeDeliveryRecordBean> {

  private List<ResumeDeliveryRecordBean> mSelectRecord;
  private boolean mCheckBoxShow = false;

  public ApplicationRecordAdapter(Context context, List<ResumeDeliveryRecordBean> list, int layoutResId) {
    super(context, layoutResId, list);
    mSelectRecord = new ArrayList<>();
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, ResumeDeliveryRecordBean resumeDeliveryRecordBean,
      int position) {
    holder.setText(R.id.tv_job_name, resumeDeliveryRecordBean.getName());

    TextView tvJobType = holder.findViewById(R.id.tv_type);
    if (resumeDeliveryRecordBean.emptyNatureName()) {
      tvJobType.setVisibility(View.GONE);
    } else {
      tvJobType.setVisibility(View.VISIBLE);
      tvJobType.setText(resumeDeliveryRecordBean.getJnName());
    }

    TextView tvJobArea = holder.findViewById(R.id.tv_job_area);
    if (CheckUtils.isNullOrEmpty(resumeDeliveryRecordBean.getXianName())) {
      tvJobArea.setVisibility(View.GONE);
    } else {
      tvJobArea.setVisibility(View.VISIBLE);
      holder.setText(R.id.tv_job_area, resumeDeliveryRecordBean.getXianName());
    }

    TextView tvIdentity = holder.findViewById(R.id.tv_identity);
    if (CheckUtils.isNullOrEmpty(resumeDeliveryRecordBean.getIdentityRequire())) {
      tvIdentity.setVisibility(View.GONE);
    } else {
      tvIdentity.setVisibility(View.VISIBLE);
      tvIdentity.setText(resumeDeliveryRecordBean.getIdentityRequire());
    }

    TextView tvPartner = holder.findViewById(R.id.tv_partner);
    if (CheckUtils.isNullOrEmpty(resumeDeliveryRecordBean.getPartnerNature())) {
      tvPartner.setVisibility(View.GONE);
    } else {
      tvPartner.setVisibility(View.VISIBLE);
      tvPartner.setText(resumeDeliveryRecordBean.getPartnerNature());
    }

    holder.setText(R.id.tv_job_degree, resumeDeliveryRecordBean.getQuaName());
    holder.setText(R.id.tv_job_exp, resumeDeliveryRecordBean.getWtName());
    holder.setText(R.id.tv_date, resumeDeliveryRecordBean.getFormatSubmitDate());
    holder.setText(R.id.tv_job_publish_enterprise,
        CheckUtils.isNullOrEmpty(resumeDeliveryRecordBean.getConame()) ? mContext.getString(
            R.string.application_record_position_deleted) : resumeDeliveryRecordBean.getConame());
    holder.findViewById(R.id.tv_application_state).setVisibility(View.VISIBLE);
    holder.setText(R.id.tv_application_state, resumeDeliveryRecordBean.getStateName());

    ImageView ivCover = holder.findViewById(R.id.iv_video_cover);
    TextView tvSalary = holder.findViewById(R.id.tv_job_wages);
    tvSalary.setText(resumeDeliveryRecordBean.getConvertSalary());
    if (!CheckUtils.isNullOrEmpty(resumeDeliveryRecordBean.getVideoPic())) {
      ivCover.setVisibility(View.VISIBLE);
      ImageLoader.loadImage(mContext,
          new GlideLoadConfig.Builder().url(resumeDeliveryRecordBean.getVideoPic())
              .into(ivCover)
              .radius(DensityUtils.dp2px(mContext, 4))
              .build());
    } else {
      ivCover.setVisibility(View.GONE);
    }

    CheckBox cbSelectThis = holder.findViewById(R.id.cb_select_this);
    cbSelectThis.setVisibility(mCheckBoxShow ? View.VISIBLE : View.GONE);
    cbSelectThis.setChecked(mSelectRecord.contains(resumeDeliveryRecordBean));
    cbSelectThis.setOnClickListener(view -> {
      if (cbSelectThis.isChecked()) {
        mSelectRecord.add(resumeDeliveryRecordBean);
      } else {
        mSelectRecord.remove(resumeDeliveryRecordBean);
      }
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(cbSelectThis, position);
      }
    });

    holder.itemView.setOnClickListener(view -> {
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(holder.itemView, position);
      }
    });
  }

  public void showCheckBox(boolean showCbSelect) {
    mCheckBoxShow = showCbSelect;
    notifyDataSetChanged();
  }

  public boolean getCheckBoxShow() {
    return mCheckBoxShow;
  }

  public List<ResumeDeliveryRecordBean> getSelectedList() {
    return mSelectRecord;
  }

  public void clearSelectedList() {
    mSelectRecord.clear();
  }
}
