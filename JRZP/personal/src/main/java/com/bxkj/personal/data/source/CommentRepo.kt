package com.bxkj.personal.data.source

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.data.CommentSuccessResultData
import com.bxkj.personal.api.PersonalApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data.source
 * @Description:
 * <AUTHOR>
 * @date 2019/11/25
 * @version V1.0
 */
class CommentRepo @Inject constructor(private val mPersonalApi: PersonalApi) : BaseRepo() {

    fun getCommentList(
        userId: Int,
        newsId: Int,
        newsType: Int,
        parentId: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<CommentItemData>>
    ) {
        mPersonalApi.getCommendList(userId, newsId, newsType, parentId, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun likeOrUnlikeTheComment(
        userId: Int,
        commentId: Int,
        newsType: Int,
        newsId: Int,
        callBack: ResultCallBack
    ) {
        mPersonalApi.likeOrUnlikeTheComment(userId, commentId, newsType, newsId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack.onError(respondThrowable)
                }
            })
    }

    fun addComment(
        userId: Int,
        newsId: Int,
        typeId: Int,
        parentId: Int,
        replyUserId: Int,
        replyUserName: String,
        content: String,
        callback: ResultDataCallBack<CommentItemData>
    ) {
        mPersonalApi.addComment(userId, newsId, typeId, parentId, replyUserId, content)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    val resultData = baseResponse.data as CommentSuccessResultData
                    callback.onSuccess(
                        CommentItemData.fromUserCreate(
                            resultData.id,
                            userId,
                            content,
                            "刚刚",
                            resultData.nickName2,
                            resultData.photo,
                            replyUserId,
                            if (replyUserId == CommonApiConstants.NO_ID) CommonApiConstants.NO_TEXT else replyUserName
                        )
                    )
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun deleteComment(userId: Int, commentId: Int, callback: ResultCallBack) {
        mPersonalApi.deleteComment(userId, commentId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }
}