package com.bxkj.personal.ui.activity.agreement;

import android.view.View;

import com.bxkj.common.base.BaseActivity;
import com.bxkj.common.util.ActivityRouterUtils;
import com.bxkj.personal.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.agreement
 * @Description:
 * @TODO: TODO
 * @date 2019/5/30
 */
public class AgreementActivity extends BaseActivity implements View.OnClickListener {

    @Override
    protected int getLayoutId() {
        return R.layout.personal_activity_agreement;
    }

    @Override
    protected void initPage() {
        setupItemClickListener();
    }

    private void setupItemClickListener() {
        findViewById(R.id.tv_agreement).setOnClickListener(this);
        findViewById(R.id.tv_privacy).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_agreement) {
            ActivityRouterUtils.toAgreementActivity();
        } else {
            ActivityRouterUtils.toPrivacyActivity();
        }
    }
}
