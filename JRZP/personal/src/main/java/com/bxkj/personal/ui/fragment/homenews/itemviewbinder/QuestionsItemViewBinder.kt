package com.bxkj.personal.ui.fragment.homenews.itemviewbinder

import androidx.core.content.ContextCompat
import androidx.databinding.library.baseAdapters.BR
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.NewsItemData.MedialistBean
import com.bxkj.personal.data.NewsItemData.PhotoListBean

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.homenews.itemviewbinder
 * @Description:
 * <AUTHOR>
 * @date 2019/12/24
 * @version V1.0
 */
class QuestionsItemViewBinder :
  DefaultViewBinder<NewsItemData>(R.layout.personal_recycler_news_list_question, BR.data, true) {
  private var mPhotoListAdapter: SimpleDBListAdapter<MedialistBean>? = null
  private var mAnswerListAdapter: SimpleDBListAdapter<PhotoListBean>? = null

  override fun onBindViewHolder(
    holder: SuperViewHolder,
    item: NewsItemData,
    position: Int
  ) {
    super.onBindViewHolder(holder, item, position)
    if (item.hasMultiPic()) {
      if (mPhotoListAdapter == null) {
        mPhotoListAdapter =
          SimpleDBListAdapter(
            holder.itemView.context,
            layout.personal_recycler_news_img_item
          )
        val recyclerPhotoList = holder.findViewById<RecyclerView>(R.id.recycler_photo)
        recyclerPhotoList.layoutManager =
          GridLayoutManager(holder.itemView.context, item.listMedia.size)
        recyclerPhotoList.addItemDecoration(
          GridItemDecoration(
            ContextCompat.getDrawable(
              holder.itemView.context,
              R.drawable.divider_ffffff_4
            )
          )
        )
        recyclerPhotoList.adapter = mPhotoListAdapter
      }
      mPhotoListAdapter?.reset(item.listMedia)
    }
    if (item.hasAnswerAvatar()) {
      val showList = if (item.photoList.size > 3) item.photoList.subList(0, 3) else item.photoList
      if (mAnswerListAdapter == null) {
        mAnswerListAdapter =
          SimpleDBListAdapter(
            holder.itemView.context,
            layout.personal_recycler_answerer_avatar_item
          )
        val recyclerAnswererList = holder.findViewById<RecyclerView>(R.id.recycler_answerer_list)
        recyclerAnswererList.isNestedScrollingEnabled = false
        recyclerAnswererList.layoutManager =
          LinearLayoutManager(holder.itemView.context, LinearLayoutManager.HORIZONTAL, false)
        recyclerAnswererList.adapter = mAnswerListAdapter
      }
      mAnswerListAdapter?.reset(showList)
    }
  }
}