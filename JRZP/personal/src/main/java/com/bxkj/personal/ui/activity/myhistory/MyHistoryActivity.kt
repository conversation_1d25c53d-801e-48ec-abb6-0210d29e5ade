package com.bxkj.personal.ui.activity.myhistory

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import cn.jzvd.Jzvd
import com.therouter.router.Route
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityMyBinding
import com.bxkj.personal.ui.fragment.collection.CollectionFragment
import com.bxkj.personal.ui.fragment.comment.CommentFragment
import com.bxkj.personal.ui.fragment.like.LikeFragment
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.my
 * @Description: 我的历史
 * <AUTHOR>
 * @date 2020/2/12
 * @version V1.0
 */
@Route(path = MyHistoryNavigation.PATH)
class MyHistoryActivity : BaseDBActivity<PersonalActivityMyBinding, MyHistoryViewModel>(),
  View.OnClickListener, HasEditListGroup {

  companion object {

    fun newIntent(context: Context, pageIndex: Int): Intent {
      return Intent(context, MyHistoryActivity::class.java)
        .apply {
          putExtra(MyHistoryNavigation.EXTRA_PAGE_INDEX, pageIndex)
        }
    }
  }

  private var mContentPagerAdapter: CommonPagerAdapter? = null

  override fun getViewModelClass(): Class<MyHistoryViewModel> = MyHistoryViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_my

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this
    setupViewPageAdapter()
    subscribeViewModelEvent()
    setupIndicator()
    viewBinding.vpContent.currentItem = intent.getIntExtra(MyHistoryNavigation.EXTRA_PAGE_INDEX, 0)
  }

  private fun subscribeViewModelEvent() {
    viewModel.openEdit.observe(this, Observer {
      mContentPagerAdapter?.let { adapter ->
        val currentPage = adapter.getItem(viewBinding.vpContent.currentItem)
        if (currentPage is EditList) {
          currentPage.switchEditState(it)
        }
      }
    })
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.iv_back) {
        finish()
      } else {
        viewModel.openOrCloseEdit()
      }
    }
  }

  private fun setupViewPageAdapter() {
    mContentPagerAdapter =
      CommonPagerAdapter(
        supportFragmentManager,
        arrayListOf(
          CollectionFragment.newInstance(),
          CommentFragment.newInstance(),
          LikeFragment.newInstance()
//          MyVideoListNavigation.from(AppConstants.USER_TYPE_PERSONAL)
        )
      )
    viewBinding.vpContent.adapter = mContentPagerAdapter
    viewBinding.vpContent.offscreenPageLimit = 3
    viewBinding.vpContent.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
      override fun onPageScrollStateChanged(state: Int) {
      }

      override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
      }

      override fun onPageSelected(position: Int) {
        viewModel.closeEdit()
        viewModel.setCurrentPage(position)
      }
    })
  }

  private fun setupIndicator() {
    val commonNavigator = CommonNavigator(this)
    commonNavigator.isAdjustMode = true
    commonNavigator.adapter =
      object : MagicIndicatorAdapter(resources.getStringArray(R.array.my_history_type)) {
        override fun getTitleView(context: Context, index: Int): IPagerTitleView {
          val pageTitleView =
            ScalePagerTitleView(context)
          pageTitleView.textSize = 16f
          pageTitleView.normalColor = ContextCompat.getColor(context, R.color.cl_333333)
          pageTitleView.selectedColor = ContextCompat.getColor(context, R.color.cl_333333)
          pageTitleView.text = getTitles()[index]
          pageTitleView.setOnClickListener { view ->
            getOnTabClickListener()?.onTabClicked(view, index)
          }
          return pageTitleView
        }
      }.apply {
        setOnTabClickListener(object :
          OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    viewBinding.indicator.navigator = commonNavigator
    ViewPagerHelper.bind(viewBinding.indicator, viewBinding.vpContent)
  }

  override fun onBackPressed() {
    if (Jzvd.backPress()) {
      return
    }
    super.onBackPressed()
  }

  override fun closeEdit() {
    viewModel.closeEdit()
  }

  override fun enableEdit(index: Int, enabled: Boolean) {
    viewModel.enableEdit(index, enabled)
  }

}