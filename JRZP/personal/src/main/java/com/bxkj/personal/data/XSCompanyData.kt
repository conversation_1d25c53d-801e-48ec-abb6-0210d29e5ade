package com.bxkj.personal.data

/**
 * author:Sanjin
 * date:2025/2/12
 **/
data class XSCompanyData(
  val id: Int,
  val glid: Int,
  val comLogo: String,
  val coname: String,
  val huoyueDate: String,
  val laiyuanName: String,
  val status: Int,
  val lxr: String,
  val mobile: String
) {

  fun getConvertCompanyName(): String {
    return coname.ifBlank {
      "待完善($laiyuanName)"
    }
  }

  fun getConvertContact(): String {
    return lxr.ifBlank {
      "<font color='#888888'>未留称呼</font>"
    }
  }

  fun getConvertAuthStatus(): String {
    if (status == 0) {
      return "认证状态：<font color='#888888'>未认证</font>"
    } else {
      return "认证状态：<font color='#FF7405'>已认证</font>"
    }
  }
}