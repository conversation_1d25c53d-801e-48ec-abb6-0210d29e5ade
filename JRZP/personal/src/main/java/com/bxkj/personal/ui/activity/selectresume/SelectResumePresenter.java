package com.bxkj.personal.ui.activity.selectresume;

import androidx.annotation.NonNull;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.data.ResumeItemData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.selectresume
 * @Description: SelectResume
 * @TODO: TODO
 * @date 2018/3/27
 */

public class SelectResumePresenter extends SelectResumeContract.Presenter {

  private static final String TAG = SelectResumePresenter.class.getSimpleName();
  private PersonalApi mPersonalApi;

  @Inject
  public SelectResumePresenter(PersonalApi personalApi) {
    mPersonalApi = personalApi;
  }

  @Override
  public void getAvailableResumeList(int userId, int jobId) {
    mPersonalApi.getAvailableResumeList(userId, jobId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.getAvailableResumeSuccess((List<ResumeItemData>) baseResponse.getDataList());
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.noResume();
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  public void submitResume(int userId, int companyUserId, int jobId, int resumeId, int type,
      int from) {
    mView.showLoading();
    mPersonalApi.submitResume(userId, companyUserId, jobId, resumeId, type, from)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.submitResumeSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            if (respondThrowable.getErrCode() == 30002) {
              mView.resumeInfoNotPrefect(resumeId);
            } else {
              mView.onError(respondThrowable.getErrMsg());
            }
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }

          @Override
          public void onComplete() {
            super.onComplete();
            mView.hiddenLoading();
          }
        });
  }

  @Override
  public void checkUserInfoCompleted(int userId) {
    mView.showLoading();
    mPersonalApi.checkUserInfoIsComplete(userId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(@NonNull BaseResponse baseResponse) {
            mView.userInfoCompleted();
          }

          @Override
          protected void onError(@NonNull RespondThrowable respondThrowable) {
            if (respondThrowable.isNetworkError()) {
              mView.onError(respondThrowable.getErrMsg());
            } else {
              mView.userInfoNotCompleted();
            }
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }

          @Override
          public void onComplete() {
            mView.hiddenLoading();
          }
        });
  }
}
