package com.bxkj.personal.ui.activity.myresume.languageskills;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.data.LanguageSkillsItemData;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.languageskills
 * @Description: LanguageSkills
 * @TODO: TODO
 * @date 2018/3/27
 */

public class LanguageSkillsPresenter extends LanguageSkillsContract.Presenter {

    private static final String TAG = LanguageSkillsPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public LanguageSkillsPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getLanguageSkillsDetails(int userId, int skillId) {
        mView.showLoading();
        mPersonalApi.getLanguageSkillsDetails(userId, skillId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getLanguageSkillsDetailsSuccess((LanguageSkillsItemData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }

    @Override
    public void addLanguageSkills(int userId, int resumeId, LanguageSkillsItemData languageSkillsItemData) {
        if (!checkLanguageSkills(languageSkillsItemData)) return;
        mView.showLoading();
        mPersonalApi.addLanguageSkills(userId, resumeId, languageSkillsItemData.getLanid(), languageSkillsItemData.getQuaid(), languageSkillsItemData.getRemark())
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.addLanguageSkillsSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }

    private boolean checkLanguageSkills(LanguageSkillsItemData languageSkillsItemData) {
        if (languageSkillsItemData.getLanid() == 0) {
            mView.onError("未选择语种");
            return false;
        } else if (languageSkillsItemData.getQuaid() == 0) {
            mView.onError("未选择熟练程度");
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void updateLanguageSkills(int userId, int skillId, LanguageSkillsItemData languageSkillsItemData) {
        if (!checkLanguageSkills(languageSkillsItemData)) return;
        mView.showLoading();
        mPersonalApi.updateLanguageSkills(userId, skillId, languageSkillsItemData.getLanid(), languageSkillsItemData.getQuaid(), languageSkillsItemData.getRemark())
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateLanguageSkills();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }

    @Override
    public void deleteLanguageSkills(int userId, int skillId) {
        mView.showLoading();
        mPersonalApi.deleteLanguageSkills(userId, skillId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.deleteLanguageSkills();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }
}
