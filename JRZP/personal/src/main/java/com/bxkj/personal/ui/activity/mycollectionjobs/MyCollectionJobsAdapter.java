package com.bxkj.personal.ui.activity.mycollectionjobs;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.personal.R;
import com.bxkj.personal.data.MyCollectionJobData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.mycollectionjobs
 * @Description:
 * @TODO: TODO
 * @date 2018/5/8
 */

public class MyCollectionJobsAdapter extends SuperAdapter<MyCollectionJobData> {

  public MyCollectionJobsAdapter(Context context, List<MyCollectionJobData> list, int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, MyCollectionJobData myCollectionJobData,
      int position) {
    holder.setText(R.id.tv_job_name, myCollectionJobData.getName());
    holder.setText(R.id.tv_job_area, myCollectionJobData.getQuName());
    holder.setText(R.id.tv_job_degree, myCollectionJobData.getFormatEduName());
    holder.setText(R.id.tv_job_exp, myCollectionJobData.getFormatExpName());
    holder.setText(R.id.tv_date, myCollectionJobData.getEdate1());
    holder.setText(R.id.tv_job_publish_enterprise, myCollectionJobData.getComName());

    TextView tvJobType = holder.findViewById(R.id.tv_type);
    if (myCollectionJobData.emptyNatureName()) {
      tvJobType.setVisibility(View.GONE);
    } else {
      tvJobType.setVisibility(View.VISIBLE);
      tvJobType.setText(myCollectionJobData.getJnName());
    }

    TextView tvJobArea = holder.findViewById(R.id.tv_job_area);
    if (CheckUtils.isNullOrEmpty(myCollectionJobData.getQuName())) {
      tvJobArea.setVisibility(View.GONE);
    } else {
      tvJobArea.setVisibility(View.VISIBLE);
      holder.setText(R.id.tv_job_area, myCollectionJobData.getQuName());
    }

    TextView tvIdentity = holder.findViewById(R.id.tv_identity);
    if (CheckUtils.isNullOrEmpty(myCollectionJobData.getIdentityRequire())) {
      tvIdentity.setVisibility(View.GONE);
    } else {
      tvIdentity.setVisibility(View.VISIBLE);
      tvIdentity.setText(myCollectionJobData.getIdentityRequire());
    }

    TextView tvPartner = holder.findViewById(R.id.tv_partner);
    if (CheckUtils.isNullOrEmpty(myCollectionJobData.getPartnerNature())) {
      tvPartner.setVisibility(View.GONE);
    } else {
      tvPartner.setVisibility(View.VISIBLE);
      tvPartner.setText(myCollectionJobData.getPartnerNature());
    }

    ImageView ivCover = holder.findViewById(R.id.iv_video_cover);
    TextView tvSalary = holder.findViewById(R.id.tv_job_wages);
    if (!CheckUtils.isNullOrEmpty(myCollectionJobData.getVideoPic())) {
      ivCover.setVisibility(View.VISIBLE);
      ImageLoader.loadImage(mContext,
          new GlideLoadConfig.Builder().url(myCollectionJobData.getVideoPic())
              .into(ivCover)
              .radius(DensityUtils.dp2px(mContext, 4))
              .build());
    } else {
      tvSalary.setText(myCollectionJobData.getConvertSalary());
      ivCover.setVisibility(View.GONE);
    }

    TextView tvCancelCollection = holder.findViewById(R.id.tv_cancel_collection);
    tvCancelCollection.setOnClickListener(view -> {
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(tvCancelCollection, position);
      }
    });

    TextView tvSubmitResume = holder.findViewById(R.id.tv_submit_resume);
    tvSubmitResume.setOnClickListener(view -> {
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(tvSubmitResume, position);
      }
    });

    holder.itemView.setOnClickListener(view -> {
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(view, position);
      }
    });
  }
}
