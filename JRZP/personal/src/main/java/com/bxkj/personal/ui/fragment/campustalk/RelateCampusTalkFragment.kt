package com.bxkj.personal.ui.fragment.campustalk

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.data.CampusTalkData
import com.bxkj.personal.databinding.PersonalFragmentRelateCampusTalkBinding
import com.bxkj.personal.ui.activity.campustalkdetails.CampusTalkDetailsActivity

/**
 * Description:关联宣讲会
 * Author:Sanjin
 **/
class RelateCampusTalkFragment :
  BaseDBFragment<PersonalFragmentRelateCampusTalkBinding, RelateCampusTalkViewModel>() {

  override fun getViewModelClass(): Class<RelateCampusTalkViewModel> =
    RelateCampusTalkViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_relate_campus_talk

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupListAdapter()

    viewModel.start(arguments?.getInt(EXTRA_SCHOOL_ID).getOrDefault())
  }

  private fun setupListAdapter() {
    viewBinding.includeRelateCampusTalk.recyclerContent.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .drawHeader(true)
          .drawFoot(true)
          .divider(getResDrawable(R.drawable.divider_6))
          .build()
      )
    }

    viewModel.listViewModel.setAdapter(
      SimpleDBListAdapter<CampusTalkData>(
        requireContext(),
        R.layout.personal_recycler_relate_campus_talk_item
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data.get(position).let {
              startActivity(CampusTalkDetailsActivity.newIntent(requireContext(), it.id))
            }
          }
        })
      }
    )
  }

  companion object {

    private const val EXTRA_SCHOOL_ID = "SCHOOL_ID"

    fun newInstance(schoolId: Int): Fragment {
      return RelateCampusTalkFragment().apply {
        arguments = android.os.Bundle().apply {
          putInt(EXTRA_SCHOOL_ID, schoolId)
        }
      }
    }
  }
}