package com.bxkj.personal.ui.activity.selectresume;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.constants.RouterConstants;
import com.bxkj.common.enums.ChatRole;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout;
import com.bxkj.personal.R;
import com.bxkj.personal.api.PersonalApiConstants;
import com.bxkj.personal.data.ResumeItemData;
import com.bxkj.personal.ui.activity.conversation.GeekChatContentActivity;
import com.bxkj.personal.ui.activity.createresumesteptwo.CreateResumeStepTwoActivity;
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation;
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant;
import com.bxkj.personal.ui.activity.userbasicinfo.FillInfoNext;
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoActivity;
import com.therouter.router.Route;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.resumelist
 * @Description: 简历列表
 * @TODO: TODO
 * @date 2018/4/20
 */
@Route(path = SendResumeNavigation.PATH)
public class SelectResumeActivity extends BaseDaggerActivity implements SelectResumeContract.View {

  public static final String JOB_ID = "mJobId";
  public static final String COMPANY_USER_ID = "mCompanyUserId";

  @Inject
  SelectResumePresenter mSelectResumePresenter;

  private RecyclerView recyclerResumeList;
  private TextView tvSubmit;

  private int mCompanyUserId;
  private int mJobId;
  private AvailableResumeAdapter mAvailableResumeAdapter;

  private PageStatusLayout mPageStatusLayout;

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mSelectResumePresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_select_resume;
  }

  @Override
  protected void onSaveInstanceState(Bundle outState) {
    super.onSaveInstanceState(outState);
    outState.putInt(JOB_ID, mJobId);
  }

  @Override
  protected void onRestoreInstanceState(Bundle savedInstanceState) {
    super.onRestoreInstanceState(savedInstanceState);
    mJobId = savedInstanceState.getInt(JOB_ID);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_resume_list));
  }

  public static Intent newIntent(Activity activity, int jobId, int companyUserId) {
    Intent intent = new Intent(activity, SelectResumeActivity.class);
    intent.putExtra(JOB_ID, jobId);
    intent.putExtra(COMPANY_USER_ID, companyUserId);
    return intent;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mJobId = getIntent().getIntExtra(JOB_ID, CommonApiConstants.NO_DATA);
    mCompanyUserId = getIntent().getIntExtra(COMPANY_USER_ID, CommonApiConstants.NO_DATA);

    setupSubmitText();

    mPageStatusLayout = PageStatusLayout.wrap(recyclerResumeList);
  }

  private void setupSubmitText() {
    if (getIntent().getIntExtra(SendResumeNavigation.EXTRA_SEND_METHOD,
      SendResumeMethod.CONVERSATION) == SendResumeMethod.CONVERSATION) {
      tvSubmit.setText(getString(R.string.select_resume_conversation_text));
    } else {
      tvSubmit.setText(getString(R.string.select_resume_send_text));
    }
  }

  @Override
  protected void onResume() {
    super.onResume();
    mSelectResumePresenter.getAvailableResumeList(getMUserID(), mJobId);
  }

  private void onViewClicked() {
    if (mAvailableResumeAdapter.getSelectPosition() >= mAvailableResumeAdapter.getData().size()) {
      showToast("未选择简历");
      return;
    }
    submitResume();
  }

  @Override
  public void getAvailableResumeSuccess(List<ResumeItemData> resumeItemDataList) {
    mPageStatusLayout.hidden();
    tvSubmit.setVisibility(View.VISIBLE);
    tvSubmit.setEnabled(true);

    mAvailableResumeAdapter = new AvailableResumeAdapter(this, resumeItemDataList,
      R.layout.personal_recycler_resume_item);
    recyclerResumeList.setLayoutManager(new LinearLayoutManager(this));
    recyclerResumeList.setAdapter(mAvailableResumeAdapter);
  }

  @Override
  public void noResume() {
    mPageStatusLayout.show(PageStatusConfigFactory.newErrorConfig()
      .setText(getString(R.string.select_resume_no_resume))
      .setBtnText("立即创建")
      .setOnButtonClickListener(
        () ->
          MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
      ));
  }

  @Override
  public void submitResumeSuccess() {
    showToast(getString(R.string.personal_submit_success));
    setResult(SendResumeNavigation.RESULT_SEND_RESUME_SUCCESS);
    finish();
  }

  @Override
  public void resumeInfoNotPrefect(int resumeId) {
    new ActionDialog.Builder()
      .setActionType(ActionDialog.BASIC_TYPE)
      .setContent(getString(R.string.select_resume_no_perfect_tips))
      .setConfirmText(getString(R.string.select_resume_to_perfect_the_resume))
      .setOnConfirmClickListener((dialog) ->
        MicroResumeInfoNavigation.create().start()
      ).build().show(getSupportFragmentManager(), ActionDialog.TAG);
  }

  @Override
  public void userInfoCompleted() {
    startActivity(CreateResumeStepTwoActivity.Companion.newIntent(this,
      RouterConstants.CREATE_RESUME_FROM_APPLICATION));
  }

  @Override
  public void userInfoNotCompleted() {
    startActivity(
      UserBasicInfoActivity.Companion.newIntent(this, FillInfoNext.NEXT_APPLICATION_RESUME));
  }

  private void submitResume() {
    int sendMethod = getIntent().getIntExtra(SendResumeNavigation.EXTRA_SEND_METHOD,
      SendResumeMethod.CONVERSATION);
    if (sendMethod == SendResumeMethod.CONVERSATION) {
      startActivity(GeekChatContentActivity.Companion.newIntent(this, ChatRole.PERSONAL,
        mCompanyUserId, mJobId,
        mAvailableResumeAdapter.getData()
          .get(mAvailableResumeAdapter.getSelectPosition())
          .getId(),
        mAvailableResumeAdapter.getData()
          .get(mAvailableResumeAdapter.getSelectPosition())
          .getName(),
        true));
      setResult(RESULT_OK, getIntent());
      finish();
    } else {
      mSelectResumePresenter.submitResume(getMUserID(), mCompanyUserId, mJobId,
        mAvailableResumeAdapter.getData()
          .get(mAvailableResumeAdapter.getSelectPosition())
          .getId(),
        PersonalApiConstants.USER_SUBMIT_RESUME_TYPE,
        sendMethod == SendResumeMethod.SEND ? 0 : 5);
    }
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  private void bindView(View bindSource) {
    recyclerResumeList = bindSource.findViewById(R.id.rv_resume_list);
    tvSubmit = bindSource.findViewById(R.id.tv_submit);
    bindSource.findViewById(R.id.tv_submit).setOnClickListener(v -> onViewClicked());
  }
}
