package com.bxkj.personal.ui.activity.searchnews

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.jrzp.support.db.entry.SearchRecord

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.searchnews
 * @Description:
 * <AUTHOR>
 * @date 2020/2/24
 * @version V1.0
 */
class SearchNewsHistoryListAdapter constructor(context: Context, private val viewModel: SearchNewsViewModel) : SimpleDBListAdapter<SearchRecord>(context, R.layout.personal_recycler_search_news_history_item) {

    private var showDeleteIcon: Boolean = false

    override fun convert(holder: SuperViewHolder, viewType: Int, item: SearchRecord, position: Int) {
        super.convert(holder, viewType, item, position)
        holder.findViewById<View>(R.id.v_split).visibility = if (position % 2 == 1) View.GONE else View.VISIBLE
        val icDelete = holder.findViewById<ImageView>(R.id.iv_delete)
        icDelete.visibility = if (showDeleteIcon) View.VISIBLE else View.GONE
        icDelete.setOnClickListener {
            viewModel.deleteHistoryByPosition(item)
        }
        holder.itemView.setOnClickListener {
            viewModel.searchByHistory(item)
        }
    }

    fun showDeleteIcon(show: Boolean) {
        showDeleteIcon = show
        notifyDataSetChanged()
    }
}