package com.bxkj.personal.ui.activity.hotdiscuss

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.support.comment.repository.CommentRepository
import com.bxkj.jrzp.support.comment.ui.CommentVMHandler
import com.bxkj.jrzp.user.LikeType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.personal.data.DiscussData
import com.bxkj.personal.data.source.NewsRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/14
 */
class HotDiscussViewModel @Inject constructor(
    private val _newsRepo: NewsRepo,
    private val _userRepo: OpenUserRepository,
    _commentRepo: CommentRepository
) : BaseViewModel() {

    val discussInfo = MutableLiveData<DiscussData>()
    val toAuthorHomePageCommand = MutableLiveData<VMEvent<Int>>()

    private val _commentVMHandler = CommentVMHandler(this, _commentRepo)

    val commentCount = MediatorLiveData<Int>().apply {
        addSource(_commentVMHandler.commentCount) {
            discussInfo.value?.updateCommentCount(it)
            value = it
        }
    }

    private var _discussId: Int = 0

    fun start(discussID: Int) {
        _discussId = discussID
        _commentVMHandler.setInitParams(discussID, LikeType.LIKE_QA, 16)
        viewModelScope.launch {
            _newsRepo.getHotDiscussDetails(discussID)
                .handleResult({
                    it?.let {
                        discussInfo.value = it[0]
                        _commentVMHandler.setInitCommentCount(it[0].plCount.getOrDefault())
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }

    fun toAuthorHomePage() {
        discussInfo.value?.let {
            toAuthorHomePageCommand.value = VMEvent(it.userID)
        }
    }

    fun addOrRemoveCollection() {}

    fun addOrRemoveLike() {
        afterLogin {
            viewModelScope.launch {
                showLoading()
                _userRepo.like(
                    getSelfUserID(),
                    LikeType.LIKE_QA,
                    _discussId,
                    AppConstants.USER_TYPE_PERSONAL
                ).handleResult({
                    switchNewsLikeState(true)
                }, {
                    if (it.errCode == 10002) {
                        switchNewsLikeState(false)
                    } else {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
            }
        }
    }

    private fun switchNewsLikeState(like: Boolean) {
        discussInfo.value?.updateLikeState(like)
    }

    fun loadComment(index: Int) {
        viewModelScope.launch {
            _commentVMHandler.handleLoadCommentResult(
                _newsRepo.getHotDiscussComments(
                    _discussId,
                    index,
                    16
                )
            )
        }
    }

    fun getCommentVMHandler(): CommentVMHandler {
        return _commentVMHandler
    }
}