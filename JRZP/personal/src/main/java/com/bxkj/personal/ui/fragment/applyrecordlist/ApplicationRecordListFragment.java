package com.bxkj.personal.ui.fragment.applyrecordlist;

import android.os.Bundle;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.View;
import android.widget.TextView;

import com.bxkj.common.base.BaseListFragment;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.recyclerutil.RecycleViewDivider;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.pagestatuslayout.EmptyConfig;
import com.bxkj.personal.R;
import com.bxkj.personal.data.ResumeDeliveryRecordBean;
import com.bxkj.personal.ui.activity.applicationrecord.ApplicationRecordAdapter;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.fragment.applicationrecordlist
 * @Description:
 * @TODO: TODO
 * @date 2018/9/21
 */
public class ApplicationRecordListFragment extends BaseListFragment
  implements ApplicationRecordContract.View {

  private static final String PAGE_INDEX = "page_state";

  @Inject
  ApplicationRecordPresenter mApplicationRecordPresenter;

  private ConstraintLayout clBottomDeleteBar;
  private TextView tvSelected;
  private TextView tvDelete;

  private ApplicationRecordAdapter mApplicationRecordAdapter;
  //查询分类：0、投递成功1、已查看3、邀请面试6、面试通过2、淘汰
  private String mPageType;

  public static ApplicationRecordListFragment newInstance(int pageIndex) {
    Bundle args = new Bundle();
    args.putInt(PAGE_INDEX, pageIndex);
    ApplicationRecordListFragment fragment = new ApplicationRecordListFragment();
    fragment.setArguments(args);
    return fragment;
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_fragment_application_record_list;
  }

  @Override
  public void initPresenter() {
    mApplicationRecordPresenter.attachView(this);
  }

  @Override
  public void setUserVisibleHint(boolean isVisibleToUser) {
    super.setUserVisibleHint(isVisibleToUser);
    if (!isVisibleToUser) {
      hiddenCheckBoxAndBottomBar();
    }
  }

  @Override
  public void initPage() {
    super.initPage();
    bindView(getView());

    getPageType();
    mApplicationRecordAdapter = new ApplicationRecordAdapter(getContext(), null,
      R.layout.personal_recycler_submit_record_item);
    mApplicationRecordAdapter.setOnItemClickListener((view, position) -> {
      ResumeDeliveryRecordBean submitRecordItem = mApplicationRecordAdapter.getData().get(position);
      if (view.getId() == R.id.cb_select_this) {
        tvDelete.setEnabled(!CheckUtils.isNullOrEmpty(mApplicationRecordAdapter.getSelectedList()));
        tvSelected.setText(getString(R.string.personal_selected,
          mApplicationRecordAdapter.getSelectedList().size()));
      } else {
        if (CheckUtils.isNullOrEmpty(submitRecordItem.getConame())) {
          showToast(getString(R.string.application_record_position_deleted));
        } else {
          startActivity(
            JobDetailsActivityV2.Companion.newIntent(getActivity(), submitRecordItem.getRelid()));
        }
      }
    });
    getRecyclerView().setLayoutManager(new LinearLayoutManager(getContext()));
    getRecyclerView().addItemDecoration(
      new RecycleViewDivider(getContext(), LinearLayoutManager.HORIZONTAL,
        DensityUtils.dp2px(getParentActivity(), 8), getMColor(R.color.common_f4f4f4), true));
    getRecyclerView().setAdapter(mApplicationRecordAdapter);

    tvSelected.setText(getString(R.string.personal_selected, 0));
  }

  private void hiddenCheckBoxAndBottomBar() {
    if (mApplicationRecordAdapter != null && mApplicationRecordAdapter.getCheckBoxShow()) {
      mApplicationRecordAdapter.showCheckBox(false);
      mApplicationRecordAdapter.clearSelectedList();
      clBottomDeleteBar.setVisibility(View.GONE);
    }
  }

  /**
   * 檢查page類型
   */
  private void getPageType() {
    int pageIndex = getArguments() != null ? getArguments().getInt(PAGE_INDEX) : 0;
    switch (pageIndex) {
      case 0:
        mPageType = "-1";
        break;
      case 1:
        mPageType = "0";
        break;
      case 2:
        mPageType = "1";
        break;
      case 3:
        mPageType = "3,4,5,6";
        break;
      case 4:
        mPageType = "2";
        break;
      default:
        break;
    }
  }

  private void onViewClicked() {
    new ActionDialog.Builder()
      .setTitle(getString(R.string.common_confirm_delete_information))
      .setContent(getString(R.string.common_unrecoverable_information_after_deletion))
      .setOnConfirmClickListener(
        (dialog) -> mApplicationRecordPresenter.batchDeletionSubmitRecord(getUserId(),
          mApplicationRecordAdapter.getSelectedList()))
      .build()
      .show(getChildFragmentManager(), ActionDialog.TAG);
  }

  @Override
  protected void fetchData() {
    if (getRefreshLayoutManager().getCurrentPage() == 1) {
      mApplicationRecordAdapter.clearSelectedList();
      tvSelected.setText(getString(R.string.personal_selected, 0));
    }
    mApplicationRecordPresenter.getUserApplyRecord(getUserId(), 1
      , 0, mPageType, getRefreshLayoutManager().getCurrentPage(),
      CommonApiConstants.DEFAULT_PAGE_SIZE);
  }

  /**
   * 多选checkbox开关
   */
  public void switchCheckBoxShow() {
    mApplicationRecordAdapter.showCheckBox(!mApplicationRecordAdapter.getCheckBoxShow());
    clBottomDeleteBar.setVisibility(
      mApplicationRecordAdapter.getCheckBoxShow() ? View.VISIBLE : View.GONE);
  }

  /**
   * 設置頁面type
   */
  public void setPageType(String pageType) {
    mPageType = pageType;
    getRefreshLayoutManager().refreshPage();
  }

  public boolean getCheckBoxShow() {
    return mApplicationRecordAdapter != null && mApplicationRecordAdapter.getCheckBoxShow();
  }

  @Override
  public void onGetApplyRecordSuccess(List<ResumeDeliveryRecordBean> resumeDeliveryRecordBeanList) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    if (getRefreshLayoutManager().currentFirstPage()) {
      mApplicationRecordAdapter.reset(resumeDeliveryRecordBeanList);
    } else {
      mApplicationRecordAdapter.addAll(resumeDeliveryRecordBeanList);
    }
  }

  @Override
  public void batchDeletionSubmitRecordSuccess(List<ResumeDeliveryRecordBean> selectedList) {
    mApplicationRecordAdapter.removeAll(selectedList);
    if (mApplicationRecordAdapter.getData().size() == 0) {
      getPageStatusLayout().show(new EmptyConfig());
    }
    mApplicationRecordAdapter.clearSelectedList();
    tvSelected.setText(getString(R.string.personal_selected, 0));
    tvDelete.setEnabled(false);
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override public void showLoading(String text) {

  }

  @Override
  public void onDestroy() {
    super.onDestroy();
    mApplicationRecordPresenter.detachView();
  }

  private void bindView(View bindSource) {
    clBottomDeleteBar = bindSource.findViewById(R.id.cl_bottom_delete_bar);
    tvSelected = bindSource.findViewById(R.id.tv_selected);
    tvDelete = bindSource.findViewById(R.id.tv_delete);
    bindSource.findViewById(R.id.tv_delete).setOnClickListener(v -> onViewClicked());
  }
}
