package com.bxkj.personal.ui.activity.answerdetails

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/22
 * @version: V1.0
 */
class AnswerDetailsNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/answerdetails"

    const val EXTRA_QUESTION_CONTENT = "QUESTION"
    const val EXTRA_ANSWER_ID = "ANSWER_ID"
    const val EXTRA_JUMP_TO_COMMENT = "JUMP_TO_COMMENT"

    fun navigate(questionContent: String, answerId: Int, jumpToComment: Boolean=false): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(EXTRA_QUESTION_CONTENT, questionContent)
        .withInt(EXTRA_ANSWER_ID, answerId)
        .withBoolean(EXTRA_JUMP_TO_COMMENT, jumpToComment)
    }
  }
}