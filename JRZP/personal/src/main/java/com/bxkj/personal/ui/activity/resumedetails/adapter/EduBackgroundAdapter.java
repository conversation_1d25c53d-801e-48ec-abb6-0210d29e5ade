package com.bxkj.personal.ui.activity.resumedetails.adapter;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;
import com.bxkj.personal.data.EduBackgroundItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description:
 * @TODO: TODO
 * @date 2018/5/9
 */

public class EduBackgroundAdapter extends SuperAdapter<EduBackgroundItemData> {
    public EduBackgroundAdapter(Context context, List<EduBackgroundItemData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override protected void convert(SuperViewHolder holder, int viewType, EduBackgroundItemData item,
        int position) {
        holder.setText(R.id.tv_edu_time, mContext.getString(R.string.resume_details_time_between, item.getDate1(), item.getDate2()));
        holder.setText(R.id.tv_edu_about, item.getSchool() + "|" + item.getProName2() + "|" + item.getQuaName());
    }
}
