package com.bxkj.personal.weight.selectaddressdialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.contract.GetAreaListContract;
import com.bxkj.common.mvp.persenter.GetAreaListPresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.hjq.toast.Toaster;
import dagger.android.AndroidInjector;
import dagger.android.DispatchingAndroidInjector;
import dagger.android.HasAndroidInjector;
import dagger.android.support.AndroidSupportInjection;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description: 选择地址dialog
 * @TODO: TODO
 * @date 2018/4/16
 */

public class SelectCityDialog extends DialogFragment implements HasAndroidInjector, GetAreaListContract.View {

    @Inject
    DispatchingAndroidInjector<Object> androidInjector;
    @Inject
    GetAreaListPresenter mGetAreaListPresenter;

    private View rootView;
    private ImageView ivBack;
    private RecyclerView recyclerAddress;

    private ProvinceListAdapter mProvinceListAdapter;

    @Override
    public void onAttach(Context context) {
        AndroidSupportInjection.inject(this);
        super.onAttach(context);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.BaseDialogFragmentStyle);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.personal_dialog_select_address, container, false);
        mGetAreaListPresenter.attachView(this);
        initView();
        return rootView;
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null) {
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            dialog.getWindow().setLayout((int) (dm.widthPixels * 0.75), ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.getWindow().setGravity(Gravity.END);
            dialog.getWindow().setWindowAnimations(com.bxkj.personal.R.style.RightPopupAnim);
        }
    }

    private void initView() {
        recyclerAddress = rootView.findViewById(R.id.recycler_address);
        ivBack = rootView.findViewById(R.id.iv_left);
        ivBack.setImageResource(R.drawable.common_ic_close);
        ivBack.setOnClickListener(view -> dismiss());

        mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_PROVINCE_TYPE, 0);
    }

    @Override
    public AndroidInjector<Object> androidInjector() {
        return androidInjector;
    }

    @Override
    public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {
        if (type == CommonApiConstants.GET_PROVINCE_TYPE) {
            mProvinceListAdapter = new ProvinceListAdapter(getContext(), optionsDataList, R.layout.personal_recycler_select_address_item);
            mProvinceListAdapter.setOnItemClickListener((view, position) ->
                    mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_CITY_TYPE, optionsDataList.get(position).getId())
            );
            recyclerAddress.setLayoutManager(new LinearLayoutManager(getContext()));
            recyclerAddress.setAdapter(mProvinceListAdapter);
        } else {
            if (mProvinceListAdapter == null || CheckUtils.isNullOrEmpty(mProvinceListAdapter.getData()))
                return;
            AreaOptionsData selectedProvince = mProvinceListAdapter.getData().get(mProvinceListAdapter.getSelectPosition());
            selectedProvince.setCityList(optionsDataList);
            mProvinceListAdapter.notifyDataSetChanged();
            if (mProvinceListAdapter.getSelectPosition() != ProvinceListAdapter.NO_POSITION) {
                ((LinearLayoutManager) recyclerAddress.getLayoutManager()).scrollToPositionWithOffset(mProvinceListAdapter.getSelectPosition(), 0);
            }
            mProvinceListAdapter.setOnChildClickListener(position -> {
                if (OnSelectedListener != null) {
                    OnSelectedListener.onSelected(mProvinceListAdapter.getSelectPosition(), position);
                    dismiss();
                }
            });
        }
    }

    @Override
    public void onError(String errMsg) {
        Toaster.show(errMsg);
    }

    @Override
    public void showLoading() {

    }

    @Override public void showLoading(String text) {

    }

    @Override
    public void hiddenLoading() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mGetAreaListPresenter.detachView();
    }

    private OnSelectedListener OnSelectedListener;

    public void setOnSelectedListener(OnSelectedListener onSelectedListener) {
        OnSelectedListener = onSelectedListener;
    }

    public interface OnSelectedListener {
        void onSelected(int provincePosition, int cityPosition);
    }

    public List<AreaOptionsData> getProviceList() {
        return mProvinceListAdapter.getData();
    }

    public List<AreaOptionsData> getCityList() {
        return mProvinceListAdapter.getData().get(mProvinceListAdapter.getSelectPosition()).getCityList();
    }
}
