package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.api.PersonalApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.api.new
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/4/8
 * @version V1.0
 */
class FeedbackRepo @Inject constructor(personalApi: PersonalApi) : BaseRepo() {

    private val mPersonalApi = personalApi

    public fun submitFeedback(type: Int, content: String, mobile: String, email: String, resultCallBack: ResultCallBack) {
        mPersonalApi.submitFeedback(type, content, email, mobile)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        resultCallBack.onSuccess()
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        resultCallBack.onError(respondThrowable)
                    }
                })
    }
}