package com.bxkj.personal.ui.activity.campusrecruitdetails

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.source.JobRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class HotCampusRecruitListViewModel @Inject constructor(
    private val _jobRepo: JobRepo
) : BaseViewModel() {

    val hotCampusRecruitListViewModel = RefreshListViewModel()

    init {
        hotCampusRecruitListViewModel.refreshLayoutViewModel.enableLoadMore(false)
        hotCampusRecruitListViewModel.setOnLoadDataListener {
            viewModelScope.launch {
                _jobRepo.getHotCampusRecruitList(20)
                    .handleResult({
                        hotCampusRecruitListViewModel.autoAddAll(it?.dataList)
                    }, {
                        if (it.isNoDataError) {
                            hotCampusRecruitListViewModel.noMoreData()
                        } else {
                            hotCampusRecruitListViewModel.loadError()
                        }
                    })
            }
        }
    }

    fun start() {
        hotCampusRecruitListViewModel.refresh()
    }
}