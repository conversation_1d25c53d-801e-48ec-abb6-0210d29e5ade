package com.bxkj.personal.ui.activity.msgnotification

import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.PersonalApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.msgnotification
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/22
 * @version V1.0
 */
class MsgNotificationPresenter @Inject constructor(api: PersonalApi) : MsgNotificationContract.Presenter() {

    var mPersonalApi: PersonalApi = api

    override fun getMsgNotificationList(userId: Int, pageIndex: Int, pageSize: Int) {
        mPersonalApi.getMsgNotificationList(userId, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        mView.getMsgNotificationListSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode != 30002) {
                            mView.onRequestError(respondThrowable)
                        } else {
                            mView.onResultNoData()
                        }
                    }
                })
    }
}