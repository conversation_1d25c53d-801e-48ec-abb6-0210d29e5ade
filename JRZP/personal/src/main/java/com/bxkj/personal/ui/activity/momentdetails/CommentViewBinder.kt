package com.bxkj.personal.ui.activity.momentdetails

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.CheckUtils
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.R.layout

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.momentdetails
 * @Description:
 * <AUTHOR>
 * @date 2019/12/4
 * @version V1.0
 */
class CommentViewBinder : DefaultViewBinder<CommentItemData>(
    R.layout.personal_recycler_moment_comment_item,
    BR.data,
    true
) {

  override fun onBindViewHolder(holder: SuperViewHolder, item: CommentItemData, position: Int) {
    super.onBindViewHolder(holder, item, position)
    val recyclerReplyList = holder.findViewById<RecyclerView>(R.id.recycler_child_reply)
    recyclerReplyList.layoutManager = LinearLayoutManager(holder.itemView.context)
    recyclerReplyList.adapter = SimpleDBListAdapter<CommentItemData>(
      holder.itemView.context,
      layout.personal_recycler_comment_reply_item
    ).apply {
      if (!CheckUtils.isNullOrEmpty(item.nesPinglun2)) {
        addAll(item.nesPinglun2)
      }
    }
  }
}