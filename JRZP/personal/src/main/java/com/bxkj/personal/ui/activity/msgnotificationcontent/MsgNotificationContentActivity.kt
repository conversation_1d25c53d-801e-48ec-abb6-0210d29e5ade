package com.bxkj.personal.ui.activity.msgnotificationcontent

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.base.BaseListActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.TitleBarManager
import com.bxkj.personal.R
import com.bxkj.personal.data.FeedbackMsgItemData
import com.bxkj.personal.data.FeedbackMsgContentItemData
import com.bxkj.personal.mvp.contract.UpdateMessageStatusContract
import com.bxkj.personal.mvp.presenter.UpdateMessageStatusPresenter
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.msgnotificationcontent
 * @Description: 消息通知内容
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/22
 * @version V1.0
 */
class MsgNotificationContentActivity : BaseListActivity(), MsgNotificationContentContract.View,
  UpdateMessageStatusContract.View {

  companion object {
    private const val NOTIFICATION_ITEM_INFO: String = "notification_item_info"

    fun newIntent(content: Context, feedbackMsgItemData: FeedbackMsgItemData): Intent {
      val intent = Intent(content, MsgNotificationContentActivity::class.java)
      val bundle = Bundle()
      bundle.putParcelable(NOTIFICATION_ITEM_INFO, feedbackMsgItemData)
      intent.putExtras(bundle)
      return intent
    }
  }

  @Inject
  lateinit var mMsgNotificationContentPresenter: MsgNotificationContentPresenter

  @Inject
  lateinit var mUpdateMessageStatusPresenter: UpdateMessageStatusPresenter

  private lateinit var mMsgNotificationContentListAdapter: MsgNotificationContentListAdapter
  private lateinit var mMsgData: FeedbackMsgItemData

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>): MutableList<BasePresenter<BaseView>> {
    presenters.add(CheckUtils.cast(mMsgNotificationContentPresenter))
    presenters.add(CheckUtils.cast(mUpdateMessageStatusPresenter))
    return presenters
  }

  override fun getLayoutId(): Int = R.layout.personal_activity_only_list

  override fun initIntent(intent: Intent?) {
    mMsgData = intent!!.getParcelableExtra(NOTIFICATION_ITEM_INFO)!!
  }

  override fun initTitleBar(titleBarManager: TitleBarManager?) {
//        titleBarManager!!.setTitle(mMsgData.company.name)
  }

  override fun initPage() {
    super.initPage()
    findViewById<LinearLayout>(R.id.ll_content).setBackgroundColor(getMColor(R.color.common_f4f4f4))
    mMsgNotificationContentListAdapter = MsgNotificationContentListAdapter(
      this,
      null,
      R.layout.personal_recycler_msg_notification_content_item
    )
    recyclerView.adapter = mMsgNotificationContentListAdapter
    recyclerView.layoutManager = LinearLayoutManager(this)

    mUpdateMessageStatusPresenter.updateMessageStatus(mUserID, mMsgData.cuid)
    refreshLayoutManager.refreshPage()
  }

  override fun loadData() {
    mMsgNotificationContentPresenter.getMsgNotificationContentListByCompany(
      mUserID,
      mMsgData.cuid,
      refreshLayoutManager.currentPage,
      CommonApiConstants.DEFAULT_PAGE_SIZE
    )
  }

  override fun getMsgContentSuccess(msgContentList: MutableList<FeedbackMsgContentItemData>) {
    pageStatusLayout.hidden()
    refreshLayoutManager.finishRefreshOrLoadMore()
    if (refreshLayoutManager.currentFirstPage()) {
      mMsgNotificationContentListAdapter.reset(msgContentList)
    } else {
      mMsgNotificationContentListAdapter.addAll(msgContentList)
    }
  }

  override fun updateMsgStatusSuccess() {
    setResult(Activity.RESULT_OK)
  }
}