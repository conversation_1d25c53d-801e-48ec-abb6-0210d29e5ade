package com.bxkj.personal.data.source.datasource

import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.JobDetailsData
import com.bxkj.personal.ui.activity.searchjobresult.FilterJobParams

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.data.source.datasource
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/7/25
 * @version V1.0
 */
interface JobDataSource {
    fun getJobList(filterParameters: FilterJobParams, pageIndex: Int, pageSize: Int, callBack: ResultListCallBack<List<JobData>>)

    fun getJobDetailsInfo(jobId: Int, userId: Int, callBack: ResultDataCallBack<JobDetailsData>)

    fun checkJobsCollected(jobId: Int, userId: Int, callback: ResultCallBack)

    fun collectionJob(jobId: Int, companyId: Int, userId: Int, callback: ResultCallBack)

    fun cancelCollectionJob(jobId: Int, userId: Int, callback: ResultCallBack)
}