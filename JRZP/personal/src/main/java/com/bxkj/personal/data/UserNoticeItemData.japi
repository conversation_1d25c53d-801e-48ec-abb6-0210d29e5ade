package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;
import com.bxkj.personal.api.BR;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description: 用户发布的公告
 * @date 2020/1/9
 */
public class UserNoticeItemData extends BaseObservable {

  /**
   * id : 96184
   * title : 2019年深圳市罗湖高级中学招聘非在编教师2名公告
   * Medialist : [{"type":1,"url":"http://img.jrzp.com/images_server/shejiao/6223909_111506547191_2.jpg"}]
   * createTime : 2019.12.09 09:56:20
   * count : 104
   * commentsCount : 0
   * likesCount : 0
   */

  private int id;
  private String title;
  private String createTime;
  private int count;
  private int commentsCount;
  private int likesCount;
  private List<MedialistBean> Medialist;
  private String dwLogo;
  private String dwName;
  private boolean isLike;
  private String pic;

  public String getRealLogoUrl() {
    return CheckUtils.fixImgUrl(dwLogo);
  }

  public boolean hasPic() {
    return !CheckUtils.isNullOrEmpty(pic);
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getCreateTime() {
    return TimeUtils.getNewTimeDiff(createTime);
  }

  public String getRealCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCommentsCount() {
    return commentsCount;
  }

  public void setCommentsCount(int commentsCount) {
    this.commentsCount = commentsCount;
  }

  public String getPic() {
    return pic;
  }

  public void setPic(String pic) {
    this.pic = pic;
  }

  @Bindable
  public int getLikesCount() {
    return likesCount;
  }

  public void setLikesCount(int likesCount) {
    this.likesCount = likesCount;
    notifyPropertyChanged(BR.likesCount);
  }

  public List<MedialistBean> getMedialist() {
    return Medialist;
  }

  public void setMedialist(List<MedialistBean> Medialist) {
    this.Medialist = Medialist;
  }

  public static class MedialistBean {
    /**
     * type : 1
     * url : http://img.jrzp.com/images_server/shejiao/6223909_111506547191_2.jpg
     */

    private int type;
    private String url;

    public int getType() {
      return type;
    }

    public void setType(int type) {
      this.type = type;
    }

    public String getUrl() {
      return url;
    }

    public void setUrl(String url) {
      this.url = url;
    }
  }

  public String getDwLogo() {
    return dwLogo;
  }

  public void setDwLogo(String dwLogo) {
    this.dwLogo = dwLogo;
  }

  public String getDwName() {
    return dwName;
  }

  public void setDwName(String dwName) {
    this.dwName = dwName;
  }

  @Bindable
  public boolean isLike() {
    return isLike;
  }

  public void setLike(boolean like) {
    isLike = like;
    notifyPropertyChanged(BR.like);
  }

  public void addLike() {
    setLike(true);
    setLikesCount(likesCount + 1);
  }

  public void removeLike() {
    setLike(false);
    setLikesCount(likesCount - 1);
  }
}
