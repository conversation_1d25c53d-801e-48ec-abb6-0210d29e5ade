package com.bxkj.personal.ui.activity.findjobbymap

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.cachedIn
import com.baidu.mapapi.model.LatLng
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.jrzp.support.feature.api.FeatureRepository
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.UserResumeData
import com.bxkj.personal.data.source.JobRepo
import com.bxkj.personal.data.source.PickerOptionsRepo
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/10/8
 **/
class FindJobOnMapViewModel @Inject constructor(
  private val jobRepo: JobRepo,
  private val myResumeRepo: MyResumeRepo,
  private val featureRepo: FeatureRepository,
  private val pickerOptionsRepo: PickerOptionsRepo
) : BaseViewModel() {

  val jobCountText = MutableLiveData<String>().apply { value = "职位数量未知" }

  val toCreateResumeCommand = MutableLiveData<VMEvent<Unit>>()

  val jobFirstTypeList = MutableLiveData<List<JobTypeData>>()

  val jobSecondTypeList = MutableLiveData<List<JobTypeData>>()

  val salaryRangeOptions = MutableLiveData<List<FilterOptionData>>()

  val workExpOptions = MutableLiveData<List<FilterOptionData>>()

  val jobListFlow = Pager(PagingConfig(16), pagingSourceFactory = {
    object : PagingSource<Int, JobData>() {
      override fun getRefreshKey(state: PagingState<Int, JobData>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
          val anchorPage = state.closestPageToPosition(anchorPosition)
          anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
      }

      override suspend fun load(params: LoadParams<Int>): LoadResult<Int, JobData> {
        val pageIndex = params.key.getOrDefault(1)
        var loadResult: LoadResult<Int, JobData> =
          LoadResult.Invalid()
        findJobOnMapParams.pageIndex = pageIndex
        jobRepo.getJobListByLocation(findJobOnMapParams)
          .handleResult({ result ->
            jobCountText.value = "附近有${result?.count}个职位"
            result?.dataList?.let { list ->
              loadResult = LoadResult.Page(
                list.map { item ->
                  if (item.hasLastUpdateTime()) {
                    item.setEdate1(item.lastUpdateTime.split(" ")[0])
                  }
                  return@map item
                },
                if (pageIndex == 1) null else pageIndex - 1,
                if (list.isEmpty()) null else pageIndex + 1
              )
            } ?: let { loadResult = LoadResult.Error(RespondThrowable.getNoDataError()) }
          }, {
            loadResult = LoadResult.Error(it)
          })
        return loadResult
      }
    }
  }).flow.cachedIn(viewModelScope)

  private val findJobOnMapParams = FindJobOnMapParams()

  init {
    loadJobFirstTypeList()
    loadSalaryRangeOptions()
    loadWorkExpOptions()
  }

  fun updateLocation(target: LatLng?, llLatlng: LatLng, trLatlng: LatLng) {
    findJobOnMapParams.apply {
      target?.let {
        centerLng = it.longitude
        centerLat = it.latitude
      }
      llLng = llLatlng.longitude
      llLat = llLatlng.latitude
      trLng = trLatlng.longitude
      trLat = trLatlng.latitude
    }
  }

  fun sendResumePreCheck(job: JobData) {
    afterLogin {
      viewModelScope.launch {
        showLoading()
        myResumeRepo.getUserResumeList(getSelfUserID())
          .handleResult({ resultList ->
            if (resultList.isNullOrEmpty()) {
              toCreateResumeCommand.value = VMEvent(Unit)
            } else {
              val userResume = resultList[0]
              sendResume(job, userResume)
            }
          }, { err ->
            if (err.isNoDataError) {
              toCreateResumeCommand.value = VMEvent(Unit)
            } else {
              showToast(err.errMsg)
            }
          }, {
            hideLoading()
          })
      }
    }
  }

  private fun sendResume(job: JobData, userResume: UserResumeData) {
    viewModelScope.launch {
      showLoading()
      jobRepo.sendResume(getSelfUserID(), job.uid, job.id, userResume.id)
        .handleResult({
          job.markJobApplied()
          showToast("申请成功")
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun loadJobFirstTypeList() {
    viewModelScope.launch {
      featureRepo.getJobType(1, 0)
        .handleResult({
          it?.let {
            jobFirstTypeList.value = it
            loadJobSecondTypeList(it[0].id)
          }
        }, {
          showToast("职位分类加载失败")
        })
    }
  }

  fun setJobListFirstType(id: Int) {
    findJobOnMapParams.jobFirstClassId = id
    loadJobSecondTypeList(id)
  }

  fun setJobListSecondType(id: Int) {
    findJobOnMapParams.jobSecondClassId = id
  }

  fun setupSalaryRangeId(salaryRangeId: Int?) {
    salaryRangeId?.let {
      findJobOnMapParams.salaryId = it
    }
  }

  fun setupWorkExpId(id: Int) {
    findJobOnMapParams.workExpId = id
  }

  fun setupWorkNatureId(id: Int) {
    findJobOnMapParams.workNatureId = id
  }

  fun setupSearchKeyword(keyword: String) {
    findJobOnMapParams.name = keyword
  }

  private fun loadJobSecondTypeList(firstTypeId: Int) {
    viewModelScope.launch {
      featureRepo.getJobType(2, firstTypeId)
        .handleResult({
          jobSecondTypeList.value = it
        }, {
          showToast("职位分类加载失败")
        })
    }
  }

  private fun loadSalaryRangeOptions() {
    viewModelScope.launch {
      pickerOptionsRepo.getSalaryRange()
        .handleResult({
          it?.let {
            salaryRangeOptions.value = ArrayList(it).apply {
              add(0, FilterOptionData(0, "不限"))
            }
          }
        }, {
          showToast("薪资范围加载失败")
        })
    }
  }

  private fun loadWorkExpOptions() {
    viewModelScope.launch {
      pickerOptionsRepo.getWorkExpOptions()
        .handleResult({
          it?.let {
            workExpOptions.value = ArrayList(it).apply {
              add(0, FilterOptionData(0, "不限"))
            }
          }
        }, {
          showToast("工作经验加载失败")
        })
    }
  }
}