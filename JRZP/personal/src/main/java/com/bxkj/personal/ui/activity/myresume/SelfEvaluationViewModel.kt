package com.bxkj.personal.ui.activity.myresume

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/9
 */
class SelfEvaluationViewModel @Inject constructor(
  private val myResumeRepo: MyResumeRepo
) : BaseViewModel() {

  val content = MutableLiveData<String>()
  val tagList = MutableLiveData<List<String>>().apply {
    value =
      "阳光有活力,工作态度认真,踏实肯干,责任心强,性格开朗,服务意识好,独立工作,思想成熟,创新能力强,团队精神好,注重工作效率,个性独立,做事果断有主见,时间观念强,语言表达好,逻辑思维强,能接受新鲜事物,富有开拓意识,注意细节,爱运动,喜欢打游戏,业绩突出,技能好".split(
        ","
      )
  }

  val saveSuccessCommand = MutableLiveData<VMEvent<String>>()

  private var _resumeId: Int = 0

  fun start(extraResumeId: Int, extraContent: String) {
    _resumeId = extraResumeId
    content.value = extraContent
  }

  fun appendTag(tag: String) {
    content.value?.let {
      if (it.isBlank() || it.endsWith(",") || it.endsWith("，") || it.endsWith(".")) {
        content.value = it.plus(tag)
      } else {
        content.value = it.plus("，${tag}")
      }
    }
  }

  fun save() {
    content.value?.let { selfEvaluation ->
      if (selfEvaluation.isNotBlank()) {
        viewModelScope.launch {
          showLoading()
          myResumeRepo.updateSelfEvaluation(getSelfUserID(), _resumeId, selfEvaluation)
            .handleResult({
              saveSuccessCommand.value = VMEvent(selfEvaluation)
            }, {
              showToast(it.errMsg)
            }, {
              hideLoading()
            })
        }
      } else {
        showToast("请填写后保存")
      }
    }
  }
}