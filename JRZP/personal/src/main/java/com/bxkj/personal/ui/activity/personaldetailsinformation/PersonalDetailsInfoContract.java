package com.bxkj.personal.ui.activity.personaldetailsinformation;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.common.data.PickerOptionsData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.personaldetailsinformation
 * @Description: PersonalDetailsInfo
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface PersonalDetailsInfoContract {
    interface View extends BaseView {
        void getNationListSuccess(List<PickerOptionsData> nationListData);

        void updateSuccess();

        void getInfoSuccess(PersonalDetailsInfoData personalDetailsInfoData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getNationList();

        public abstract void updateInfo(int userId, PersonalDetailsInfoData personalDetailsInfoData);

        public abstract void getInfo(int userId);
    }
}
