package com.bxkj.personal.data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 专业技能
 * @TODO: TODO
 * @date 2018/5/11
 */

public class ProfessionalSkillItemData {
    private int id;
    private int resumeId;
    private String name;
    private String degree;
    private String remark;

    private List<ProfessionalSkillItemData> professionalSkillItemDataList;

    public ProfessionalSkillItemData() {
    }

    public ProfessionalSkillItemData(int resumeId, List<ProfessionalSkillItemData> professionalSkillItemDataList) {
        this.resumeId = resumeId;
        this.professionalSkillItemDataList = professionalSkillItemDataList;
    }

    public List<ProfessionalSkillItemData> getProfessionalSkillItemDataList() {
        return professionalSkillItemDataList;
    }

    public void setProfessionalSkillItemDataList(List<ProfessionalSkillItemData> professionalSkillItemDataList) {
        this.professionalSkillItemDataList = professionalSkillItemDataList;
    }

    public int getResumeId() {
        return resumeId;
    }

    public void setResumeId(int resumeId) {
        this.resumeId = resumeId;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ProfessionalSkillItemData{" +
            "id=" + id +
            ", resumeId=" + resumeId +
            ", name='" + name + '\'' +
            ", degree='" + degree + '\'' +
            ", remark='" + remark + '\'' +
            ", professionalSkillItemDataList=" + professionalSkillItemDataList +
            '}';
    }
}
