package com.bxkj.personal.ui.activity.privacysetting

import androidx.databinding.ObservableBoolean
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/3
 * @version: V1.0
 */
class PrivacySettingViewModel @Inject constructor() : BaseViewModel() {

  val isGeek = ObservableBoolean()

  val isLogin = ObservableBoolean(false)

  fun start() {
    isGeek.set(UserUtils.logged() && UserUtils.isPersonalRole())
   }

  fun checkLoginState() {
    isLogin.set(UserUtils.logged())
  }
}