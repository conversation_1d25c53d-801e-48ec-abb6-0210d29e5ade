package com.bxkj.personal.ui.activity.resumeopenstatesetting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CActivityResumeOpenStateSettingBinding

/**
 * Description: 简历公开状态
 * Author:Sanjin
 * Date:2024/3/29
 **/
class ResumeOpenStateSettingActivity :
  BaseDBActivity<CActivityResumeOpenStateSettingBinding, ResumeOpenStateSettingViewModel>() {

  private var resumeOpenStateListAdapter: SimpleDiffListAdapter<ResumeOpenState>? = null

  override fun getViewModelClass(): Class<ResumeOpenStateSettingViewModel> =
    ResumeOpenStateSettingViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_activity_resume_open_state_setting

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupResumeOpenStateListAdapter()

    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.updateSuccessEvent.observe(this, EventObserver {
      resumeOpenStateListAdapter?.notifyDataSetChanged()
    })
  }

  private fun setupResumeOpenStateListAdapter() {
    resumeOpenStateListAdapter = object : SimpleDiffListAdapter<ResumeOpenState>(
      R.layout.c_recycler_resume_open_state_item,
      ResumeOpenState.DiffCallback()
    ) {
      override fun bind(holder: SuperViewHolder, item: ResumeOpenState, position: Int) {
        super.bind(holder, item, position)
        holder.findViewById<ImageView>(R.id.iv_selected).visibility =
          if (viewModel.isSelected(position)) View.VISIBLE else View.GONE
      }
    }.apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          viewModel.updateSelectedIndex(position)
        }
      })
    }

    viewBinding.recyclerResumeOpenState.apply {
      layoutManager = LinearLayoutManager(this@ResumeOpenStateSettingActivity)
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4))
          .margin(dip(12)).build()
      )
      adapter = resumeOpenStateListAdapter
    }
  }

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, ResumeOpenStateSettingActivity::class.java)
    }
  }
}