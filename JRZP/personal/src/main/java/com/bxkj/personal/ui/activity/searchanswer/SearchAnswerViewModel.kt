package com.bxkj.personal.ui.activity.searchanswer

import android.app.Application
import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.InviteUserItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.QuestionRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.searchanswer
 * @Description: 搜索答主
 * <AUTHOR>
 * @date 2020/2/26
 * @version V1.0
 */
class SearchAnswerViewModel @Inject constructor(application: Application
                                                , private val mAccountRepo: AccountRepo
                                                , private val mQuestionRepo: QuestionRepo) : BaseViewModel() {

    val searchResultListViewModel = RefreshListViewModel()
    val searchContent = MutableLiveData<String>()
    val showSearchResult = ObservableBoolean(false)
    val startSearchEvent = LiveEvent<Void>()

    private var _searchContent: String = CommonApiConstants.NO_TEXT
    private var mQuestionId: Int = CommonApiConstants.NO_ID

    init {
        setupSearchResultListViewModel()
    }

    fun start(intent: Intent) {
        mQuestionId = intent.getIntExtra(SearchAnswerActivity.EXTRA_QUESTION_ID, CommonApiConstants.NO_ID)
    }

    private fun setupSearchResultListViewModel() {
        searchResultListViewModel.refreshLayoutViewModel.enableRefresh(false)
        searchResultListViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.searchUserByName(_searchContent, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<InviteUserItemData>> {
                override fun onSuccess(data: List<InviteUserItemData>?) {
                    searchResultListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        searchResultListViewModel.noMoreData()
                    } else {
                        searchResultListViewModel.loadError()
                    }
                }
            })
        }
    }

    fun showSearchResult(show: Boolean) {
        if (!show) {
            searchResultListViewModel.hiddenPageStatusLayout()
            mAccountRepo.clear()
        }
        showSearchResult.set(show)
    }

    fun inviteUser(user: InviteUserItemData) {
        mQuestionRepo.inviteUserToAnswer(getSelfUserID(), mQuestionId, user.userID
                , object : ResultCallBack {
            override fun onSuccess() {
                user.isInvited = true
            }

            override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun startSearch() {
        searchContent.value?.let {
            if (it.isNotEmpty()) {
                showSearchResult(true)
                startSearchEvent.call()
                _searchContent = it
                searchResultListViewModel.refresh(true)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        mAccountRepo.clear()
    }

}