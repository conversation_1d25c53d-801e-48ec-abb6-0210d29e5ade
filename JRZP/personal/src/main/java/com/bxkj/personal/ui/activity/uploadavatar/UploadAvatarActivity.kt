package com.bxkj.personal.ui.activity.uploadavatar

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.imageloader.UCropEngine
import com.bxkj.common.util.kotlin.applyDefaultConfig
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityUploadAvatarBinding
import com.bxkj.personal.ui.activity.selectdefaultavatar.SelectDefaultAvatarActivity
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route
import com.yalantis.ucrop.UCrop.Options

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.uploadavatar
 * @Description: 上传头像
 * <AUTHOR>
 * @date 2019/12/19
 * @version V1.0
 */
@Route(path = UploadAvatarNavigation.PATH)
class UploadAvatarActivity :
  BaseDBActivity<PersonalActivityUploadAvatarBinding, UploadAvatarViewModel>(),
  View.OnClickListener {
  companion object {
    const val TO_SELECT_AVATAR_CODE = 1

    fun newIntent(context: Context): Intent {
      return Intent(context, UploadAvatarActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<UploadAvatarViewModel> = UploadAvatarViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_upload_avatar

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewBinding.titleBar.setRightOptionClickListener {
      SystemUtil.hideSoftKeyboard(this)
      viewModel.submitUpdate()
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.uploadAvatarSuccessEvent.observe(this, Observer {
      setResult(Activity.RESULT_OK)
      finish()
    })

    viewModel.showSoftKeyboardCommand.observe(this, Observer {
      SystemUtil.showSoftKeyboardForView(viewBinding.etNickName)
    })
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.iv_avatar) {
        PermissionUtils.requestPermission(
          this@UploadAvatarActivity,
          getString(R.string.permission_tips_title),
          getString(R.string.permission_select_img_tips),
          object : PermissionUtils.OnRequestResultListener {
            override fun onRequestSuccess() {
              PictureSelector.create(this@UploadAvatarActivity)
                .openGallery(SelectMimeType.ofImage())
                .applyDefaultConfig()
                .setSelectionMode(SelectModeConfig.SINGLE)
                .isDirectReturnSingle(true)
                .setCropEngine(UCropEngine(Options().apply {
                  setCircleDimmedLayer(true)
                  withAspectRatio(1f, 1f)
                }))
                .forResult(PictureConfig.CHOOSE_REQUEST)
            }

            override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
              showToast(getString(R.string.cancel_operation))
            }
          },
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE
        )
      } else {
        startActivityForResult(SelectDefaultAvatarActivity.newIntent(this), TO_SELECT_AVATAR_CODE)
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}