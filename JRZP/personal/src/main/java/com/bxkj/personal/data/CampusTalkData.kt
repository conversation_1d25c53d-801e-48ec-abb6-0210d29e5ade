package com.bxkj.personal.data

import android.os.Parcelable
import androidx.recyclerview.widget.DiffUtil
import kotlinx.parcelize.Parcelize

@Parcelize
data class CampusTalkData(
    var nid: Int,
    var gxid: Int,
    var gxName: String,
    var ksdate: String,
    var didian: String,
    var fbdate: String,
    var weekName: String
) : CampusRecruitData(), Parcelable {

    fun getCampusTalkDesc(): String {
        return "$ksdate  $weekName"
    }

    fun getCompanyNameAndSchoolName(): String {
        return "$comName ${if (gxName.isEmpty()) "" else "-${gxName}"}"
    }

    class DiffCallback : DiffUtil.ItemCallback<CampusTalkData>() {

        override fun areItemsTheSame(
            oldItem: CampusTalkData,
            newItem: CampusTalkData
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: CampusTalkData,
            newItem: CampusTalkData
        ): Boolean {
            return oldItem.comName == newItem.comName
        }
    }
}