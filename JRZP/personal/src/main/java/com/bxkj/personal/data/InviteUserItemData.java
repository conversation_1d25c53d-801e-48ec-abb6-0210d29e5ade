package com.bxkj.personal.data;

import androidx.annotation.NonNull;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description: 邀请用户item项
 * @date 2020/2/25
 */
public class InviteUserItemData extends BaseObservable {
    private int userID;
    private String photo;
    private String name;
    private String nickName;
    private int sex;
    private boolean isInvited;

    public int getUserID() {
        return userID;
    }

    public void setUserID(int userID) {
        this.userID = userID;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    @Bindable
    public boolean isInvited() {
        return isInvited;
    }

    public void setInvited(boolean invited) {
        isInvited = invited;
        notifyPropertyChanged(com.bxkj.personal.BR.invited);
    }

    public static class Group {
        private List<InviteUserItemData> items;

        public static Group from(List<InviteUserItemData> items) {
            return new Group(items);
        }

        Group(List<InviteUserItemData> items) {
            this.items = items;
        }

        public List<InviteUserItemData> getItems() {
            return items;
        }

        public void setItems(List<InviteUserItemData> items) {
            this.items = items;
        }
    }

    public static class DiffCallback extends DiffUtil.ItemCallback<InviteUserItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull InviteUserItemData oldItem, @NonNull InviteUserItemData newItem) {
            return oldItem == newItem;
        }

        @Override
        public boolean areContentsTheSame(@NonNull InviteUserItemData oldItem, @NonNull InviteUserItemData newItem) {
            return oldItem.userID == newItem.userID && oldItem.nickName.equals(newItem.nickName);
        }
    }
}
