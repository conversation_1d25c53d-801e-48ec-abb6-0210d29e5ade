package com.bxkj.personal.ui.activity.myresumelist

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.UserResumeData

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/12
 * @version: V1.0
 */
class MyResumeListAdapter constructor(
  context: Context,
  private val pickerMode: <PERSON>olean,
  private val resumeListViewModel: MyResumeListViewModel
) : SuperAdapter<UserResumeData>(
  context,
  R.layout.personal_recycler_my_resume_list_item
) {

  private val mSelectedItems = ArrayList<UserResumeData>()
  private var mSelectAll = false

  override fun convert(
      holder: SuperViewHolder,
      viewType: Int,
      item: UserResumeData,
      position: Int
  ) {
    holder.bind(BR.data, item)
    val checkbox = holder.findViewById<ImageView>(R.id.iv_select)
    if (pickerMode) {
      checkbox.isSelected = mSelectedItems.contains(item)
      checkbox.visibility = View.VISIBLE
      holder.findViewById<ImageView>(R.id.iv_delete).visibility = View.GONE
      holder.findViewById<TextView>(R.id.tv_edit).visibility = View.GONE
      holder.findViewById<TextView>(R.id.tv_top).visibility = View.GONE
      holder.itemView.setOnClickListener {
        if (checkbox.isSelected) {
          mSelectedItems.remove(item)
        } else {
          mSelectedItems.add(item)
        }
        selectNotify()
      }
    }
  }

  fun selectAllResume() {
    mSelectAll = mSelectAll.not()
    mSelectedItems.clear()
    if (mSelectAll) {
      mSelectedItems.addAll(data)
    }
    selectNotify()
  }

  private fun selectNotify() {
    mSelectAll = mSelectedItems.size == data.size
    resumeListViewModel.setSelectAll(mSelectAll)
    notifyDataSetChanged()
    resumeListViewModel.setSelectedList(mSelectedItems)
  }
}