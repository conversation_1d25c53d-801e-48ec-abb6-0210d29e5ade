package com.bxkj.personal.ui.fragment.schoolmate

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.baidu.location.BDLocation
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.SchoolMateItemData

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.fragment.schoolmate
 * @Description: 校友
 * <AUTHOR>
 * @date 2019/12/5
 * @version V1.0
 */
class SchoolMateFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, SchoolMateViewModel>() {
    override fun getViewModelClass(): Class<SchoolMateViewModel> = SchoolMateViewModel::class.java

    override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.listViewModel = viewModel.listViewModel
        setupListAdapter()
    }

    override fun lazyLoadData() {
        getLocation()
    }

    private fun setupListAdapter() {
        val schoolMateListAdapter =
          SimpleDBListAdapter<SchoolMateItemData>(
            parentActivity,
            layout.personal_recycler_schoolmate_item
          )
        schoolMateListAdapter.setOnItemClickListener(object :
          SuperItemClickListener {
            override fun onClick(v: View, position: Int) {
                if (v.id == R.id.tv_follow) {
                    viewModel.addOrRemoveFollow(schoolMateListAdapter.data[position])
                } else {
//                    startActivity(UserHomeActivity.newIntent(parentActivity, schoolMateListAdapter.data[position].uid))
                }
            }
        }, R.id.tv_follow)
        val recyclerSchoolMateList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        recyclerSchoolMateList.layoutManager = LinearLayoutManager(context)
        viewModel.listViewModel.setAdapter(schoolMateListAdapter)
    }

    private fun getLocation() {
        LocationManager.getLocationInfo(parentActivity, getString(R.string.permission_tips_title), getString(R.string.schoolmate_location_permission_tips)
                , object : LocationManager.OnLocationListener {
            override fun onSuccess(location: BDLocation) {
                viewModel.refreshListByLocation(location)
            }

            override fun onFailed() {
                showNoPermissionLayout()
            }
        })
    }

    private fun showNoPermissionLayout() {
        viewBinding.pslContent.show(PageStatusConfigFactory.newErrorConfig()
                .setText(getString(R.string.schoolmate_location_permission_tips))
                .setBtnText(getString(R.string.schoolmate_location_permission_open))
                .setOnButtonClickListener {
                    getLocation()
                })
    }
}