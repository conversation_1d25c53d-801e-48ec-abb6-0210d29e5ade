package com.bxkj.personal.ui.activity.main

import android.util.DisplayMetrics
import android.view.View.OnClickListener
import android.view.ViewGroup.LayoutParams
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.widget.dialog.BaseDialogFragment
import com.bxkj.personal.R

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
class SelectLiveTypeDialog constructor(private var mOnItemClickListener: OnClickListener? = null) :
    BaseDialogFragment() {

    override fun getRootViewId(): Int {
        return R.layout.dialog_select_live_type
    }

    override fun initView() {
        rootView?.findViewById<TextView>(R.id.tv_type_live)?.setOnClickListener {
            dismiss()
            mOnItemClickListener?.onClick(it)
        }
        rootView?.findViewById<TextView>(R.id.tv_type_notice)?.setOnClickListener {
            dismiss()
            mOnItemClickListener?.onClick(it)
        }

        rootView?.findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val dm = DisplayMetrics()
            requireActivity().windowManager.defaultDisplay.getMetrics(dm)
            dialog.window?.setLayout((dm.widthPixels * 0.9).toInt(), LayoutParams.WRAP_CONTENT)
        }
    }
}