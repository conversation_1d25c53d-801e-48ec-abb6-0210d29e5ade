package com.bxkj.personal.ui.activity.selectresume

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/20
 * @version: V1.0
 */
class SendResumeMethod {
  companion object {
    const val SEND = 1
    const val CONVERSATION = 2
    const val SEND_FOR_LIVE = 3
  }

  @IntDef(SEND, CONVERSATION, SEND_FOR_LIVE)
  @Target(VALUE_PARAMETER)
  @Retention(SOURCE)
  annotation class Method
}