package com.bxkj.personal.ui.activity.systemmsg;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;
import com.bxkj.personal.data.SystemMsgData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.systemmsg
 * @Description:
 * @TODO: TODO
 * @date 2018/11/23
 */
public class SystemMsgListAdapter extends SuperAdapter<SystemMsgData> {
    public SystemMsgListAdapter(Context context, List<SystemMsgData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, SystemMsgData systemMsgData, int position) {
        holder.setText(R.id.tv_title, systemMsgData.getContent());
        holder.setText(R.id.tv_date, systemMsgData.getDate());
    }
}
