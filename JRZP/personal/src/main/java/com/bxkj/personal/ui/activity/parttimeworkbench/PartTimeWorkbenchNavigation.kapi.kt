package com.bxkj.personal.ui.activity.parttimeworkbench

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * author:Sanjin
 * date:2025/2/8
 **/
class PartTimeWorkbenchNavigation {
  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/PartTimeWorkbenchActivity"

    fun create():RouterNavigator{
      return Router.getInstance().to(PATH)
    }
  }
}