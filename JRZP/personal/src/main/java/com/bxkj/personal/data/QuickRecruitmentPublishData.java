package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/5/5
 */

public class QuickRecruitmentPublishData {


    /**
     * id : 1
     * name : 传菜员
     * num : 5
     * coname : 滨兴小厨
     * lxr : 刘先生
     * phone : 15868480780
     * email : <EMAIL>
     * qq : 12345678
     * sheng : 0
     * shi : 0
     * xian : 0
     * xianName : 滨江区
     * jie : 0
     * jieName : 浦沿街道
     * address : 蒋家里
     * date : null
     * shdate : null
     * jsdate : 2018/6/2 0:00:00
     * des : 速来面试
     * htc : 0
     * pwd : null
     * status : 0
     * fmsg : null
     */

    private int id;
    private String name;
    private String num;
    private String coname;
    private String lxr;
    private String phone;
    private String email;
    private String qq;
    private int sheng;
    private int shi;
    private int xian;
    private String xianName;
    private int jie;
    private String jieName;
    private String address;
    private String date;
    private Object shdate;
    private String jsdate;
    private String des;
    private int htc;
    private String pwd;
    private int status;
    private Object fmsg;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getConame() {
        return coname;
    }

    public void setConame(String coname) {
        this.coname = coname;
    }

    public String getLxr() {
        return lxr;
    }

    public void setLxr(String lxr) {
        this.lxr = lxr;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public int getSheng() {
        return sheng;
    }

    public void setSheng(int sheng) {
        this.sheng = sheng;
    }

    public int getShi() {
        return shi;
    }

    public void setShi(int shi) {
        this.shi = shi;
    }

    public int getXian() {
        return xian;
    }

    public void setXian(int xian) {
        this.xian = xian;
    }

    public String getXianName() {
        return xianName;
    }

    public void setXianName(String xianName) {
        this.xianName = xianName;
    }

    public int getJie() {
        return jie;
    }

    public void setJie(int jie) {
        this.jie = jie;
    }

    public String getJieName() {
        return jieName;
    }

    public void setJieName(String jieName) {
        this.jieName = jieName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Object getShdate() {
        return shdate;
    }

    public void setShdate(Object shdate) {
        this.shdate = shdate;
    }

    public String getJsdate() {
        return jsdate;
    }

    public void setJsdate(String jsdate) {
        this.jsdate = jsdate;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public int getHtc() {
        return htc;
    }

    public void setHtc(int htc) {
        this.htc = htc;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getFmsg() {
        return fmsg;
    }

    public void setFmsg(Object fmsg) {
        this.fmsg = fmsg;
    }
}
