package com.bxkj.personal.ui.activity.userbasicinfo

import android.os.Bundle
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class UserBasicInfoNavigation {

    companion object {

        const val PATH = RouterNavigation.UserBasicInfoActivity.URL

        const val EXTRA_FROM_PAGE = "FROM"
        const val EXTRA_NEXT_STEP = "NEXT_STEP"

        const val FROM_DEFAULT = 0
        const val FROM_REGISTER = 1

        @JvmStatic
        @JvmOverloads
        fun create(from: Int = FROM_DEFAULT, nextStep: FillInfoNext? = null): RouterNavigator {
            return Router.getInstance().to(PATH)
                .with(Bundle().apply {
                    putInt(EXTRA_FROM_PAGE, from)
                    putSerializable(EXTRA_NEXT_STEP, nextStep)
                })
        }
    }
}