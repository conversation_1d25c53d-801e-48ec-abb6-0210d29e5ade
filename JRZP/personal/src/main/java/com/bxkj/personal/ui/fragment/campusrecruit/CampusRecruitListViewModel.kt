package com.bxkj.personal.ui.fragment.campusrecruit

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.*
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.jrzp.support.feature.api.FeatureRepository
import com.bxkj.personal.data.CampusRecruitData
import com.bxkj.personal.data.source.JobRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class CampusRecruitListViewModel @Inject constructor(
    private val _jobRepo: JobRepo,
    private val _infoCheckRepo: FeatureRepository
) : BaseViewModel() {

    val jobCateList = MutableLiveData<List<JobTypeData>>()
    val cityList = MutableLiveData<List<FilterOptionData>>()

    private var _jobFistCategory = 0
    private var _provinceID = 0
    private var _cityID = "0"
    private var searchKeyword = ""

    val campusRecruitListFlow = Pager(PagingConfig(16)) {
        object : PagingSource<Int, CampusRecruitData>() {
            override fun getRefreshKey(state: PagingState<Int, CampusRecruitData>): Int? {
                return state.anchorPosition?.let { anchorPosition ->
                    val anchorPage = state.closestPageToPosition(anchorPosition)
                    anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
                }
            }

            override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CampusRecruitData> {
                val pageIndex = params.key.getOrDefault(1)
                var loadResult: LoadResult<Int, CampusRecruitData> =
                    LoadResult.Invalid()

                _jobRepo.getCampusRecruitList(
                    _jobFistCategory,
                    _provinceID,
                    _cityID,
                    searchKeyword,
                    pageIndex,
                    16
                ).handleResult({
                    it?.let {
                        loadResult = if (it.isEmpty()) {
                            LoadResult.Error(RespondThrowable.getNoDataError())
                        } else {
                            LoadResult.Page(
                                it,
                                if (pageIndex == 1) null else pageIndex - 1,
                                if (it.isEmpty()) null else pageIndex + 1
                            )
                        }
                    }
                }, {
                    loadResult = LoadResult.Error(it)
                })
                return loadResult
            }
        }
    }.flow.cachedIn(viewModelScope)

    fun start() {
        getJobFirstCategory()
        getCityList()
    }

    private fun getJobFirstCategory() {
        viewModelScope.launch {
            _infoCheckRepo.getJobType(1, 0)
                .handleResult({
                    jobCateList.value = ArrayList(it).apply {
                        add(0, JobTypeData(0, "全部"))
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }

    private fun getCityList() {
        viewModelScope.launch {
            _jobRepo.getCampusRecruitCityList()
                .handleResult({
                    cityList.value = it?.dataList?.let { it ->
                        ArrayList(it).apply {
                            add(
                                0,
                                FilterOptionData(0, "全部")
                            )
                        }
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }

    fun changeJobCategory(selectPosition: Int) {
        jobCateList.value?.let {
            _jobFistCategory = it[selectPosition].id
        }
    }

    fun changeJobCity(cityIds: List<Int>) {
        if (cityIds.isEmpty()) {
            _cityID = "0"
        } else {
            _cityID = cityIds.joinToString(",")
        }
    }

    fun setSearchKeyword(keyword: String) {
        this.searchKeyword = keyword
    }
}