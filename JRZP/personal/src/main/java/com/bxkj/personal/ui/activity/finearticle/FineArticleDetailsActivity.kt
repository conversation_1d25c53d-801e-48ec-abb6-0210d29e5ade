package com.bxkj.personal.ui.activity.finearticle

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.jrzp.support.comment.ui.CommentLayout
import com.bxkj.jrzp.support.comment.ui.CommentUIHandler
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityFineArticleDetailsBinding
import com.ethanhua.skeleton.Skeleton

/**
 * 好文详情
 * @author: sanjin
 * @date: 2022/9/1
 */
class FineArticleDetailsActivity :
    BaseDBActivity<PersonalActivityFineArticleDetailsBinding, FineArticleDetailsViewModel>(),
    View.OnClickListener {

    companion object {

        private const val EXTRA_ARTICLE_ID = "ARTICLE_ID"

        const val EXTRA_ARTICLE_DETAILS = "ARTICLE_DETAILS"

        fun newIntent(context: Context, articleId: Int): Intent {
            return Intent(context, FineArticleDetailsActivity::class.java).apply {
                putExtra(EXTRA_ARTICLE_ID, articleId)
            }
        }
    }

    private lateinit var _commentUIHandler: CommentUIHandler

    override fun getViewModelClass(): Class<FineArticleDetailsViewModel> =
        FineArticleDetailsViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_fine_article_details

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this
        setupContent()

        subscribeViewModelEvent()

        _commentUIHandler =
            CommentUIHandler(this, viewBinding.commentLayout, viewBinding.srlContent).apply {
                bindCommentVMHandler(viewModel.getCommentVMHandler())
                setOnLoadCommentListener(object : CommentLayout.OnLoadCommentListener {
                    override fun onLoadData(index: Int) {
                        viewModel.loadComment(index)
                    }
                })
            }

        viewModel.start(getExtraArticleId())
    }

    private fun subscribeViewModelEvent() {
        viewModel.toAuthorHomeCommand.observe(this, EventObserver {
            UserHomeNavigation.navigate(it).start()
        })
    }

    private fun setupContent() {
        val skeleton = Skeleton.bind(viewBinding.srlContent)
            .load(R.layout.personal_activity_fine_article_details_skeleton)
            .color(R.color.common_f7f9fb)
            .duration(1500)
            .show()

        viewBinding.webContent.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (newProgress > 70) {
                    skeleton.hide()
                }
                viewBinding.scrollParent.checkLayoutChange()
            }
        }
    }

    private fun getExtraArticleId(): Int {
        return intent.getIntExtra(EXTRA_ARTICLE_ID, 0)
    }

    override fun onClick(v: View?) {
        v?.let {
            if (v.id == R.id.tv_comment) {
                _commentUIHandler.showCommentDialog()
            }
        }
    }

    override fun finish() {
        viewModel.fineArticleDetails.value?.let {
            setResult(RESULT_OK,Intent().apply {
                putExtra(EXTRA_ARTICLE_DETAILS,it.apply {
                    //清空内容，避免超出Intent传递上限
                    content=""
                })
            })
        }
        super.finish()
    }
}