package com.bxkj.personal.ui.activity.questioninvite

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.personal.ui.fragment.questionrecommend.QuestionRecommendFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.questioninvite
 * @Description:
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
@Module
abstract class QuestionInviteModule {
    @PerFragment
    @ContributesAndroidInjector
    abstract fun questionRecommendFragment(): QuestionRecommendFragment
}