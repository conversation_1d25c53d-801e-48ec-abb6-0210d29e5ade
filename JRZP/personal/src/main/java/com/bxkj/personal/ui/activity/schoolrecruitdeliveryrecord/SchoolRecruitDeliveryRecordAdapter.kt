package com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.personal.data.SchoolRecruitRecordData

/**
 *
 * @author: sanjin
 * @date: 2022/4/14
 */
class SchoolRecruitDeliveryRecordAdapter constructor(
    context: Context,
    private val viewModel: DeliveryRecordViewModel
) : SimpleDBListAdapter<SchoolRecruitRecordData>(
    context,
    R.layout.personal_recycler_school_recruit_delivery_record_item
) {

    private var showSelect: Boolean = false

    override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: SchoolRecruitRecordData,
        position: Int
    ) {
        super.convert(holder, viewType, item, position)
        val ivChecked = holder.findViewById<ImageView>(R.id.iv_checked)
        holder.findViewById<ImageView>(R.id.iv_delete).visibility =
            if (showSelect) View.GONE else View.VISIBLE
        ivChecked.visibility =
            if (showSelect) View.VISIBLE else View.GONE
        ivChecked.isSelected = viewModel.getSelectedRecordValue().contains(item)
        ivChecked.setOnClickListener {
            viewModel.switchRecordSelectedState(item)
        }
    }

    fun showSelect(show: Boolean) {
        showSelect = show
        notifyDataSetChanged()
    }
}