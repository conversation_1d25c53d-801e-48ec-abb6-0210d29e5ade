package com.bxkj.personal.ui.activity.searchnews

import android.content.Context
import android.view.View
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.personal.data.SearchHotKeyItemData

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.searchnews
 * @Description:
 * <AUTHOR>
 * @date 2020/2/24
 * @version V1.0
 */
class SearchRecommendListAdapter constructor(context: Context, private val viewModel: SearchNewsViewModel) : SimpleDBListAdapter<SearchHotKeyItemData>(context, R.layout.personal_recycler_search_recommend_item) {

    override fun convert(holder: SuperViewHolder, viewType: Int, item: SearchHotKeyItemData, position: Int) {
        super.convert(holder, viewType, item, position)
        holder.findViewById<View>(R.id.v_split).visibility = if (position % 2 == 1) View.GONE else View.VISIBLE
        holder.itemView.setOnClickListener {
            viewModel.searchByRecommend(item)
        }
    }
}