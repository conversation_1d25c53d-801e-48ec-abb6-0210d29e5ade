package com.bxkj.personal.ui.activity.myresume.edubackground;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.EduBackgroundData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.edubackground
 * @Description: EduBackground
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface EduBackgroundContract {
    interface View extends BaseView {
        void getEduBackgroundSuccess(EduBackgroundData eduBackgroundData);
        void addEduBackgroundSuccess();
        void updateEduBackgroundSuccess();
        void deleteEduBackgroundSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getEduBackgroundDetails(int userId, int id);

        public abstract void addEduBackground(int userId, int resumeId,EduBackgroundData eduBackgroundData);

        public abstract void updateEduBackground(int userId,int eduId,EduBackgroundData eduBackgroundData);

        public abstract void deleteEduBackground(int userId, int eduId);
    }
}
