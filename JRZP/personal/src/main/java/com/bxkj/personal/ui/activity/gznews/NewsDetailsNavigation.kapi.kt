package com.bxkj.personal.ui.activity.gznews

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/25
 * @version: V1.0
 */
class NewsDetailsNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/newsdetails"

    const val EXTRA_NEWS_ID = "NEWS_ID"
    const val EXTRA_JUMP_TO_COMMENT = "JUMP_TO_COMMENT"

    fun navigation(newsId: Int, jumpToComment: Boolean = false): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_NEWS_ID, newsId)
        .withBoolean(EXTRA_JUMP_TO_COMMENT, jumpToComment)
    }
  }
}