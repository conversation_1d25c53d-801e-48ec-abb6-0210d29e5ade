package com.bxkj.personal.ui.fragment.parttimejoblist

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.cachedIn
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.UserResumeData
import com.bxkj.personal.data.source.JobRepo
import com.bxkj.personal.data.source.JobRepo.GetPartTimeJobParams
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class PartTimeJobListViewModel @Inject constructor(
  private val _jobRepo: JobRepo,
  private val _My_resumeRepo: MyResumeRepo
) : BaseViewModel() {

  val toCreateResumeCommand = MutableLiveData<VMEvent<Unit>>()

  private var _getPartTimeJobParams = GetPartTimeJobParams()

  val partTimeJobListFlow = Pager(PagingConfig(16)) {
    object : PagingSource<Int, JobData>() {
      override fun getRefreshKey(state: PagingState<Int, JobData>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
          val anchorPage = state.closestPageToPosition(anchorPosition)
          anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
      }

      override suspend fun load(params: LoadParams<Int>): LoadResult<Int, JobData> {
        val currentIndex = params.key.getOrDefault(1)
        var loadResult: LoadResult<Int, JobData> = LoadResult.Invalid()

        _jobRepo.getPartTimeJobList(_getPartTimeJobParams.apply {
          pageIndex = currentIndex
          pageSize = params.loadSize
          px = 4
        }).handleResult({
          loadResult = LoadResult.Page(
            it ?: emptyList(),
            if (currentIndex == 1) null else currentIndex - 1,
            if (it.isNullOrEmpty()) null else currentIndex + 1
          )
        }, {
          loadResult = LoadResult.Error(it)
        })
        return loadResult
      }
    }
  }.flow.cachedIn(viewModelScope)

  fun start(getPartTimeJobParams: GetPartTimeJobParams?) {
    getPartTimeJobParams?.let {
      _getPartTimeJobParams = it
    }
  }

  fun setSortMode(sortMode: Int) {
    _getPartTimeJobParams.apply {
      px = sortMode
    }
  }

  fun setSearchKeyword(keyword: String) {
    _getPartTimeJobParams.apply {
      name = keyword
    }
  }

  fun setCity(cityId: Int) {
    _getPartTimeJobParams.apply {
      if (shi > 0) {
        shi = cityId
      }
    }
  }

  fun sendResumePreCheck(job: JobData) {
    afterLogin {
      viewModelScope.launch {
        showLoading()
        _My_resumeRepo.getUserResumeList(getSelfUserID())
          .handleResult({ resultList ->
            if (resultList.isNullOrEmpty()) {
              toCreateResumeCommand.value = VMEvent(Unit)
            } else {
              val userResume = resultList[0]
              sendResume(job, userResume)
            }
          }, { err ->
            if (err.isNoDataError) {
              toCreateResumeCommand.value = VMEvent(Unit)
            } else {
              showToast(err.errMsg)
            }
          }, {
            hideLoading()
          })
      }
    }
  }

  private fun sendResume(job: JobData, userResume: UserResumeData) {
    viewModelScope.launch {
      showLoading()
      _jobRepo.sendResume(getSelfUserID(), job.uid, job.id, userResume.id)
        .handleResult({
          job.markJobApplied()
          showToast("报名成功")
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }
}