package com.bxkj.personal.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 订阅头部
 * @TODO: TODO
 * @date 2018/4/28
 */

public class SubscriptionHeaderData {
    private int id;
    private int logo;
    private String title;
    private String about;

    public SubscriptionHeaderData(int id, int logo, String title, String about) {
        this.id = id;
        this.logo = logo;
        this.title = title;
        this.about = about;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLogo() {
        return logo;
    }

    public void setLogo(int logo) {
        this.logo = logo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAbout() {
        return about;
    }

    public void setAbout(String about) {
        this.about = about;
    }
}
