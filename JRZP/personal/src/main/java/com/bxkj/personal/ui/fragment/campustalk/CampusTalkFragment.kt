package com.bxkj.personal.ui.fragment.campustalk

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bigkoo.pickerview.view.TimePickerView
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.bxkj.personal.R
import com.bxkj.personal.data.CampusTalkData
import com.bxkj.personal.databinding.PersonalFragmentCampusTalkBinding
import com.bxkj.personal.databinding.PersonalListCampusTalkItemBinding
import com.bxkj.personal.ui.activity.campustalkdetails.CampusTalkDetailsActivity
import com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitFragment
import com.bxkj.personal.ui.fragment.campustalk.CampusTalkDateFilterView.OnDateSelectedListener
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description 宣讲会
 */
class CampusTalkFragment :
  BaseDBFragment<PersonalFragmentCampusTalkBinding, CampusTalkViewModel>(), OnClickListener {

  companion object {

    fun newInstance(): Fragment {
      return CampusTalkFragment()
    }
  }

  private var _campusRecruitListAdapter: SimplePageDataAdapter<CampusTalkData, PersonalListCampusTalkItemBinding>? =
    null

  private var _filterSchoolPopup: DropDownPopup? = null
  private var _filterTimePopup: DropDownPopup? = null
  private var _cityFilterView: CampusTalkSchoolFilterView? = null
  private var _timePickerDialog: TimePickerView? = null

  override fun getViewModelClass(): Class<CampusTalkViewModel> = CampusTalkViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_campus_talk

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.onClickListener = this

    setupContentList()
    setupRefreshLayout()

    setupFilterPopup()

    subscribeCityChangeEvent()

    subscribeViewModelEvent()

    viewModel.start()
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.fl_filter_school -> {
          if (UserUtils.getUserSelectedCityId() > 0) {
            _filterSchoolPopup?.showAsDropDown()
          } else {
            ActionDialog.Builder()
              .setContent("请选择城市后再选择学校")
              .setOnConfirmClickListener {
                startActivity(CityPickerActivity.newIntent(activity))
              }.build().show(childFragmentManager)
          }
        }

        else -> {
          _filterTimePopup?.showAsDropDown()
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    (parentFragment as? CampusRecruitFragment)?.let {
      it.searchKeyword.observe(this) { keyword ->
        viewModel.setSearchKeyword(keyword)
        refreshList()
      }
    }

    viewModel.refreshCampusTalkCommand.observe(this, EventObserver {
      refreshList()
    })
  }

  private fun setupFilterPopup() {
    _cityFilterView = CampusTalkSchoolFilterView(requireContext(), {
      viewModel.setFilterSchool(it)
      _filterSchoolPopup?.dismiss()
    }).apply {
      setCity(UserUtils.getUserSelectedCityName())
    }

    _filterSchoolPopup = DropDownPopup(requireActivity(), viewBinding.llFilterBar).apply {
      isOutsideTouchable = true
      addContentViews(_cityFilterView)
      setOnItemExpandStatusChangeListener { index, opened ->
        viewBinding.flFilterSchool.isSelected = opened
      }
    }

    val timeFilterView = CampusTalkDateFilterView(requireContext()).apply {
      setOnDateSelectedListener(object : OnDateSelectedListener {
        override fun onDateSelected(dateFlag: String) {
          if (dateFlag == CampusTalkDateFilterView.DATE_FLAG_CUSTOM) {
            _timePickerDialog?.show()
          } else {
            viewModel.setFilterDate(dateFlag)
          }
          _filterTimePopup?.dismiss()
        }
      })
    }

    _timePickerDialog = TimePickerBuilder(
      requireContext()
    ) { date, v ->
      timeFilterView.changeCustomDate(TimeUtils.formatDate(date, "yyyy-MM-dd"))
    }
      .setCancelColor(R.color.cl_999999)
      .setSubmitColor(R.color.cl_ff7405)
      .build()

    _filterTimePopup = DropDownPopup(requireActivity(), viewBinding.llFilterBar).apply {
      isOutsideTouchable = true
      addContentViews(timeFilterView)

      setOnItemExpandStatusChangeListener { index, opened ->
        viewBinding.flFilterTime.isSelected = opened
      }
    }

    viewModel.schoolList.observe(this) {
      _cityFilterView?.setSchoolList(it)
    }

    viewModel.cityList.observe(this) {
      _cityFilterView?.setCityList(it)
    }
  }

  private fun subscribeCityChangeEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
            viewModel.refreshSchoolList()
            _cityFilterView?.setCity(UserUtils.getUserSelectedCityName())
          }
        }
    )
  }

  private fun setupRefreshLayout() {
    viewBinding.srlContent.setOnRefreshListener {
      _campusRecruitListAdapter?.refresh()
    }
  }

  private fun setupContentList() {
    viewBinding.pslContainer.show(LoadingPageState::class.java)
    _campusRecruitListAdapter =
      SimplePageDataAdapter<CampusTalkData, PersonalListCampusTalkItemBinding>(
        R.layout.personal_list_campus_talk_item,
        CampusTalkData.DiffCallback()
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            val item = getItemBean(position)
            item?.let {
              startActivity(CampusTalkDetailsActivity.newIntent(requireContext(), it.id))
            }
          }
        })
        addLoadStateListener { loadStateFlow ->
          loadStateFlow.handleState(notLoading = {
            viewBinding.srlContent.finishRefresh()
            viewBinding.pslContainer.hidden()
          }, noData = {
            viewBinding.pslContainer.show(
              EmptyPageState::class.java,
              object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                  pageState.setContent("未查到数据")
                }
              })
          }, error = {
            viewBinding.pslContainer.show(
              ErrorPageState::class.java,
              object : OnStateSetUpListener<ErrorPageState> {
                override fun onStateSetUp(pageState: ErrorPageState) {
                  pageState.setContent(it.message.getOrDefault())
                  pageState.setNextOptionClickListener { refreshList() }
                }
              })
          })
        }
      }

    viewBinding.recyclerCampusTalk.apply {
      closeDefaultAnim()
      layoutManager = LinearLayoutManager(requireContext())
      adapter = _campusRecruitListAdapter
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_8))
          .build()
      )
    }

    lifecycleScope.launch {
      viewModel.campusTalkListFlow.collectLatest {
        _campusRecruitListAdapter?.submitData(it)
      }
    }
  }

  private fun refreshList() {
    viewBinding.pslContainer.show(LoadingPageState::class.java)
    _campusRecruitListAdapter?.refresh()
  }
}