package com.bxkj.personal.ui.activity.parttimeworkbench.incomeanalyzer

import android.app.Dialog
import android.os.Bundle
import androidx.appcompat.app.AppCompatDialog
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.personal.R
import com.bxkj.personal.data.IncomeAnalyzerRecord
import com.bxkj.personal.databinding.CDialogIncomeAnalyzerBinding

/**
 * author:Sanjin
 * date:2025/2/26
 **/
class IncomeAnalyzerDialogFragment(private val onFeedbackClickListener: () -> Unit) :
  BaseDBDialogFragment<CDialogIncomeAnalyzerBinding, IncomeAnalyzerViewModel>() {

  private var incomeAnalyzerAdapter: SimpleDBListAdapter<IncomeAnalyzerRecord>? = null

  override fun getViewModelClass(): Class<IncomeAnalyzerViewModel> =
    IncomeAnalyzerViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_dialog_income_analyzer

  override fun initPage() {
    initListAdapter()
    subscribeViewModelEvent()

    viewBinding.tvConfirm.setOnClickListener {
      dismiss()
    }

    viewBinding.tvFeedback.setOnClickListener {
      onFeedbackClickListener.invoke()
    }

    viewModel.start()
  }

  override fun onStart() {
    super.onStart()
    dialog?.window?.let {
      it.attributes = it.attributes.apply {
        width = (resources.displayMetrics.widthPixels * 0.7f).toInt()
      }
    }
  }

  override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
    return AppCompatDialog(requireContext(), R.style.BaseDialogFragmentStyle)
  }

  private fun initListAdapter() {
    incomeAnalyzerAdapter = SimpleDBListAdapter(
      requireContext(),
      R.layout.c_recycler_income_analyzer_item
    )
    viewBinding.recyclerIncomeAnalyzer.apply {
      layoutManager = LinearLayoutManager(requireContext())
      adapter = incomeAnalyzerAdapter
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_eaeaea))
          .drawHeader(true).drawFoot(true).build()
      )
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.incomeList.observe(this) {
      incomeAnalyzerAdapter?.reset(it)
    }
  }
}