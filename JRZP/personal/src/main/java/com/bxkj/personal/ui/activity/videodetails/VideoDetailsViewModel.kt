package com.bxkj.personal.ui.activity.videodetails

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.exception.ExceptionCode
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplayNavigation
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.VideoItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.AppStatusInfoRepo
import com.bxkj.personal.data.source.CommentRepo
import com.bxkj.personal.data.source.NewsRepo
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.videodetails
 * @Description: 视频详情
 * <AUTHOR>
 * @date 2019/12/27
 * @version V1.0
 */
class VideoDetailsViewModel @Inject constructor(
  private val mCommentRepo: CommentRepo
  , private val mAccountRepo: AccountRepo
  , private val mNewsRepo: NewsRepo
  , private val mAppStatusInfoRepo: AppStatusInfoRepo
) : BaseViewModel() {

  val videoInfo = MutableLiveData<VideoItemData>()
  val commentListViewModel = RefreshListViewModel()
  val toUploadAvatarCommand = LiveEvent<Void>()
  val toAuthorPageComment = LiveEvent<VideoItemData>()
  val addCommentSuccessEvent = LiveEvent<Void>()
  val jumpToCommentCommand = LiveEvent<Int>()
  val followStatusChangeEvent = LiveEvent<Boolean>()

  //展示分享页面
  val showShareCommand = LiveEvent<ShareInfoData>()

  private var mNewsId: Int = CommonApiConstants.NO_ID
  private var mNeedJumpToComment = false
  private var mRecommendNewsSize = 0
  private var noComment: Boolean = false

  private val contentWrapList = ArrayList<VideoItemData>()

  init {
    setupListViewModel()
  }

  private fun setupListViewModel() {
    commentListViewModel.refreshLayoutViewModel.enableRefresh(false)
    commentListViewModel.setOnLoadDataListener { currentPage ->
      if (currentPage == 1) {
        mNewsRepo.getRecommendVideo(object : ResultDataCallBack<List<VideoItemData>> {
          override fun onSuccess(data: List<VideoItemData>) {
            mRecommendNewsSize = data.size + 1
            contentWrapList.addAll(data)
            commentListViewModel.reset(contentWrapList)
            getCommentByPage(mNewsId, currentPage)
          }

          override fun onError(respondThrowable: RespondThrowable) {
            getCommentByPage(mNewsId, currentPage)
          }
        })
      } else {
        getCommentByPage(mNewsId, currentPage)
      }
    }
  }

  fun start(intent: Intent) {
    mNewsId = intent.getIntExtra(VideoDetailsActivity.EXTRA_VIDEO_ID, CommonApiConstants.NO_ID)
    mNeedJumpToComment = intent.getBooleanExtra(VideoDetailsActivity.EXTRA_JUMP_TO_COMMENT, false)
    getVideoDetails()
  }

  private fun getVideoDetails() {
    mNewsRepo.getVideoDetails(getSelfUserID(), mNewsId, object : ResultDataCallBack<VideoItemData> {
      override fun onSuccess(data: VideoItemData) {
        data.isParent = true
        videoInfo.value = data
        contentWrapList.clear()
        if (data.type == PersonalApiConstants.I_POST_VIDEO_RECRUIT_TYPE) {
          contentWrapList.add(data)
        }
        commentListViewModel.refresh(true)
      }

      override fun onError(respondThrowable: RespondThrowable) {
        showToast(respondThrowable.errMsg)
      }
    })
  }

  private fun getCommentByPage(id: Int, currentPage: Int) {
    mCommentRepo.getCommentList(getSelfUserID(),
      id,
      PersonalApiConstants.NEWS_TYPE_VIDEO,
      CommonApiConstants.NO_ID,
      currentPage,
      CommonApiConstants.DEFAULT_PAGE_SIZE,
      object : ResultDataCallBack<List<CommentItemData>> {
        override fun onSuccess(data: List<CommentItemData>?) {
          commentListViewModel.addAll(data)
          if (currentPage == 1) {
            checkNeedToComment()
          }
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (currentPage > 1) {
            if (respondThrowable.errCode == 30003) {
              commentListViewModel.noMoreData()
            } else {
              commentListViewModel.loadError()
            }
          } else {
            setupNoCommentLayout()
            checkNeedToComment()
          }
        }
      })
  }

  private fun checkNeedToComment() {
    if (mNeedJumpToComment) {
      callJumpToCommentCommand()
    }
  }

  fun callJumpToCommentCommand() {
    jumpToCommentCommand.value = mRecommendNewsSize
  }

  /**
   * 设置无评论布局
   */
  private fun setupNoCommentLayout() {
    noComment = true
    commentListViewModel.add(CommentItemData.NoCommentData.getInstance())
    commentListViewModel.refreshLayoutViewModel.enableLoadMore(false)
  }

  /**
   * 检查是否上传头像
   */
  fun checkAvatarIsUpload(method: () -> Unit) {
    showLoading()
    mAccountRepo.checkAvatarIsUpload(getSelfUserID(), object : ResultCallBack {
      override fun onSuccess() {
        hideLoading()
        method.invoke()
      }

      override fun onError(respondThrowable: RespondThrowable) {
        hideLoading()
        if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30003) {
          toUploadAvatarCommand.call()
        } else {
          showToast(respondThrowable.errMsg)
        }
      }
    })
  }

  /**
   * 添加评论
   */
  fun addComment(content: String) {
    if (CheckUtils.isNullOrEmpty(content)) {
      showToast(R.string.comment_content_not_be_null)
      return
    }
    mCommentRepo.addComment(getSelfUserID(),
      mNewsId,
      PersonalApiConstants.NEWS_TYPE_VIDEO,
      CommonApiConstants.NO_ID,
      CommonApiConstants.NO_ID,
      CommonApiConstants.NO_TEXT,
      content,
      object : ResultDataCallBack<CommentItemData> {
        override fun onSuccess(data: CommentItemData?) {
          showToast(R.string.moment_details_comment_success)
          //无评论
          if (noComment) {
            noComment = false
            commentListViewModel.removeAt(commentListViewModel.childCount - 1)
            commentListViewModel.add(mRecommendNewsSize, data)
          } else {
            if (commentListViewModel.childCount >= CommonApiConstants.DEFAULT_PAGE_SIZE + mRecommendNewsSize) {
              commentListViewModel.refreshLayoutViewModel.enableLoadMore(true)
              commentListViewModel.removeAt(commentListViewModel.childCount - 1)
            }
            commentListViewModel.add(mRecommendNewsSize, data)
          }
          addCommentSuccessEvent.call()
          callJumpToCommentCommand()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  /**
   * 添加回复
   */
  fun addReply(commentPosition: Int, parentComment: CommentItemData, content: String) {
    mCommentRepo.addComment(getSelfUserID(),
      mNewsId,
      PersonalApiConstants.NEWS_TYPE_VIDEO,
      parentComment.pid,
      CommonApiConstants.NO_ID,
      parentComment.nickName,
      content
      ,
      object : ResultDataCallBack<CommentItemData> {
        override fun onSuccess(data: CommentItemData?) {
          parentComment.addReply(data)
          commentListViewModel.replace(commentPosition, parentComment)
          showToast(R.string.moment_details_comment_success)
          addCommentSuccessEvent.call()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  /**
   * 评论点赞或取消点赞
   */
  fun commentLikeOrUnlike(comment: CommentItemData) {
    if (checkLoginStateAndToLogin()) {
      mCommentRepo.likeOrUnlikeTheComment(
        getSelfUserID(),
        comment.pid,
        PersonalApiConstants.NEWS_TYPE_VIDEO,
        mNewsId,
        object : ResultCallBack {
          override fun onSuccess() {
            comment.addLike()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 10002) {
              comment.removeLike()
            } else {
              showToast(respondThrowable.errMsg)
            }
          }
        })
    }
  }

  /**
   * 添加或取消收藏
   */
  fun addOrRemoveCollection() {
    mAccountRepo.addOrRemoveCollection(
      getSelfUserID(),
      PersonalApiConstants.NEWS_TYPE_VIDEO,
      mNewsId,
      object : ResultDataCallBack<BaseResponse<*>> {
        override fun onSuccess(data: BaseResponse<*>?) {
          videoInfo.value?.addCollection()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          when (respondThrowable.errCode) {
            10002 -> {
              videoInfo.value?.removeCollection()
            }
            ExceptionCode.UNLOGIN_ERROR -> loginCommand.call()
            else -> showToast(respondThrowable.errMsg)
          }
        }
      })
  }

  /**
   * 点赞或取消点赞
   */
  fun addOrRemoveLike(item: CommentItemData? = null) {
    afterLogin {
      mCommentRepo.likeOrUnlikeTheComment(getSelfUserID(),
        item?.pid
          ?: CommonApiConstants.NO_ID,
        PersonalApiConstants.NEWS_TYPE_VIDEO,
        mNewsId,
        object : ResultCallBack {
          override fun onSuccess() {
            if (item == null) {
              videoInfo.value?.addLike()
            } else {
              item.addLike()
            }
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 10002) {
              if (item == null) {
                videoInfo.value?.removeLike()
              } else {
                item.addLike()
              }
            } else {
              showToast(respondThrowable.errMsg)
            }
          }
        })
    }
  }

  fun followOrUnFollow() {
    afterLogin {
      videoInfo.value?.let {
        mAccountRepo.addOrRemoveFollow(
          getSelfUserID(),
          PersonalApiConstants.FOLLOW_USER_TYPE,
          it.userID,
          object : ResultCallBack {
            override fun onSuccess() {
              authorAddFollow(it)
            }

            override fun onError(respondThrowable: RespondThrowable) {
              if (respondThrowable.errCode == 10002) {
                authorRemoveFollow(it)
              } else {
                showToast(respondThrowable.errMsg)
              }
            }
          })
      }
    }
  }

  private fun authorRemoveFollow(it: VideoItemData) {
    it.removeFollow()
    followStatusChangeEvent.value = false
  }

  private fun authorAddFollow(it: VideoItemData) {
    it.addFollow()
    followStatusChangeEvent.value = true
  }

  fun getVideoNewsId(): Int {
    return mNewsId
  }

  fun toVideoAuthorPage() {
    videoInfo.value?.let {
      toAuthorPageComment.value = it
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == VideoDetailsActivity.TO_COMMENT_REPLY_CODE) {
      if (data != null) {
        val index =
          data.getIntExtra(
            CommentReplayNavigation.EXTRA_PARENT_POSITION,
            CommonApiConstants.NO_DATA
          )
        val comment =
          data.getParcelableExtra<CommentItemData>(CommentReplayNavigation.EXTRA_PARENT_COMMENT)
        val newCommentCount =
          data.getIntExtra(
            CommentReplayNavigation.EXTRA_NEW_COMMENT_COUNT,
            CommonApiConstants.NO_DATA
          )
        if (resultCode == CommentReplayNavigation.RESULT_COMMENT_CHANGED) {
          //将新加的评论数量添加到详情
          if (newCommentCount != CommonApiConstants.NO_DATA) {
            videoInfo.value?.addComment(newCommentCount)
          }
          commentListViewModel.replace(index, comment)
        } else if (resultCode == CommentReplayNavigation.RESULT_COMMENT_DELETED) {
          removeCommentByIndex(index)
        }
      }
    } else if (requestCode == VideoDetailsActivity.TO_VIDEO_AUTHOR_HOME_CODE && resultCode == GzUserHomeActivity.RESULT_FOLLOW_STATUS_CHANGE) {
      if (data != null) {
        val followed = data.getBooleanExtra(GzUserHomeActivity.EXTRA_FOLLOW_STATUS, false)
        videoInfo.value?.let {
          if (followed) authorAddFollow(it) else authorRemoveFollow(it)
        }
      }
    }
  }

  fun deleteComment(position: Int, item: CommentItemData) {
    mAccountRepo.deleteMyPublish(getSelfUserID(),
      PersonalApiConstants.MY_PUBLISH_COMMENT_TYPE,
      PersonalApiConstants.MY_PUBLISH_VIDEO_COMMENT_TYPE,
      item.pid,
      object : ResultCallBack {
        override fun onSuccess() {
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (respondThrowable.errCode == 30001) {
            showToast(R.string.common_delete_success)
            removeCommentByIndex(position)
          } else {
            showToast(respondThrowable.errMsg)
          }
        }
      })
  }

  private fun removeCommentByIndex(index: Int) {
    commentListViewModel.loadFinish()
    commentListViewModel.removeAt(index)
    if (commentListViewModel.childCount == mRecommendNewsSize) {
      setupNoCommentLayout()
    }
  }

  fun getShareInfo() {
    videoInfo.value?.let {
      viewModelScope.launch {
        showLoading()
        mAppStatusInfoRepo.getShareInfo(CommonApiConstants.SHARE_VIDEO, it.id, it.type)
          .handleResult({
            showShareCommand.value = it
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

}