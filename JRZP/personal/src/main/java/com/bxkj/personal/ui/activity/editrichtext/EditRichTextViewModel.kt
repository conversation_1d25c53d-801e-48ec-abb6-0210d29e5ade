package com.bxkj.personal.ui.activity.editrichtext

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.source.MomentRepo
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/7
 * @version: V1.0
 */
class EditRichTextViewModel @Inject constructor(
  private val mMomentRepo: MomentRepo
) : BaseViewModel() {

  val picUploadSuccessEvent = LiveEvent<String>()

  fun uploadPicture(imgPath: String) {
    showLoading()
    mMomentRepo.uploadMomentFile(imgPath,
      UploadFileRequestParams.fromFileType(getSelfUserID(), UploadFileRequestParams.TYPE_IMG),
      object : ResultDataCallBack<String> {
        override fun onSuccess(data: String?) {
          hideLoading()
          picUploadSuccessEvent.value = CommonApiConstants.BASE_JRZP_IMG_URL + data
        }

        override fun onError(respondThrowable: RespondThrowable) {
          hideLoading()
          showToast(respondThrowable.errMsg)
        }
      })
  }

}