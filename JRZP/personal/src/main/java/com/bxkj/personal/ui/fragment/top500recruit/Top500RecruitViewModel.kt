package com.bxkj.personal.ui.fragment.top500recruit

import androidx.lifecycle.viewModelScope
import androidx.paging.*
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.CampusRecruitData
import com.bxkj.personal.data.source.JobRepo
import javax.inject.Inject

class Top500RecruitViewModel @Inject constructor(
    private val _jobRepo: JobRepo
) : BaseViewModel() {

    private var _flag1 = false

    private var _flag2 = false
    private var _flag3 = false
    private var _flag4 = false
    private var _searchKeyword = ""
    val top500RecruitListFlow = Pager(PagingConfig(16)) {
        object : PagingSource<Int, CampusRecruitData>() {
            override fun getRefreshKey(state: PagingState<Int, CampusRecruitData>): Int? {
                return state.anchorPosition?.let { anchorPosition ->
                    val anchorPage = state.closestPageToPosition(anchorPosition)
                    anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
                }
            }

            override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CampusRecruitData> {
                val pageIndex = params.key.getOrDefault(1)
                var loadResult: LoadResult<Int, CampusRecruitData> =
                    LoadResult.Invalid()

                _jobRepo.getTop500RecruitList(
                    _flag1,
                    _flag2,
                    _flag3,
                    _flag4,
                    pageIndex,
                    params.loadSize,
                    _searchKeyword
                ).handleResult(
                    {
                        loadResult = LoadResult.Page(
                            it ?: emptyList(),
                            if (pageIndex == 1) null else pageIndex - 1,
                            if (it.isNullOrEmpty()) null else pageIndex + 1
                        )
                    }, {
                        loadResult = LoadResult.Error(it)
                    }
                )
                return loadResult
            }
        }
    }.flow.cachedIn(viewModelScope)

    fun setFilterParams(position: Int) {
        when (position) {
            0 -> {
                _flag1 = false
                _flag2 = false
                _flag3 = false
                _flag4 = false
            }

            1 -> {
                _flag1 = true
                _flag2 = false
                _flag3 = false
                _flag4 = false
            }

            2 -> {
                _flag1 = false
                _flag2 = true
                _flag3 = false
                _flag4 = false
            }

            3 -> {
                _flag1 = false
                _flag2 = false
                _flag3 = true
                _flag4 = false
            }

            4 -> {
                _flag1 = false
                _flag2 = false
                _flag3 = false
                _flag4 = true
            }
        }
    }

    fun setSearchKeyword(keyword: String) {
        _searchKeyword = keyword
    }
}