package com.bxkj.personal.ui.activity.createresumestepthree;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.data.WorkExpItemData;


import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumestepthree
 * @Description: CreateResumeStepThree
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CreateResumeStepThreePresenter extends CreateResumeStepThreeContract.Presenter {

    private static final String TAG = CreateResumeStepThreePresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public CreateResumeStepThreePresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    void getResumeWorkExpList(int userId, int resumeId) {
        mView.showLoading();
        mPersonalApi.getWorkExpList(userId, resumeId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.getExpListSuccess((List<WorkExpItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.getExpListSuccess(new ArrayList<>());
                        if (respondThrowable.getErrCode() != 30002) {
                            mView.onError(respondThrowable.getErrMsg());
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void addResumeWorkExp(int userId, int resumeId, WorkExpData workExpData) {
        mView.showLoading();
        mPersonalApi.addResumeWorkExp(userId, resumeId, workExpData.getDate1(), workExpData.getDate2(), workExpData.getConame(), workExpData.getTradeid(), workExpData.getJob(), workExpData.getDes())
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.addSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void deleteResumeWorkExp(int userId, int workExpId, int position) {
        mView.showLoading();
        mPersonalApi.deleteWorkExp(userId, workExpId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.deleteResumeWorkExpSuccess(position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
