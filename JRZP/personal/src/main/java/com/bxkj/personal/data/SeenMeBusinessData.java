package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/11/22
 */
public class SeenMeBusinessData extends BaseObservable {

    /**
     * id : 2537 lx : 0 type : 0 types : null typeName : null otherid : 0 resid : 0 uid : 0 cuid :
     * 242943 content : null look : 0 date : 2018-11-21 15:12:24 pageIndex : 0 pageSize : 0 company :
     * {"id":139214,"uid":0,"name":"杭州滨兴科技股份有限公司","name2":null,"logo":"images_server/logo/242943/F0XZ0F4020_3003550.jpg","address":null,"phone":null,"lxr":null,"fax":null,"qq":null,"Info":null,"proid":0,"proName":"外商独资·外企办事处","tradeid":0,"tradeName":"计算机软件","sizeid":0,"sizeName":"21-50人","comUrl":null,"province":0,"provinceName":null,"city":0,"cityName":null,"county":0,"countyName":null,"town":0,"townName":null,"count":0,"date":null,"coordinate":null,"traffic":null,"url":null,"domain":"http://img.jrzp.com/"}
     * jInterview : null relId : 0 relName : null state : 0 type : 0 nolookCount : 0
     */

    private int id;

    private int lx;

    private int type;

    private int otherid;

    private int resid;

    private int uid;

    private int cuid;

    private int look;

    private String date;

    private int pageIndex;

    private int pageSize;

    private CompanyBean company;

    private int relId;

    private int state;

    private int index;

    private int nolookCount;

    private String relName;

    private int relCount;

    @Bindable
    public boolean isViewed() {
        return look == 1;
    }

    public void setupViewed(boolean viewed) {
        look = viewed ? 1 : 0;
        notifyPropertyChanged(BR.viewed);
    }

    public String getRelName() {
        return relName;
    }

    public void setRelName(String relName) {
        this.relName = relName;
    }

    public int getRelCount() {
        return relCount;
    }

    public void setRelCount(int relCount) {
        this.relCount = relCount;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLx() {
        return lx;
    }

    public void setLx(int lx) {
        this.lx = lx;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getOtherid() {
        return otherid;
    }

    public void setOtherid(int otherid) {
        this.otherid = otherid;
    }

    public int getResid() {
        return resid;
    }

    public void setResid(int resid) {
        this.resid = resid;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getCuid() {
        return cuid;
    }

    public void setCuid(int cuid) {
        this.cuid = cuid;
    }

    public int getLook() {
        return look;
    }

    public void setLook(int look) {
        this.look = look;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public CompanyBean getCompany() {
        return company;
    }

    public void setCompany(CompanyBean company) {
        this.company = company;
    }

    public int getRelId() {
        return relId;
    }

    public void setRelId(int relId) {
        this.relId = relId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getNolookCount() {
        return nolookCount;
    }

    public void setNolookCount(int nolookCount) {
        this.nolookCount = nolookCount;
    }


    public static class CompanyBean {

        /**
         * id : 139214 uid : 0 name : 杭州滨兴科技股份有限公司 name2 : null logo :
         * images_server/logo/242943/F0XZ0F4020_3003550.jpg
         * address : null phone : null lxr : null fax : null qq : null Info : null proid : 0 proName :
         * 外商独资·外企办事处 tradeid : 0 tradeName : 计算机软件 sizeid : 0 sizeName : 21-50人 comUrl : null province
         * : 0 provinceName : null city : 0 cityName : null county : 0 countyName : null town : 0
         * townName : null count : 0 date : null coordinate : null traffic : null url : null domain :
         * http://img.jrzp.com/
         */

        private int id;

        private int uid;

        private String name;

        private String logo;

        private int proid;

        private String proName;

        private int tradeid;

        private String tradeName;

        private int sizeid;

        private String sizeName;

        private int province;

        private int city;

        private int county;

        private int town;

        private int count;

        private String domain;

        public String getAbout() {
            final StringBuilder aboutBuilder = new StringBuilder();
            if (!CheckUtils.isNullOrEmpty(tradeName)) {
                aboutBuilder.append(tradeName).append("|");
            }
            if (!CheckUtils.isNullOrEmpty(sizeName)) {
                aboutBuilder.append(sizeName).append("|");
            }
            if (!CheckUtils.isNullOrEmpty(proName)) {
                aboutBuilder.append(proName);
            }
            return aboutBuilder.toString();
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getUid() {
            return uid;
        }

        public void setUid(int uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public int getProid() {
            return proid;
        }

        public void setProid(int proid) {
            this.proid = proid;
        }

        public String getProName() {
            return proName;
        }

        public void setProName(String proName) {
            this.proName = proName;
        }

        public int getTradeid() {
            return tradeid;
        }

        public void setTradeid(int tradeid) {
            this.tradeid = tradeid;
        }

        public String getTradeName() {
            return tradeName;
        }

        public void setTradeName(String tradeName) {
            this.tradeName = tradeName;
        }

        public int getSizeid() {
            return sizeid;
        }

        public void setSizeid(int sizeid) {
            this.sizeid = sizeid;
        }

        public String getSizeName() {
            return sizeName;
        }

        public void setSizeName(String sizeName) {
            this.sizeName = sizeName;
        }

        public int getProvince() {
            return province;
        }

        public void setProvince(int province) {
            this.province = province;
        }

        public int getCity() {
            return city;
        }

        public void setCity(int city) {
            this.city = city;
        }

        public int getCounty() {
            return county;
        }

        public void setCounty(int county) {
            this.county = county;
        }

        public int getTown() {
            return town;
        }

        public void setTown(int town) {
            this.town = town;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }
    }
}
