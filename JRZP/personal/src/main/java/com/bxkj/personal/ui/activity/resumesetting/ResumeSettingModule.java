package com.bxkj.personal.ui.activity.resumesetting;

import com.bxkj.common.api.CommonApiConstants;

import javax.inject.Named;

import dagger.Module;
import dagger.Provides;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.resumesetting
 * @Description:
 * @TODO: TODO
 * @date 2018/9/14
 */
@Module
public class ResumeSettingModule {

  @Provides
  @Named(ResumeSettingActivity.EXTRA_RESUME_ID)
  int provideResumeId(ResumeSettingActivity resumeSettingActivity) {
    return resumeSettingActivity.getIntent()
        .getIntExtra(ResumeSettingActivity.EXTRA_RESUME_ID, CommonApiConstants.NO_ID);
  }

  @Provides
  @Named(ResumeSettingActivity.EXTRA_RESUME_STATE)
  int provideResumeState(ResumeSettingActivity resumeSettingActivity) {
    return resumeSettingActivity.getIntent()
        .getIntExtra(ResumeSettingActivity.EXTRA_RESUME_STATE, CommonApiConstants.NO_ID);
  }

  @Provides
  @Named(ResumeSettingActivity.EXTRA_IS_DEFAULT)
  int provideResumeIsDefault(ResumeSettingActivity resumeSettingActivity) {
    return resumeSettingActivity.getIntent()
        .getIntExtra(ResumeSettingActivity.EXTRA_IS_DEFAULT, CommonApiConstants.NO_ID);
  }

  @Provides
  @Named(ResumeSettingActivity.EXTRA_AUTO_REFRESH)
  int provideResumeAutoRefresh(ResumeSettingActivity resumeSettingActivity) {
    return resumeSettingActivity.getIntent()
        .getIntExtra(ResumeSettingActivity.EXTRA_AUTO_REFRESH, CommonApiConstants.NO_ID);
  }
}
