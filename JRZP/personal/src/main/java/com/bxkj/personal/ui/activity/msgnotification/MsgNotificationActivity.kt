package com.bxkj.personal.ui.activity.msgnotification

import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.base.BaseListActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.TitleBarManager
import com.bxkj.personal.R
import com.bxkj.personal.data.FeedbackMsgItemData
import com.bxkj.personal.ui.activity.msgnotificationcontent.MsgNotificationContentActivity
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.msgnotification
 * @Description: 消息通知
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/22
 * @version V1.0
 */
class MsgNotificationActivity : BaseListActivity(), MsgNotificationContract.View {

  companion object {
    private const val TO_MSG_CONTENT_CODE = 1
  }

  @Inject
  lateinit var mMsgNotificationPresenter: MsgNotificationPresenter

  private lateinit var mMsgNotificationListAdapter: MsgNotificationListAdapter
  private var mClickedPosition: Int = 0

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>?): MutableList<BasePresenter<BaseView>> {
    presenters!!.add(CheckUtils.cast(mMsgNotificationPresenter))
    return presenters
  }

  override fun getLayoutId(): Int = R.layout.personal_activity_only_list

  override fun initTitleBar(titleBarManager: TitleBarManager?) {
    titleBarManager!!.setTitle(getString(R.string.message_notice))
  }

  override fun initPage() {
    super.initPage()

    mMsgNotificationListAdapter =
      MsgNotificationListAdapter(this, null, R.layout.personal_recycler_feedback_message_item)
    mMsgNotificationListAdapter.setOnItemClickListener(object : SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        mClickedPosition = position
        startActivityForResult(
          MsgNotificationContentActivity.newIntent(
            this@MsgNotificationActivity,
            mMsgNotificationListAdapter.data[position]
          ), TO_MSG_CONTENT_CODE
        )
      }
    })
    recyclerView.adapter = mMsgNotificationListAdapter
    recyclerView.layoutManager = LinearLayoutManager(this)
  }

  override fun onResume() {
    super.onResume()
    refreshLayoutManager.refreshPage()
  }

  override fun loadData() {
    mMsgNotificationPresenter.getMsgNotificationList(
      mUserID,
      refreshLayoutManager.currentPage,
      CommonApiConstants.DEFAULT_PAGE_SIZE
    )
  }

  override fun getMsgNotificationListSuccess(msgNotificationList: MutableList<FeedbackMsgItemData>) {
    pageStatusLayout.hidden()
    refreshLayoutManager.finishRefreshOrLoadMore()
    if (refreshLayoutManager.currentFirstPage()) {
      mMsgNotificationListAdapter.reset(msgNotificationList)
    } else {
      mMsgNotificationListAdapter.addAll(msgNotificationList)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_MSG_CONTENT_CODE && resultCode == Activity.RESULT_OK) {
      mMsgNotificationListAdapter.data[this.mClickedPosition].nolookCount = 0
      mMsgNotificationListAdapter.notifyItemChanged(mClickedPosition)
    }
  }
}