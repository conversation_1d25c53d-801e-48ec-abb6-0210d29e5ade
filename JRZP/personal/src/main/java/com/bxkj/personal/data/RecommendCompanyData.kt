package com.bxkj.personal.data

import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.common.util.kotlin.getOrDefault

/**
 *
 * @author: sanjin
 * @date: 2022/8/29
 */
data class RecommendCompanyData(
    var CityName: String?,
    var comid: Int?,
    var uid: Int?,
    var domain: String?,
    var logo: String?,
    var money: String?,
    var name: String?,
    var qualification: String?,
    var relCount: Int?,
    var relData: List<RelData?>?,
    var relId: Int?,
    var relName: String?,
    var welData: List<WelData?>?,
    var workTime: String?
) {
    fun getFullLogoUrl(): String {
        return domain + logo
    }

    fun getJobNameText(): String {
        return if (relData.isNullOrEmpty()) {
            ""
        } else {
            relData!!.map { it?.relName.getOrDefault() }.appendItem()
        }
    }

    fun getWelfareText(): String {
        return if (welData.isNullOrEmpty()) {
            "无"
        } else {
            welData!!.map { it?.name.getOrDefault() }.appendItem()
        }
    }

    fun getRecommendJobDesc(): String {
        val descBuilder = StringBuilder()
        if (!CityName.isNullOrBlank()) {
            descBuilder.append(CityName).append(" ")
        }
        if (!qualification.isNullOrBlank()) {
            descBuilder.append(qualification).append(" ")
        }
        if (!workTime.isNullOrBlank()) {
            descBuilder.append(workTime)
        }
        return descBuilder.toString()
    }

    class DiffCallback : DiffUtil.ItemCallback<RecommendCompanyData>() {
        override fun areItemsTheSame(
            oldItem: RecommendCompanyData,
            newItem: RecommendCompanyData
        ): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(
            oldItem: RecommendCompanyData,
            newItem: RecommendCompanyData
        ): Boolean {
            return oldItem.comid == newItem.comid
        }

    }
}

data class RelData(
    var relName: String?,
    var relid: Int?
)

data class WelData(
    var name: String?
)