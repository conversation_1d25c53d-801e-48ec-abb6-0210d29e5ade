package com.bxkj.personal.ui.activity.questiondetails

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/22
 * @version: V1.0
 */
class QuestionDetailsNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/questiondetails"

    const val EXTRA_QUESTION_ID = "QUESTION_ID"

    fun navigate(questionId: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_QUESTION_ID, questionId)
    }
  }
}