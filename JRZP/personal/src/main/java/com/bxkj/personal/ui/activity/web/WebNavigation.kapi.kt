package com.bxkj.personal.ui.activity.web

import androidx.core.os.bundleOf
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/18
 * @version: V1.0
 */
class WebNavigation {
  companion object {
    const val PATH = RouterNavigation.WebActivity.URL

    const val EXTRA_URL = "URL"
    const val EXTRA_PAGE_TITLE = "PAGE_TITLE"
    const val EXTRA_FULL_SCREEN = "FULL_SCREEN"

    @JvmOverloads
    @JvmStatic
    fun navigate(
      url: String,
      pageTitle: String? = null,
      fullScreen: Boolean = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .with(
          bundleOf(
            EXTRA_URL to url,
            EXTRA_PAGE_TITLE to pageTitle,
            EXTRA_FULL_SCREEN to fullScreen
          )
        )
    }
  }
}