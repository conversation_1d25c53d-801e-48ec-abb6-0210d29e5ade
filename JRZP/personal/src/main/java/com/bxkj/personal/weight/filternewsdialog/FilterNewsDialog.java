package com.bxkj.personal.weight.filternewsdialog;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.base.BaseDaggerFragment;
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.contract.GetAreaListContract;
import com.bxkj.common.mvp.persenter.GetAreaListPresenter;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.widget.filterpopup.FilterGroupTitle;
import com.bxkj.common.widget.filterpopup.FilterOption;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup;
import com.bxkj.personal.R;
import com.bxkj.personal.mvp.contract.NewsTypeTwoContract;
import com.bxkj.personal.mvp.presenter.NewsTypeTwoPresenter;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description: 筛选资讯dialog
 * @TODO: TODO
 * @date 2018/4/16
 */

public class FilterNewsDialog extends BaseDaggerFragment
  implements View.OnClickListener, NewsTypeTwoContract.View, GetAreaListContract.View {

  private static final String NEED_TYPE = "needType";
  private static final String NEED_BOTTOM_BAR = "needBottomBar";

  public static final String TAG = "FilterNewsDialog";

  @Inject
  NewsTypeTwoPresenter mNewsTypeTwoPresenter;
  @Inject
  GetAreaListPresenter mGetAreaListPresenter;

  private LinearLayout llFilterBottomBar;

  private TextView tvReset, tvConfirm;
  private RecyclerView recyclerFilter;
  private MultiTypeAdapter mMultiTypeAdapter;
  private List mDataList;
  private FilterNewsItemRecyclerViewBinder mFilterNewsItemRecyclerViewBinder;

  public static FilterNewsDialog newInstance(boolean needType, boolean needBottomBar) {
    FilterNewsDialog filterNewsDialog = new FilterNewsDialog();
    Bundle bundle = new Bundle();
    bundle.putBoolean(NEED_TYPE, needType);
    bundle.putBoolean(NEED_BOTTOM_BAR, needBottomBar);
    filterNewsDialog.setArguments(bundle);
    return filterNewsDialog;
  }

  @Override
  public void initPresenter() {
    mNewsTypeTwoPresenter.attachView(this);
    mGetAreaListPresenter.attachView(this);
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_dialog_filter_news;
  }

  @Override
  public void initPage() {
    bindView(getView());

    recyclerFilter = rootView.findViewById(R.id.recycler_filter);
    tvReset = rootView.findViewById(R.id.tv_reset);
    tvConfirm = rootView.findViewById(R.id.tv_confirm);

    tvReset.setOnClickListener(this);
    tvConfirm.setOnClickListener(this);

    if (!getArguments().getBoolean(NEED_BOTTOM_BAR, true)) {
      llFilterBottomBar.setVisibility(View.GONE);
    }

    initRecycler();
  }

  private void initRecycler() {
    mDataList = new ArrayList();
    mMultiTypeAdapter = new MultiTypeAdapter(getContext(), mDataList);
    FilterNewsTitleViewBinder filterNewsTitleViewBinder = new FilterNewsTitleViewBinder();
    filterNewsTitleViewBinder.setOnGroupTitleClickListener(type -> {
      if (OnMoreOrConfirmClickListener != null) {
        OnMoreOrConfirmClickListener.onMoreClick(type);
      }
    });
    mMultiTypeAdapter.register(FilterGroupTitle.class, filterNewsTitleViewBinder);
    mFilterNewsItemRecyclerViewBinder = new FilterNewsItemRecyclerViewBinder(getContext());
    mFilterNewsItemRecyclerViewBinder.setOnFilterOptionClickListener(
      new FilterNewsItemRecyclerViewBinder.OnFilterOptionClickListener() {
        @Override
        public void onFilterOptionClick(int type, int position) {
          if (OnMoreOrConfirmClickListener != null) {
            OnMoreOrConfirmClickListener.onItemOptionClick(type, position);
          }
        }

        @Override
        public void onMoreClick(int type) {
          if (OnMoreOrConfirmClickListener != null) {
            OnMoreOrConfirmClickListener.onMoreClick(type);
          }
        }
      });
    mMultiTypeAdapter.register(FilterOptionsGroup.class, mFilterNewsItemRecyclerViewBinder);

    recyclerFilter.setLayoutManager(new LinearLayoutManager(getContext()));
    recyclerFilter.setAdapter(mMultiTypeAdapter);
    initData();
  }

  private void initData() {
    mDataList.add(
      new FilterGroupTitle(FilterOptionData.NEWS_HOT_CITY, "地区选择", CommonApiConstants.NO_TEXT,
        true));
    mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
      UserUtils.getUserSelectedCityId());
  }

  @Override
  public void onClick(View view) {
    if (view.getId() == R.id.tv_reset) {
      if (OnMoreOrConfirmClickListener != null) {
        OnMoreOrConfirmClickListener.onResetClick();
      }
    } else {
      if (OnMoreOrConfirmClickListener != null) {
        OnMoreOrConfirmClickListener.onConfirmClick();
      }
    }
  }

  private OnMoreOrConfirmClickListener OnMoreOrConfirmClickListener;

  public void setOnMoreOrConfirmClickListener(
    OnMoreOrConfirmClickListener onMoreOrConfirmClickListener) {
    OnMoreOrConfirmClickListener = onMoreOrConfirmClickListener;
  }

  @Override
  public void getNewsTypeTwoSuccess(List<FilterOptionData> filterOptionData) {
    mDataList.add(new FilterGroupTitle("信息类型"));
    mDataList.add(new FilterOptionsGroup(FilterOptionData.NEWS_TYPE, filterOptionData));
    mMultiTypeAdapter.notifyDataSetChanged();
  }

  private boolean isFirstLoad = true;

  @Override
  public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {
    if (isFirstLoad) {
      ((FilterGroupTitle) mDataList.get(0)).setMoreTitle(UserUtils.getUserSelectedCityName());
      optionsDataList.add(0,
        new AreaOptionsData(UserUtils.getUserSelectedCityId(), CommonApiConstants.GET_CITY_TYPE,
          new StringBuffer("全").append(UserUtils.getUserSelectedCityName()).toString()));
      mDataList.add(new FilterOptionsGroup(FilterOptionData.NEWS_HOT_CITY, optionsDataList));
      if (getArguments().getBoolean(NEED_TYPE)) {
        mNewsTypeTwoPresenter.getNewsTypeTwo();
      } else {
        mMultiTypeAdapter.notifyDataSetChanged();
      }
      isFirstLoad = false;
    } else {
      //size等于1时说明是香港、澳门等特殊城市,不需要添加全xx
      if (optionsDataList.size() != 1) {
        mCityData.setName(new StringBuilder("全").append(mCityData.getName()).toString());
        optionsDataList.add(0, mCityData);
      }
      mMultiTypeAdapter.replace(1,
        new FilterOptionsGroup(FilterOptionData.NEWS_HOT_CITY, optionsDataList));
    }
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override public void showLoading(String text) {

  }

  private void bindView(View bindSource) {
    llFilterBottomBar = bindSource.findViewById(R.id.ll_filter_bottom_bar);
  }

  public interface OnMoreOrConfirmClickListener {
    //点击更多时
    void onMoreClick(int type);

    //点击选项
    void onItemOptionClick(int type, int position);

    //点击确定
    void onConfirmClick();

    //点击重置
    void onResetClick();
  }

  private AreaOptionsData mCityData;

  /**
   * 插入城市
   */
  public void insertCity(AreaOptionsData areaItemData) {
    mCityData = areaItemData;
    ((FilterGroupTitle) mDataList.get(0)).setMoreTitle(areaItemData.getName());
    mMultiTypeAdapter.notifyItemChanged(0);
    mGetAreaListPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE, areaItemData.getId());
  }

  /**
   * 获取分组数据
   *
   * @return 分组数据
   */
  public FilterOptionsGroup getGroupData(int position) {
    return (FilterOptionsGroup) mDataList.get(position * 2 + 1);
  }

  /**
   * 重置选中项
   */
  public void resetSelectedPosition() {
    mFilterNewsItemRecyclerViewBinder.resetSelectPosition();
    mMultiTypeAdapter.notifyDataSetChanged();
  }

  /**
   * 是否显示底部重置取消按钮
   */
  public void setShowBottomBar(boolean need) {
    llFilterBottomBar.setVisibility(need ? View.VISIBLE : View.GONE);
  }

  /**
   *
   */
  public void addGroupTitle(FilterGroupTitle filterGroupTitle) {
    mDataList.add(filterGroupTitle);
  }

  /**
   * 添加分组数据
   *
   * @param tag 分组标记
   * @param data 分组数据
   * @param isFlow 是否流式布局
   * @param span 列数
   */
  public void addGroupData(int tag, List<? extends FilterOption> data, boolean isFlow, int span) {
    mDataList.add(new FilterOptionsGroup(tag, data, isFlow, span));
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    mNewsTypeTwoPresenter.detachView();
    mGetAreaListPresenter.detachView();
  }
}
