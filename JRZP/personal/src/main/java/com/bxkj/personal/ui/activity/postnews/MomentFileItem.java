package com.bxkj.personal.ui.activity.postnews;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.data.GalleryItem;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.releasenews
 * @Description:
 * @date 2019/11/22
 */
public class MomentFileItem implements GalleryItem, Serializable {

  private int fileType;
  private String filePath;
  private String fileUrl;
  private boolean isVideo;
  private boolean isAddItem;

  public static MomentFileItem fromPathAndUrl(String path, String url, boolean isVideo) {
    return new MomentFileItem(path, url, false, isVideo);
  }

  public static MomentFileItem getAddItem() {
    return new MomentFileItem(true);
  }

  private MomentFileItem(boolean isAddItem) {
    this.isAddItem = isAddItem;
  }

  public MomentFileItem(String filePath, String url, boolean isAddItem, boolean isVideo) {
    this.filePath = filePath;
    this.fileUrl = url;
    this.isAddItem = isAddItem;
    this.isVideo = isVideo;
  }

  public String getFilePath() {
    return filePath;
  }

  public void setFilePath(String filePath) {
    this.filePath = filePath;
  }

  public boolean isAddItem() {
    return isAddItem;
  }

  public void setAddItem(boolean addItem) {
    isAddItem = addItem;
  }

  public int getFileType() {
    return fileType;
  }

  public void setFileType(int fileType) {
    this.fileType = fileType;
  }

  public String getFileUrl() {
    return fileUrl;
  }

  public void setFileUrl(String fileUrl) {
    this.fileUrl = fileUrl;
  }

  public boolean isVideo() {
    return isVideo;
  }

  public void setVideo(boolean video) {
    isVideo = video;
  }

  private static final String TAG = "MomentFileItem";

  @NotNull @Override public String getItemUrl() {
    return fileUrl;
  }

  public static class ItemDiffCallBack extends DiffUtil.ItemCallback<MomentFileItem> {

    @Override
    public boolean areItemsTheSame(@NonNull MomentFileItem oldItem,
        @NonNull MomentFileItem newItem) {
      return false;
    }

    @Override
    public boolean areContentsTheSame(@NonNull MomentFileItem oldItem,
        @NonNull MomentFileItem newItem) {
      return oldItem.fileUrl != null && newItem.fileUrl != null && oldItem.fileUrl.equals(
          newItem.fileUrl);
    }
  }
}
