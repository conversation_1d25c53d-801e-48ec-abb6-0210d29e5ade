package com.bxkj.personal.ui.activity.sysrecomendsetting

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.personal.data.source.AccountRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class SysRecommendViewModel @Inject constructor(
    private val mAccountRepo: AccountRepo
) : BaseViewModel() {

    val openPersonalizedPush = MutableLiveData<Boolean>().apply { value = false }

    val switchSuccessEvent = LiveEvent<Unit>()

    fun start() {
        checkOpenHomePush()
    }

    private fun checkOpenHomePush() {
        if (UserUtils.logged()) {
            viewModelScope.launch {
                mAccountRepo.checkOpenPersonalizedPush(getSelfUserID())
                    .handleResult({
                        openPersonalizedPush.value = it
                    })
            }
        } else {
            openPersonalizedPush.value = UserUtils.getPersonalizedPushState()
        }
    }

    fun switchHomePushStatus(open: Boolean) {
        if (UserUtils.logged()) {
            viewModelScope.launch {
                mAccountRepo.switchHomePushStatus(getSelfUserID(), open)
                    .handleResult({
                        switchSuccessEvent.call()
                    }, {
                        checkOpenHomePush()
                        showToast(it.errMsg)
                    })
            }
        } else {
            UserUtils.savePersonalizedPushState(open)
            switchSuccessEvent.call()
        }
    }
}