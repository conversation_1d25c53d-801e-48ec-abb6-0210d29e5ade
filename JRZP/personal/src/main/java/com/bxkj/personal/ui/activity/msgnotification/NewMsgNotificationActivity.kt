package com.bxkj.personal.ui.activity.msgnotification

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.FeedbackMsgItemData
import com.bxkj.personal.databinding.PersonalActivityMsgNotificationBinding
import com.bxkj.personal.ui.activity.msgnotificationcontent.NewMsgNotificationContentActivity

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.msgnotification
 * @Description: 消息通知
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class NewMsgNotificationActivity : BaseDBActivity<PersonalActivityMsgNotificationBinding, MsgNotificationViewModel>() {
    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, NewMsgNotificationActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<MsgNotificationViewModel> = MsgNotificationViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_msg_notification

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        setupNotificationListAdapter()
        viewModel.start()
    }

    private fun setupNotificationListAdapter() {
        val notificationListAdapter = SimpleDBListAdapter<FeedbackMsgItemData>(
          this,
          layout.personal_recycler_feedback_message_item
        )
                .apply {
                  setOnItemClickListener(object :
                    SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                      val msgItem = data[position]
                      startActivity(NewMsgNotificationContentActivity.newIntent(this@NewMsgNotificationActivity, msgItem.cuid, msgItem.author))
                      msgItem.nolookCount = 0
                    }
                  })
                }
        val recyclerNotificationList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        recyclerNotificationList.layoutManager = LinearLayoutManager(this)
        viewModel.listViewModel.setAdapter(notificationListAdapter)
    }
}
