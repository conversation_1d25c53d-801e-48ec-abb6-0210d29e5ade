package com.bxkj.personal.ui.activity.parttimeworkbench.workbench

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.jrzp.support.feature.ui.commonsearch.CommonSearchActivity
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchFlag
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchType.NEWS
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchType.XS_CUSTOMER
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CFragmentWorkbenchBinding
import com.bxkj.personal.ui.activity.parttimeworkbench.customerlist.CustomerListFragment
import com.bxkj.personal.ui.activity.parttimeworkbench.customersearchresult.CustomerSearchResultActivity
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * author:Sanjin
 * date:2025/2/8
 **/
class WorkBenchFragment :
  BaseDBFragment<CFragmentWorkbenchBinding, WorkBenchViewModel>() {

  private val searchLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == Activity.RESULT_OK) {
      it.data?.let { data ->
        val searchContent = data.getStringExtra(CommonSearchActivity.EXTRA_SEARCH_CONTENT)
        val searchFlag = data.getParcelableExtra<SearchFlag>(CommonSearchActivity.EXTRA_RESULT_FLAG)
        startActivity(
          CustomerSearchResultActivity.newIntent(
            requireContext(),
            searchContent,
            searchFlag
          )
        )
      }
    }
  }

  override fun getViewModelClass(): Class<WorkBenchViewModel> =
    WorkBenchViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_fragment_workbench

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    initIndicator()
    setupContentPager()

    viewBinding.ivBack.setOnClickListener {
      parentActivity.finish()
    }

    viewBinding.ivSearch.setOnClickListener {
      searchLauncher.launch(
        CommonSearchActivity.newIntent(
          requireContext(),
          XS_CUSTOMER,
          "输入关键词搜索",
          arrayListOf(
            SearchFlag("1", "公司"),
            SearchFlag("2", "手机"),
            SearchFlag("3", "联系人"),
            SearchFlag("4", "地址"),
          )
        )
      )
    }
  }

  private fun setupContentPager() {
    viewBinding.vpContent.apply {
      adapter = object : FragmentStateAdapter(this@WorkBenchFragment) {
        override fun getItemCount(): Int = 4

        override fun createFragment(position: Int): Fragment {
          return CustomerListFragment.newInstance(position + 1)
        }
      }
    }

    viewBinding.vpContent.attachIndicator(viewBinding.indicatorEnterpriseType)
  }

  private fun initIndicator() {
    viewBinding.indicatorEnterpriseType.navigator = CommonNavigator(requireContext()).apply {
      adapter = MagicIndicatorAdapter(
        arrayOf(
          "待发展企业",
          "收藏企业",
          "有付费记录企业",
          "成功列表"
        )
      ).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    }
  }

  override fun initImmersionBar() {
    statusBarManager.titleBar(viewBinding.clTitleBar).statusBarDarkFont(true, 0.4f).init()
  }

  companion object {
    fun newInstance(): WorkBenchFragment {
      return WorkBenchFragment()
    }
  }
}