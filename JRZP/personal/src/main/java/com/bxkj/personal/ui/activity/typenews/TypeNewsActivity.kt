package com.bxkj.personal.ui.activity.typenews

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import cn.jzvd.JzvdStd
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.R
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.StudyNewsItemData
import com.bxkj.personal.databinding.PersonalActivityTypeNewsBinding
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.JobItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewsItemOnePhotoViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewsItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.QuestionsItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.VideoItemViewBinder

/**
 * @Project: gzgk
 * @Description: 分类资讯
 * @author:45457
 * @date: 2020/3/12
 * @version: V1.
 */
class TypeNewsActivity : BaseDBActivity<PersonalActivityTypeNewsBinding, TypeNewsViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_NEWS_TYPE = "NEWS_TYPE"
    const val EXTRA_NEWS_TYPE_NAME = "NEWS_TYPE_NAME"
    const val EXTRA_IS_STUDY_NEWS = "IS_STUDY_NEWS"

    fun newIntent(
      context: Context,
      newsTypeId: Int,
      newsTypeName: String,
      isStudyInfo: Boolean = false
    ): Intent {
      return Intent(context, TypeNewsActivity::class.java)
        .apply {
          putExtra(EXTRA_NEWS_TYPE, newsTypeId)
          putExtra(EXTRA_NEWS_TYPE_NAME, newsTypeName)
          putExtra(EXTRA_IS_STUDY_NEWS, isStudyInfo)
        }
    }

  }

  override fun getViewModelClass(): Class<TypeNewsViewModel> = TypeNewsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_type_news

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    if (intent.getBooleanExtra(EXTRA_IS_STUDY_NEWS, false)) {
      setupStudyNewsListAdapter()
    } else {
      setupListAdapter()
    }

    viewModel.start(intent)
  }

  private fun setupStudyNewsListAdapter() {
    val studyNewsListAdapter = object :
      SimpleDBListAdapter<StudyNewsItemData>(this, R.layout.personal_recycler_study_news_item) {
      private var lastPosition = 0
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: StudyNewsItemData,
        position: Int
      ) {
        super.convert(holder, viewType, item, position)
        if (position % 15 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
        lastPosition = position
      }
    }.apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          startActivity(
            StudyNewsDetailsActivity.newIntent(
              this@TypeNewsActivity,
              data[position].id
            )
          )
        }
      })
    }
    val recyclerStudyNewsList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerStudyNewsList.layoutManager = LinearLayoutManager(this)
    viewModel.listViewModel.setAdapter(studyNewsListAdapter)
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.iv_left) {
        finish()
      } else {
        if (intent.getBooleanExtra(EXTRA_IS_STUDY_NEWS, false)) {
          startActivity(SearchNewsActivity.newIntent(this))
        } else {
          startActivity(SearchNewsActivity.newIntent(this))
        }
      }
    }
  }

  private fun setupListAdapter() {
    val newListAdapter = object : MultiTypeAdapter(this) {
      var lastPosition: Int = 0
      override fun convert(holder: SuperViewHolder, viewType: Int, item: Any?, position: Int) {
        super.convert(holder, viewType, item, position)
        if (position % 13 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
        lastPosition = position
      }
    }
    newListAdapter.register(NewsItemData::class.java)
      .to(NewsItemViewBinder().apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
          override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
            if (v.id == R.id.tv_author) {
              startActivity(
                GzUserHomeActivity.newIntent(
                  this@TypeNewsActivity,
                  item.userID,
                  item.dwID
                )
              )
            } else {
              startActivity(
                GzNewsDetailsActivity.newIntent(
                  this@TypeNewsActivity,
                  item.id,
                  newsType = item.subTypeName
                )
              )
            }
          }
        }, R.id.tv_author)
      }
        , NewsItemOnePhotoViewBinder()
          .apply {
            setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
              override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
                startActivity(GzNewsDetailsActivity.newIntent(this@TypeNewsActivity, item.id))
              }
            })
          }
        , QuestionsItemViewBinder()
          .apply {
            setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
              override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
                startActivity(QuestionDetailsActivity.newIntent(this@TypeNewsActivity, item.id))
              }
            })
          }
        , VideoItemViewBinder()
          .apply {
            setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
              override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
                startActivity(VideoDetailsActivity.newIntent(this@TypeNewsActivity, item.id))
              }
            })
          }
        , JobItemViewBinder()
          .apply {
            setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
              override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
                if (!item.listJobIDs.isNullOrEmpty()) {
                  startActivity(
                    JobDetailsActivityV2.newIntent(
                      this@TypeNewsActivity,
                      item.listJobIDs.split(",")[0].toInt()
                    )
                  )
                }
              }
            })
          })
      .withClassLinker { _, item ->
        when (item.itemType) {
          0 -> {
            if (!CheckUtils.isNullOrEmpty(item.listMedia) && item.listMedia.size == 1) NewsItemOnePhotoViewBinder::class.java
            else NewsItemViewBinder::class.java
          }
          1 -> {
            QuestionsItemViewBinder::class.java
          }
          3 -> {
            JobItemViewBinder::class.java
          }
          else -> {
            VideoItemViewBinder::class.java
          }
        }
      }
    val recyclerNewsList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    val layoutManager = LinearLayoutManager(this)
    recyclerNewsList.layoutManager = layoutManager
    recyclerNewsList.addOnChildAttachStateChangeListener(object :
      RecyclerView.OnChildAttachStateChangeListener {
      override fun onChildViewDetachedFromWindow(view: View) {
        val jzVideoPlayer = view.findViewById<JzvdStd>(R.id.video_player)
        if (jzVideoPlayer != null && Jzvd.CURRENT_JZVD != null && jzVideoPlayer.jzDataSource != null && jzVideoPlayer.jzDataSource.containsTheUrl(
            Jzvd.CURRENT_JZVD.jzDataSource.currentUrl
          )
        ) {
          if (Jzvd.CURRENT_JZVD != null && Jzvd.CURRENT_JZVD.screen != Jzvd.SCREEN_FULLSCREEN) {
            Jzvd.releaseAllVideos()
          }
        }
      }

      override fun onChildViewAttachedToWindow(view: View) {

      }
    })
    viewModel.listViewModel.setAdapter(newListAdapter)
  }

  override fun onBackPressed() {
    if (Jzvd.backPress()) {
      return
    }
    super.onBackPressed()
  }

  override fun onDestroy() {
    super.onDestroy()
    Jzvd.releaseAllVideos()
  }
}