package com.bxkj.personal.ui.activity.applicationrecord;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.util.DensityUtils;
import com.bxkj.personal.R;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.resume
 * @Description:
 * @TODO: TODO
 * @date 2018/8/10
 */
public class ApplicationRecordNavigatorAdapter extends CommonNavigatorAdapter {

    private ViewPager mViewPager;
    private String[] mItems;
    private int mCurrentIndex;

    public ApplicationRecordNavigatorAdapter(ViewPager viewPager, String[] items) {
        mViewPager = viewPager;
        mItems = items;
    }

    @Override
    public int getCount() {
        return mItems.length;
    }

    @Override
    public IPagerTitleView getTitleView(Context context, int index) {
        CommonPagerTitleView commonPagerTitleView = new CommonPagerTitleView(context);
        View customView = LayoutInflater.from(context).inflate(R.layout.personal_layout_custom_tab, null);
        TextView tvTabItem = customView.findViewById(R.id.tv_tab_item);
        tvTabItem.setText(mItems[index]);
        ImageView ivExpand = customView.findViewById(R.id.iv_expand);
        if (index == 3) {
            ivExpand.setVisibility(View.VISIBLE);
        }
        commonPagerTitleView.setContentView(customView);
        commonPagerTitleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {
            @Override
            public void onSelected(int i, int i1) {
                tvTabItem.setSelected(true);
                ivExpand.setSelected(true);
            }

            @Override
            public void onDeselected(int i, int i1) {
                tvTabItem.setSelected(false);
                ivExpand.setSelected(false);
            }

            @Override
            public void onLeave(int i, int i1, float v, boolean b) {

            }

            @Override
            public void onEnter(int i, int i1, float v, boolean b) {

            }
        });
        commonPagerTitleView.setOnClickListener(view -> {
            mViewPager.setCurrentItem(index);
            if (mOnTabItemClickListener != null) {
                mOnTabItemClickListener.onTabClicked(commonPagerTitleView, index);
            }
            mCurrentIndex = index;
        });
        commonPagerTitleView.setContentPositionDataProvider(new CommonPagerTitleView.ContentPositionDataProvider() {
            @Override
            public int getContentLeft() {
                Rect bound = new Rect();
                tvTabItem.getPaint().getTextBounds(tvTabItem.getText().toString(), 0, tvTabItem.getText().length(), bound);
                int contentWidth = bound.width();
                return tvTabItem.getLeft() + tvTabItem.getWidth() / 2 - contentWidth / 2;
            }

            @Override
            public int getContentTop() {
                Paint.FontMetrics metrics = tvTabItem.getPaint().getFontMetrics();
                float contentHeight = metrics.bottom - metrics.top;
                return (int) ((tvTabItem.getHeight() / 2) - contentHeight / 2);
            }

            @Override
            public int getContentRight() {
                Rect bound = new Rect();
                tvTabItem.getPaint().getTextBounds(tvTabItem.getText().toString(), 0, tvTabItem.getText().length(), bound);
                int contentWidth = bound.width();
                return tvTabItem.getLeft() + tvTabItem.getWidth() / 2 + contentWidth / 2;
            }

            @Override
            public int getContentBottom() {
                Paint.FontMetrics metrics = tvTabItem.getPaint().getFontMetrics();
                float contentHeight = metrics.bottom - metrics.top;
                return (int) ((tvTabItem.getHeight() / 2) - contentHeight / 2);
            }
        });
        return commonPagerTitleView;
    }

    @Override
    public IPagerIndicator getIndicator(Context context) {
        LinePagerIndicator indicator = new LinePagerIndicator(context);
        indicator.setMode(LinePagerIndicator.MODE_EXACTLY);
        indicator.setLineHeight(DensityUtils.dp2px(context, 3));
        indicator.setLineWidth(DensityUtils.dp2px(context, 10));
        indicator.setColors(ContextCompat.getColor(context, R.color.cl_ff7405));
        return indicator;
    }

    public interface OnTabItemClickListener {
        void onTabClicked(View view, int index);
    }

    private OnTabItemClickListener mOnTabItemClickListener;

    public void setOnTabItemClickListener(OnTabItemClickListener onTabItemClickListener) {
        mOnTabItemClickListener = onTabItemClickListener;
    }

    public int getCurrentIndex() {
        return mCurrentIndex;
    }
}
