package com.bxkj.personal.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.video.api.BR

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * <AUTHOR>
 * @date 2019/10/14
 * @version V1.0
 */
data class JobFairItemData(
  var id: Int,
  var title: String,
  var content: String,
  var date: String,
  var startTime: String,
  var endTime: String,
  var pic: String,
  @get:Bindable
  var count: Int,
  var commentsCount: Int,
  var comName: String
) : BaseObservable() {

  fun getTimeRange(): String {
    if (startTime == "00-00" && endTime == "00-00") {
      return ""
    }
    return "${startTime}-${endTime}"
  }

  fun addView() {
    count += 1
    notifyPropertyChanged(BR.count)
  }
}
