package com.bxkj.personal.data

import com.bxkj.common.util.kotlin.appendItem

/**
 *
 * @author: sanjin
 * @date: 2022/4/13
 */
data class SchoolRecruitRecordData(
    var comName: String? = null,
    var cuid: Int,
    var email: String? = null,
    var id: Int?,
    var isPayShangjin: Int?,
    var mobile: List<String>? = null,
    var name: String? = null,
    var newsTitle: String? = null,
    var nid: Int?,
    var tel: List<String>? = null,
    var time: String? = null
) {

    fun getMobileText(): String {
        return mobile.appendItem(",")
    }

    fun getTelText(): String {
        return tel.appendItem(",")
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SchoolRecruitRecordData

        if (comName != other.comName) return false
        if (cuid != other.cuid) return false
        if (email != other.email) return false
        if (id != other.id) return false
        if (isPayShangjin != other.isPayShangjin) return false
        if (mobile != other.mobile) return false
        if (name != other.name) return false
        if (newsTitle != other.newsTitle) return false
        if (nid != other.nid) return false
        if (tel != other.tel) return false
        if (time != other.time) return false

        return true
    }

    override fun hashCode(): Int {
        var result = comName?.hashCode() ?: 0
        result = 31 * result + cuid
        result = 31 * result + (email?.hashCode() ?: 0)
        result = 31 * result + (id ?: 0)
        result = 31 * result + (isPayShangjin ?: 0)
        result = 31 * result + (mobile?.hashCode() ?: 0)
        result = 31 * result + (name?.hashCode() ?: 0)
        result = 31 * result + (newsTitle?.hashCode() ?: 0)
        result = 31 * result + (nid ?: 0)
        result = 31 * result + (tel?.hashCode() ?: 0)
        result = 31 * result + (time?.hashCode() ?: 0)
        return result
    }


}