package com.bxkj.personal.data

import com.bxkj.common.util.CheckUtils

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/14
 * @version: V1.0
 */
data class SearchJobResultItemData(
        var id: Int,
        var title: String,
        var money: String,
        var wtName: String,
        var countyID: Int,
        var countyName: String,
        var cityID: Int,
        var cityName: String,
        var quaName: String,
        var date: String,
        var companyID: Int,
        var companyName: String
) {
    fun getPublishDate(): String {
        return date.split("\\s")[0]
    }

    fun getEduText(): String = if (CheckUtils.isNullOrEmpty(quaName)) "学历不限" else quaName

    fun getExpText(): String = if (CheckUtils.isNullOrEmpty(wtName)) "经验不限" else wtName
}