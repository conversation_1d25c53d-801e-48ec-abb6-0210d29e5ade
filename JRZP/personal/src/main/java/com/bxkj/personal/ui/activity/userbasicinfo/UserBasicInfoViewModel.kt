package com.bxkj.personal.ui.activity.userbasicinfo

import android.app.Activity.RESULT_OK
import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.schoolinfo.data.SchoolAttachInfoData
import com.bxkj.jrzp.user.schoolinfo.ui.bindschoolattachinfo.BindSchoolAttachInfoNavigation
import com.bxkj.personal.R
import com.bxkj.personal.data.UserBasicInfoData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.AddressInfoRepo
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import javax.inject.Inject

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.userbasicinfo
 * @Description:
 * <AUTHOR>
 * @date 2019/12/7
 * @version V1.0
 */
class UserBasicInfoViewModel @Inject constructor(
    private val mAccountRepo: AccountRepo,
    private val mAddressInfoRepo: AddressInfoRepo
) : BaseViewModel() {

    val userBasicInfo = MutableLiveData<UserBasicInfoData>()
    val updateUserInfoSuccessEvent =
        LiveEvent<UserBasicInfoData>()
    val provinceList = MutableLiveData<List<AreaOptionsData>>()
    val cityList = MutableLiveData<List<AreaOptionsData>>()
    val showAvatarPath = MutableLiveData<String>()
    val showInfoChangeTipsCommand = LiveEvent<Boolean>()

    val toSelectSchoolCommand = LiveEvent<String>()
    val toSelectDepartmentCommand = LiveEvent<Int>()
    val toSelectProfessionCommand = LiveEvent<Int>()

    private var oldUserData: UserBasicInfoData? = null

    private val _registerMode = MutableLiveData<Boolean>().apply { value = false }
    val registerMode: LiveData<Boolean> = _registerMode

    init {
        getUserBasicInfo()
    }

    fun start(intentExtraFromPage: Int) {
        _registerMode.value =
            (intentExtraFromPage == UserBasicInfoNavigation.FROM_REGISTER)
    }

    private fun getUserBasicInfo() {
        showLoading()
        mAccountRepo.getUserBasicInfo1(
            getSelfUserID(),
            object : ResultDataCallBack<UserBasicInfoData> {
                override fun onSuccess(data: UserBasicInfoData) {
                    oldUserData = UserBasicInfoData()
                    hideLoading()
                    if (CheckUtils.isNullOrEmpty(data.photo)) {
                        if (data.sex == 1) {
                            showAvatarPath.value = UserBasicInfoActivity.WOMAN_DEFAULT_AVATAR
                        } else {
                            showAvatarPath.value = UserBasicInfoActivity.MAN_DEFAULT_AVATAR
                        }
                    } else {
                        showAvatarPath.value = data.photo
                    }
                    userBasicInfo.value = data
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    oldUserData = UserBasicInfoData()
                    userBasicInfo.value = UserBasicInfoData().apply {
                        photo = UserBasicInfoActivity.MAN_DEFAULT_AVATAR
                    }
                    showAvatarPath.value = UserBasicInfoActivity.MAN_DEFAULT_AVATAR
                    if (respondThrowable.errCode != 30002 && respondThrowable.errCode != 30001) {
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
    }

    /**
     * 初始化省份列表
     */
    fun initAddressPickerProvinceList() {
        mAddressInfoRepo.getAddressList(
            CommonApiConstants.GET_PROVINCE_TYPE,
            CommonApiConstants.NO_ID,
            object : ResultDataCallBack<List<AreaOptionsData>> {
                override fun onSuccess(data: List<AreaOptionsData>?) {
                    provinceList.value = data
                    getCityListByProvinceIndex(0)
                }

                override fun onError(respondThrowable: RespondThrowable) {

                }
            })
    }

    fun getCityListByProvinceIndex(index: Int) {
        provinceList.value?.let {
            mAddressInfoRepo.getAddressList(
                CommonApiConstants.GET_CITY_TYPE,
                it[index].id,
                object : ResultDataCallBack<List<AreaOptionsData>> {
                    override fun onSuccess(data: List<AreaOptionsData>) {
                        cityList.value = data
                    }

                    override fun onError(respondThrowable: RespondThrowable) {

                    }
                })
        }
    }

    fun toSelectSchool() {
        userBasicInfo.value?.let {
            toSelectSchoolCommand.value = it.gxName.getOrDefault()
        }
    }

    fun toSelectDepartment() {
        userBasicInfo.value?.let {
            if (it.noSchoolInfo()) {
                showToast("请先选择院校")
            } else {
                toSelectDepartmentCommand.value = it.gxId
            }
        }
    }

    fun getDepartmentName(): String {
        return userBasicInfo.value?.yxName.getOrDefault()
    }

    fun toSelectProfession() {
        userBasicInfo.value?.let {
            if (it.noDepartmentInfo()) {
                showToast("请先选择院系")
            } else {
                toSelectProfessionCommand.value = it.yxId
            }
        }
    }

    fun getProfessionName(): String {
        return userBasicInfo.value?.zyName.getOrDefault()
    }

    fun setUserSex(sex: Int) {
        userBasicInfo.value?.let {
            if (CheckUtils.isNullOrEmpty(it.photo)) {
                if (sex == 1) {
                    showAvatarPath.value = UserBasicInfoActivity.WOMAN_DEFAULT_AVATAR
                } else {
                    showAvatarPath.value = UserBasicInfoActivity.MAN_DEFAULT_AVATAR
                }
                it.photo = showAvatarPath.value
            }
            it.sex = sex
        }
    }

    fun setAddressIndex(provinceIndex: Int, cityIndex: Int) {
        userBasicInfo.value?.let { info ->
            provinceList.value?.let {
                info.provinceID = it[provinceIndex].id
                info.provinceName = it[provinceIndex].name
            }
            cityList.value?.let {
                info.cityID = it[cityIndex].id
                info.cityName = it[cityIndex].name
            }
        }
    }

    fun setUserEducation(name: String, id: Int) {
        userBasicInfo.value?.let {
            it.eduName = name
            it.eduCode = id
        }
    }

    fun setUserGTime(gTime: String) {
        userBasicInfo.value?.let {
            it.graduationTime = gTime
        }
    }

    fun setUserBirthday(birthday: String) {
        userBasicInfo.value?.let {
            it.birthDay = birthday
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == RESULT_OK && data != null) {
            when (requestCode) {
                PictureConfig.CHOOSE_REQUEST -> {
                    val resultFileList = PictureSelector.obtainSelectorList(data)
                    resultFileList[0]?.let {
                        val finalPath = ZPFileUtils.getPictureSelectorPath(it)
                        showAvatarPath.value = finalPath
                        userBasicInfo.value?.let { userInfo ->
                            userInfo.photo =
                                CommonApiConstants.IMG_UPLOAD_PREFIX + ZPFileUtils.bitmapToString(
                                    finalPath,
                                    0.1f
                                )
                        }
                    }
                }
                UserBasicInfoActivity.TO_SELECT_UNIVERSITY_CODE -> {
                    val selectedSchool =
                        data.getParcelableExtra<SchoolAttachInfoData>(BindSchoolAttachInfoNavigation.EXTRA_RESULT_ATTACH_INFO)
                    selectedSchool?.let {
                        userBasicInfo.value?.let {
                            it.gxId = selectedSchool.id
                            it.gxName = selectedSchool.name
                            it.yxId = CommonApiConstants.NO_ID
                            it.yxName = ""
                            it.zyId = CommonApiConstants.NO_ID
                            it.zyName = ""
                        }
                    }
                }
                UserBasicInfoActivity.TO_SELECT_DEPARTMENT_CODE -> {
                    val selectedDepartment =
                        data.getParcelableExtra<SchoolAttachInfoData>(BindSchoolAttachInfoNavigation.EXTRA_RESULT_ATTACH_INFO)
                    selectedDepartment?.let {
                        userBasicInfo.value?.let {
                            it.yxId = selectedDepartment.id
                            it.yxName = selectedDepartment.name
                            it.zyId = CommonApiConstants.NO_ID
                            it.zyName = ""
                        }
                    }
                }
                UserBasicInfoActivity.TO_SELECT_PROFESSION_CODE -> {
                    val selectedPermission =
                        data.getParcelableExtra<SchoolAttachInfoData>(BindSchoolAttachInfoNavigation.EXTRA_RESULT_ATTACH_INFO)
                    selectedPermission?.let {
                        userBasicInfo.value?.let {
                            it.zyId = selectedPermission.id
                            it.zyName = selectedPermission.name
                        }
                    }
                }
            }
        }
    }

    fun submit() {
        userBasicInfo.value?.let {
            if (infoIsCompleted(it)) {
                it.userID = getSelfUserID()
                showLoading()
                mAccountRepo.updateUserBasicInfo(it, object : ResultCallBack {
                    override fun onSuccess() {
                        hideLoading()
                        updateUserInfoSuccessEvent.value = it
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        hideLoading()
                        showToast(respondThrowable.errMsg)
                    }
                })
            }
        }
    }

    fun checkNeedShowInfoChangeDialog() {
        userBasicInfo.value?.let {
            showInfoChangeTipsCommand.value = (it != oldUserData)
            return
        }
        showInfoChangeTipsCommand.value = false
    }

    /**
     * 检查个人信息是否完善
     */
    private fun infoIsCompleted(it: UserBasicInfoData): Boolean {
        when {
            CheckUtils.isNullOrEmpty(it.name) -> {
                showToast(R.string.user_info_name_not_be_null)
                return false
            }
            CheckUtils.isNullOrEmpty(it.nickName) -> {
                showToast(R.string.user_info_nike_name_not_be_null)
                return false
            }
            CheckUtils.isNullOrEmpty(it.birthDay) -> {
                showToast(R.string.user_info_birthday_not_be_null)
                return false
            }
            it.cityID == CommonApiConstants.NO_ID -> {
                showToast(R.string.user_info_address_not_be_null)
                return false
            }
            it.eduCode == CommonApiConstants.NO_ID -> {
                showToast(R.string.user_info_education_not_be_null)
                return false
            }
//            _registerMode.value == false && CheckUtils.isNullOrEmpty(it.graduationTime) -> {
//                showToast(R.string.user_info_gtime_not_be_null)
//                return false
//            }
            else -> return true
        }
    }

    fun resetUserSchoolInfo() {
        userBasicInfo.value?.resetSchoolInfo()
    }
}