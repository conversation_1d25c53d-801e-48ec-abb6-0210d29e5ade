package com.bxkj.personal.ui.activity.shieldcompany

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.personal.data.ShieldCompanyData

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/6/11
 * @version: V1.0
 */
class ShieldCompanyListAdapter constructor(
  context: Context,
  private val viewModel: ShieldCompanyViewModel
) :
  SimpleDBListAdapter<ShieldCompanyData>(
    context,
    R.layout.personal_recycler_shield_company_item
  ) {

  private var showSelect = false
  private var mSelectedAll = false

  override fun convert(holder: SuperViewHolder, viewType: Int, item: ShieldCompanyData, position: Int) {
    super.convert(holder, viewType, item, position)
    val ivSelect = holder.findViewById<ImageView>(R.id.iv_checked)
    val ivDelete = holder.findViewById<ImageView>(R.id.iv_delete)
    ivSelect.visibility = if (showSelect) View.VISIBLE else View.GONE
    ivSelect.isSelected = viewModel.getSelectedCompany().contains(item)
    ivDelete.visibility = if (showSelect) View.GONE else View.VISIBLE
    holder.itemView.setOnClickListener {
      if (showSelect) {
        if (viewModel.getSelectedCompany().contains(item)) {
          viewModel.getSelectedCompany().remove(item)
        } else {
          viewModel.getSelectedCompany().add(item)
        }
        notifySelectedChange()
      }
    }
  }

  fun switchSelectState(open: Boolean) {
    showSelect = open
    notifyDataSetChanged()
  }

  fun switchSelectedAll() {
    mSelectedAll = mSelectedAll.not()
    viewModel.getSelectedCompany().clear()
    if (mSelectedAll) {
      viewModel.getSelectedCompany().addAll(data)
    }
    notifySelectedChange()
  }

  private fun notifySelectedChange() {
    mSelectedAll = viewModel.getSelectedCompany().size == data.size
    viewModel.setSelectAll(mSelectedAll)
    notifyDataSetChanged()
    viewModel.notifySelectedChange()
  }
}