package com.bxkj.personal.ui.activity.signupuser

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import com.bxkj.personal.R
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.personal.databinding.PersonalActivitySignUpUserBinding
import com.bxkj.personal.ui.fragment.signupuser.SignUpUserFragment
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView

/**
 * @Project: gzgk
 * @Description: 已报名用户
 * @author:45457
 * @date: 2020/3/27
 * @version: V1.0
 */
class SignUpUserActivity :
  BaseDBActivity<PersonalActivitySignUpUserBinding, SignUpUserViewModel>() {

  companion object {
    const val EXTRA_VIDEO_ID = "VIDEO_ID"

    fun newIntent(context: Context, videoId: Int): Intent {
      return Intent(context, SignUpUserActivity::class.java)
        .apply {
          putExtra(EXTRA_VIDEO_ID, videoId)
        }
    }
  }

  override fun getViewModelClass(): Class<SignUpUserViewModel> = SignUpUserViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_sign_up_user

  override fun initPage(savedInstanceState: Bundle?) {
    setupViewPager()
    setupIndicator()
  }

  private fun setupViewPager() {
    val videoId = intent.getIntExtra(EXTRA_VIDEO_ID, CommonApiConstants.NO_ID);

    val signUpUserChildPager =
      CommonPagerAdapter(
        supportFragmentManager
        , arrayListOf(
          SignUpUserFragment.newInstance(videoId, SignUpUserFragment.SIGN_UP_TYPE_PENDING)
          , SignUpUserFragment.newInstance(videoId, SignUpUserFragment.SIGN_UP_TYPE_PASSED)
          , SignUpUserFragment.newInstance(videoId, SignUpUserFragment.SIGN_UP_TYPE_OUT)
        )
      )
    viewBinding.vpContent.adapter = signUpUserChildPager
    viewBinding.vpContent.offscreenPageLimit = 3
  }

  private fun setupIndicator() {
    val commonNavigator = CommonNavigator(this)
    commonNavigator.isAdjustMode = true
    commonNavigator.adapter =
      object : MagicIndicatorAdapter(resources.getStringArray(R.array.sign_up_user_type)) {
        override fun getTitleView(context: Context, index: Int): IPagerTitleView {
          val pageTitleView =
            ScalePagerTitleView(context)
          pageTitleView.textSize = 16f
          pageTitleView.normalColor = ContextCompat.getColor(context, R.color.cl_333333)
          pageTitleView.selectedColor = ContextCompat.getColor(context, R.color.cl_333333)
          pageTitleView.text = getTitles()[index]
          pageTitleView.setOnClickListener { view ->
            getOnTabClickListener()?.onTabClicked(view, index)
          }
          return pageTitleView
        }
      }.apply {
        setOnTabClickListener(object :
          OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    viewBinding.indicator.navigator = commonNavigator
    ViewPagerHelper.bind(viewBinding.indicator, viewBinding.vpContent)
  }

  fun refreshPassedPage() {
    viewModel.refreshPassedPageCommand.call()
  }

  fun refreshOutPage() {
    viewModel.refreshOutPageCommand.call()
  }
}