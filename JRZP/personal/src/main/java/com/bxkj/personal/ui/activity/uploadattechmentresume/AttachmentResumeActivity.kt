package com.bxkj.personal.ui.activity.uploadattechmentresume

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.jrzp.support.feature.ui.filepreview.FilePreviewActivity
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CActivityAttachmentResumeBinding
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.hjq.permissions.Permission
import com.therouter.router.Route

/**
 * Description: 附件简历
 * Author:Sanjin
 * Date:2024/4/19
 **/
@Route(path = AttachmentResumeNavigation.PATH)
class AttachmentResumeActivity :
  BaseDBActivity<CActivityAttachmentResumeBinding, AttachmentResumeViewModel>(),
  OnClickListener {
  private val _selectResumeFileLauncher =
    registerForActivityResult(ActivityResultContracts.GetContent()) {
      it?.let { uri ->
        val filePath = ZPFileUtils.getFileAbsolutePath(this, uri)
        if (filePath == null) {
          showToast("简历读取失败")
        } else {
          viewModel.uploadPreCheck(filePath)
        }
      }
    }

  private val _completeResumeLauncher =
    registerForActivityResult(StartActivityForResult()) {
      viewModel.start()
    }

  override fun getViewModelClass(): Class<AttachmentResumeViewModel> = AttachmentResumeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_activity_attachment_resume

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.hasResume.observe(
      this,
      Observer {
        if (it) {
          viewBinding.titleBar.setRightText("删除")
          viewBinding.titleBar.setRightOptionClickListener {
            viewModel.deleteAttachmentResume()
          }
        } else {
          viewBinding.titleBar.setShowRight(false)
        }
      },
    )

    viewModel.showConfirmUploadDialog.observe(
      this,
      EventObserver { filePath ->
        ActionDialog
          .Builder()
          .setTitle(getString(R.string.attachment_resume_upload_this))
          .setOnConfirmClickListener {
            viewModel.uploadAttachmentResume(filePath)
          }.build()
          .show(supportFragmentManager, ActionDialog.TAG)
      },
    )

    viewModel.previewResumeCommand.observe(
      this,
      EventObserver { url ->
        startActivity(FilePreviewActivity.newIntent(this, url, "简历预览"))
      },
    )

    viewModel.showCompleteResumeTipsCommand.observe(
      this,
      EventObserver {
        ActionDialog
          .Builder()
          .setCancelable(false)
          .setContent("请先完善简历基本信息")
          .setConfirmText("去完善")
          .setOnCancelClickListener {
            finish()
          }.setOnConfirmClickListener {
            it.dismiss()
            _completeResumeLauncher.launch(MicroResumeInfoNavigation.create().createIntent(this))
          }.build()
          .show(supportFragmentManager)
      },
    )

    viewModel.uploadSuccessCommand.observe(
      this,
      EventObserver {
        val intent = Intent()
        intent.putExtra(FILE_WEB_URL, it)
        setResult(RESULT_HAS_ATTACHMENT, intent)
      },
    )
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.tv_upload_by_phone -> {
          PermissionUtils.requestPermission(
            this,
            getString(R.string.permission_tips_title),
            "上传附件简历需要读取存储权限",
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                val intent = Intent(Intent.ACTION_GET_CONTENT)
                intent.type = "*/*"
                intent.addCategory(Intent.CATEGORY_OPENABLE)
                _selectResumeFileLauncher.launch("*/*")
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE,
          )
        }
      }
    }
  }

  companion object {
    const val FILE_WEB_URL = "web_url"
    const val RESULT_HAS_ATTACHMENT = 10
    const val RESULT_NO_ATTACHMENT = 11
  }
}
