package com.bxkj.personal.ui.activity.qaranklist

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R.id
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.QARankItemData
import com.bxkj.personal.databinding.PersonalActivityQaRankListBinding

/**
 * @Project: gzgk
 * @Description: 问答榜单
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
class QARankListActivity :
  BaseDBActivity<PersonalActivityQaRankListBinding, QARankListViewModel>() {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, QARankListActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<QARankListViewModel> = QARankListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_qa_rank_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupQARankListAdapter()

    viewModel.start()
  }

  private fun setupQARankListAdapter() {
    val qaRankListAdapter =
      SimpleDBListAdapter<QARankItemData>(
        this,
        layout.personal_recycler_qa_rank_list_item
      ).apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            val clickItem = data[position]
            if (v.id == id.tv_follow) {
              viewModel.followOrUnFollow(clickItem)
            } else {
              UserHomeNavigation.navigate(
                clickItem.userID,
                targetTab = UserInfoNavigationData.NAVIGATION_QA
              ).start()
            }
          }
        }, id.tv_follow)
      }
    val recyclerQARankList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerQARankList.layoutManager = LinearLayoutManager(this)
    viewModel.qaRankListViewModel.setAdapter(qaRankListAdapter)
  }
}