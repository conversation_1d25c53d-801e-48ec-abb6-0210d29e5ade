package com.bxkj.personal.ui.activity.fans

import android.content.Intent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.FansItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.fans
 * @Description:
 * <AUTHOR>
 * @date 2019/12/10
 * @version V1.0
 */
class FansViewModel @Inject constructor(
  private val mAccountRepo: AccountRepo
) : BaseViewModel() {
  val listViewModel = RefreshListViewModel()

  fun start(intent: Intent) {
    setupListViewModel(
      intent.getIntExtra(FansNavigation.EXTRA_QUERY_TYPE, FansNavigation.QUERY_TYPE_PERSONAL),
      intent.getIntExtra(FansNavigation.EXTRA_QUERY_ID, CommonApiConstants.NO_DATA)
    )
    listViewModel.refresh()
  }

  private fun setupListViewModel(queryType: Int, queryId: Int) {
    listViewModel.setOnLoadDataListener { currentPage ->
      mAccountRepo.getUserFansList(queryType, queryId, getSelfUserID(),
        currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE,
        object : ResultDataCallBack<List<FansItemData>> {
          override fun onSuccess(data: List<FansItemData>?) {
            listViewModel.autoAddAll(data)
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.isNoDataError) {
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          }
        })
    }
  }

  /**
   * 添加或取消关注
   */
  fun addOrRemoveFollow(fansItemData: FansItemData) {
    if (checkLoginStateAndToLogin()) {
      mAccountRepo.addOrRemoveFollow(getSelfUserID(), PersonalApiConstants.FOLLOW_USER_TYPE,
        fansItemData.id, object : ResultCallBack {
          override fun onSuccess() {
            fansItemData.addFollow()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 10002) {
              fansItemData.removeFollow()
            } else {
              showToast(respondThrowable.errMsg)
            }
          }
        })
    }
  }

}