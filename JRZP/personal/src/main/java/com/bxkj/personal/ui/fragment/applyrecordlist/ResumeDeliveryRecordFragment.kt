package com.bxkj.personal.ui.fragment.applyrecordlist

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.ResumeDeliveryRecordBean
import com.bxkj.personal.databinding.CFragmentResumeDeliveryRecordBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2

/**
 * Description: 简历投递记录
 * Author:Sanjin
 * Date:2024/4/22
 **/
class ResumeDeliveryRecordFragment :
  BaseDBFragment<CFragmentResumeDeliveryRecordBinding, ResumeDeliveryRecordViewModel>() {
  override fun getViewModelClass(): Class<ResumeDeliveryRecordViewModel> =
    ResumeDeliveryRecordViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_fragment_resume_delivery_record
  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    viewBinding.rgDeliveryState.setOnCheckedChangeListener { group, checkedId ->
      when (checkedId) {
        R.id.rb_state_1 -> viewModel.filterState("-1")
        R.id.rb_state_2 -> viewModel.filterState("1")
        R.id.rb_state_3 -> viewModel.filterState("2")
      }
    }

    setupDeliveryRecordListAdapter()

    viewModel.start(requireArguments().getInt(EXTRA_JOB_TYPE))
  }

  private fun setupDeliveryRecordListAdapter() {
    viewBinding.includeJobList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_8))
          .drawHeader(true)
          .drawFoot(true)
          .build()
      )
    }

    val listAdapter =
      object : SimpleDBListAdapter<ResumeDeliveryRecordBean>(
        requireContext(),
        layout.c_recycler_resume_delivery_record_item
      ) {
        override fun convert(
          holder: SuperViewHolder,
          viewType: Int,
          item: ResumeDeliveryRecordBean,
          position: Int
        ) {
          super.convert(holder, viewType, item, position)
          holder.findViewById<TextView>(R.id.tv_application).visibility = View.GONE
        }
      }.apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data[position]?.let {
              startActivity(JobDetailsActivityV2.newIntent(requireContext(), it.relid))
            }
          }
        })
      }

    viewModel.listViewModel.setAdapter(listAdapter)
  }

  companion object {

    const val EXTRA_JOB_TYPE = "job_type"

    const val TYPE_FULL_TIME = 1
    const val TYPE_PART_TIME = 2
    fun newInstance(jobType: Int) = ResumeDeliveryRecordFragment().apply {
      arguments = bundleOf(EXTRA_JOB_TYPE to jobType)
    }
  }
}