package com.bxkj.personal.ui.activity.selectaddressbymap

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.common.widget.adresspickerdialog.AddressData
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/5
 * @version: V1.0
 */
class SelectAddressNavigation {
  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/selectaddress"

    const val ADDRESS_DATA = "address_data"
    const val DETAILS_ADDRESS = "details_address"
    const val EXTRA_SELECT_LEVEL = "SELECT_LEVEL"

    const val EXTRA_SHOW_MAP = "SHOW_MAP"

    fun navigate(
      showMap: Boolean,
      address: AddressData,
      detailsAddress: String,
      selectLevel: Int
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withBoolean(EXTRA_SHOW_MAP, showMap)
        .withParcelable(ADDRESS_DATA, address)
        .withString(DETAILS_ADDRESS, detailsAddress)
        .withInt(EXTRA_SELECT_LEVEL, selectLevel)
    }
  }
}