package com.bxkj.personal.ui.activity.inviteuser

import com.bxkj.common.di.scope.PerFragment
import com.bxkj.personal.ui.fragment.inviteuser.InviteUserFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.inviteuser
 * @Description:
 * <AUTHOR>
 * @date 2020/2/26
 * @version V1.0
 */
@Module
abstract class InviteUserModule {

    @PerFragment
    @ContributesAndroidInjector
    abstract fun inviteUserFragment():InviteUserFragment
}