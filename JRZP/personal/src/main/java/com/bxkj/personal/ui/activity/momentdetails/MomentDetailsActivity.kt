package com.bxkj.personal.ui.activity.momentdetails

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import com.therouter.router.Route
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.qmui.QMUIKeyboardHelper
import com.bxkj.common.util.qmui.QMUIKeyboardHelper.KeyboardVisibilityEventListener
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.InputDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.jrzp.support.comment.CommentInfoType
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.data.CommentItemData.NoCommentData
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyActivity
import com.bxkj.jrzp.userhome.data.UserMomentData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.MomentDetailsData
import com.bxkj.personal.databinding.PersonalActivityMomentDetailsBinding
import com.bxkj.personal.ui.activity.gallery.GalleryActivity
import com.bxkj.personal.ui.activity.integralrecharge.IntegralRechargeActivity
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation
import com.bxkj.personal.ui.activity.momentdetails.ContentViewBinder.OnJobItemClickListener
import com.bxkj.personal.ui.activity.paiduser.PaidUserActivity
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.momentdetails
 * @Description:
 * <AUTHOR>
 * @date 2019/11/23
 * @version V1.0
 */
@Route(path = MomentDetailsNavigation.PATH)
class MomentDetailsActivity :
  BaseDBActivity<PersonalActivityMomentDetailsBinding, MomentDetailsViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_CHANGE_INDEX = "index"
    const val RESULT_DATA_CHANGE = Activity.RESULT_FIRST_USER + 1
    const val RESULT_DELETED = Activity.RESULT_FIRST_USER + 2

    const val TO_COMMENT_REPLY_CODE = 1
    const val TO_USER_HOME_CODE = 2

    private const val COMMENT_POSITION = 1

    fun newIntent(context: Context, index: Int, moment: UserMomentData): Intent {
      return newIntent(context, index, moment, false)
    }

    fun newIntent(
      context: Context,
      index: Int,
      moment: UserMomentData,
      jumpToCommend: Boolean
    ): Intent {
      val intent = Intent(context, MomentDetailsActivity::class.java)
      intent.putExtra(EXTRA_CHANGE_INDEX, index)
      intent.putExtra(MomentDetailsNavigation.EXTRA_JUMP_TO_COMMEND, jumpToCommend)
      val bundle = Bundle()
      bundle.putParcelable(MomentDetailsNavigation.EXTRA_MOMENT, moment)
      intent.putExtras(bundle)
      return intent
    }
  }

  private var mCommentDialog: InputDialog? = null
  private var mContentListAdapter: MultiTypeAdapter? = null

  override fun getViewModelClass(): Class<MomentDetailsViewModel> =
    MomentDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_moment_details

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()
    setupContentListAdapter()
    listenerKeyboardHidden()
    viewModel.start(intent)
  }

  private fun subscribeViewModelEvent() {
    viewModel.momentDetails.observe(this, Observer {
      setResult(RESULT_DATA_CHANGE, intent)
      if (it.isSelf) {
        viewBinding.titleBar.setRightImage(R.drawable.ic_more_options)
        viewBinding.titleBar.setRightOptionClickListener {
          MenuPopup.Builder(this)
            .setData(resources.getStringArray(R.array.moment_details_delete_menu))
            .setOnItemClickListener { _, position ->
              if (position == 0) {
                ActionDialog.Builder()
                  .setTitle(getString(R.string.moment_details_delete_confirm_title))
                  .setContent(getString(R.string.moment_details_delete_confirm_content))
                  .setOnConfirmClickListener {
                    viewModel.deleteTheMoment()
                  }.build().show(supportFragmentManager)
              }
            }.build().show()
        }
      }
    })

    viewModel.addCommentSuccessEvent.observe(this, Observer {
      (viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
        COMMENT_POSITION,
        0
      )
      hideReplyDialog()
    })

    viewModel.toGalleryActivityCommand.observe(this, Observer { index ->
      viewModel.getMomentDetailsValue()?.let {
        startActivity(GalleryActivity.newIntent(this, ArrayList(it.picList), index))
      }
    })

    viewModel.toIntegralRechargeCommand.observe(this, Observer {
      startActivity(IntegralRechargeActivity.newIntent(this))
    })

    viewModel.momentDeleteSuccessEvent.observe(this, Observer {
      setResult(RESULT_DELETED, intent)
      finish()
    })

    viewModel.scrollToCommentCommand.observe(this, Observer {
      (viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
        COMMENT_POSITION,
        0
      )
    })

    viewModel.toUploadAvatarCommand.observe(this, Observer {
      showToast(getString(R.string.please_upload_avatar))
      startActivity(UploadAvatarActivity.newIntent(this))
    })

    viewModel.momentDeletedEvent.observe(this, Observer {
      finish()
    })
  }

  private fun listenerKeyboardHidden() {
    QMUIKeyboardHelper.setVisibilityEventListener(this, object : KeyboardVisibilityEventListener {
      override fun onVisibilityChanged(isOpen: Boolean, heightDiff: Int): Boolean {
        if (!isOpen) {
          hideReplyDialog()
        }
        return false
      }
    })
  }

  private fun hideReplyDialog() {
    mCommentDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.tv_add_comment, R.id.tv_comment -> {
          showCommentDialog()
        }

        else -> {
          startActivity(PaidUserActivity.newIntent(this, viewModel.getMomentId()))
        }
      }
    }
  }

  private fun showCommentDialog() {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this).apply {
          onSendClickListener = object : InputDialog.OnSendClickListener {
            override fun onSendClicked(commentDialog: InputDialog, content: String) {
              viewModel.addComment(content)
            }
          }
        }
        mCommentDialog?.show()
      }
    }
  }

  private fun setupContentListAdapter() {
    viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).layoutManager =
      LinearLayoutManager(this)
    mContentListAdapter = MultiTypeAdapter(this).also {
      it.register(MomentDetailsData::class.java, ContentViewBinder(this, viewModel)
        .apply {
          setOnItemClickListener(object :
            DefaultViewBinder.OnItemClickListener<MomentDetailsData> {
            override fun onItemClicked(
              v: View,
              position: Int,
              item: MomentDetailsData
            ) {
              if (v.id == R.id.iv_avatar || v.id == R.id.tv_nick_name) {
                UserHomeNavigation.navigate(
                  item.uid
                ).startForResult(this@MomentDetailsActivity, TO_USER_HOME_CODE)
              }
            }
          }, R.id.iv_avatar, R.id.tv_nick_name)
          setOnJobItemClickListener(object : OnJobItemClickListener {
            override fun onJobClick(jobData: com.bxkj.jrzp.user.data.JobData) {
              JobDetailsNavigation.navigate(jobData.id).start()
            }
          })
        })
      it.register(
        CommentItemData::class.java, CommentViewBinder()
          .apply {
            setOnItemClickListener(object :
              DefaultViewBinder.OnItemClickListener<CommentItemData> {
              override fun onItemClicked(
                v: View,
                position: Int,
                item: CommentItemData
              ) {
                when (v.id) {
                  R.id.tv_nick_name, R.id.iv_avatar -> {
                    UserHomeNavigation.navigate(
                      item.uid
                    ).start()
                  }

                  R.id.tv_like -> viewModel.commentLikeOrUnlike(item)
                  R.id.ll_reply -> toCommentReplyActivity(position, item)
                  else -> showReplyDialog(position, item)
                }
              }
            }, R.id.tv_nick_name, R.id.iv_avatar, R.id.tv_like, R.id.ll_reply)
            setOnItemLongClickListener(object :
              OnItemLongClickListener<CommentItemData> {
              override fun onLongClicked(
                v: View,
                position: Int,
                item: CommentItemData
              ): Boolean {
                if (UserUtils.logged()) {
                  if (item.uid == localUserId) {
                    MenuPopup.Builder(this@MomentDetailsActivity)
                      .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                      .setOnItemClickListener { _, menuPosition ->
                        if (menuPosition == 0) {
                          showDeleteCommentConfirmDialog(position, item)
                        }
                      }.build().show()
                  }
                }
                return true
              }
            })
          })
      it.register(
        CommentItemData.NoCommentData::class.java,
        DefaultViewBinder<NoCommentData>(
          R.layout.personal_recycler_no_comment_layout,
          BR.data
        ).apply {
          setOnItemClickListener(object :
            DefaultViewBinder.OnItemClickListener<NoCommentData> {
            override fun onItemClicked(v: View, position: Int, item: NoCommentData) {
              showCommentDialog()
            }
          })
        }
      )
    }
    viewModel.listViewModel.setAdapter(mContentListAdapter)
  }

  private fun showDeleteCommentConfirmDialog(position: Int, item: CommentItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.delete_comment_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteComment(position, item)
      }.build().show(supportFragmentManager)
  }

  private fun toCommentReplyActivity(position: Int, item: CommentItemData) {
    startActivityForResult(
      CommentReplyActivity.newIntent(
        this@MomentDetailsActivity,
        CommentInfoType.MOMENT,
        viewModel.getMomentId(),
        position,
        item
      ), TO_COMMENT_REPLY_CODE
    )
  }

  private fun showReplyDialog(commentPosition: Int, commentItemData: CommentItemData) {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this)
          .apply {
            hint =
              <EMAIL>(
                R.string.reply_format,
                commentItemData.nickName
              )
            onSendClickListener = object : InputDialog.OnSendClickListener {
              override fun onSendClicked(
                commentDialog: InputDialog,
                content: String
              ) {
                viewModel.addReply(commentPosition, commentItemData, content)
              }
            }
          }
        mCommentDialog?.show()
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    super.onDestroy()
    Jzvd.releaseAllVideos()
  }
}
