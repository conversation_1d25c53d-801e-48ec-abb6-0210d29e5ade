package com.bxkj.personal.data;

import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.utils.DistanceUtil;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TextUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/12/13
 */
public class MomentIdItemData implements Comparable<MomentIdItemData> {
    private int sjid;
    private String sj_lng;
    private String sj_lat;
    private double distance;
    private String juli;

    public int getSjid() {
        return sjid;
    }

    public void setSjid(int sjid) {
        this.sjid = sjid;
    }

    public String getSj_lng() {
        return sj_lng;
    }

    public void setSj_lng(String sj_lng) {
        this.sj_lng = sj_lng;
    }

    public String getSj_lat() {
        return sj_lat;
    }

    public void setSj_lat(String sj_lat) {
        this.sj_lat = sj_lat;
    }

    public double getDistance() {
        return distance;
    }

    public void calculateDistanceBySelfLocation(double lng, double lat) {
        if (CheckUtils.isNullOrEmpty(sj_lat) || CheckUtils.isNullOrEmpty(sj_lng) || lng == 0 || lat == 0) {
            distance = 0;
        } else {
            distance = DistanceUtil.getDistance(new LatLng(lat, lng), new LatLng(Double.parseDouble(sj_lat), Double.parseDouble(sj_lng)));
        }
        juli = TextUtils.formatDistance(distance);
    }

    @Override
    public int compareTo(MomentIdItemData o) {
        return Double.compare(this.distance, o.getDistance());
    }

    @Override
    public String toString() {
        return "MomentIdItemData{" +
                "sjid=" + sjid +
                ", sj_lng='" + sj_lng + '\'' +
                ", sj_lat='" + sj_lat + '\'' +
                ", distance=" + distance +
                '}';
    }
}
