package com.bxkj.personal.ui.fragment.invitationstodelivery;

import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.personal.data.InvitationsToDeliveryItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.fragment.invitationstodelivery
 * @Description: InvitationToDelivery
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface InvitationsToDeliveryContract {
    interface View extends BaseHasListView {
        void getInvitationToDeliveryListSuccess(List<InvitationsToDeliveryItemData> invitationsToDeliveryItemDataList, boolean noMore);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getInvitationToDeliveryList(int userId, int state, int pageIndex, int pageSize);
    }
}
