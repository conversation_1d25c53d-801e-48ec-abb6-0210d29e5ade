package com.bxkj.personal.ui.fragment.like

import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.UserHistoryItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.CommentRepo
import com.bxkj.personal.data.source.NewsRepo
import com.bxkj.common.base.mvvm.viewmodel.HasDeleteBarViewModel
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.like
 * @Description: 点赞
 * <AUTHOR>
 * @date 2020/2/13
 * @version V1.0
 */
class LikeViewModel @Inject constructor(
    private val mNewsRepo: NewsRepo, private val mAccountRepo: AccountRepo, private val mCommentRepo: CommentRepo
) : HasDeleteBarViewModel() {

    val listViewModel = RefreshListViewModel()
    val enableEditCommand = LiveEvent<Boolean>()

    init {
        setupLikeListViewModel()
    }

    private fun setupLikeListViewModel() {
        listViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getMyLikeList(getSelfUserID(),
                currentPage,
                CommonApiConstants.DEFAULT_PAGE_SIZE,
                object : ResultDataCallBack<List<UserHistoryItemData>> {
                    override fun onSuccess(data: List<UserHistoryItemData>?) {
                        if (CheckUtils.isNullOrEmpty(data)) {
                            if (currentPage == 1) {
                                enableEditCommand.value = false
                            }
                            listViewModel.noMoreData()
                        } else {
                            if (currentPage == 1) {
                                enableEditCommand.value = true
                            }
                            listViewModel.autoAddAll(data)
                        }
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode == 30001) {
                            if (currentPage == 1) {
                                enableEditCommand.value = false
                            }
                            listViewModel.noMoreData()
                        } else {
                            listViewModel.loadError()
                        }
                    }
                })
        }
    }

    fun start() {
        listViewModel.refresh()
    }

    override fun confirmDelete(item: List<Int>?) {
        item?.let {
            showLoading()
            val needDeleteTempList = ArrayList<Any>()
            batchDelete(it, needDeleteTempList, 0)
        }
    }

    private fun batchDelete(items: List<Int>, needDeleteList: ArrayList<Any>, currentPosition: Int) {
        val item = listViewModel.data[items[currentPosition]]
        val realItem: UserHistoryItemData = item as UserHistoryItemData
        val likeType = when (realItem.type) {
            1 -> PersonalApiConstants.NEWS_TYPE_NEWS
            4 -> PersonalApiConstants.NEWS_TYPE_QUESTIONS
            else -> PersonalApiConstants.NEWS_TYPE_VIDEO
        }
        mCommentRepo.likeOrUnlikeTheComment(
            getSelfUserID(),
            realItem.cid,
            likeType,
            realItem.infoID,
            object : ResultCallBack {
                override fun onSuccess() {
                    hideLoading()
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 10002) {
                        needDeleteList.add(item)
                        if (currentPosition < items.size - 1) {
                            batchDelete(items, needDeleteList, currentPosition + 1)
                        } else {
                            listViewModel.removeAll(needDeleteList)
                            if (listViewModel.childCount == 0) {
                                deleteToEmptyEvent.call()
                                listViewModel.refresh()
                            }
                            clearChecked()
                            hideLoading()
                        }
                    } else {
                        hideLoading()
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
    }

    fun addOrRemoveLike(item: UserHistoryItemData, newsType: Int) {
        mCommentRepo.likeOrUnlikeTheComment(
            getSelfUserID(),
            item.cid,
            newsType,
            item.infoID,
            object : ResultCallBack {
                override fun onSuccess() {
                    item.addLike()
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 10002) {
                        item.removeLike()
                    } else {
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
    }

    fun clearAllLike() {
        showLoading()
        mAccountRepo.clearMyHistory(
            getSelfUserID(),
            PersonalApiConstants.CLEAR_HISTORY_LIKE_TYPE,
            object : ResultCallBack {
                override fun onSuccess() {
                    hideLoading()
                    listViewModel.refresh()
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    showToast(respondThrowable.errMsg)
                }
            })
    }

    fun updateVideoPlayCount(item: UserHistoryItemData) {
        mNewsRepo.updateVideoPlayCount(item.infoID)
        item.addVideoPlay()
    }
}