package com.bxkj.personal.ui.activity.personalmember

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.enterprise.ui.activity.paymentweb.PaymentWebNavigation
import com.bxkj.personal.R
import com.bxkj.personal.data.PersonalMemberPackageBean
import com.bxkj.personal.databinding.CActivityPersonalMemberBinding
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.therouter.router.Route

/**
 * Description:
 * Author:Sanjin
 * Date:2024/6/1
 **/
@Route(path = PersonalMemberNavigation.PATH)
class PersonalMemberActivity :
  BaseDBActivity<CActivityPersonalMemberBinding, PersonalMemberViewModel>() {
  override fun getViewModelClass(): Class<PersonalMemberViewModel> =
    PersonalMemberViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_activity_personal_member

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    viewBinding.tvInvite.setOnClickListener {
      WebNavigation.navigate("file:///android_asset/invite/index.html").start()
    }

    subscribeViewModelEvent()

    setupPackageListAdapter()
  }

  override fun onResume() {
    super.onResume()
    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.createOrderSuccessEvent.observe(
      this,
      EventObserver {
        PaymentWebNavigation
          .create(
            "${CommonApiConstants.PAYMENT_URL}AppPay/confirmPay.aspx?ids=$it&paras=${
              AESOperator.safeEncrypt(
                UserUtils.getUserId().toString(),
              )
            }",
          ).start()
      },
    )
  }

  private fun setupPackageListAdapter() {
    viewBinding.recyclerService.apply {
      layoutManager =
        GridLayoutManager(this@PersonalMemberActivity, 3)
      addItemDecoration(GridItemDecoration(getResDrawable(R.drawable.divider_8)))
      adapter =
        object : SimpleDiffListAdapter<PersonalMemberPackageBean>(
          R.layout.c_recycler_personal_vip_service_item,
          PersonalMemberPackageBean.DiffCallback(),
        ) {
          override fun bind(
            holder: SuperViewHolder,
            item: PersonalMemberPackageBean,
            position: Int,
          ) {
            super.bind(holder, item, position)
            holder.itemView.isSelected = viewModel.hasSelected(getItem(position))
            holder.findViewById<ImageView>(R.id.iv_selected).let {
              if (viewModel.hasSelected(getItem(position))) {
                it.visibility = View.VISIBLE
              } else {
                it.visibility = View.GONE
              }
            }
            holder.itemView.setOnClickListener {
              if (viewModel.hasSelected(item)) {
                return@setOnClickListener
              }
              viewModel.select(item)
              notifyDataSetChanged()
            }
          }
        }
    }
  }

  companion object {
    fun newIntent(context: Context): Intent = Intent(context, PersonalMemberActivity::class.java)
  }
}
