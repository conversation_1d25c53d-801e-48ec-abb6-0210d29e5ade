package com.bxkj.personal.ui.activity.systemmsg;

import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.personal.data.SystemMsgData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.systemmsg
 * @Description: SystemMsg
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface SystemMsgContract {
    interface View extends BaseHasListView {
        void getSystemMsgSuccess(List<SystemMsgData> systemMsgDataList);

        void updateSystemMsgReadStateSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getSystemMsgList(int userId, int pageIndex, int pageSize);

        abstract void updateSystemMsgReadState(int userId);
    }
}
