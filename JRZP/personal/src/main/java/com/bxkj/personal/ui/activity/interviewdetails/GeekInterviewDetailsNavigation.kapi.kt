package com.bxkj.personal.ui.activity.interviewdetails

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * Description:
 * Author:45457
 **/
class GeekInterviewDetailsNavigation {

    companion object {

        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/geek_interview_details"

        const val EXTRA_JOB_ID = "KEY_JOB_ID"
        const val EXTRA_RESUME_ID = "KEY_RESUME_ID"

        fun create(jobID: Int, resumeID: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_JOB_ID, jobID)
                .withInt(EXTRA_RESUME_ID, resumeID)
        }
    }
}