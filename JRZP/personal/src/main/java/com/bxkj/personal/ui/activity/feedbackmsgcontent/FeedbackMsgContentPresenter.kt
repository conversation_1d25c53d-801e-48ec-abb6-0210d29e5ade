package com.bxkj.personal.ui.activity.feedbackmsgcontent

import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.PersonalApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.feedbackmsgcontent
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/21
 * @version V1.0
 */
class FeedbackMsgContentPresenter @Inject constructor(api: PersonalApi) : FeedbackMsgContentContract.Presenter() {

    var mPersonalApi: PersonalApi = api

    override fun getFeedbackMsgContentByCompany(userId: Int, companyId: Int, pageIndex: Int, pageSize: Int) {
        mPersonalApi.getFeedbackMsgContentByCompany(userId, companyId, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        mView.getFeedbackMsgSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode != 30002) {
                            mView.onRequestError(respondThrowable)
                        } else {
                            mView.onResultNoData()
                        }
                    }
                })
    }

    override fun acceptInterviewInvitation(userId: Int, otherId: Int) {
        mPersonalApi.acceptInterviewInvitation(userId, otherId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        mView.acceptOrRefuseSuccess()
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        mView.onError(respondThrowable.errMsg)
                    }

                })
    }

    override fun refuseInterviewInvitation(userId: Int, otherId: Int) {
        mPersonalApi.refuseInterviewInvitation(userId, otherId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        mView.acceptOrRefuseSuccess()
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        mView.onError(respondThrowable.errMsg)
                    }
                })
    }
}