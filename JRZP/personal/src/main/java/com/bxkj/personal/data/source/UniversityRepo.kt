package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.PersonalApi
import com.bxkj.personal.data.DepartmentItemData
import com.bxkj.personal.data.SchoolMateItemData
import com.bxkj.personal.data.UniversityItemData
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * @date 2019/10/24
 */
class UniversityRepo @Inject constructor(private val mPersonalApi: PersonalApi) : BaseRepo() {


    fun getUniversityList(cityId: Int, name: String, pageIndex: Int, pageSize: Int, callback: ResultDataCallBack<List<UniversityItemData>>) {
        mPersonalApi.getUniversityList(cityId, name, pageIndex, pageSize)
                .debounce(1000, TimeUnit.MILLISECONDS)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callback.onError(respondThrowable)
                    }
                })

    }

    fun getDepartmentList(name: String, callback: ResultDataCallBack<List<DepartmentItemData>>) {
        mPersonalApi.getDepartmentList(name)
                .debounce(1000, TimeUnit.MILLISECONDS)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callback.onError(respondThrowable)
                    }
                })
    }

    fun getProfessionList(departmentId: Int, name: String, callback: ResultDataCallBack<List<DepartmentItemData>>) {
        mPersonalApi.getProfessionList(departmentId, name)
                .debounce(1000, TimeUnit.MILLISECONDS)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callback.onError(respondThrowable)
                    }
                })
    }

    fun getClassList(professionId: Int, name: String, callback: ResultDataCallBack<List<DepartmentItemData>>) {
        mPersonalApi.getClassList(professionId, name)
                .debounce(1000, TimeUnit.MILLISECONDS)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callback.onError(respondThrowable)
                    }
                })
    }

    fun getSchoolMateList(userId: Int, lng: String, lat: String, pageIndex: Int, pageSize: Int, callBack: ResultDataCallBack<List<SchoolMateItemData>>) {
        mPersonalApi.getSchoolMateList(userId, lng, lat, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver() {
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callBack.onError(respondThrowable)
                    }
                })
    }
}