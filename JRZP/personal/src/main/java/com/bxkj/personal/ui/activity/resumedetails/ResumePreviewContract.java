package com.bxkj.personal.ui.activity.resumedetails;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.CertificateItemData;
import com.bxkj.personal.data.EduBackgroundItemData;
import com.bxkj.personal.data.LanguageSkillsItemData;
import com.bxkj.personal.data.ProfessionalSkillItemData;
import com.bxkj.personal.data.ResumeBasicData;
import com.bxkj.personal.data.SchoolSituationItemData;
import com.bxkj.personal.data.UserCenterPersonalData;
import com.bxkj.personal.data.WorkExpItemData;
import com.bxkj.video.data.VideoData;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.resumedetails
 * @Description: ResumeDetails
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface ResumePreviewContract {
    interface View extends BaseView {

        void getResumeAttachVideoSuccess(List<VideoData> attachVideos);

        void getResumeBaseInfoSuccess(UserCenterPersonalData resumeBaseInfoData);

        void getCareerObjectiveSuccess(ResumeBasicData resumeCareerObjectiveData);

        void getEduBackgroundSuccess(EduBackgroundItemData resumeEduBackgroundData);

        void getWorkExpSuccess(WorkExpItemData resumeWorkExpData);

        void getSkillSuccess(ProfessionalSkillItemData resumeSkillData);

        void getLanguageSuccess(LanguageSkillsItemData resumeLanguageData);

        void getSchoolSituationSuccess(SchoolSituationItemData resumeSchoolSituationData);

        void getCertificateSuccess(CertificateItemData resumeCertificateData);

        void resumeDeleted();

    }

    abstract class Presenter extends BaseMvpPresenter<View> {

        abstract void getResumeBaseInfo();

        abstract void getResumeAttachVideo();

        abstract void getResumeCareerObjective();

        abstract void getResumeEduBackground();

        abstract void getResumeWorkExp();

        abstract void getResumeSkill();

        abstract void getResumeLanguage();

        abstract void getResumeSchoolSituation();

        abstract void getResumeCertificate();

    }
}
