package com.bxkj.personal.ui.activity.jobdetails

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.cachedIn
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.JobDetailsData
import com.bxkj.personal.data.UserResumeData
import com.bxkj.personal.data.source.AppStatusInfoRepo
import com.bxkj.personal.data.source.JobRepo
import com.bxkj.personal.data.source.MyResumeRepo
import com.bxkj.personal.ui.activity.searchjobresult.FilterJobParams
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * author:Sanjin
 * date:2024/12/27
 **/
class JobDetailsViewModelV2 @Inject constructor(
  private val jobRepo: JobRepo,
  private val myResumeRepo: MyResumeRepo,
  private val appStatusInfoRepo: AppStatusInfoRepo,
) : BaseViewModel() {

  val jobDetails = MutableLiveData<JobDetailsData>()

  val jobIsCollected = MutableLiveData<Boolean>()
  val hasConversation = MutableLiveData<Boolean>()

  val hasContractWay = MutableLiveData<Boolean>().apply { value = false }

  val submittedResume = MutableLiveData<Boolean>()

  val showShareDialogCommand = MutableLiveData<VMEvent<ShareInfoData>>()
  val toCompanyHomePageCommand = MutableLiveData<VMEvent<Int>>()

  //显示加入个人会员提示
  val showJoinPersonalVipTipsCommand = MutableLiveData<VMEvent<String>>()

  //显示拨打电话确认弹窗
  val showCallCompanyConfirmDialogCommand = MutableLiveData<VMEvent<String>>()

  //显示未投递简历提示
  val showNoSendResumeTipsCommand = MutableLiveData<VMEvent<Unit>>()

  //跳转到创建简历页面
  val toCreateResumeCommand = MutableLiveData<VMEvent<Int>>()

  //拨打电话
  val openDialPadCommand = MutableLiveData<VMEvent<String>>()

  //跳转到打招呼页面
  val toSayHelloCommand = MutableLiveData<VMEvent<UserResumeData>>()

  val toConversationCommand = MutableLiveData<VMEvent<JobDetailsData>>()

  //推荐职位列表
  val recommendJobListFlow = Pager(PagingConfig(16)) {
    object : PagingSource<Int, JobData>() {
      override fun getRefreshKey(state: PagingState<Int, JobData>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
          val anchorPage = state.closestPageToPosition(anchorPosition)
          anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
      }

      override suspend fun load(params: LoadParams<Int>): LoadResult<Int, JobData> {
        val pageIndex = params.key.getOrDefault(1)
        var loadResult: LoadResult<Int, JobData> = LoadResult.Invalid()
        jobRepo.getJobListFullOptions(
          filterParameters, pageIndex, params.loadSize
        ).handleResult({
          loadResult = LoadResult.Page(
            it ?: emptyList(),
            if (pageIndex == 1) null else pageIndex - 1,
            if (it.isNullOrEmpty()) null else pageIndex + 1
          )
        }, {
          loadResult = LoadResult.Error(it)
        })
        return loadResult
      }
    }
  }.flow.cachedIn(viewModelScope)

  private val filterParameters = FilterJobParams()

  private var _jobId = 0

  private var _createResumeNextAction = NEXT_ACTION_CONVERSATION

  //直接投递简历
  private var _isDirectSubmit: Boolean = false

  fun start(jobId: Int, directSubmit: Boolean) {
    _jobId = jobId
    _isDirectSubmit = directSubmit
    getJobDetails(jobId)
    checkJobIsCollected(jobId)
  }

  fun fallbackQueryJobListParams() {
    filterParameters.cityId = 0
    filterParameters.provinceId = UserUtils.getUserProvinceId()
  }

  fun jumpToCompanyHomePage() {
    jobDetails.value?.uid?.let {
      toCompanyHomePageCommand.value = VMEvent(it)
    }
  }

  fun toggleFavorite() {
    afterLogin {
      jobDetails.value?.let { jobDetails ->
        if (jobIsCollected.value == true) {
          jobRepo.cancelCollectionJob(jobDetails.id, getSelfUserID(), object : ResultCallBack {
            override fun onSuccess() {
              jobIsCollected.value = false
            }

            override fun onError(respondThrowable: RespondThrowable) {
              showToast(respondThrowable.errMsg)
            }
          })
        } else {
          jobRepo.collectionJob(jobDetails.id,
            jobDetails.com.id,
            getSelfUserID(),
            object : ResultCallBack {
              override fun onSuccess() {
                jobIsCollected.value = true
              }

              override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
              }
            })
        }
      }
    }
  }

  fun showShareDialog() {
    jobDetails.value?.let { jobInfo ->
      viewModelScope.launch {
        showLoading()
        appStatusInfoRepo.getShareInfo(CommonApiConstants.SHARE_JOB, jobInfo.id).handleResult({
          it?.let { shareInfo ->
            shareInfo.shareUrl = "http://m.jrzp.com/jobfenxiang/view.aspx?relID=${jobInfo.id}"
            shareInfo.mineProgramPath = "pages/postDetails/postDetails?id=${jobInfo.id}"
            showShareDialogCommand.value = VMEvent(shareInfo)
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun callCompanyPreCheck() {
    afterLogin {
      showLoading()
      viewModelScope.launch {
        jobRepo.getCallCompanyBalanceV2(_jobId).handleResult({
          callCompany()
        }, {
          when (it.errCode) {
            30006, 30009 -> {   //需要开通会员
              showJoinPersonalVipTipsCommand.value = VMEvent(it.errMsg)
            }

            30007 -> {
              showToast(it.errMsg)
            }

            30008 -> {
              showCallCompanyConfirmDialogCommand.value = VMEvent(it.errMsg)
            }

            else -> {
              showToast(it.errMsg)
            }
          }
        }, {
          hideLoading()
        })
      }
    }
  }

  fun callCompany() {
    showLoading()
    viewModelScope.launch {
      jobRepo.callCompanyPrecheck(_jobId).handleResult({
        it?.let {
          openDialPadCommand.value = VMEvent(it)
        }
      }, {
        when (it.errCode) {
          30006 -> {
            showJoinPersonalVipTipsCommand.value = VMEvent(it.errMsg)
          }

          30013 -> {
            showNoSendResumeTipsCommand.value = VMEvent(Unit)
          }

          else -> {
            showToast(it.errMsg)
          }
        }
      }, {
        hideLoading()
      })
    }
  }

  fun callCompanyAfterSendResume() {
    toSendResume {
      callCompany()
    }
  }

  fun toConversation() {
    afterLogin {
      jobDetails.value?.let {
        //存在会话
        if (hasConversation.value == true) {
          toConversationCommand.value = VMEvent(it)
        } else {
          sendResumePreCheck(JobDetailsViewModel.NEXT_ACTION_CONVERSATION)
        }
      }
    }
  }

  @JvmOverloads
  fun toSendResume(nextStep: (() -> Unit)? = null) {
    afterLogin {
      jobDetails.value?.let {
        sendResumePreCheck(NEXT_ACTION_SEND_RESUME, nextStep)
      }
    }
  }

  private fun checkSubmittedResume() {
    jobDetails.value?.let {
      viewModelScope.launch {
        myResumeRepo.getResumeListForJobId(getSelfUserID(), it.id)
          .handleResult({
            it?.let {
              if (it.isNotEmpty()) {
                //未投过简历
                if (it[0].isApply == 0) {
                  if (_isDirectSubmit) {
                    toSendResume()
                    _isDirectSubmit = false
                  }
                }
                submittedResume.value = (it[0].isApply > 0)
              }
            }
          })
      }
    }
  }

  private fun sendResumePreCheck(nextAction: Int, nextStep: (() -> Unit)? = null) {
    viewModelScope.launch {
      showLoading()
      myResumeRepo.getUserResumeList(getSelfUserID()).handleResult({ resultList ->
        if (resultList.isNullOrEmpty()) {
          toCreateResume(nextAction)
        } else {
          val userResume = resultList[0]
          if (nextAction == NEXT_ACTION_CONVERSATION) {
            toSayHelloCommand.value = VMEvent(userResume)
          } else {
            sendResume(userResume, nextStep)
          }
        }
      }, { err ->
        if (err.isNoDataError) {
          toCreateResume(nextAction)
        } else {
          showToast(err.errMsg)
        }
      }, {
        hideLoading()
      })
    }
  }

  private fun toCreateResume(nextAction: Int) {
    _createResumeNextAction = nextAction
    showToast("请先创建简历")
    toCreateResumeCommand.value = VMEvent(nextAction)
  }

  private fun sendResume(resumeData: UserResumeData, nextStep: (() -> Unit)?) {
    jobDetails.value?.let { jobInfo ->
      viewModelScope.launch {
        showLoading()
        jobRepo.sendResume(getSelfUserID(), jobInfo.uid, jobInfo.id, resumeData.id)
          .handleResult({
            submittedResume.value = true
            showToast("投递成功")
            nextStep?.invoke()
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

  private fun getJobDetails(jobId: Int) {
    viewModelScope.launch {
      jobRepo.getJobDetails(jobId, getSelfUserID()).handleResult({
        hasContractWay.value = it?.isphone == 1
        filterParameters.jobSecondTypeId = it?.type2 ?: 0
        filterParameters.cityId = UserUtils.getUserSelectedCityId()
        jobDetails.value = it
        checkSubmittedResume()
      }, {
        showToast(it.errMsg)
      })
    }
  }

  private fun checkJobIsCollected(jobId: Int) {
    viewModelScope.launch {
      jobRepo.checkJobIsCollected(getSelfUserID(), jobId).handleResult({
        jobIsCollected.value = true
      }, {
        jobIsCollected.value = false
      })
    }
  }

  companion object {

    const val NEXT_ACTION_CONVERSATION = 1
    const val NEXT_ACTION_SEND_RESUME = 2
  }
}