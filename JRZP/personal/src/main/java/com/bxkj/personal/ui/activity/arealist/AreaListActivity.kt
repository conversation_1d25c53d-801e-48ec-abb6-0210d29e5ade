package com.bxkj.personal.ui.activity.arealist

import android.app.Activity.RESULT_FIRST_USER
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.data.AreaOptionsData.DiffCallBack
import com.bxkj.personal.R.layout
import com.bxkj.personal.databinding.PersonalActivityAreaListBinding

/**
 * @Description: 区域列表
 * @date: 2020/5/18
 */

const val RESULT_SELECTED = RESULT_FIRST_USER + 1
const val EXTRA_RESULT_AREA = "EXTRA_RESULT_AREA"

class AreaListActivity : BaseDBActivity<PersonalActivityAreaListBinding, AreaListViewModel>() {

    companion object {
        const val EXTRA_PARENT_CITY_ID = "PARENT_CITY_ID"
        fun newIntent(context: Context, parentCityId: Int): Intent {
            return Intent(context, AreaListActivity::class.java)
                    .apply {
                        putExtra(EXTRA_PARENT_CITY_ID, parentCityId)
                    }
        }
    }

    override fun getViewModelClass(): Class<AreaListViewModel> = AreaListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_area_list

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupListAdapter()

        viewModel.start(intent.getIntExtra(EXTRA_PARENT_CITY_ID, CommonApiConstants.NO_ID))
    }

    private fun setupListAdapter() {
        val areaListAdapter = SimpleDiffListAdapter<AreaOptionsData>(
          layout.personal_recycler_area_list_item,
          DiffCallBack()
        )
                .apply {
                    setOnItemClickListener(object :
                      SuperItemClickListener {
                        override fun onClick(v: View, position: Int) {
                            getData()?.let {
                                val resultIntent = Intent()
                                resultIntent.putExtra(EXTRA_RESULT_AREA, it[position])
                                setResult(RESULT_SELECTED, resultIntent)
                                finish()
                            }
                        }
                    })
                }
        viewBinding.recyclerArea.layoutManager = LinearLayoutManager(this)
        viewBinding.recyclerArea.addItemDecoration(LineItemDecoration(ContextCompat.getDrawable(this, R.drawable.divider_f4f4f4), LinearLayoutManager.VERTICAL))
        viewBinding.recyclerArea.adapter = areaListAdapter
    }

}