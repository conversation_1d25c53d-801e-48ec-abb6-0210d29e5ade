package com.bxkj.personal.ui.activity.answerdetails

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.AnswerDetailsData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.CommentRepo
import com.bxkj.personal.data.source.QuestionRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.answerdetails
 * @Description:
 * <AUTHOR>
 * @date 2020/1/13
 * @version V1.0
 */
class AnswerDetailsViewModel @Inject constructor(
    private val mQuestionRepo: QuestionRepo,
    private val mAccountRepo: AccountRepo,
    private val mCommentRepo: CommentRepo
) : BaseViewModel() {

    val answerDetails = MutableLiveData<AnswerDetailsData>()
    val question = MutableLiveData<String>()
    val toUploadAvatarCommand = LiveEvent<Void>()
    val addCommentSuccessEvent = LiveEvent<Void>()
    val commentViewModel = RefreshListViewModel()
    val toAnswerAuthorHomeCommand =
        LiveEvent<AnswerDetailsData>()
    val answerDeletedEvent = LiveEvent<Void>()
    val scrollToCommentLayout = LiveEvent<Void>()

    private var mAnswerId: Int = CommonApiConstants.NO_ID

    init {
        commentViewModel.refreshLayoutViewModel.enableRefresh(false)
    }

    fun start(intent: Intent) {
        val needToCommentLayout =
            intent.getBooleanExtra(AnswerDetailsNavigation.EXTRA_JUMP_TO_COMMENT, false)
        question.value = intent.getStringExtra(AnswerDetailsNavigation.EXTRA_QUESTION_CONTENT)
        mAnswerId =
            intent.getIntExtra(AnswerDetailsNavigation.EXTRA_ANSWER_ID, CommonApiConstants.NO_ID)
        setupRefreshLayoutViewModel()
        mQuestionRepo.getAnswerDetails(
            getSelfUserID(),
            mAnswerId,
            object : ResultDataCallBack<AnswerDetailsData> {
                override fun onSuccess(data: AnswerDetailsData?) {
                    answerDetails.value = data
                    if (needToCommentLayout) {
                        scrollToCommentLayout.call()
                    }
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        answerDeletedEvent.call()
                    } else {
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
    }

    private fun setupRefreshLayoutViewModel() {
        commentViewModel.setOnLoadDataListener { currentPage ->
            mCommentRepo.getCommentList(getSelfUserID(),
                mAnswerId,
                PersonalApiConstants.NEWS_TYPE_QUESTIONS,
                CommonApiConstants.NO_ID,
                currentPage,
                CommonApiConstants.DEFAULT_PAGE_SIZE,
                object : ResultDataCallBack<List<CommentItemData>> {
                    override fun onSuccess(data: List<CommentItemData>?) {
                        commentViewModel.addAll(data)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode == 30003) {
                            if (currentPage == 1) {
                                commentViewModel.refreshLayoutViewModel.enableLoadMore(false)
                            }
                            commentViewModel.noMoreData()
                        } else {
                            commentViewModel.loadError()
                        }
                    }
                })
        }
        commentViewModel.refresh()
    }

    fun getAnswerId(): Int {
        return mAnswerId
    }

    fun addOrRemoveFollow() {
        if (checkLoginStateAndToLogin()) {
            answerDetails.value?.let {
                mAccountRepo.addOrRemoveFollow(
                    getSelfUserID(),
                    PersonalApiConstants.FOLLOW_USER_TYPE,
                    it.userID,
                    object : ResultCallBack {
                        override fun onSuccess() {
                            it.addFollow()
                        }

                        override fun onError(respondThrowable: RespondThrowable) {
                            if (respondThrowable.errCode == 10002) {
                                it.removeFollow()
                            } else {
                                showToast(respondThrowable.errMsg)
                            }
                        }
                    })
            }
        }
    }

    fun addOrRemoveCollection() {
        answerDetails.value?.let {
            mAccountRepo.addOrRemoveCollection(
                getSelfUserID(),
                PersonalApiConstants.NEWS_TYPE_QUESTIONS,
                it.id,
                object : ResultDataCallBack<BaseResponse<*>> {
                    override fun onSuccess(data: BaseResponse<*>?) {
                        it.addCollect()
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode == 10002) {
                            it.removeCollect()
                        } else {
                            showToast(respondThrowable.errMsg)
                        }
                    }
                })
        }
    }

    fun addOrRemoveLike() {
        answerDetails.value?.let {
            mCommentRepo.likeOrUnlikeTheComment(getSelfUserID(),
                CommonApiConstants.NO_ID,
                PersonalApiConstants.NEWS_TYPE_QUESTIONS,
                it.id,
                object : ResultCallBack {
                    override fun onSuccess() {
                        it.addLike()
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode == 10002) {
                            it.removeLike()
                        } else {
                            showToast(respondThrowable.errMsg)
                        }
                    }
                })
        }
    }

    /**
     * 评论点赞或取消点赞
     */
    fun commentLikeOrUnlike(comment: CommentItemData) {
        if (checkLoginStateAndToLogin()) {
            answerDetails.value?.let {
                mCommentRepo.likeOrUnlikeTheComment(
                    getSelfUserID(),
                    comment.pid,
                    PersonalApiConstants.NEWS_TYPE_QUESTIONS,
                    it.id,
                    object : ResultCallBack {
                        override fun onSuccess() {
                            comment.addLike()
                        }

                        override fun onError(respondThrowable: RespondThrowable) {
                            if (respondThrowable.errCode == 10002) {
                                comment.removeLike()
                            } else {
                                showToast(respondThrowable.errMsg)
                            }
                        }
                    })
            }
        }
    }

    /**
     * 检查是否上传头像
     */
    fun checkAvatarIsUpload(method: () -> Unit) {
        showLoading()
        mAccountRepo.checkAvatarIsUpload(getSelfUserID(), object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                method.invoke()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30003) {
                    toUploadAvatarCommand.call()
                } else {
                    showToast(respondThrowable.errMsg)
                }
            }
        })
    }

    /**
     * 添加评论
     */
    fun addComment(content: String) {
        if (CheckUtils.isNullOrEmpty(content)) {
            showToast(R.string.comment_content_not_be_null)
            return
        }
        showLoading()
        mCommentRepo.addComment(getSelfUserID(),
            mAnswerId,
            PersonalApiConstants.NEWS_TYPE_QUESTIONS,
            CommonApiConstants.NO_ID,
            CommonApiConstants.NO_ID,
            CommonApiConstants.NO_TEXT,
            content,
            object : ResultDataCallBack<CommentItemData> {
                override fun onSuccess(data: CommentItemData?) {
                    hideLoading()
                    answerDetails.value?.updateCommentCount(1)
                    commentViewModel.hiddenPageStatusLayout()
                    commentViewModel.refreshLayoutViewModel.enableLoadMore(true)
                    showToast(R.string.moment_details_comment_success)
                    //无评论
                    if (commentViewModel.childCount >= 15) {
                        commentViewModel.removeAt(commentViewModel.childCount - 1)
                    }
                    commentViewModel.add(0, data)
                    addCommentSuccessEvent.call()
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    showToast(respondThrowable.errMsg)
                }
            })
    }

    /**
     * 添加回复
     */
    fun addReply(commentPosition: Int, parentComment: CommentItemData, content: String) {
        showLoading()
        mCommentRepo.addComment(getSelfUserID(),
            mAnswerId,
            PersonalApiConstants.NEWS_TYPE_QUESTIONS,
            parentComment.pid,
            CommonApiConstants.NO_ID,
            parentComment.nickName,
            content,
            object : ResultDataCallBack<CommentItemData> {
                override fun onSuccess(data: CommentItemData?) {
                    hideLoading()
                    answerDetails.value?.updateCommentCount(1)
                    parentComment.addReply(data)
                    commentViewModel.replace(commentPosition, parentComment)
                    showToast(R.string.moment_details_comment_success)
                    addCommentSuccessEvent.call()
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    showToast(respondThrowable.errMsg)
                }
            })
    }

    fun toAnswerAuthorHome() {
        answerDetails.value?.let {
            toAnswerAuthorHomeCommand.value = it
        }
    }

    fun deleteComment(position: Int, item: CommentItemData) {
//        mCommentRepo.deleteComment(getUserId(),item.pid,object :ResultCallBack{
//            override fun onSuccess() {
//                showToast(R.string.common_delete_success)
//                removeCommentByIndex(position, item)
//            }
//
//            override fun onError(respondThrowable: RespondThrowable) {
//                showToast(respondThrowable.errMsg)
//            }
//
//        })
        mAccountRepo.deleteMyPublish(getSelfUserID(),
            PersonalApiConstants.MY_PUBLISH_COMMENT_TYPE,
            PersonalApiConstants.MY_PUBLISH_ANSWER_COMMENT_TYPE,
            item.pid,
            object : ResultCallBack {
                override fun onSuccess() {
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        showToast(R.string.common_delete_success)
                        removeCommentByIndex(position)
                    } else {
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
    }

    private fun removeCommentByIndex(index: Int) {
        answerDetails.value?.updateCommentCount(-1)
        commentViewModel.loadFinish()
        commentViewModel.removeAt(index)
        if (commentViewModel.childCount == 0) {
            commentViewModel.refresh()
        }
    }

}