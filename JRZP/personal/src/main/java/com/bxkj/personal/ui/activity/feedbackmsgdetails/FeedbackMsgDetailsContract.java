package com.bxkj.personal.ui.activity.feedbackmsgdetails;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.feedbackmsgdetails
 * @Description: FeedbackMsgDetails
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface FeedbackMsgDetailsContract {
    interface View extends BaseView {
        void acceptSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void updateFeedbackMsgState(int msgId);

        abstract void acceptInterviewInvitation(int userId, int otherId);

        abstract void refuseInterviewInvitation(int userId, int otherId);
    }
}
