package com.bxkj.personal.weight.filterjobclasspopup;

import android.content.Context;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.personal.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.jobselection
 * @Description: 职位二级分类适配器
 * @TODO: TODO
 * @date 2018/3/29
 */

public class JobSecondClassAdapter extends SuperAdapter<JobTypeData> {
    private static final int NO_POSITION = -1;
    private int mSelectPosition = NO_POSITION;

    public JobSecondClassAdapter(Context context, List<JobTypeData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, JobTypeData jobTypeData, int position) {
        holder.setText(R.id.tv_second_class, jobTypeData.getName());
        TextView tvSecondClass = holder.findViewById(R.id.tv_second_class);
        tvSecondClass.setSelected(position == mSelectPosition);
        holder.itemView.setOnClickListener(view -> {
            if (mSelectPosition == position) return;
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(holder.itemView, position);
            }
            mSelectPosition = position;
            notifyDataSetChanged();
        });
    }

    public void resetSelectPosition() {
        mSelectPosition = NO_POSITION;
    }
}
