package com.bxkj.personal.ui.activity.arealist

import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.source.AddressInfoRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/5/18
 * @version: V1.0
 */
class AreaListViewModel @Inject constructor(
  private val mAddressInfoRepo: AddressInfoRepo
) : BaseViewModel() {

  val areaList = MutableLiveData<List<AreaOptionsData>>()

  fun start(parentCityId: Int) {
    mAddressInfoRepo.getAddressList(
      CommonApiConstants.GET_AREA_TYPE, parentCityId,
      object : ResultDataCallBack<List<AreaOptionsData>> {
        override fun onSuccess(data: List<AreaOptionsData>?) {
          data?.let {
            areaList.value = ArrayList(data)
          }
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }
}