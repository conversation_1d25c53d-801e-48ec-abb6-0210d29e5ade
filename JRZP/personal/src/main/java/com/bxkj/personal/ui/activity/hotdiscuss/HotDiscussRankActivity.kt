package com.bxkj.personal.ui.activity.hotdiscuss

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.data.DiscussData
import com.bxkj.personal.databinding.PersonalActivityHotDiscussRankBinding

/**
 *  热议榜
 * @author: sanjin
 * @date: 2022/9/5
 */
class HotDiscussRankActivity :
    BaseDBActivity<PersonalActivityHotDiscussRankBinding, HotDiscussRankViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, HotDiscussRankActivity::class.java)
        }
    }

    private val _detailsLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            viewModel.handleDetailsPageResult(it)
        }

    override fun getViewModelClass(): Class<HotDiscussRankViewModel> =
        HotDiscussRankViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_hot_discuss_rank

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupDiscussListAdapter()

        viewModel.start()
    }

    private fun setupDiscussListAdapter() {
        val discussListAdapter =
            SimpleDBListAdapter<DiscussData>(
                this,
                R.layout.personal_recycler_hot_discuss_rank_item
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        _detailsLauncher.launch(
                            HotDiscussActivity.newIntent(
                                this@HotDiscussRankActivity,
                                data[position].wdid
                            )
                        )
                    }
                })
            }

        viewBinding.includeDiscussList.recyclerContent.apply {
            layoutManager = LinearLayoutManager(this@HotDiscussRankActivity)
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f8f8f8_8))
                    .build()
            )
        }

        viewModel.hotDiscussListViewModel.setAdapter(discussListAdapter)
    }
}