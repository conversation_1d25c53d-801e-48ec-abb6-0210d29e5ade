package com.bxkj.personal.ui.fragment.parttimejoblist

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.data.source.JobRepo.GetPartTimeJobParams
import com.bxkj.personal.databinding.PersonalFragmentPartTimeListBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.fragment.homev3.GeekHomeFragmentV3
import com.bxkj.personal.ui.fragment.joblist.JobListPage
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class PartTimeJobListFragment :
  BaseDBFragment<PersonalFragmentPartTimeListBinding, PartTimeJobListViewModel>(),
  JobListPage {

  companion object {

    private const val EXTRA_REQUEST_PARAMS = "REQUEST_PARAMS"

    fun newInstance(getPartTimeJobParams: GetPartTimeJobParams): PartTimeJobListFragment {
      return PartTimeJobListFragment().apply {
        arguments = bundleOf(
          EXTRA_REQUEST_PARAMS to getPartTimeJobParams
        )
      }
    }
  }

  private var _partTimeJobListAdapter: SimplePageDataAdapter<JobData, PersonalFragmentPartTimeListBinding>? =
    null

  override fun getViewModelClass(): Class<PartTimeJobListViewModel> =
    PartTimeJobListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_part_time_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.refreshContent.setEnableLoadMore(false)
    viewBinding.refreshContent.setOnRefreshListener {
      _partTimeJobListAdapter?.refresh()
    }

    subscribeCityChangeEvent()
    subscribeViewModelEvent()

    viewModel.start(arguments?.getParcelable(EXTRA_REQUEST_PARAMS))

    viewBinding.pslContent.show(LoadingPageState::class.java)
    setupPartTimeJobList()
  }

  fun startFilter(sortMode: Int) {
    viewModel.setSortMode(sortMode)
    viewBinding.pslContent.show(LoadingPageState::class.java)
    _partTimeJobListAdapter?.refresh()
  }

  fun startSearch(keyword: String) {
    viewModel.setSearchKeyword(keyword)
    viewBinding.pslContent.show(LoadingPageState::class.java)
    _partTimeJobListAdapter?.refresh()
  }

  private fun subscribeCityChangeEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
        if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
          viewModel.setCity(UserUtils.getUserSelectedCityId())
          viewBinding.pslContent.show(LoadingPageState::class.java)
          _partTimeJobListAdapter?.refresh()
        }
      }
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })
  }

  private fun setupPartTimeJobList() {
    _partTimeJobListAdapter = SimplePageDataAdapter<JobData, PersonalFragmentPartTimeListBinding>(
      R.layout.personal_list_part_time_job_item,
      JobData.DiffCallback()
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getItemBean(position)?.let {
            if (v.id == R.id.tv_send_resume) {
              viewModel.sendResumePreCheck(it)
            } else {
              startActivity(JobDetailsActivityV2.newIntent(requireContext(), it.id))
            }
          }
        }
      }, R.id.tv_send_resume)
      addLoadStateListener { loadStateFlow ->
        loadStateFlow.handleState({
          finishParentRefresh()
          viewBinding.refreshContent.finishRefresh()
          viewBinding.pslContent.hidden()
        }, {
          finishParentRefresh()
          viewBinding.refreshContent.finishRefresh()
          viewBinding.pslContent.show(
            EmptyPageState::class.java,
            object : OnStateSetUpListener<EmptyPageState> {
              override fun onStateSetUp(pageState: EmptyPageState) {
                pageState.setContent("未查到数据")
              }
            })
        }, {
          finishParentRefresh()
          viewBinding.refreshContent.finishRefresh()
          viewBinding.pslContent.show(
            ErrorPageState::class.java,
            object : OnStateSetUpListener<ErrorPageState> {
              override fun onStateSetUp(pageState: ErrorPageState) {
                pageState.setContent(it.message.getOrDefault())
                pageState.setNextOptionClickListener { refresh() }
              }
            })
        })
      }
    }

    viewBinding.recyclerList.apply {
      closeDefaultAnim()
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_8))
          .build()
      )
      adapter = _partTimeJobListAdapter?.withLoadStateFooter(
        LoadStateFooterAdapter { _partTimeJobListAdapter?.retry() })
    }

    lifecycleScope.launch {
      viewModel.partTimeJobListFlow.collectLatest {
        _partTimeJobListAdapter?.submitData(it)
      }
    }
  }

  private fun finishParentRefresh() {
    if (parentFragment is GeekHomeFragmentV3) {
      (parentFragment as GeekHomeFragmentV3).finishRefresh()
    }
  }

  override fun refreshPage() {
    _partTimeJobListAdapter?.refresh()
  }
}