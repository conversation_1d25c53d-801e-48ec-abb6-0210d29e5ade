package com.bxkj.personal.ui.activity.jobdetails.itemviewbinder;

import android.app.Activity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.jrzp.user.data.JobData;
import com.bxkj.personal.R;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.home.itemviewbinder
 * @Description:
 * @TODO: TODO
 * @date 2018/3/31
 */

public class RecommendJobViewBinder implements ItemViewBinder<JobData> {

  private Activity mActivity;

  public RecommendJobViewBinder(Activity activity) {
    mActivity = activity;
  }

  @Override
  public void onBindViewHolder(SuperViewHolder holder, JobData item, int position) {
    holder.setText(R.id.tv_job_name, item.getName());

    TextView tvJobArea = holder.findViewById(R.id.tv_job_area);
    if (CheckUtils.isNullOrEmpty(item.getQuName())) {
      tvJobArea.setVisibility(View.GONE);
    } else {
      tvJobArea.setVisibility(View.VISIBLE);
      holder.setText(R.id.tv_job_area, item.getQuName());
    }

    if (CheckUtils.isNullOrEmpty(item.getQuaName()) || item.getQuaName().equals("请选择")) {
      holder.setText(R.id.tv_job_degree, "学历不限");
    } else {
      holder.setText(R.id.tv_job_degree, item.getQuaName());
    }
    if (CheckUtils.isNullOrEmpty(item.getWtName()) || item.getWtName().equals("请选择")) {
      holder.setText(R.id.tv_job_exp, "经验不限");
    } else {
      holder.setText(R.id.tv_job_exp, item.getWtName());
    }
    holder.setText(R.id.tv_job_publish_enterprise, item.getComName());
    holder.setText(R.id.tv_date, item.getFormatPublishDate());

    TextView tvJobType = holder.findViewById(R.id.tv_type);
    if (item.emptyNatureName()) {
      tvJobType.setVisibility(View.GONE);
    } else {
      tvJobType.setVisibility(View.VISIBLE);
      tvJobType.setText(item.getJnName());
    }

    TextView tvIdentity = holder.findViewById(R.id.tv_identity);
    if (CheckUtils.isNullOrEmpty(item.getIdentityRequire())) {
      tvIdentity.setVisibility(View.GONE);
    } else {
      tvIdentity.setVisibility(View.VISIBLE);
      tvIdentity.setText(item.getIdentityRequire());
    }

    TextView tvPartner = holder.findViewById(R.id.tv_partner);
    if (CheckUtils.isNullOrEmpty(item.getPartnerNature())) {
      tvPartner.setVisibility(View.GONE);
    } else {
      tvPartner.setVisibility(View.VISIBLE);
      tvPartner.setText(item.getPartnerNature());
    }

    ImageView ivCover = holder.findViewById(R.id.iv_video_cover);
    TextView tvSalary = holder.findViewById(R.id.tv_job_wages);
    if (!CheckUtils.isNullOrEmpty(item.getVideoPic())) {
      ivCover.setVisibility(View.VISIBLE);
      ImageLoader.loadImage(holder.itemView.getContext(),
          new GlideLoadConfig.Builder().url(item.getVideoPic())
              .into(ivCover)
              .radius(DensityUtils.dp2px(holder.itemView.getContext(), 4))
              .build());
    } else {
      tvSalary.setText(item.getConvertSalary());
      ivCover.setVisibility(View.GONE);
    }

    holder.itemView.setOnClickListener(view -> mActivity.startActivity(
        JobDetailsActivityV2.Companion.newIntent(mActivity, item.getId())));
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_recycler_job_details_recommed_item;
  }
}
