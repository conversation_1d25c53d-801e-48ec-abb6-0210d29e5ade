package com.bxkj.personal.ui.fragment.home;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.bxkj.common.util.DensityUtils;
import com.bxkj.personal.R;
import com.bxkj.common.adapter.indicator.OnTabClickListener;
import com.bxkj.personal.data.NewsTypeItemData;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.resume
 * @Description:
 * @TODO: TODO
 * @date 2018/8/10
 */
public class NewsTypeTabLayoutAdapter extends CommonNavigatorAdapter {

  private List<NewsTypeItemData> mItems;
  private int mCurrentIndex;

  public NewsTypeTabLayoutAdapter(List<NewsTypeItemData> items) {
    mItems = items;
  }

  @Override
  public int getCount() {
    return mItems.size();
  }

  @Override
  public IPagerTitleView getTitleView(Context context, int index) {
    final NewsTypeItemData currentItem = mItems.get(index);
    CommonPagerTitleView commonPagerTitleView = new CommonPagerTitleView(context);
    int padding = DensityUtils.dp2px(context, 10);
    commonPagerTitleView.setPadding(padding, 0, padding, 0);
    View customView =
        LayoutInflater.from(context).inflate(R.layout.personal_layout_custom_news_type_tab, null);
    TextView tvTabItem = customView.findViewById(R.id.tv_tab_item);
    tvTabItem.setText(currentItem.getName());
    ImageView ivExpand = customView.findViewById(R.id.iv_expand);
    if (currentItem.isShowExpand()) {
      ivExpand.setVisibility(View.VISIBLE);
    } else {
      ivExpand.setVisibility(View.GONE);
    }
    commonPagerTitleView.setContentView(customView);
    commonPagerTitleView.setOnPagerTitleChangeListener(
        new CommonPagerTitleView.OnPagerTitleChangeListener() {
          @Override
          public void onSelected(int i, int i1) {
            mCurrentIndex = index;
            tvTabItem.setSelected(true);
            tvTabItem.setTypeface(Typeface.DEFAULT_BOLD);
            ivExpand.setSelected(true);
          }

          @Override
          public void onDeselected(int i, int i1) {
            tvTabItem.setSelected(false);
            tvTabItem.setTypeface(Typeface.DEFAULT);
            ivExpand.setSelected(false);
          }

          @Override
          public void onLeave(int i, int i1, float v, boolean b) {

          }

          @Override
          public void onEnter(int i, int i1, float v, boolean b) {

          }
        });
    commonPagerTitleView.setOnClickListener(view -> {
      if (mOnTabItemClickListener != null) {
        mOnTabItemClickListener.onTabClicked(commonPagerTitleView, index);
      }
    });
    commonPagerTitleView.setContentPositionDataProvider(
        new CommonPagerTitleView.ContentPositionDataProvider() {
          @Override
          public int getContentLeft() {
            Rect bound = new Rect();
            tvTabItem.getPaint()
                .getTextBounds(tvTabItem.getText().toString(), 0, tvTabItem.getText().length(),
                    bound);
            int contentWidth = bound.width();
            return tvTabItem.getLeft() + tvTabItem.getWidth() / 2 - contentWidth / 2;
          }

          @Override
          public int getContentTop() {
            Paint.FontMetrics metrics = tvTabItem.getPaint().getFontMetrics();
            float contentHeight = metrics.bottom - metrics.top;
            return (int) ((tvTabItem.getHeight() / 2) - contentHeight / 2);
          }

          @Override
          public int getContentRight() {
            Rect bound = new Rect();
            tvTabItem.getPaint()
                .getTextBounds(tvTabItem.getText().toString(), 0, tvTabItem.getText().length(),
                    bound);
            int contentWidth = bound.width();
            return tvTabItem.getLeft() + tvTabItem.getWidth() / 2 + contentWidth / 2;
          }

          @Override
          public int getContentBottom() {
            Paint.FontMetrics metrics = tvTabItem.getPaint().getFontMetrics();
            float contentHeight = metrics.bottom - metrics.top;
            return (int) ((tvTabItem.getHeight() / 2) - contentHeight / 2);
          }
        });
    return commonPagerTitleView;
  }

  @Override
  public IPagerIndicator getIndicator(Context context) {
    LinePagerIndicator indicator = new LinePagerIndicator(context);
    indicator.setMode(LinePagerIndicator.MODE_EXACTLY);
    indicator.setLineHeight(DensityUtils.dp2px(context, 3));
    indicator.setLineWidth(DensityUtils.dp2px(context, 10));
    indicator.setColors(ContextCompat.getColor(context, R.color.cl_ff7405));
    return indicator;
  }

  private OnTabClickListener mOnTabItemClickListener;

  public void setOnTabItemClickListener(OnTabClickListener onTabItemClickListener) {
    mOnTabItemClickListener = onTabItemClickListener;
  }

  public int getCurrentIndex() {
    return mCurrentIndex;
  }

  public void setTitles(@NotNull List<NewsTypeItemData> newsTypeList) {
    mItems = newsTypeList;
    notifyDataSetChanged();
  }

  public List<NewsTypeItemData> getData() {
    return mItems;
  }
}
