package com.bxkj.personal.ui.fragment.home

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import cn.jzvd.Jzvd
import com.baidu.location.BDLocation
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.fragmentback.FragmentBackPressHandler
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.util.location.LocationManager.OnLocationListener
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.bxkj.jrzp.support.scan.ui.qrcode.ScanQrCodeActivity
import com.bxkj.personal.R
import com.bxkj.personal.data.NewsTypeItemData
import com.bxkj.personal.data.SearchHotKeyItemData
import com.bxkj.personal.databinding.PersonalFragmentGovRecruitmentBinding
import com.bxkj.personal.enums.NewsType
import com.bxkj.personal.ui.activity.arealist.AreaListActivity
import com.bxkj.personal.ui.activity.arealist.EXTRA_RESULT_AREA
import com.bxkj.personal.ui.activity.arealist.RESULT_SELECTED
import com.bxkj.personal.ui.activity.main.MainActivity
import com.bxkj.personal.ui.activity.minesearchjobs.MineSearchJobsActivity
import com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity
import com.bxkj.personal.ui.fragment.homenews.RecruitmentNewsFragment
import com.bxkj.personal.ui.fragment.homesubjob.HomeSubJobFragment
import com.bxkj.personal.ui.fragment.qa.QAFragment
import com.bxkj.personal.ui.fragment.study.StudyFragment
import com.bxkj.personal.weight.SelectCityView
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.zaaach.citypicker.CityPicker
import com.zaaach.citypicker.adapter.OnPickListener
import com.zaaach.citypicker.model.City
import com.zaaach.citypicker.model.LocatedCity
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.home
 * @Description: 政企招聘
 * @version V1.0
 */

const val TO_SELECT_NEWS_AREA_CODE = 1

class GovRecruitmentFragment :
  BaseDBFragment<PersonalFragmentGovRecruitmentBinding, GovRecruitmentViewModel>(),
  View.OnClickListener, FragmentBackPressHandler {

  private var mCityPicker: CityPicker? = null
  private var mCommonNavigator: CommonNavigator? = null

  private var mDropDownPopup: DropDownPopup? = null

  private var mNewsPagerAdapter: CommonPagerAdapter? = null
  private var mSelectAddressView: SelectCityView? = null

  override fun getViewModelClass(): Class<GovRecruitmentViewModel> = GovRecruitmentViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_gov_recruitment

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    setupTitleBar()
  }

  private fun setupTitleBar() {
    statusBarManager.titleBar(viewBinding.titleBar).statusBarDarkFont(true, 0.4f).init()

    viewBinding.ivBack.setOnClickListener {
      activity?.finish()
    }
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    if (hidden) {
      Jzvd.releaseAllVideos()
    } else {
      statusBarManager.init()
    }
  }

  override fun onResume() {
    super.onResume()
    statusBarManager.init()
  }

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupCityPicker()

    subscribeViewModelEvent()
    subscribeRxMsg()
    setupDropDownPopup()
    setupNewsPagerAdapter()
    getLocation()
  }

  private fun setupNewsPagerAdapter() {
    mNewsPagerAdapter = CommonPagerAdapter(
      childFragmentManager
    )
    viewBinding.vpNewsList.adapter = mNewsPagerAdapter
  }

  private fun subscribeRxMsg() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) { //手动选择的城市发生了变化
            viewModel.changeSelectCity(
              AreaOptionsData(
                UserUtils.getUserSelectedCityId(),
                UserUtils.getUserSelectedCityName()
              )
            )
          } else if (it.code == RxMsgCode.ACTION_SWITCH_SYS_RECOMMEND || it.code == RxMsgCode.ACTION_USER_ROLE_CHANGE) {
            viewModel.refreshNewsTab()
          }
        }
    )
  }

  fun switchToJobFragment() {
    viewBinding.vpNewsList.currentItem = 3
  }

  private fun setupDropDownPopup() {
    mSelectAddressView = SelectCityView(parentActivity)
      .apply {
        setOnCitySelectedListener(object : SelectCityView.OnCitySelectedListener {
          override fun onCitySelected(item: AreaOptionsData) {
            mDropDownPopup?.close()
            UserUtils.saveUserSelectedCityInfo(item.id, item.name)
          }

          override fun onAreaSelected(areaItem: AreaOptionsData) {
            mDropDownPopup?.close()
            viewModel.changeSelectDistract(areaItem)
          }

          override fun onViewAllClicked(type: Int) {
            if (type == CommonApiConstants.GET_CITY_TYPE) {
              mDropDownPopup?.close()
              startActivity(CityPickerActivity.newIntent(requireActivity()))
            } else {
              mDropDownPopup?.close()
              startActivityForResult(
                AreaListActivity.newIntent(
                  parentActivity,
                  UserUtils.getUserSelectedCityId()
                ), TO_SELECT_NEWS_AREA_CODE
              )
            }
          }
        })
      }
    mDropDownPopup = DropDownPopup(
      parentActivity,
      viewBinding.clIndicator
    ).apply {
      addContentViews(mSelectAddressView)
      isOutsideTouchable = true
    }
  }

  private fun getLocation() {
    if (XXPermissions.isGranted(requireActivity(), Permission.ACCESS_FINE_LOCATION)) {
      requestLocation()
    } else {
      viewModel.getCityInfoByCityName(null)
    }
  }

  private fun requestLocation() {
    LocationManager.requestLocation(
      parentActivity,
      object : OnLocationListener {
        override fun onSuccess(location: BDLocation) {
          viewModel.getCityInfoByCityName(location)
        }

        override fun onFailed() {
          viewModel.getCityInfoByCityName(null)
        }
      })
  }

  private fun setupCityPicker() {
    mCityPicker = CityPicker()
      .setLocatedCity(
        LocatedCity(
          UserUtils.getUserLocateCityName(),
          UserUtils.getUserProvinceName(),
          UserUtils.getUserLocateCityId().toString()
        )
      )
      .setFragmentManager(childFragmentManager)
      .enableAnimation(true)
      .setAnimationStyle(R.style.RightPopupAnim)
      .setOnPickListener(object : OnPickListener {
        override fun onPick(position: Int, data: City?) {
          if (data != null) {
            UserUtils.saveUserSelectedCityInfo(data.code.toInt(), data.name)
          }
        }

        override fun onLocate() {
        }
      })
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_scan -> {
          startActivity(ScanQrCodeActivity.newIntent(parentActivity))
        }

        R.id.tv_location -> {
          mCityPicker?.setLocatedCity(
            LocatedCity(
              UserUtils.getUserLocateCityName(),
              CommonApiConstants.NO_TEXT,
              UserUtils.getUserLocateCityId().toString()
            )
          )
          mCityPicker?.show()
        }

        R.id.tv_post -> {
          if (parentActivity is MainActivity) {
            (parentActivity as MainActivity).showPostMenu()
          }
        }

        R.id.tv_search_job -> {
          startActivity(MineSearchJobsActivity.newIntent(parentActivity))
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.newsTypeList.observe(this, Observer {
      setupPager(it)
    })

    viewModel.updateIndicatorCommand.observe(this, Observer {
      updatePager(it)
    })

    viewModel.userCityDistrictList.observe(this, Observer {
      mSelectAddressView?.updateAreaList(it)
    })

    viewModel.searchHotKey.observe(this, Observer {
      val searchKeywordAdapter = SearchKeywordAdapter(parentActivity, it)
      searchKeywordAdapter.setSSClickListener(object : SearchKeywordAdapter.OnSSClickListener {
        override fun onClicked(list: ArrayList<SearchHotKeyItemData>) {
          if (viewBinding.vpNewsList.currentItem < viewModel.getNewsTypeList().size) {
            startActivity(SearchNewsActivity.newIntent(parentActivity, list))
          }
        }
      })
      viewBinding.marqueeHotKeyword.setAdapter(searchKeywordAdapter)
      viewBinding.marqueeHotKeyword.start()
    })
  }

  /**
   * 设置内容
   */
  private fun setupPager(newsTypeList: List<NewsTypeItemData>) {
    val newsPageList = tranPagerList(newsTypeList)
    viewBinding.vpNewsList.offscreenPageLimit = newsTypeList.size
    viewBinding.vpNewsList.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
      override fun onPageScrollStateChanged(state: Int) {
      }

      override fun onPageScrolled(
        position: Int,
        positionOffset: Float,
        positionOffsetPixels: Int,
      ) {
      }

      override fun onPageSelected(position: Int) {
        if (newsTypeList[position].id == NewsType.NEWS_TYPE_JOB) {
          viewBinding.tvSearchJob.visibility = View.VISIBLE
          viewBinding.marqueeHotKeyword.visibility = View.GONE
        } else {
          viewBinding.tvSearchJob.visibility = View.GONE
          viewBinding.marqueeHotKeyword.visibility = View.VISIBLE
        }
      }
    })
    mNewsPagerAdapter?.reset(newsPageList)
    mCommonNavigator = CommonNavigator(context).apply {
      adapter = NewsTypeTabLayoutAdapter(newsTypeList).apply {
        setOnTabItemClickListener(object :
          OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            if (currentIndex == index) {
              if (data[index].id == NewsType.NEWS_TYPE_CITY) {
                mDropDownPopup?.showAsDropDown()
              }
            }
            viewBinding.vpNewsList.currentItem = index
          }
        })
      }
    }
    viewBinding.indicator.navigator = mCommonNavigator
    ViewPagerHelper.bind(viewBinding.indicator, viewBinding.vpNewsList)
    viewBinding.vpNewsList.currentItem = 1
  }

  private fun tranPagerList(newsTypeList: List<NewsTypeItemData>): ArrayList<Fragment> {
    val newsPageList = ArrayList<Fragment>()
    for (typeItem in newsTypeList) {
      when (typeItem.id) {
        NewsType.NEWS_TYPE_QUESTION -> {
          newsPageList.add(QAFragment.newInstance())
        }

        NewsType.NEWS_TYPE_STUDY -> {
          newsPageList.add(StudyFragment.newInstance())
        }

        NewsType.NEWS_TYPE_JOB -> {
          newsPageList.add(HomeSubJobFragment.newInstance())
        }

        else -> {
          newsPageList.add(RecruitmentNewsFragment.newInstance(typeItem.id))
        }
      }
    }
    return newsPageList
  }

  private fun updatePager(newsTypeList: List<NewsTypeItemData>) {
    val pagerList = tranPagerList(newsTypeList)
    if (viewBinding.vpNewsList.childCount != pagerList.size) {
      mNewsPagerAdapter?.reset(pagerList)
    }
    mCommonNavigator?.let {
      (it.adapter as NewsTypeTabLayoutAdapter).setTitles(newsTypeList)
      it.notifyDataSetChanged()
    }
  }

  override fun onDestroyView() {
    super.onDestroyView()
    Jzvd.releaseAllVideos()
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

  override fun onBackPressed(): Boolean {
    return if (mDropDownPopup?.isShowing != false) {
      mDropDownPopup?.dismiss()
      true
    } else {
      false
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_SELECT_NEWS_AREA_CODE && resultCode == RESULT_SELECTED && data != null) {
      data.getParcelableExtra<AreaOptionsData>(EXTRA_RESULT_AREA)?.let {
        viewModel.changeSelectDistract(it)
      }
    }
  }
}