package com.bxkj.personal.ui.fragment.home

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.baidu.location.BDLocation
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.personal.data.NewsTypeItemData
import com.bxkj.personal.data.SearchHotKeyItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.AddressInfoRepo
import com.bxkj.personal.data.source.SearchRepo
import com.bxkj.personal.enums.NewsType
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.home
 * @Description:
 * <AUTHOR>
 * @date 2019/12/23
 * @version V1.0
 */
class GovRecruitmentViewModel @Inject constructor(
    private val mSearchRepo: SearchRepo,
    private val mAddressInfoRepo: AddressInfoRepo,
    private val mAccountRepo: AccountRepo
) : BaseViewModel() {

    private val _newsTypeList = ArrayList<NewsTypeItemData>()
    val newsTypeList = MutableLiveData<List<NewsTypeItemData>>()
    val searchHotKey = MutableLiveData<List<SearchHotKeyItemData>>()
    val userCityDistrictList = MutableLiveData<List<AreaOptionsData>>()
    val updateIndicatorCommand =
        LiveEvent<List<NewsTypeItemData>>()
    val refreshCityPageCommand = LiveEvent<Void>()
    val refreshDistrictPageCommand = LiveEvent<Void>()
    val refreshJobPageCommand = LiveEvent<Void>()

    init {
        getSearchHotKeyword()
    }

    private fun getSearchHotKeyword() {
        mSearchRepo.getSearchHotKey(12, object : ResultDataCallBack<List<SearchHotKeyItemData>> {
            override fun onSuccess(data: List<SearchHotKeyItemData>?) {
                searchHotKey.value = data
            }

            override fun onError(respondThrowable: RespondThrowable) {
                searchHotKey.value = emptyList()
            }
        })
    }

    fun refreshNewsTab(isRefresh: Boolean = true) {
        if (UserUtils.logged()) {
            viewModelScope.launch {
                mAccountRepo.checkOpenPersonalizedPush(getSelfUserID())
                    .handleResult({
                        if (it == true) {
                            refreshNewsIndicator(refreshNewsPager = isRefresh)
                        } else {
                            refreshNewsIndicator("热门", isRefresh)
                        }
                    }, {
                        refreshNewsIndicator(refreshNewsPager = isRefresh)
                    })
            }
        } else {
            refreshNewsIndicator(refreshNewsPager = isRefresh)
        }
    }

    private fun refreshNewsIndicator(
        recommendTabName: String = "推荐",
        refreshNewsPager: Boolean = true
    ) {
        _newsTypeList.clear()
        _newsTypeList.add(NewsTypeItemData.fromCustom(NewsType.NEWS_TYPE_FOLLOW, "关注"))
        _newsTypeList.add(
            NewsTypeItemData.fromCustom(
                NewsType.NEWS_TYPE_RECOMMEND,
                recommendTabName
            )
        )
        val showNewsAddress =
            if (CheckUtils.isNullOrEmpty(UserUtils.getUserSelectedDistrictName())) {
                UserUtils.getUserSelectedCityName()
            } else {
                UserUtils.getUserSelectedDistrictName()
            }
        _newsTypeList.add(
            NewsTypeItemData.fromCustom(
                NewsType.NEWS_TYPE_CITY,
                showNewsAddress, true
            )
        )
        // if (UserUtils.isPersonalRole()) {
        //     _newsTypeList.add(NewsTypeItemData.fromCustom(NewsType.NEWS_TYPE_JOB, "直聘"))
        // }
        _newsTypeList.add(NewsTypeItemData.fromCustom(NewsType.NEWS_TYPE_QUESTION, "问答"))
        _newsTypeList.add(NewsTypeItemData.fromCustom(NewsType.NEWS_TYPE_STUDY, "学习"))
        _newsTypeList.add(NewsTypeItemData.fromCustom(NewsType.NEWS_TYPE_NORMAL, "公招公考"))
        if (refreshNewsPager) {    //需要刷新viewPager
            updateIndicatorCommand.value = _newsTypeList
        } else {    //仅刷新indicator
            newsTypeList.value = _newsTypeList
        }
    }

    /**
     * 切换城市
     */
    fun changeSelectCity(cityInfo: AreaOptionsData) {
        refreshCityPageCommand.callAll()
        refreshJobPageCommand.call()
        getDistractList(cityInfo.id)
    }

    /**
     * 却换区县
     */
    fun changeSelectDistract(item: AreaOptionsData) {
        if (UserUtils.getUserSelectedDistrictId() != item.id) {
            UserUtils.saveUserSelectedDistrictInfo(item.id, item.name)
            refreshCityPageCommand.call()
            refreshNewsTab()
        }
    }

    fun getCityInfoByCityName(location: BDLocation?) {
        val city = if (location?.city.isNullOrBlank()) {
            "杭州"
        } else {
            location?.city?.substring(0, location.city.length - 1) ?: "杭州"
        }
        mAddressInfoRepo.getAddressInfoByName(
            CommonApiConstants.GET_CITY_TYPE,
            city,
            object : ResultDataCallBack<AreaOptionsData> {
                override fun onSuccess(data: AreaOptionsData) {
                    UserUtils.saveUserLocateCityData(data.id, data.name)
                    //不存在用户选择地址(第一次定位)
                    if (UserUtils.getUserSelectedCityId() == CommonApiConstants.NO_ID || CheckUtils.isNullOrEmpty(
                            UserUtils.getUserSelectedCityName()
                        )
                    ) {
                        UserUtils.saveUserSelectedCityInfo(data.id, data.name)
                        UserUtils.saveUserProvinceInfo(data.pid, data.pName)
                    }
                    getDistractList(UserUtils.getUserSelectedCityId(), false)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    refreshNewsTab(false)
                }
            })
    }

    /**
     * 获取区县列表
     */
    private fun getDistractList(parentId: Int, isRefresh: Boolean = true) {
        showLoading()
        mAddressInfoRepo.getAddressList(
            CommonApiConstants.GET_AREA_TYPE,
            parentId,
            object : ResultDataCallBack<List<AreaOptionsData>> {
                override fun onSuccess(data: List<AreaOptionsData>) {
                    hideLoading()
                    userCityDistrictList.value = data
                    refreshDistrictPageCommand.call()
                    refreshNewsTab(isRefresh)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    hideLoading()
                    UserUtils.clearUserDistrictInfo()
                    refreshNewsTab(isRefresh)
                }
            })
    }

    fun getNewsTypeList(): List<NewsTypeItemData> {
        return _newsTypeList
    }
}