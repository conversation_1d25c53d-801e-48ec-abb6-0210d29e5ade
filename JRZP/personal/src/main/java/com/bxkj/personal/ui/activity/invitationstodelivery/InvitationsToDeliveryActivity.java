package com.bxkj.personal.ui.activity.invitationstodelivery;

import android.content.Context;
import android.content.Intent;

import android.view.View;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.therouter.router.Route;
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter;
import com.bxkj.common.adapter.indicator.OnTabClickListener;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.personal.R;

import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.ViewPagerHelper;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.invitationstodelivery
 * @Description: 投递邀請
 * @TODO: TODO
 * @date 2018/9/26
 */
@Route(path = InviteToDeliveryNavigation.PATH)
public class InvitationsToDeliveryActivity extends BaseDaggerActivity {

  private MagicIndicator tabLayout;
  private ViewPager vpInvitationsToDelivery;

  public static void start(Context context) {
    Intent starter = new Intent(context, InvitationsToDeliveryActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_invitations_to_delivery;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.invitations_to_delivery));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    vpInvitationsToDelivery.setAdapter(
      new InvitationsToDeliveryPagerAdapter(getSupportFragmentManager(),
        getResources().getStringArray(R.array.invitations_to_delivery_states)));
    vpInvitationsToDelivery.setOffscreenPageLimit(3);

    final MagicIndicatorAdapter magicIndicatorAdapter = new MagicIndicatorAdapter(
      getResources().getStringArray(R.array.invitations_to_delivery_states));
    magicIndicatorAdapter.setOnTabClickListener(new OnTabClickListener() {
      @Override public void onTabClicked(@NonNull View v, int index) {
        vpInvitationsToDelivery.setCurrentItem(index);
      }
    });

    final CommonNavigator commonNavigator = new CommonNavigator(this);
    commonNavigator.setAdjustMode(true);
    commonNavigator.setAdapter(magicIndicatorAdapter);
    tabLayout.setNavigator(commonNavigator);
    ViewPagerHelper.bind(tabLayout, vpInvitationsToDelivery);
  }

  private void bindView(View bindSource) {
    tabLayout = bindSource.findViewById(R.id.tab_layout);
    vpInvitationsToDelivery = bindSource.findViewById(R.id.vp_invitations_to_delivery);
  }
}
