package com.bxkj.personal.ui.activity.scanloginconfirm

import android.app.Activity
import androidx.lifecycle.Observer
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityScanLoginConfirmBinding

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.scanloginconfirm
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/7/16
 * @version V1.0
 */
@Route(path = ScanLoginConfirmNavigation.PATH)
class ScanLoginConfirmActivity :
  BaseDBActivity<PersonalActivityScanLoginConfirmBinding, ScanLoginConfirmViewModel>(),
  View.OnClickListener {

  companion object {

    fun newIntent(context: Context, channel: String): Intent {
      val intent = Intent(context, ScanLoginConfirmActivity::class.java)
      intent.putExtra(ScanLoginConfirmNavigation.EXTRA_LOGIN_CHANNEL, channel)
      return intent
    }
  }

  override fun getViewModelClass(): Class<ScanLoginConfirmViewModel> =
    ScanLoginConfirmViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_scan_login_confirm

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this
    viewBinding.viewModel = viewModel
    viewModel.channel = intent.getStringExtra(ScanLoginConfirmNavigation.EXTRA_LOGIN_CHANNEL)
    subscribeViewModelEvent()
  }

  private fun subscribeViewModelEvent() {
    viewModel.loginSuccessEvent.observe(this, Observer {
      showToast(getString(R.string.account_login_success))
      setResult(Activity.RESULT_OK)
      finish()
    })

    viewModel.loginErrorEvent.observe(this, Observer {
      showToast(getString(R.string.login_for_computer_error_tips))
      finish()
    })
  }

  override fun onClick(v: View?) {
    if (v?.id == R.id.tv_cancel) {
      finish()
    }
  }
}