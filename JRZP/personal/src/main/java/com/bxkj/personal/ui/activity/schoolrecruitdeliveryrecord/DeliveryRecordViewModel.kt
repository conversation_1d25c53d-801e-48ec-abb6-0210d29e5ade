package com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.personal.data.SchoolRecruitRecordData
import com.bxkj.personal.data.source.AccountRepo
import com.elvishew.xlog.XLog
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/4/13
 */
class DeliveryRecordViewModel @Inject constructor(
  private val accountRepo: AccountRepo
) : BaseViewModel() {

  //是否显示全选
  val showSelectAll = MutableLiveData<Boolean>().apply { value = false }

  val recordListViewModel = RefreshListViewModel()
  private val _selectedRecord = ArrayList<SchoolRecruitRecordData>()
  val selectedRecord = MutableLiveData<List<SchoolRecruitRecordData>>()

  private var recordType = 0

  init {
    initRecordListViewModel()
  }

  private fun initRecordListViewModel() {
    recordListViewModel.setOnLoadDataListener { pageIndex ->
      viewModelScope.launch {
        accountRepo.getSchoolRecruitDeliveryRecord(
          recordType,
          pageIndex,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          it?.let {
            recordListViewModel.autoAddAll(it.dataList)
          } ?: let { recordListViewModel.noMoreData() }
        }, {
          if (it.isNoDataError) {
            if (pageIndex == 1) {
              showSelectAll.value = false
            }
            recordListViewModel.noMoreData()
          } else {
            recordListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start(type: Int) {
    recordType = type
    recordListViewModel.refresh()
  }

  /**
   * 切换显示全选状态
   */
  fun switchShowSelectAllState(show: Boolean) {
    showSelectAll.value = show
    notifySelectedChange()
  }

  /**
   * 切换全选状态
   */
  fun switchSelectAllState() {
    val loadedAllRecord = recordListViewModel.data as List<SchoolRecruitRecordData>
    if (loadedAllRecord.size != _selectedRecord.size) {
      _selectedRecord.clear()
      _selectedRecord.addAll(loadedAllRecord)
    } else {
      _selectedRecord.clear()
    }
    notifySelectedChange()
  }

  /**
   * 选中记录
   */
  fun getSelectedRecordValue(): List<SchoolRecruitRecordData> {
    return _selectedRecord
  }

  /**
   * 切换单个选中项状态
   */
  fun switchRecordSelectedState(item: SchoolRecruitRecordData) {
    if (_selectedRecord.contains(item)) {
      _selectedRecord.remove(item)
    } else {
      _selectedRecord.add(item)
    }
    notifySelectedChange()
  }

  /**
   * 通知数据变化
   */
  private fun notifySelectedChange() {
    selectedRecord.value = ArrayList(_selectedRecord)
  }

  fun deleteSelected() {
    deleteRecord(_selectedRecord)
  }

  fun deleteRecord(list: List<SchoolRecruitRecordData>) {
    showLoading()
    viewModelScope.launch {
      accountRepo.deleteSchoolRecruitDeliveryRecord(
        list.map { it.id.toString() }.appendItem()
      ).handleResult({
        showToast("删除成功")
        hideLoading()
        XLog.d(list)
        XLog.d(recordListViewModel.data)
        if (recordListViewModel.removeAll(list) == 0) {
          recordListViewModel.refresh()
        }
        _selectedRecord.removeAll(list)
        notifySelectedChange()
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  fun canFinish(): Boolean {
    if (showSelectAll.value == true) {
      showSelectAll.value = false
      return false
    } else {
      return true
    }
  }
}