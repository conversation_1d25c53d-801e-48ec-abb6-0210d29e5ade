package com.bxkj.personal.ui.activity.myresume

import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.R
import com.bxkj.personal.data.EduBackgroundItemData
import com.bxkj.personal.data.WorkExpItemData
import com.bxkj.personal.databinding.PersonalActivityMyResumeDeatilsV2Binding
import com.bxkj.personal.ui.activity.jobintention.JobIntentionActivity
import com.bxkj.personal.ui.activity.main.PersonalMainNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.activity.myresume.edubackground.EduBackgroundActivity
import com.bxkj.personal.ui.activity.myresume.workexp.EditWorkExpActivity
import com.bxkj.personal.ui.activity.personalmember.PersonalMemberNavigation
import com.bxkj.personal.ui.activity.uploadattechmentresume.AttachmentResumeNavigation

/**
 * 简历详情
 * @author: sanjin
 * @date: 2022/9/8
 */
@Route(path = MyResumeDetailsNavigation.PATH)
class MyResumeDetailsActivityV2 :
  BaseDBActivity<PersonalActivityMyResumeDeatilsV2Binding, MyResumeDetailsViewModel>(),
  View.OnClickListener {

  private val _extraEditResumeAction by lazy {
    intent.getIntExtra(
      ResumeRouteConstant.EXTRA_EDIT_RESUME_ACTION,
      ResumeRouteConstant.ACTION_EDIT_AND_BACK
    )
  }

  private val _toEditBasicInfoLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditBasicInfoResult(it)
    }

  private val _toEditJobIntentionLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditJobIntentionResult(it)
    }

  private val _addWorkExpLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditWorkExpResult(it)
    }

  private val _addEduExpLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditEduExpResult(it)
    }

  private val _editSelfEvaluationLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditSelfEvaluationResult(it)
    }

  private val _editAttachResumeLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleEditAttachResumeResult(it)
    }

  override fun getViewModelClass(): Class<MyResumeDetailsViewModel> =
    MyResumeDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_my_resume_deatils_v2

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupPageShowMode()

    setupWorkExpListAdapter()
    setupEduExpListAdapter()

    subscribeViewModelEvent()

    viewModel.start()
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkUserGeekVipStatus()
  }

  private fun setupPageShowMode() {
    when (_extraEditResumeAction) {
      ResumeRouteConstant.ACTION_CREATE_RESUME -> {
        viewBinding.titleBar.apply {
          setRightText(R.string.personal_resume_details_skip)
          setRightOptionClickListener {
            skip()
          }
        }
      }

      ResumeRouteConstant.ACTION_EDIT_AND_BACK -> {
        viewBinding.tvSkip.visibility = View.GONE
      }
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.tv_skip -> {
          skip()
        }

        R.id.tv_join_member -> {
          PersonalMemberNavigation.create().start()
        }

        else -> {
          viewModel.continueCompleteResume()
        }
      }
    }
  }

  private fun skip() {
    if (_extraEditResumeAction == ResumeRouteConstant.ACTION_CREATE_RESUME_FOR_REGISTER) {
      PersonalMainNavigation.navigate().start()
      finish()
    } else {
      setResult(RESULT_OK)
      finish()
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.toEditBasicInfoCommand.observe(this, EventObserver {
      _toEditBasicInfoLauncher.launch(
        MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_EDIT_AND_BACK)
          .createIntent(this)
      )
    })

    viewModel.toEditJobIntentionCommand.observe(this, EventObserver {
      _toEditJobIntentionLauncher.launch(JobIntentionActivity.newIntent(this, it))
    })

    viewModel.toAddWorkExpCommand.observe(this, EventObserver {
      _addWorkExpLauncher.launch(
        EditWorkExpActivity.newIntent(
          this,
          it
        )
      )
    })

    viewModel.toAddEduExpCommand.observe(this, EventObserver {
      _addEduExpLauncher.launch(
        EduBackgroundActivity.newIntent(
          this,
          EduBackgroundActivity.CREATE_TYPE,
          it,
          CommonApiConstants.NO_ID
        )
      )
    })

    viewModel.toEditSelfEvaluationCommand.observe(this, EventObserver {
      _editSelfEvaluationLauncher.launch(
        SelfEvaluationActivity.newIntent(
          this,
          it.resid.getOrDefault(),
          it.selfEvaluation.getOrDefault()
        )
      )
    })

    viewModel.toEditAttachResumeCommand.observe(this, EventObserver {
      _editAttachResumeLauncher.launch(AttachmentResumeNavigation.create().createIntent(this))
    })
  }

  private fun setupWorkExpListAdapter() {
    val workExpListAdapter =
      SimpleDiffListAdapter<WorkExpItemData>(
        R.layout.personal_recycler_resume_work_exp_item,
        WorkExpItemData.DiffCallback()
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getData()?.get(position)?.let {
              if (v.id == R.id.iv_edit) {
                _addWorkExpLauncher.launch(
                  EditWorkExpActivity.newIntent(this@MyResumeDetailsActivityV2, it.resumeId, it.id)
                )
              }
            }
          }
        }, R.id.iv_edit)
      }
    viewBinding.recyclerWorkExp.apply {
      layoutManager = LinearLayoutManager(this@MyResumeDetailsActivityV2)
      adapter = workExpListAdapter
    }
  }

  private fun setupEduExpListAdapter() {
    val eduExpListAdapter = SimpleDiffListAdapter<EduBackgroundItemData>(
      R.layout.personal_recycler_resume_edu_exp_item,
      EduBackgroundItemData.DiffCallabck()
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getData()?.get(position)?.let {
            if (v.id == R.id.iv_edit) {
              _addEduExpLauncher.launch(
                EduBackgroundActivity.newIntent(
                  this@MyResumeDetailsActivityV2,
                  EduBackgroundActivity.UPDATE_TYPE,
                  it.resumeId,
                  it.id
                )
              )
            }
          }
        }
      }, R.id.iv_edit)
    }
    viewBinding.recyclerEducation.apply {
      layoutManager = LinearLayoutManager(this@MyResumeDetailsActivityV2)
      adapter = eduExpListAdapter
    }
  }
}