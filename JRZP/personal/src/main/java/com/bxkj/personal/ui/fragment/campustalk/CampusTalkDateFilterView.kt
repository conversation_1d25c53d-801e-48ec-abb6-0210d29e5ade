package com.bxkj.personal.ui.fragment.campustalk

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalLayoutCampusTalkDateFilterBinding

/**
 * Description:宣讲会日期筛选
 * Author:Sanjin
 **/
class CampusTalkDateFilterView constructor(context: Context, attributeSet: AttributeSet? = null, defStyle: Int = 0) :
    LinearLayout(context, attributeSet, defStyle) {

    private val viewBinding by lazy {
        PersonalLayoutCampusTalkDateFilterBinding.inflate(
            LayoutInflater.from(context),
            this,
            true
        )
    }

    private var _dateListAdapter: SuperAdapter<String>? = null
    private var _selectDatePosition: Int = 0

    private var _onDateSelectedListener: OnDateSelectedListener? = null

    private val _dateOptions = arrayListOf("全部", "未来", "过去", "自选")

    init {
        setupDateList()
    }

    private fun setupDateList() {
        _dateListAdapter = object : SuperAdapter<String>(context, R.layout.common_recycler_menu_select_item) {
            override fun convert(holder: SuperViewHolder, viewType: Int, item: String, position: Int) {
                holder.setText(R.id.tv_item, item)
                holder.findViewById<TextView>(R.id.tv_item).isSelected = position == _selectDatePosition
                val ivSelected = holder.findViewById<ImageView>(R.id.iv_selected)
                ivSelected.visibility = if (position == _selectDatePosition) VISIBLE else GONE
                holder.itemView.setOnClickListener {
                    if (position == 3) {
                        _onDateSelectedListener?.onDateSelected(DATE_FLAG_CUSTOM)
                    } else {
                        if (_selectDatePosition != position) {
                            _selectDatePosition = position
                            notifyDataSetChanged()
                            val convertFlag = if (position == 0) "" else position.toString()
                            _onDateSelectedListener?.onDateSelected(convertFlag)
                        }
                    }
                }
            }
        }
        viewBinding.recyclerDate.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = _dateListAdapter
        }

        _dateListAdapter?.data = _dateOptions
    }

    fun setOnDateSelectedListener(listener: OnDateSelectedListener) {
        _onDateSelectedListener = listener
    }

    fun changeCustomDate(date: String) {
        _selectDatePosition = 3
        _dateOptions[3] = date
        _dateListAdapter?.data = _dateOptions
        _dateListAdapter?.notifyDataSetChanged()
        _onDateSelectedListener?.onDateSelected(date)
    }

    interface OnDateSelectedListener {

        fun onDateSelected(dateFlag: String)
    }

    companion object {

        const val DATE_FLAG_CUSTOM = "CUSTOM"
    }
}