package com.bxkj.personal.weight.filternewsdialog;

import android.view.View;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;
import com.bxkj.common.widget.filterpopup.FilterGroupTitle;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.FilterNewsDialog
 * @Description: 筛选资讯头部绑定
 * @TODO: TODO
 * @date 2018/5/3
 */

public class FilterNewsTitleViewBinder implements ItemViewBinder<FilterGroupTitle> {
    @Override
    public void onBindViewHolder(SuperViewHolder holder, FilterGroupTitle item, int p) {
        holder.setText(R.id.tv_group_title, item.getGroupTitle());
        holder.setText(R.id.tv_more, item.getMoreTitle());
        holder.findViewById(R.id.tv_more).setVisibility(item.isShowMoreTitle() ? View.VISIBLE : View.GONE);
        if (item.isShowMoreTitle()) {
            holder.itemView.setOnClickListener(view -> {
                if (OnGroupTitleClickListener != null) {
                    OnGroupTitleClickListener.onGroupTitleClick(item.getTag());
                }
            });
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.personal_recycler_filter_news_group_title;
    }

    private OnGroupTitleClickListener OnGroupTitleClickListener;

    public void setOnGroupTitleClickListener(OnGroupTitleClickListener onGroupTitleClickListener) {
        OnGroupTitleClickListener = onGroupTitleClickListener;
    }

    public interface OnGroupTitleClickListener {
        void onGroupTitleClick(int position);
    }

}
