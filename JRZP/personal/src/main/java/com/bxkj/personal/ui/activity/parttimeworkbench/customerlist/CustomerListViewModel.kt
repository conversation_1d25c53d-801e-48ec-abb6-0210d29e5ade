package com.bxkj.personal.ui.activity.parttimeworkbench.customerlist

import android.provider.CallLog.Calls
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.support.db.entry.CallLogInfo
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchFlag
import com.bxkj.personal.data.XSCompanyData
import com.bxkj.personal.data.source.SalesServiceRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * author:Sanjin
 * date:2025/2/10
 **/
class CustomerListViewModel @Inject constructor(
  private val salesServiceRepo: SalesServiceRepo
) : BaseViewModel() {
  val listViewModel = RefreshListViewModel()

  val showCallErrorMsgCommand = MutableLiveData<VMEvent<String>>()

  val callPhoneCommand = MutableLiveData<VMEvent<String>>()

  //1、活跃，2、收藏
  private var enterpriseType: Int = CustomerListFragment.TYPE_ACTIVE

  private var searchKeyword = ""

  private var searchFlag: SearchFlag? = null

  init {
    listViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        salesServiceRepo.getActiveCompanyList(
          enterpriseType,
          currentPage,
          16,
          searchKeyword,
          searchFlag?.id.getOrDefault("1")
        ).handleResult({
          listViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            listViewModel.noMoreData()
          } else {
            listViewModel.loadError()
          }
        })
      }
    }
  }

  fun start(type: Int, searchKeyword: String = "", searchFlag: SearchFlag? = null) {
    enterpriseType = type
    this.searchKeyword = searchKeyword
    this.searchFlag = searchFlag
    listViewModel.refresh()
  }

  fun checkCompanyIsCollect(id: Int, mobileNumber: String) {
    viewModelScope.launch {
      salesServiceRepo.checkIsCollect(id)
        .handleResult({
          callPhoneCommand.value = VMEvent(mobileNumber)
        }, {
          showCallErrorMsgCommand.value = VMEvent(it.errMsg)
        })
    }
  }

  fun addCallLog(callLogInfo: CallLogInfo) {
    if (callLogInfo.duration == 0L) {
      return
    }
    if (callLogInfo.type == Calls.OUTGOING_TYPE) {
      val calledCompany =
        listViewModel.data.find { item -> (item as XSCompanyData).mobile == callLogInfo.number }
      if (calledCompany != null) {
        val companyId = (calledCompany as XSCompanyData).id
        viewModelScope.launch {
          salesServiceRepo.addCallLog(companyId, callLogInfo)
            .handleResult({
              RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.EVENT_CALL_END))
              showToast("通话记录已添加")
            }, {
              showToast(it.errMsg)
            })
        }
      } else {
        showToast("未匹配到企业信息，请联系客服添加记录")
      }
    }
  }
}