package com.bxkj.personal.ui.activity.privacysetting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityPrivacySettingBinding
import com.bxkj.personal.ui.activity.permissionmanagement.PermissionManagementActivity
import com.bxkj.personal.ui.activity.resumeopenstatesetting.ResumeOpenStateSettingActivity
import com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyActivity
import com.bxkj.personal.ui.activity.sysrecomendsetting.SysRecommendSettingActivity

/**
 * @Project: gzgk
 * @Description: 隐私设置
 * @author:45457
 * @date: 2020/7/3
 * @version: V1.0
 */
class PrivacySettingActivity :
  BaseDBActivity<PersonalActivityPrivacySettingBinding, PrivacySettingViewModel>(),
  OnClickListener {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, PrivacySettingActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<PrivacySettingViewModel> =
    PrivacySettingViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_privacy_setting

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this
    viewBinding.viewModel = viewModel

    viewModel.start()
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkLoginState()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.tv_resume_open_state -> {
          startActivity(ResumeOpenStateSettingActivity.newIntent(this))
        }

        R.id.tv_shield_company -> {
          startActivity(ShieldCompanyActivity.newIntent(this))
        }

        R.id.tv_permission_management -> {
          startActivity(PermissionManagementActivity.newIntent(this))
        }

        R.id.tv_sys_recommend -> {
          startActivity(SysRecommendSettingActivity.newIntent(this))
        }
      }
    }
  }
}