package com.bxkj.personal.mvp.presenter;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.mvp.contract.ProfessionalContract;
import com.bxkj.personal.api.PersonalApi;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: Professional
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ProfessionalPresenter extends ProfessionalContract.Presenter {

    private static final String TAG = ProfessionalPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public ProfessionalPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getProfessionalList() {
        mPersonalApi.getProfessionalList()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getProfessionalListSuccess((List<PickerOptionsData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
