package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.widget.filterpopup.FilterOptionData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: GetMoreRequirements
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface GetFilterOptionsContract {
    interface View extends BaseView {
        void getNaturesOfCompanySuccess(List<FilterOptionData> filterOptionDataList);

        void getWorkingExpSuccess(List<FilterOptionData> filterOptionDataList);

        void getSalaryRangeSuccess(List<FilterOptionData> filterOptionDataList);

        void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getNaturesOfCompany();

        public abstract void getWorkingExp();

        public abstract void getSalaryRange();

        public abstract void getAreaList(int type, int parentId);
    }
}
