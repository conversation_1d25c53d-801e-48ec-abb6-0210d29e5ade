package com.bxkj.personal.ui.fragment.top500recruit

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.widget.DropDownMenuView
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.personal.R
import com.bxkj.personal.data.CampusRecruitData
import com.bxkj.personal.databinding.PersonalFragmentCampusRecruitListBinding
import com.bxkj.personal.databinding.PersonalFragmentTop500RecruitBinding
import com.bxkj.personal.ui.activity.campusrecruitdetails.CampusRecruitDetailsActivity
import com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitFragment
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class Top500RecruitFragment :
    BaseDBFragment<PersonalFragmentTop500RecruitBinding, Top500RecruitViewModel>() {

    companion object {

        fun newInstance(): Fragment {
            return Top500RecruitFragment()
        }
    }

    private var _filterDropDownPopup: DropDownPopup? = null

    private var _top500RecruitListAdapter: SimplePageDataAdapter<CampusRecruitData, PersonalFragmentCampusRecruitListBinding>? =
        null

    override fun getViewModelClass(): Class<Top500RecruitViewModel> =
        Top500RecruitViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_fragment_top_500_recruit

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        setupContentList()
        setupRefreshLayout()

        setupFilterPopup()

        subscribeSearchKeywordChange()
    }

    private fun subscribeSearchKeywordChange() {
        (parentFragment as? CampusRecruitFragment)?.let {
            it.searchKeyword.observe(this) { keyword ->
                viewModel.setSearchKeyword(keyword)
                refreshList()
            }
        }
    }

    private fun setupFilterPopup() {
        val filterOptions =
            arrayOf("全部500强", "财富世界500强", "财富中国500强", "中国企业500强", "中国民营企业500强")
        _filterDropDownPopup = DropDownPopup(requireActivity(), viewBinding.flFilter).apply {
            isOutsideTouchable = true
            addContentViews(DropDownMenuView(requireContext()).apply {
                setData(filterOptions)
                setOnItemClickListener { view, position ->
                    _filterDropDownPopup?.dismiss()
                    viewBinding.tvFilterOption.text = filterOptions[position]
                    viewModel.setFilterParams(position)
                    refreshList()
                }
            })
        }

        viewBinding.flFilter.setOnClickListener {
            _filterDropDownPopup?.showAsDropDown()
        }
    }

    private fun setupRefreshLayout() {
        viewBinding.srlContent.setOnRefreshListener {
            _top500RecruitListAdapter?.refresh()
        }
    }

    private fun setupContentList() {
        viewBinding.pslContainer.show(LoadingPageState::class.java)
        _top500RecruitListAdapter =
            SimplePageDataAdapter<CampusRecruitData, PersonalFragmentCampusRecruitListBinding>(
                R.layout.personal_list_top_500_recruit_item,
                CampusRecruitData.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getItemBean(position)?.let {
                            startActivity(CampusRecruitDetailsActivity.newInstance(requireContext(), it.id))
                        }
                    }
                })
                addLoadStateListener { loadStateFlow ->
                    loadStateFlow.handleState(notLoading = {
                        viewBinding.srlContent.finishRefresh()
                        viewBinding.pslContainer.hidden()
                    }, noData = {
                        viewBinding.pslContainer.show(
                            EmptyPageState::class.java,
                            object : OnStateSetUpListener<EmptyPageState> {
                                override fun onStateSetUp(pageState: EmptyPageState) {
                                    pageState.setContent("未查到数据")
                                }
                            })
                    }, error = {
                        viewBinding.pslContainer.show(
                            ErrorPageState::class.java,
                            object : OnStateSetUpListener<ErrorPageState> {
                                override fun onStateSetUp(pageState: ErrorPageState) {
                                    pageState.setContent(it.message.getOrDefault())
                                    pageState.setNextOptionClickListener { refreshList() }
                                }
                            })
                    })
                }
            }

        viewBinding.recyclerList.apply {
            closeDefaultAnim()
            layoutManager = LinearLayoutManager(requireContext())
            adapter = _top500RecruitListAdapter?.withLoadStateFooter(LoadStateFooterAdapter {
                _top500RecruitListAdapter?.retry()
            })
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_8))
                    .build()
            )
        }

        lifecycleScope.launch {
            viewModel.top500RecruitListFlow.collectLatest {
                _top500RecruitListAdapter?.submitData(it)
            }
        }
    }

    private fun refreshList() {
        viewBinding.pslContainer.show(LoadingPageState::class.java)
        _top500RecruitListAdapter?.refresh()
    }
}