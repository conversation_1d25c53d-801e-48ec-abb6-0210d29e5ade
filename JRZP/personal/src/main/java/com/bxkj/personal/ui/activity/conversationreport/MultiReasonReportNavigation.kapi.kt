package com.bxkj.personal.ui.activity.conversationreport

import androidx.annotation.IntDef
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @description:
 * @author:45457
 * @date: 2020/11/3
 * @version: V1.0
 */
class MultiReasonReportNavigation {

    companion object {
        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/multireasonreport"

        //举报会话
        const val REPORT_CONVERSATION = 2

        //举报直播
        const val REPORT_LIVE_SHOW = 3

        //举报职位
        const val REPORT_JOB = 4

        const val EXTRA_REPORT_TYPE = "REPORT_TYPE"
        const val EXTRA_REPORT_ID = "REPORT_ID"

        fun navigate(@ReportType reportType: Int, reportID: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_REPORT_TYPE, reportType)
                .withInt(EXTRA_REPORT_ID, reportID)
        }
    }

    @IntDef(REPORT_CONVERSATION, REPORT_LIVE_SHOW, REPORT_JOB)
    @Retention(SOURCE)
    @Target(VALUE_PARAMETER)
    annotation class ReportType
}