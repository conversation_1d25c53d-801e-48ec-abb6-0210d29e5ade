package com.bxkj.personal.ui.fragment.joblist

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.paging3.PagingViewHolder
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CRecyclerJobItemBinding
import com.bxkj.personal.databinding.PersonalFragmentJobListBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.fragment.homev3.GeekHomeFragmentV3
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description: 最新职位列表
 * @author: sanjin
 * @date: 2022/8/17
 */
class LatestJobListFragment :
  BaseDBFragment<PersonalFragmentJobListBinding, LatestJobListViewModel>(),
  JobListPage {

  private var _jobListAdapter: SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>? = null

  override fun getViewModelClass(): Class<LatestJobListViewModel> =
    LatestJobListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_job_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    subscribeSelectCityChangeEvent()
    subscribeViewModelEvent()

    setupJobList()
  }

  private fun subscribeViewModelEvent() {
    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })
  }

  private fun subscribeSelectCityChangeEvent() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
            _jobListAdapter?.refresh()
          }
        }
    )
  }

  override fun refreshPage() {
    _jobListAdapter?.refresh()
  }

  private fun setupJobList() {
    _jobListAdapter = SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>(
      R.layout.c_recycler_job_item,
      JobData.DiffCallback()
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getItemBean(position)?.let {
            if (v.id == R.id.tv_application) {
              viewModel.sendResumePreCheck(it)
            } else {
              startActivity(JobDetailsActivityV2.newIntent(requireContext(), it.id))
            }
          }
        }
      }, R.id.tv_application)
      addLoadStateListener { loadStateFlow ->
        loadStateFlow.handleState({
          finishParentLoading()
          viewBinding.pslContent.hidden()
        }, {
          finishParentLoading()
          viewBinding.pslContent.show(
            EmptyPageState::class.java,
            object : OnStateSetUpListener<EmptyPageState> {
              override fun onStateSetUp(pageState: EmptyPageState) {
                pageState.setContent("未查到数据")
              }
            })
        }, {
          finishParentLoading()
          viewBinding.pslContent.show(
            ErrorPageState::class.java,
            object : OnStateSetUpListener<ErrorPageState> {
              override fun onStateSetUp(pageState: ErrorPageState) {
                pageState.setContent(it.message.getOrDefault())
                pageState.setNextOptionClickListener { refresh() }
              }
            })
        })
      }
    }

    viewBinding.recyclerContent.apply {
      closeDefaultAnim()
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_6))
          .drawFoot(true)
          .build()
      )
      adapter = _jobListAdapter?.withLoadStateFooter(
        LoadStateFooterAdapter {
          _jobListAdapter?.refresh()
        }
      )
    }

    lifecycleScope.launch {
      viewModel.jobListFlow.collectLatest {
        _jobListAdapter?.submitData(it)
      }
    }
  }

  private fun finishParentLoading() {
    if (parentFragment is GeekHomeFragmentV3) {
      (parentFragment as GeekHomeFragmentV3).finishRefresh()
    }
  }
}