package com.bxkj.personal.ui.activity.searchjobresult;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @date 2018/5/29
 */

public class FilterJobParams implements Parcelable {
  //        var title: String = "",
  //        var companyId: Int = 0,
  //        var provinceId: Int = 0,
  //        var cityId: Int = 0,
  //        var areaId: Int = 0,
  //        var streetId: Int = 0,
  //        var jobFirstClassId: Int = 0,
  //        var jobSecondClassId: Int = 0,
  //        var natureOfCompanyId: Int = 0,
  //        var natureOfJobId: Int = 0,
  //        var degreeId: Int = 0,
  //        var salaryId: Int = 0,
  //        var workingExpId: Int = 0,
  //        var timeId: Int = 0
  private String title = "";
  private int companyId = 0;
  private int provinceId = 0;
  private int cityId = 0;
  private int areaId = 0;
  private int streetId = 0;
  private int jobFirstTypeId = 0;
  private int jobSecondTypeId = 0;
  private int natureOfCompanyId = 0;
  private int natureOfJobId = 0;
  private int degreeId = 0;
  private int salaryId = 0;
  private int workingExpId = 0;
  private int timeId = 0;
  private int sort;
  private String longitude;
  private int teshutype;

  public FilterJobParams() {
  }

  public FilterJobParams(String title) {
    this.title = title;
  }

  public static FilterJobParams getSearchParams(String keyword) {
    return new FilterJobParams(keyword);
  }

  protected FilterJobParams(Parcel in) {
    title = in.readString();
    companyId = in.readInt();
    provinceId = in.readInt();
    cityId = in.readInt();
    areaId = in.readInt();
    streetId = in.readInt();
    jobFirstTypeId = in.readInt();
    jobSecondTypeId = in.readInt();
    natureOfCompanyId = in.readInt();
    natureOfJobId = in.readInt();
    degreeId = in.readInt();
    salaryId = in.readInt();
    workingExpId = in.readInt();
    timeId = in.readInt();
    sort = in.readInt();
    longitude = in.readString();
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeString(title);
    dest.writeInt(companyId);
    dest.writeInt(provinceId);
    dest.writeInt(cityId);
    dest.writeInt(areaId);
    dest.writeInt(streetId);
    dest.writeInt(jobFirstTypeId);
    dest.writeInt(jobSecondTypeId);
    dest.writeInt(natureOfCompanyId);
    dest.writeInt(natureOfJobId);
    dest.writeInt(degreeId);
    dest.writeInt(salaryId);
    dest.writeInt(workingExpId);
    dest.writeInt(timeId);
    dest.writeInt(sort);
    dest.writeString(longitude);
  }

  @Override
  public int describeContents() {
    return 0;
  }

  public static final Creator<FilterJobParams> CREATOR = new Creator<FilterJobParams>() {
    @Override
    public FilterJobParams createFromParcel(Parcel in) {
      return new FilterJobParams(in);
    }

    @Override
    public FilterJobParams[] newArray(int size) {
      return new FilterJobParams[size];
    }
  };

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public int getCompanyId() {
    return companyId;
  }

  public void setCompanyId(int companyId) {
    this.companyId = companyId;
  }

  public int getProvinceId() {
    return provinceId;
  }

  public void setProvinceId(int provinceId) {
    this.provinceId = provinceId;
  }

  public int getCityId() {
    return cityId;
  }

  public void setCityId(int cityId) {
    this.cityId = cityId;
  }

  public int getAreaId() {
    return areaId;
  }

  public void setAreaId(int areaId) {
    this.areaId = areaId;
  }

  public int getStreetId() {
    return streetId;
  }

  public void setStreetId(int streetId) {
    this.streetId = streetId;
  }

  public int getJobFirstTypeId() {
    return jobFirstTypeId;
  }

  public void setJobFirstTypeId(int jobFirstClassId) {
    this.jobFirstTypeId = jobFirstClassId;
  }

  public int getJobSecondTypeId() {
    return jobSecondTypeId;
  }

  public void setJobSecondTypeId(int jobSecondTypeId) {
    this.jobSecondTypeId = jobSecondTypeId;
  }

  public int getNatureOfCompanyId() {
    return natureOfCompanyId;
  }

  public void setNatureOfCompanyId(int natureOfCompanyId) {
    this.natureOfCompanyId = natureOfCompanyId;
  }

  public int getNatureOfJobId() {
    return natureOfJobId;
  }

  public void setNatureOfJobId(int natureOfJobId) {
    this.natureOfJobId = natureOfJobId;
  }

  public int getDegreeId() {
    return degreeId;
  }

  public void setDegreeId(int degreeId) {
    this.degreeId = degreeId;
  }

  public int getSalaryId() {
    return salaryId;
  }

  public void setSalaryId(int salaryId) {
    this.salaryId = salaryId;
  }

  public int getWorkingExpId() {
    return workingExpId;
  }

  public void setWorkingExpId(int workingExpId) {
    this.workingExpId = workingExpId;
  }

  public int getTimeId() {
    return timeId;
  }

  public void setTimeId(int timeId) {
    this.timeId = timeId;
  }

  public int getSort() {
    return sort;
  }

  public void setSort(int sort) {
    this.sort = sort;
  }

  public String getLongitude() {
    return longitude;
  }

  public void setLongitude(String longitude) {
    this.longitude = longitude;
  }

  public int getTeshutype() {
    return teshutype;
  }

  public void setTeshutype(int teshutype) {
    this.teshutype = teshutype;
  }
}
