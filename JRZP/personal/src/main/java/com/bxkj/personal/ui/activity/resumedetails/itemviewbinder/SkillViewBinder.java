package com.bxkj.personal.ui.activity.resumedetails.itemviewbinder;

import android.app.Activity;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.ProfessionalSkillItemData;
import com.bxkj.personal.ui.activity.resumedetails.adapter.SkillAdapter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 专业技能视图绑定
 * @TODO: TODO
 * @date 2018/5/9
 */

public class SkillViewBinder implements ItemViewBinder<ProfessionalSkillItemData> {

    private Activity mActivity;

    public SkillViewBinder(Activity activity) {
        mActivity = activity;
    }

    @Override
    public void onBindViewHolder(SuperViewHolder holder, ProfessionalSkillItemData item, int position) {
        holder.setText(R.id.tv_tag,mActivity.getString(R.string.resume_details_skill));
        SkillAdapter professionalSkillAdapter = new SkillAdapter(mActivity, item.getProfessionalSkillItemDataList(), R.layout.enterprise_recycler_resume_skill_item);
        RecyclerView recyclerView = holder.findViewById(R.id.recycler);
        recyclerView.setVisibility(
            CheckUtils.isNullOrEmpty(item.getProfessionalSkillItemDataList()) ? View.GONE : View.VISIBLE);
        recyclerView.setLayoutManager(new LinearLayoutManager(mActivity));
        recyclerView.setAdapter(professionalSkillAdapter);
    }

    @Override
    public int getLayoutId() {
        return R.layout.enterprise_recycler_resume_item;
    }
}
