package com.bxkj.personal.ui.fragment.uservideo

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import cn.jzvd.JzvdStd
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.common.util.UserUtils
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.api.PersonalApiConstants.UserVideoType
import com.bxkj.personal.data.VideoItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.ui.activity.signupuser.SignUpUserActivity
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity
import com.bxkj.personal.ui.activity.web.WebActivity
import com.bxkj.personal.weight.SendContractInfoDialog
import com.bxkj.personal.weight.videoplaylayout.VideoPlayLayout
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.uservideo
 * @Description: 用户发布的视频
 * <AUTHOR>
 * @date 2020/1/9
 * @version V1.0
 */
class UserVideoFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, UserVideoViewModel>() {

  companion object {
    const val EXTRA_USER_ID = "USER_ID"
    const val EXTRA_VIDEO_TYPE = "VIDEO_TYPE"

    fun newInstance(
      userId: Int,
      @UserVideoType videoType: Int = CommonApiConstants.NO_ID
    ): UserVideoFragment {
      val instance = UserVideoFragment()
      instance.arguments = Bundle()
        .apply {
          putInt(EXTRA_USER_ID, userId)
          putInt(EXTRA_VIDEO_TYPE, videoType)
        }
      return instance
    }
  }

  @Inject
  lateinit var mAccountRepo: AccountRepo

  private var mSendContractInfoDialog: SendContractInfoDialog? = null

  override fun getViewModelClass(): Class<UserVideoViewModel> = UserVideoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.listViewModel = viewModel.listViewModel
    setupVideoListAdapter()
    setupSendContractInfoDialog()
    viewModel.start(arguments)
  }

  private fun setupSendContractInfoDialog() {
    mSendContractInfoDialog = SendContractInfoDialog(mAccountRepo, parentActivity)
  }

  override fun setUserVisibleHint(isVisibleToUser: Boolean) {
    super.setUserVisibleHint(isVisibleToUser)
    if (!isVisibleToUser) {
      Jzvd.releaseAllVideos()
    }
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

  private fun setupVideoListAdapter() {
    val videoListAdapter = object : SimpleDBListAdapter<VideoItemData>(
      parentActivity, R.layout.personal_recycler_user_video_item
    ) {
      override fun convert(holder: SuperViewHolder, viewType: Int, item: VideoItemData, position: Int) {
        super.convert(holder, viewType, item, position)
        holder.findViewById<VideoPlayLayout>(R.id.fl_video_player)?.let {
          it.getVideoPlayer()?.setOnFirstPlayerListener {
            viewModel.updateVideoPlayCount(item)
          }
          it.setOnOptionClickListener(object : VideoPlayLayout.OnOptionClickListener {
            override fun onOptionClicked(view: View) {
              mSendContractInfoDialog?.let { dialog ->
                dialog.setVideoId(item.id)
                dialog.show()
              }
            }
          })
        }
      }
    }.apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val item = data[position]
          when (v.id) {
            R.id.tv_comment -> startActivity(
              VideoDetailsActivity.newIntent(
                parentActivity,
                data[position].id,
                true
              )
            )
            R.id.tv_like -> viewModel.addOrRemoveLike(item)
            R.id.tv_sign_up_count -> {
              startActivity(SignUpUserActivity.newIntent(parentActivity, item.id))
            }
            R.id.iv_option -> {
              MenuPopup.Builder(parentActivity)
                .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                .setOnItemClickListener { _, menuPosition ->
                  if (menuPosition == 0) {
                    showDeleteVideoConfirmDialog(position, item)
                  }
                }.build().show()
            }
            else -> {
              if (data[position].type == 4) {
                startActivity(
                  WebActivity.newIntent(
                    parentActivity,
                    webUrl = item.video + UserUtils.getUserWebParams(),
                    fullScreen = true
                  )
                )
              } else {
                startActivity(VideoDetailsActivity.newIntent(parentActivity, item.id))
              }
            }
          }
        }
      }, R.id.tv_comment, R.id.tv_like, R.id.tv_sign_up_count, R.id.iv_option)
    }
    val recyclerVideoList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerVideoList.layoutManager = LinearLayoutManager(parentActivity)
    recyclerVideoList.addOnChildAttachStateChangeListener(object :
      RecyclerView.OnChildAttachStateChangeListener {
      override fun onChildViewDetachedFromWindow(view: View) {
        val jzVideoPlayer = view.findViewById<JzvdStd>(R.id.video_player)
        if (jzVideoPlayer != null && Jzvd.CURRENT_JZVD != null && jzVideoPlayer.jzDataSource.containsTheUrl(
            Jzvd.CURRENT_JZVD.jzDataSource.currentUrl
          )
        ) {
          if (Jzvd.CURRENT_JZVD != null && Jzvd.CURRENT_JZVD.screen != Jzvd.SCREEN_FULLSCREEN) {
            Jzvd.releaseAllVideos()
          }
        }
      }

      override fun onChildViewAttachedToWindow(view: View) {

      }
    })
    viewModel.listViewModel.setAdapter(videoListAdapter)
  }

  private fun showDeleteVideoConfirmDialog(position: Int, videoItemData: VideoItemData?) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.common_delete_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteVideo(position, videoItemData)
      }.build().show(childFragmentManager)
  }

  override fun onDestroyView() {
    super.onDestroyView()
    Jzvd.releaseAllVideos()
  }
}