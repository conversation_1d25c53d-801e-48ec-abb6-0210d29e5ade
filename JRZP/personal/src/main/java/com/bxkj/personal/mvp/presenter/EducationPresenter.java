package com.bxkj.personal.mvp.presenter;


import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.mvp.contract.EducationContract;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.common.widget.filterpopup.FilterOptionData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: Education
 * @TODO: TODO
 * @date 2018/3/27
 */

public class EducationPresenter extends EducationContract.Presenter {

    private static final String TAG = EducationPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public EducationPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getEducation() {
        mPersonalApi.getEducation()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<FilterOptionData> result = (List<FilterOptionData>) baseResponse.getDataList();
                        result.add(0, new FilterOptionData(0, "不限"));
                        mView.getEducationSuccess(result);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
