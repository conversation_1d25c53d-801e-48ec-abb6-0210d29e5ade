package com.bxkj.personal.ui.fragment.invitationstodelivery;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.bxkj.common.base.BaseListFragment;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.recyclerutil.RecycleViewDivider;
import com.bxkj.common.util.rxbus.RxBus;
import com.bxkj.common.util.rxbus.RxMsgCode;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.personal.R;
import com.bxkj.personal.data.InvitationsToDeliveryItemData;
import com.bxkj.personal.mvp.contract.InvitationDeliveryContract;
import com.bxkj.personal.mvp.presenter.InvitationDeliveryPresenter;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * @Description: 投递邀请
 * @date 2018/9/26
 */
public class InvitationsToDeliveryFragment extends BaseListFragment
    implements InvitationsToDeliveryContract.View, InvitationDeliveryContract.View {

  private static final String PAGE_TYPE = "page_type";

  @Inject
  InvitationsToDeliveryPresenter mInvitationsToDeliveryPresenter;
  @Inject
  InvitationDeliveryPresenter mInvitationDeliveryPresenter;

  private InvitationsToDeliveryAdapter mInvitationsToDeliveryAdapter;
  //0、待处理2、已投递1、已拒绝
  private int mPageType;
  private Disposable mRefreshMsgDisposable;

  public static InvitationsToDeliveryFragment newInstance(int pageType) {
    Bundle args = new Bundle();
    args.putInt(PAGE_TYPE, pageType);
    InvitationsToDeliveryFragment fragment = new InvitationsToDeliveryFragment();
    fragment.setArguments(args);
    return fragment;
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_fragment_invitation_to_delivery;
  }

  @Override
  public void initPresenter() {
    mInvitationsToDeliveryPresenter.attachView(this);
    mInvitationDeliveryPresenter.attachView(this);
  }

  @Override
  public void initPage() {
    super.initPage();
    checkPageType();
    handleRxMsg();
    mInvitationsToDeliveryAdapter = new InvitationsToDeliveryAdapter(getContext(), null,
        R.layout.personal_recycler_invitations_to_delivery_item, mPageType);
    mInvitationsToDeliveryAdapter.setOnItemClickListener((view, position) -> {
          int otherId = mInvitationsToDeliveryAdapter.getData().get(position).getId();
          if (view.getId() == R.id.tv_no) {
            new ActionDialog.Builder()
                .setTitle(getString(R.string.invitations_to_delivery_no))
                .setOnConfirmClickListener(
                        (dialog) -> mInvitationDeliveryPresenter.refuseDeliveryInvitation(getUserId(), otherId,
                        position))
                .build()
                .show(getChildFragmentManager());
          } else if (view.getId() == R.id.tv_yes) {
            new ActionDialog.Builder()
                .setTitle(getString(R.string.invitations_to_delivery_yes))
                .setOnConfirmClickListener(
                        (dialog) -> mInvitationDeliveryPresenter.acceptDeliveryInvitation(getUserId(), otherId,
                        position))
                .build()
                .show(getChildFragmentManager());
          } else {
            startActivity(JobDetailsActivityV2.Companion.newIntent(getParentActivity(),
                mInvitationsToDeliveryAdapter.getData().get(position).getRelid()));
          }
        }
    );
    getRecyclerView().setLayoutManager(new LinearLayoutManager(getContext()));
    getRecyclerView().addItemDecoration(
        new RecycleViewDivider(getContext(), LinearLayoutManager.HORIZONTAL,
            DensityUtils.dp2px(getParentActivity(), 8), getMColor(R.color.common_f4f4f4), true));
    getRecyclerView().setAdapter(mInvitationsToDeliveryAdapter);
  }

  private void handleRxMsg() {
    mRefreshMsgDisposable = RxBus
        .get()
        .toObservable(RxBus.Message.class)
        .subscribe(message -> {
          if (message.getCode() == RxMsgCode.REFRESH_INVITATIONS_TO_DELIVERY_LIST) {
            if (mPageType == (int) message.getMsg()) {
              getRefreshLayoutManager().refreshPage();
            }
          }
        });
  }

  private void checkPageType() {
    mPageType = getArguments().getInt(PAGE_TYPE);
  }

  @Override
  protected void fetchData() {
    mInvitationsToDeliveryPresenter.getInvitationToDeliveryList(getUserId(), mPageType,
        getRefreshLayoutManager().getCurrentPage(), CommonApiConstants.DEFAULT_PAGE_SIZE);
  }

  @Override
  public void getInvitationToDeliveryListSuccess(
      List<InvitationsToDeliveryItemData> invitationsToDeliveryItemDataList, boolean noMore) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    getRefreshLayoutManager().setNoMoreData(noMore);
    if (getRefreshLayoutManager().currentFirstPage()) {
      mInvitationsToDeliveryAdapter.reset(invitationsToDeliveryItemDataList);
    } else {
      mInvitationsToDeliveryAdapter.addAll(invitationsToDeliveryItemDataList);
    }
  }

  @Override
  public void acceptDeliverySuccess(int position) {
    mInvitationsToDeliveryAdapter.removeAt(position);
      if (CheckUtils.isNullOrEmpty(mInvitationsToDeliveryAdapter.getData())) {
          getPageStatusLayout().show(PageStatusConfigFactory.newEmptyConfig());
      }
    RxBus.get().post(new RxBus.Message(RxMsgCode.REFRESH_INVITATIONS_TO_DELIVERY_LIST, 2));
  }

  @Override
  public void refuseDeliverySuccess(int position) {
    mInvitationsToDeliveryAdapter.removeAt(position);
      if (CheckUtils.isNullOrEmpty(mInvitationsToDeliveryAdapter.getData())) {
          getPageStatusLayout().show(PageStatusConfigFactory.newEmptyConfig());
      }
    RxBus.get().post(new RxBus.Message(RxMsgCode.REFRESH_INVITATIONS_TO_DELIVERY_LIST, 1));
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override public void showLoading(String text) {

  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    mInvitationsToDeliveryPresenter.detachView();
    mInvitationDeliveryPresenter.detachView();
    if (mRefreshMsgDisposable != null && !mRefreshMsgDisposable.isDisposed()) {
      mRefreshMsgDisposable.dispose();
      mRefreshMsgDisposable = null;
    }
  }
}
