package com.bxkj.personal.ui.activity.permissionmanagement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.SystemUtil
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.R.layout
import com.bxkj.personal.databinding.PersonalActivityPermissionManagementBinding
import com.bxkj.personal.ui.activity.permissionmanagement.PermissionItem.DiffCallBack
import com.bxkj.personal.ui.activity.web.WebActivity
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions

/**
 * @Project: gzgk
 * @Description: 权限设置
 * @author:45457
 * @date: 2020/7/3
 * @version: V1.0
 */
class PermissionManagementActivity :
    BaseDBActivity<PersonalActivityPermissionManagementBinding, BaseViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, PermissionManagementActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_permission_management

    override fun initPage(savedInstanceState: Bundle?) {
        setupPermissionList()
    }

    private fun getPermissionList(): List<PermissionItem> {
        return listOf(
            PermissionItem.create(
                "允许今日招聘访问位置信息",
                "查看详细位置权限信息使用规则",
                "https://jrzpapi2.jdzj.com/page/weizhi.html",
                XXPermissions.isGranted(this, Permission.ACCESS_FINE_LOCATION)
            ),
            PermissionItem.create(
                "允许今日招聘使用相机功能",
                "查看详细相机功能使用规则",
                "https://jrzpapi2.jdzj.com/page/xiangji.html",
                XXPermissions.isGranted(this, Permission.CAMERA)
            ),
            PermissionItem.create(
                "允许今日招聘使用麦克风功能",
                "查看详细位置权限信息使用规则",
                "https://jrzpapi2.jdzj.com/page/maikefeng.html",
                XXPermissions.isGranted(this, Permission.RECORD_AUDIO)
            ),
            PermissionItem.create(
                "允许今日招聘获取设备状态权限",
                "查看详细获取设备状态权限使用规则",
                "https://jrzpapi2.jdzj.com/page/shebei.html",
                XXPermissions.isGranted(this, Permission.READ_PHONE_STATE)
            ),
            PermissionItem.create(
                "允许今日招聘获取文件储存和访问权限",
                "查看详细获取文件储存和访问权限使用规则",
                "https://jrzpapi2.jdzj.com/page/wenjian.html",
                XXPermissions.isGranted(
                    this,
                    Permission.WRITE_EXTERNAL_STORAGE,
                    Permission.READ_EXTERNAL_STORAGE
                )
            ),
            PermissionItem.create(
                "允许今日招聘获取通讯录信息",
                "查看详细获取通讯录权限使用规则",
                "https://jrzpapi2.jdzj.com/page/tongxunlu.html",
                XXPermissions.isGranted(this, Permission.READ_CONTACTS)
            )
        )
    }

    override fun onResume() {
        super.onResume()
        viewBinding.list = getPermissionList()
    }

    private fun setupPermissionList() {
        val permissionListAdapter =
            SimpleDiffListAdapter(
                layout.personal_recyceler_permission_item,
                DiffCallBack()
            ).apply {
                setOnItemClickListener(object :
                    SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.let {
                            if (v.id == R.id.tv_desc) {
                                startActivity(
                                    WebActivity.newIntent(
                                        this@PermissionManagementActivity,
                                        "权限说明",
                                        it[position].permissionDescUrl
                                    )
                                )
                            } else {
                                SystemUtil.toSettingPage(this@PermissionManagementActivity)
                            }
                        }
                    }
                }, R.id.tv_desc)
            }
        viewBinding.recyclerPermissionList.layoutManager = LinearLayoutManager(this)
        viewBinding.recyclerPermissionList.adapter = permissionListAdapter
    }

}