package com.bxkj.personal.ui.activity.usersetting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.convertWheelOptions
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityUserSettingBinding
import com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyActivity

/**
 * @Project: VideoRecruitment
 * @Description: 用户设置
 * @author:45457
 * @date: 2020/6/29
 * @version: V1.0
 */
class UserSettingActivity :
  BaseDBActivity<PersonalActivityUserSettingBinding, UserSettingViewModel>(),
  OnClickListener {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, UserSettingActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<UserSettingViewModel> = UserSettingViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_user_setting

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewModel.start()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.ll_shield_company) {
        startActivity(ShieldCompanyActivity.newIntent(this))
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.showUserStatusPickerCommand.observe(this, Observer {
      showStatusOptions(it)
    })
  }

  private fun showStatusOptions(selectedItem: String) {
    val userOptions = resources.getStringArray(R.array.user_status).convertWheelOptions(1)
    MenuPopup.Builder(this)
      .setData(userOptions)
      .setSelected(selectedItem)
      .setOnItemClickListener { _, position ->
        viewModel.setupUserStatus(userOptions[position])
      }
      .build().show()
  }

}