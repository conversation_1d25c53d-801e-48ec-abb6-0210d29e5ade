package com.bxkj.personal.ui.activity.studynewsdetails

import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshLayoutViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.exception.ExceptionCode
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.GzNewsDetailsData
import com.bxkj.personal.data.RecommendNewsItemData
import com.bxkj.personal.data.StudyNewsAdData
import com.bxkj.personal.data.StudyNewsDetailsData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.AppStatusInfoRepo
import com.bxkj.personal.data.source.CommentRepo
import com.bxkj.personal.data.source.NewsRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/2
 * @version: V1.0
 */
class StudyNewsDetailsViewModel @Inject constructor(
  private val mNewsRepo: NewsRepo,
  private val mAccountRepo: AccountRepo,
  private val mCommentRepo: CommentRepo,
  private val mAppStatusInfoRepo: AppStatusInfoRepo
) : BaseViewModel() {

  val dataLoadSuccess = LiveEvent<Void>()

  val newsDetails = MutableLiveData<StudyNewsDetailsData>()
  val newsAdInfo = MutableLiveData<StudyNewsAdData>()
  val recommendNewsList = MutableLiveData<List<RecommendNewsItemData>>()
  val commentListViewModel = RefreshListViewModel()
  val scrollToCommentCommand = LiveEvent<Boolean>()
  val toUploadAvatarCommand = LiveEvent<Void>()
  val addCommentSuccessEvent = LiveEvent<Void>()
  val toAuthorHomeCommand =
    LiveEvent<GzNewsDetailsData>()
  val toTypeNewsCommand =
    LiveEvent<StudyNewsDetailsData>()
  val showShareCommand = LiveEvent<ShareInfoData>()
  val toAdUrlCommand = LiveEvent<String>()

  private var mNewsSubTypeId = CommonApiConstants.NO_ID
  private var mNeedScrollToCommentLayout = false
  private var mNewsId = CommonApiConstants.NO_ID

  init {
    commentListViewModel.refreshLayoutViewModel.enableRefresh(false)
  }

  fun start(newsId: Int, jumpToComment: Boolean = false, newsType: String? = null) {
    mNewsId = newsId
    setupCommentListViewModel(newsId)
    mNeedScrollToCommentLayout = jumpToComment
    mNewsRepo.getStudyNewsDetails(
      getSelfUserID(),
      newsId,
      object : ResultDataCallBack<StudyNewsDetailsData> {
        override fun onSuccess(data: StudyNewsDetailsData?) {
          data?.let {
            mNewsSubTypeId = data.typeId2
            reqAdInfo(data.title, data.typeId2)
          }
          newsDetails.value = data
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  fun openAdUrl() {
    newsAdInfo.value?.let {
      viewModelScope.launch {
        mNewsRepo.recordNewsAdClickAction(
          UserUtils.getUserUUID(),
          2,
          it.id,
          "NewsDetails" + newsDetails.value?.id.getOrDefault()
        ).handleResult(final = {
          toAdUrlCommand.setValue(it.webUrl)
        })
      }
    }
  }

  private fun reqAdInfo(title: String, typeId2: Int) {
    viewModelScope.launch {
      mNewsRepo.getStudyNewsAdInfo(typeId2, title)
        .handleResult({
          newsAdInfo.value = it
        })
    }
  }

  private fun setupCommentListViewModel(newsId: Int) {
    commentListViewModel.setOnMultiLoadDataListener(object :
      RefreshLayoutViewModel.OnMultiLoadDataListener {
      override fun onNoData() {
        setupNoCommentLayout()
      }

      override fun onLoadData(currentPage: Int) {
        mCommentRepo.getCommentList(getSelfUserID(),
          newsId,
          PersonalApiConstants.NEWS_TYPE_STUDY,
          CommonApiConstants.NO_ID,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          object : ResultDataCallBack<List<CommentItemData>> {
            override fun onSuccess(data: List<CommentItemData>?) {
              if (CheckUtils.isNullOrEmpty(data)) {
                commentListViewModel.noMoreData()
              } else {
                commentListViewModel.autoAddAll(data)
              }
              checkNeedScrollToCommentLayout(currentPage)
            }

            override fun onError(respondThrowable: RespondThrowable) {
              commentListViewModel.loadFinish()
              if (respondThrowable.errCode == 30003) {
                commentListViewModel.noMoreData()
              } else {
                commentListViewModel.loadError()
              }
              checkNeedScrollToCommentLayout(currentPage)
            }
          })
      }

      override fun onPageError() {
        commentListViewModel.showPageStatus(
          PageStatusConfigFactory.newErrorConfig().setHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
            .setOnButtonClickListener { commentListViewModel.refresh() })
      }
    })
  }

  /**
   * 设置无评论布局
   */
  private fun setupNoCommentLayout() {
    commentListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    commentListViewModel.showPageStatus(
      PageStatusConfigFactory.newEmptyConfig().setHeight(ViewGroup.LayoutParams.WRAP_CONTENT)
        .setText(R.string.news_details_no_comment_tips)
    )
  }

  /**
   * 检查是否需要跳转到评论列表
   */
  private fun checkNeedScrollToCommentLayout(currentPage: Int) {
    dataLoadSuccess.call()
    if (currentPage == 1) {
      if (mNeedScrollToCommentLayout) {
        mNeedScrollToCommentLayout = false
        scrollToCommentCommand.value = true
      }
    }
  }

  /**
   * 检查是否上传头像
   */
  fun checkAvatarIsUpload(method: () -> Unit) {
    showLoading()
    mAccountRepo.checkAvatarIsUpload(getSelfUserID(), object : ResultCallBack {
      override fun onSuccess() {
        hideLoading()
        method.invoke()
      }

      override fun onError(respondThrowable: RespondThrowable) {
        hideLoading()
        if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30003) {
          toUploadAvatarCommand.call()
        } else {
          showToast(respondThrowable.errMsg)
        }
      }
    })
  }

  /**
   * 添加评论
   */
  fun addComment(content: String) {
    if (CheckUtils.isNullOrEmpty(content)) {
      showToast(R.string.comment_content_not_be_null)
      return
    }
    mCommentRepo.addComment(getSelfUserID(),
      mNewsId,
      PersonalApiConstants.NEWS_TYPE_STUDY,
      CommonApiConstants.NO_ID,
      CommonApiConstants.NO_ID,
      CommonApiConstants.NO_TEXT,
      content,
      object : ResultDataCallBack<CommentItemData> {
        override fun onSuccess(data: CommentItemData?) {
          updateCommentCount(1)
          showToast(R.string.moment_details_comment_success)
          commentListViewModel.showPageStatus(null)
          commentListViewModel.refreshLayoutViewModel.enableLoadMore(true)
          if (commentListViewModel.childCount >= 15) {
            commentListViewModel.removeAt(commentListViewModel.childCount - 1)
          }
          commentListViewModel.add(0, data)
          addCommentSuccessEvent.call()
          scrollToCommentCommand.value = false
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  /**
   * 添加回复
   */
  fun addReply(commentPosition: Int, parentComment: CommentItemData, content: String) {
    mCommentRepo.addComment(getSelfUserID(),
      mNewsId,
      PersonalApiConstants.NEWS_TYPE_STUDY,
      parentComment.pid,
      CommonApiConstants.NO_ID,
      parentComment.nickName,
      content
      ,
      object : ResultDataCallBack<CommentItemData> {
        override fun onSuccess(data: CommentItemData?) {
          updateCommentCount(1)
          parentComment.addReply(data)
          commentListViewModel.replace(commentPosition, parentComment)
          showToast(R.string.moment_details_comment_success)
          addCommentSuccessEvent.call()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
  }

  /**
   * 评论成功后更新评论数
   */
  private fun updateCommentCount(count: Int) {
    newsDetails.value?.let {
      it.commentsCount = it.commentsCount + count
    }
  }

  /**
   * 添加或取消收藏
   */
  fun addOrRemoveCollection() {
    newsDetails.value?.let {
      mAccountRepo.addOrRemoveCollection(
        getSelfUserID(),
        PersonalApiConstants.NEWS_TYPE_STUDY,
        it.id,
        object : ResultDataCallBack<BaseResponse<*>> {
          override fun onSuccess(data: BaseResponse<*>?) {
            newsDetails.value?.addCollection()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            when (respondThrowable.errCode) {
              10002 -> {
                newsDetails.value?.removeCollection()
              }
              ExceptionCode.UNLOGIN_ERROR -> loginCommand.call()
              else -> showToast(respondThrowable.errMsg)
            }
          }
        })
    }

  }

  /**
   * 点赞或取消点赞
   */
  fun addOrRemoveLike() {
    if (checkLoginStateAndToLogin()) {
      newsDetails.value?.let {
        mCommentRepo.likeOrUnlikeTheComment(
          getSelfUserID(),
          CommonApiConstants.NO_ID,
          PersonalApiConstants.NEWS_TYPE_STUDY,
          it.id,
          object : ResultCallBack {
            override fun onSuccess() {
              it.addLike()
            }

            override fun onError(respondThrowable: RespondThrowable) {
              if (respondThrowable.errCode == 10002) {
                it.removeLike()
              } else {
                showToast(respondThrowable.errMsg)
              }
            }
          })
      }
    }
  }

  /**
   * 评论点赞或取消点赞
   */
  fun commentLikeOrUnlike(comment: CommentItemData) {
    if (checkLoginStateAndToLogin()) {
      mCommentRepo.likeOrUnlikeTheComment(
        getSelfUserID(),
        comment.pid,
        PersonalApiConstants.NEWS_TYPE_STUDY,
        mNewsId,
        object : ResultCallBack {
          override fun onSuccess() {
            comment.addLike()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 10002) {
              comment.removeLike()
            } else {
              showToast(respondThrowable.errMsg)
            }
          }
        })
    }
  }

  fun deleteComment(position: Int, item: CommentItemData) {
    mAccountRepo.deleteMyPublish(getSelfUserID(),
      PersonalApiConstants.MY_PUBLISH_COMMENT_TYPE,
      PersonalApiConstants.MY_PUBLISH_STUDY_COMMENT_TYPE,
      item.pid
      ,
      object : ResultCallBack {
        override fun onSuccess() {
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (respondThrowable.errCode == 30001) {
            showToast(R.string.common_delete_success)
            removeCommentByIndex(position)
          } else {
            showToast(respondThrowable.errMsg)
          }
        }
      })
  }

  private fun removeCommentByIndex(index: Int) {
    updateCommentCount(-1)
    commentListViewModel.loadFinish()
    commentListViewModel.removeAt(index)
    if (commentListViewModel.childCount == 0) {
      setupNoCommentLayout()
    }
  }

  fun getRecommendNewsBySubNewsTypeId() {
    mNewsRepo.getLinkRecommendNewsList(
      mNewsSubTypeId,
      object : ResultDataCallBack<List<RecommendNewsItemData>> {
        override fun onSuccess(data: List<RecommendNewsItemData>?) {
          recommendNewsList.value = data
          commentListViewModel.refresh()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          commentListViewModel.refresh()
        }
      })
  }

  fun getNewsId(): Int {
    return mNewsId
  }

  fun toTypeNewsPage() {
    newsDetails.value?.let {
      toTypeNewsCommand.value = it
    }
  }

  fun getShareInfo() {
    newsDetails.value?.let {
      viewModelScope.launch {
        showLoading()
        mAppStatusInfoRepo.getShareInfo(CommonApiConstants.SHARE_STUDY, it.id)
          .handleResult({
            showShareCommand.value = it
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

}