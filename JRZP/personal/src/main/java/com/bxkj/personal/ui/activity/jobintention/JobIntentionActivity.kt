package com.bxkj.personal.ui.activity.jobintention

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation
import com.bxkj.jrzp.support.feature.ui.selectaddress.AddressMultiSelectActivity
import com.bxkj.jrzp.support.feature.ui.seletjobtype.SelectJobTypeActivity
import com.bxkj.personal.R
import com.bxkj.personal.data.MicroResumeData
import com.bxkj.personal.databinding.PersonalActivityJobIntentionBinding
import com.bxkj.personal.weight.salaryselect.SalarySelectDialog

class JobIntentionActivity :
  BaseDBActivity<PersonalActivityJobIntentionBinding, JobIntentionViewModel>() {

  private val microResume: MicroResumeData? by lazy {
    intent.getParcelableExtra(EXTRA_MICRO_RESUME)
  }

  private val selectJobTypeLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleSelectJobTypeResult(it)
    }

  private val selectAddressLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.handleSelectAddressResult(it)
    }

  override fun getViewModelClass() = JobIntentionViewModel::class.java

  override fun getLayoutId() = R.layout.personal_activity_job_intention

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    subscribeViewModelEvent()

    viewBinding.llSalary.setOnClickListener {
      SalarySelectDialog().apply {
        setOnConfirmSelectListener { minSalary, maxSalary ->
          <EMAIL>(minSalary, maxSalary)
        }
      }.show(supportFragmentManager)
    }

    viewModel.start(microResume)
  }

  private fun subscribeViewModelEvent() {
    viewModel.toSelectJobTypeCommand.observe(this, EventObserver {
      selectJobTypeLauncher.launch(
        SelectJobTypeActivity.newIntent(this, PostJobNavigation.TYPE_FULL_TIME, 5, it)
      )
    })

    viewModel.showAddressPickerCommand.observe(this, EventObserver {
      selectAddressLauncher.launch(AddressMultiSelectActivity.newIntent(this, 3, it.area))
    })

    viewModel.saveJobIntentionCommand.observe(this, EventObserver {
      setResult(RESULT_OK, Intent().apply {
        putExtra(EXTRA_MICRO_RESUME, it)
      })
      finish()
    })
  }

  companion object {
    const val EXTRA_MICRO_RESUME = "MICRO_RESUME"

    fun newIntent(context: Context, microResume: MicroResumeData) =
      Intent(context, JobIntentionActivity::class.java).apply {
        putExtra(EXTRA_MICRO_RESUME, microResume)
      }
  }
}