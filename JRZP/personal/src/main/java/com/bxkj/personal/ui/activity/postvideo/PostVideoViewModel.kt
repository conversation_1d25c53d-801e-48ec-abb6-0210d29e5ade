package com.bxkj.personal.ui.activity.postvideo

import android.app.Activity
import android.app.Application
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.adresspickerdialog.AddressData
import com.bxkj.personal.data.VideoTypeItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.ui.activity.selectaddressbymap.SelectAddressByMapActivity
import com.bxkj.personal.ui.activity.takecover.TakeCoverActivity
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.postvideo
 * @Description:
 * <AUTHOR>
 * @date 2019/12/31
 * @version V1.0
 */
class PostVideoViewModel @Inject constructor(application: Application
                                             , private val mAccountRepo: AccountRepo) : BaseViewModel() {

    private var mVideoPath: String? = null

    val videoPathLoadedEvent = LiveEvent<String>()
    val videoCoverPath = MutableLiveData<String>()
    val videoTitle = MutableLiveData<String>()
    val videoContent = MutableLiveData<String>()
    val jobSalary = MutableLiveData<PickerOptionsData>()
    val jobAddress = MutableLiveData<AddressData>().apply { value = AddressData() }
    val jobAddressDetails = MutableLiveData<String>().apply { value = CommonApiConstants.NO_TEXT }

    val videoCompressStatus = MutableLiveData<String>()
    val releaseSuccessEvent = LiveEvent<Void>()
    val videoTypeList = MutableLiveData<List<VideoTypeItemData>>()
    val selectVideoType = MutableLiveData<VideoTypeItemData>();
    val publishVerifySuccess = LiveEvent<Void>()
    val toUploadAvatarCommand = LiveEvent<Void>()

    //企业资料未完善
    val enterpriseInfoNotCompleteEvent =
      LiveEvent<Void>()

    //未认证，可正常认证
    val notCertifiedEvent = LiveEvent<Void>()

    //认证中
    val certificationEvent = LiveEvent<Void>()

    //认证失败
    val certificationFailedEvent = LiveEvent<String>()

    val toEditCoverCommand = LiveEvent<String>()
    private var mDisposable: Disposable? = null
    private var uploading = false

    //发布成功后展示认证
    private var showCerTips: Boolean = false

    fun start(intent: Intent) {
        mVideoPath = intent.getStringExtra(PostVideoActivity.EXTRA_VIDEO_PATH)
        setupVideoCover(mVideoPath)
        videoPathLoadedEvent.value = mVideoPath
        getVideoTypeList()
    }

    private fun getVideoTypeList() {
        mAccountRepo.getPostVideoTypeList(object : ResultDataCallBack<List<VideoTypeItemData>> {
            override fun onSuccess(data: List<VideoTypeItemData>) {
                videoTypeList.value = data
            }

            override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun toEditVideoCover() {
        toEditCoverCommand.value = mVideoPath
    }

    private fun setupVideoCover(videoPath: String?) {
        mDisposable = Observable.create<String> {
            val videoCover = ZPFileUtils.getVideoCover(videoPath)
            val coverFileName = ZPFileUtils.saveBitmap(videoCover)
            it.onNext(coverFileName)
        }.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    setVideoCover(it)
                }
    }

    private fun setVideoCover(coverPath: String) {
        videoCoverPath.value = coverPath
    }

    fun setVideoTitle(title: String) {
        videoTitle.value = title
    }

    fun setVideoContent(content: String) {
        videoContent.value = content
    }

    private fun publish(compressedVideoPath: String) {
        showLoading()
        selectVideoType.value?.let {
            val cityId = if (it.id == 3) jobAddress.value!!.cityId else CommonApiConstants.NO_ID
            val moneyId = if (it.id == 3) jobSalary.value!!.id else CommonApiConstants.NO_ID
            val address = if (it.id == 3) jobAddressDetails.value!! else CommonApiConstants.NO_TEXT
            mAccountRepo.publishVideo(it.id, getSelfUserID(), videoTitle.value!!, videoContent.value
                    ?: CommonApiConstants.NO_TEXT, videoCoverPath.value!!, compressedVideoPath,
                    cityId, moneyId, address, callback = object : ResultCallBack {
                override fun onSuccess() {
                    hideLoading()
                    if (showCerTips) {
                        certificationEvent.call()
                    } else {
                        releaseSuccessEvent.call()
                    }
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    showToast(respondThrowable.errMsg)
                    hideLoading()
                }
            })
        }
    }

    fun getVideoPath(): String? {
        return mVideoPath
    }

    fun updateVideoCompressProgress(progress: Float) {
        videoCompressStatus.value = "视频处理中...进度${String.format("%.0f", progress * 100)}%"
    }

    fun videoCompressed(path: String) {
        if (!uploading) {
            uploading = true
            videoCompressStatus.value = "视频处理完成，正在上传"
            publish(path)
        }
    }

    fun setSelectVideoTypePosition(position: Int) {
        videoTypeList.value?.let {
            selectVideoType.value = it[position]
        }
    }

    fun setJobSalary(pickerOptionsData: PickerOptionsData) {
        jobSalary.value = pickerOptionsData
                .apply {
                    id += 1
                }
    }

    fun verifyVideoPublish() {
        showLoading()
        mAccountRepo.checkAvatarIsUpload(getSelfUserID(), object : ResultCallBack {
            override fun onSuccess() {
                verifyUserAuth()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30003) {
                    toUploadAvatarCommand.call()
                } else {
                    showToast(respondThrowable.errMsg)
                }
            }
        })
    }

    private fun verifyUserAuth() {
        mAccountRepo.verifyPublishVideo(getSelfUserID(), selectVideoType.value?.id ?: 0
                , object : ResultCallBack {
            override fun onSuccess() {
                publishVerifySuccess.call()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                if (respondThrowable.isNetworkError) {
                    showToast(respondThrowable.errMsg)
                } else {
                    when (respondThrowable.errCode) {
                        30005, 30006 -> {
                            showCerTips = true
                            enterpriseInfoNotCompleteEvent.call()
                        }
                        30007 -> {
                            showCerTips = true
                            publishVerifySuccess.call()
                        }
                        30008 -> {
                            showCerTips = true
                            certificationFailedEvent.value = respondThrowable.errMsg
                        }
                        30009 -> {
                            showCerTips = true
                            notCertifiedEvent.call()
                        }
                        else -> {
                            showToast(respondThrowable.errMsg)
                        }
                    }
                }
            }
        })
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == PostVideoActivity.TO_EDIT_COVER_CODE) {
            if (resultCode == TakeCoverActivity.RESULT_CAPTURE_SUCCESS && data != null) {
                setVideoCover(data.getStringExtra(TakeCoverActivity.EXTRA_COVER_PATH).getOrDefault())
            }
        } else if (requestCode == PostVideoActivity.TO_SELECT_ADDRESS_CODE) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                jobAddress.value = data.getParcelableExtra(SelectAddressByMapActivity.ADDRESS_DATA)
                jobAddressDetails.value = data.getStringExtra(SelectAddressByMapActivity.DETAILS_ADDRESS)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        mAccountRepo.clear()
        mDisposable?.dispose()
    }

}