package com.bxkj.personal.ui.activity.companydetails.companyjobs;

import android.os.Bundle;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseListFragment;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.recyclerutil.LineItemDecoration;
import com.bxkj.jrzp.user.data.JobData;
import com.bxkj.personal.R;
import com.bxkj.personal.adapter.RecommendJobAdapter;
import com.bxkj.personal.mvp.contract.GetJobListContract;
import com.bxkj.personal.mvp.presenter.GetJobListPresenter;
import com.bxkj.personal.ui.activity.companydetails.CompanyDetailsActivity;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;
import com.bxkj.personal.ui.activity.searchjobresult.FilterJobParams;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.companydetails.companyjobs
 * @Description: 公司热门职位fragment
 * @TODO: TODO
 * @date 2018/4/23
 */

public class CompanyJobsFragment extends BaseListFragment implements GetJobListContract.View {

  @Inject
  GetJobListPresenter mGetJobListPresenter;

  private RecommendJobAdapter mRecommendJobAdapter;
  private FilterJobParams mFilterParameters;

  public static CompanyJobsFragment newInstance(int companyId) {
    CompanyJobsFragment companyAboutFragment = new CompanyJobsFragment();
    Bundle bundle = new Bundle();
    bundle.putInt(CompanyDetailsActivity.EXTRA_COMPANY_ID, companyId);
    companyAboutFragment.setArguments(bundle);
    return companyAboutFragment;
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_fragment_company_jobs;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mGetJobListPresenter);
    return presenters;
  }

  @Override
  public void initPage() {
    super.initPage();
    mRecommendJobAdapter = new RecommendJobAdapter(getActivity(), null,
        R.layout.personal_recycler_job_item).showCompanyName(false);
    mRecommendJobAdapter.setOnItemClickListener((view, position) -> startActivity(
        JobDetailsActivityV2.Companion.newIntent(getParentActivity(),
            mRecommendJobAdapter.getData().get(position).getId())));
    getRecyclerView().setLayoutManager(new LinearLayoutManager(getActivity()));
    getRecyclerView().setAdapter(mRecommendJobAdapter);
    getRecyclerView().addItemDecoration(new LineItemDecoration(
        ContextCompat.getDrawable(getParentActivity(), R.drawable.divider_f4f4f4),
        LinearLayoutManager.VERTICAL));

    getRefreshLayoutManager().setEnableRefresh(false);

    if (getArguments() != null) {
      int companyId = getArguments().getInt(CompanyDetailsActivity.EXTRA_COMPANY_ID);
      mFilterParameters = new FilterJobParams();
      mFilterParameters.setCompanyId(companyId);
    }
  }

  @Override
  protected void fetchData() {
    if (mFilterParameters != null) {
      mGetJobListPresenter
          .getJobList(0, mFilterParameters, getRefreshLayoutManager().getCurrentPage(),
              CommonApiConstants.DEFAULT_PAGE_SIZE);
    }
  }

  @Override
  public void getJobListSuccess(List<JobData> jobDataList, boolean noMoreData) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    getRefreshLayoutManager().setNoMoreData(noMoreData);
    if (getRefreshLayoutManager().currentFirstPage()) {
      mRecommendJobAdapter.reset(jobDataList);
    } else {
      mRecommendJobAdapter.addAll(jobDataList);
    }
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  public void getJobListError() {
  }
}
