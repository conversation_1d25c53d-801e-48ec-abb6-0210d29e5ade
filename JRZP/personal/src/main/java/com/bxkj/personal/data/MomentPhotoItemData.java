package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.data.GalleryItem;

import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/11/27
 */
public class MomentPhotoItemData implements Parcelable, GalleryItem {

  /**
   * spid : 0
   * sjid : 0
   * sptype : 1
   * sppic : images_server/shejiao/100574307_14.jpg
   * sppx : 0
   * domain : null
   */

  private int spid;
  private int sjid;
  private int sptype;
  private String sppic;
  private int sppx;
  private String spvideo;
  private String domain;

  protected MomentPhotoItemData(Parcel in) {
    spid = in.readInt();
    sjid = in.readInt();
    sptype = in.readInt();
    sppic = in.readString();
    sppx = in.readInt();
    spvideo = in.readString();
    domain = in.readString();
  }

  public static final Creator<MomentPhotoItemData> CREATOR = new Creator<MomentPhotoItemData>() {
    @Override
    public MomentPhotoItemData createFromParcel(Parcel in) {
      return new MomentPhotoItemData(in);
    }

    @Override
    public MomentPhotoItemData[] newArray(int size) {
      return new MomentPhotoItemData[size];
    }
  };

  public static MomentPhotoItemData fromUrl(String url) {
    return new MomentPhotoItemData(url);
  }

  public MomentPhotoItemData(String sppic) {
    this.sppic = sppic;
  }

  public int getSpid() {
    return spid;
  }

  public void setSpid(int spid) {
    this.spid = spid;
  }

  public int getSjid() {
    return sjid;
  }

  public void setSjid(int sjid) {
    this.sjid = sjid;
  }

  public int getSptype() {
    return sptype;
  }

  public void setSptype(int sptype) {
    this.sptype = sptype;
  }

  public String getSppic() {
    return sppic;
  }

  public void setSppic(String sppic) {
    this.sppic = sppic;
  }

  public int getSppx() {
    return sppx;
  }

  public void setSppx(int sppx) {
    this.sppx = sppx;
  }

  public String getDomain() {
    return domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public String getSpvideo() {
    return spvideo;
  }

  public void setSpvideo(String spvideo) {
    this.spvideo = spvideo;
  }

  @NotNull @Override public String getItemUrl() {
    return sppic;
  }

  @Override public int describeContents() {
    return 0;
  }

  @Override public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(spid);
    dest.writeInt(sjid);
    dest.writeInt(sptype);
    dest.writeString(sppic);
    dest.writeInt(sppx);
    dest.writeString(spvideo);
    dest.writeString(domain);
  }

  public static class ItemDiffCallBack extends DiffUtil.ItemCallback<MomentPhotoItemData> {

    @Override
    public boolean areItemsTheSame(@NonNull MomentPhotoItemData oldItem,
        @NonNull MomentPhotoItemData newItem) {
      return oldItem.equals(newItem);
    }

    @Override
    public boolean areContentsTheSame(@NonNull MomentPhotoItemData oldItem,
        @NonNull MomentPhotoItemData newItem) {
      return oldItem.sppic.equals(newItem.sppic);
    }
  }
}
