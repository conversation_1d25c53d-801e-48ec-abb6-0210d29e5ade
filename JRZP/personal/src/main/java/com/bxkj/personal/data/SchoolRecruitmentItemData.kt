package com.bxkj.personal.data

import androidx.recyclerview.widget.DiffUtil

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * <AUTHOR>
 * @date 2019/10/15
 * @version V1.0
 */
data class SchoolRecruitmentItemData(var id: Int
                                     , var title: String
                                     , var createTime: String
                                     , var count: String)

class SchoolRecruitmentItemCallBack : DiffUtil.ItemCallback<SchoolRecruitmentItemData>() {
    override fun areItemsTheSame(oldItem: SchoolRecruitmentItemData, newItem: SchoolRecruitmentItemData): Boolean {
        return oldItem == newItem
    }

    override fun areContentsTheSame(oldItem: SchoolRecruitmentItemData, newItem: SchoolRecruitmentItemData): Boolean {
        return oldItem.id == newItem.id
    }
}