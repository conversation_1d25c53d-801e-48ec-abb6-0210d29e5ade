<?xml version="1.0" encoding="utf-8"?>
<resources>

  <style name="Theme.AppLauncher">
    <!--全屏-->
    <item name="android:windowFullscreen">true</item>
    <!--window背景-->
    <item name="android:windowBackground">@drawable/bg_splash_page</item>
    <!--导航栏背景色-->
    <item name="android:navigationBarColor">@color/common_white</item>
    <!--（v21以上有效）不让windowBackground延申到navigation bar区域-->
    <item name="android:windowDrawsSystemBarBackgrounds">false</item>
    <!--
    （v28以上有效）设置页面显示模式，允许布局延申到刘海区域
     这个设置有三种选项：
     1、default：默认情况，全屏页面不可用刘海区域，非全屏页面可以进行使用
     2、shortEdges：允许页面延申到刘海区域
     3、never：不允许使用刘海区域
    -->
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
  </style>


  <style name="Theme.AppSplash" parent="Theme.SplashScreen">
    <!--    <item name="android:windowBackground">@drawable/bg_splash_page</item>&lt;!&ndash;&ndash;&gt;-->
    <!--背景颜色-->
    <item name="windowSplashScreenBackground">@color/white</item>
    <!--动画图标,Android12以下动画不生效-->
    <item name="windowSplashScreenAnimatedIcon">@drawable/img_splash</item>
    <!--    <item name="splashScreenIconSize">0dp</item>-->

    <item name="android:windowSplashScreenAnimationDuration">0</item>
    <item name="postSplashScreenTheme">@style/Theme.AppLauncher</item>
    <item name="android:windowFullscreen">true</item>
  </style>
</resources>