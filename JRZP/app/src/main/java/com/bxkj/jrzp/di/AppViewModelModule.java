package com.bxkj.jrzp.di;

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.di.ViewModelKey;
import com.bxkj.jrzp.ui.splash.SplashViewModel;
import com.bxkj.jrzp.wxapi.WXEntryViewModel;

import dagger.Binds;
import dagger.Module;
import dagger.multibindings.IntoMap;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.di.module
 * @Description:
 * @TODO: TODO
 * @date 2019/4/4
 */
@Module
public abstract class AppViewModelModule {

  @Binds
  @IntoMap
  @ViewModelKey(SplashViewModel.class)
  abstract BaseViewModel bindSplashViewModel(SplashViewModel splashViewModel);

  @Binds
  @IntoMap
  @ViewModelKey(WXEntryViewModel.class)
  abstract BaseViewModel bindWXEntryViewModel(WXEntryViewModel wxEntryViewModel);
}
