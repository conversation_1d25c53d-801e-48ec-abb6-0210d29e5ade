package com.bxkj.jrzp.ui.splash

import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.UserUtils
import com.bxkj.personal.data.VersionData
import com.bxkj.personal.data.source.AppStatusInfoRepo
import javax.inject.Inject

class SplashViewModel @Inject constructor(
  private val mAppRepo: AppStatusInfoRepo,
) : BaseViewModel() {

  val toNextPageCommand = LiveEvent<Void>()
  val showUpdatePrivacyDialogCommand =
    LiveEvent<VersionData>()

  fun checkAgreementVersion(onlyRefreshLocalVersion: Boolean = false) {
    mAppRepo.checkAgreementVersion(
      object : ResultDataCallBack<VersionData> {
        override fun onSuccess(data: VersionData?) {
          if (onlyRefreshLocalVersion) {
            if (data != null) {
              UserUtils.saveUserAgreePrivacyVersion(data.version)
            }
          } else {
            data?.let {
              if (it.version != UserUtils.getUserAgreePrivacyVersion()) {
                showUpdatePrivacyDialogCommand.value = it
              } else {
                toNextActivity()
              }
            } ?: toNextActivity()
          }
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (!onlyRefreshLocalVersion) {
            toNextActivity()
          }
        }
      }
    )
  }

  private fun toNextActivity() {
    toNextPageCommand.call()
  }
}