package com.bxkj.jrzp.di;

import com.bxkj.jrzp.ui.splash.SplashActivity;
import com.bxkj.common.di.scope.PerActivity;
import com.bxkj.jrzp.wxapi.WXEntryActivity;

import dagger.Module;
import dagger.android.ContributesAndroidInjector;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.byj.di
 * @Description:
 * @date 2019/12/18
 */
@Module
public abstract class AppActivityModule {

    @PerActivity
    @ContributesAndroidInjector
    abstract WXEntryActivity wxEntryActivity();

    @PerActivity
    @ContributesAndroidInjector
    abstract SplashActivity splashActivity();
}
