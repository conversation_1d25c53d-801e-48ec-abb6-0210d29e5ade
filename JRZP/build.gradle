import com.google.devtools.ksp.gradle.KspTask

// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
  repositories {
    //华为Maven镜像仓库
    maven { url 'https://repo.huaweicloud.com/repository/maven/' }

    //jitpack仓库
    maven { url 'https://www.jitpack.io' }

    //google仓库
    google()

    maven {
      url 'http://*************:8082/repository/maven-public/'
      allowInsecureProtocol(true)
    }

    //华为开发者仓库
    maven { url 'https://developer.huawei.com/repo/' }
  }

  dependencies {
    classpath libs.agp
    classpath libs.kotlin.gradle.plugin

    //华为推送
    classpath libs.agcp

    //    classpath 'com.github.allenymt.PrivacySentry:plugin-sentry:1.2.7'
  }
}

plugins {
  id("com.bxkj.plugins.pins") apply(false)
  id 'org.jetbrains.kotlin.android' version libs.versions.kotlin apply false
  id 'cn.therouter.agp8' version libs.versions.therouter apply false
//  id 'cn.therouter' version libs.versions.therouter apply false
  id 'com.google.devtools.ksp' version '2.1.0-1.0.29' apply false
  alias(libs.plugins.kotlin.compose.compiler) apply(false)
}

allprojects {
  repositories {
    flatDir {
      dirs project(':lib-common').file('libs'), project(':group-support:yd_login').file("libs")
    }

    //华为Maven镜像仓库
    maven { url 'https://repo.huaweicloud.com/repository/maven/' }

    //jitpack仓库
    maven { url 'https://www.jitpack.io' }

    //google仓库
    google()

    maven {
      url 'http://sdk.mentamob.com/repository/vlion/'
      allowInsecureProtocol = true
    }

    maven {
      url 'http://*************:8082/repository/maven-public/'
      allowInsecureProtocol(true)
    }

    //华为开发者仓库
    maven { url 'https://developer.huawei.com/repo/' }
  }

  configurations.configureEach {
    resolutionStrategy {
      force 'com.squareup:javapoet:1.13.0'
    }
  }
}

tasks.register('javadoc', Javadoc) {
  failOnError false
}

tasks.register('clean', Delete) {
  delete rootProject.buildDir
}

tasks.withType(KspTask).forEach {
  jvmArgs("-Xmx4g")
}
