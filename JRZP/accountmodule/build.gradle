plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
  id 'com.google.devtools.ksp'
}

android {

  namespace "com.bxkj.accountmodule"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  lintOptions {
    abortOnError false
  }

  buildFeatures {
    dataBinding = true
  }

  resourcePrefix "account_"
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.jar'])

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor

  implementation project(":lib-common")

  includeApi(":group-business:user")
  includeApi(':group-enterprise:enterprise')
  includeApi(':personal')

  implementation project(':group-support:yd_login')
  implementation project(':group-support:db')
}
