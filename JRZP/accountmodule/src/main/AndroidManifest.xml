<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.bxkj.accountmodule"
  tools:ignore="LockedOrientationActivity">

  <application
    android:label="@string/common_app_name"
    android:supportsRtl="true"
    android:theme="@style/JrzpAppTheme"
    tools:replace="android:label">

    <activity
      android:name="com.bxkj.account.ui.updatepassword.UpdatePasswordActivityV2"
      android:launchMode="singleTask"
      android:screenOrientation="portrait" />
    <activity
      android:name="com.bxkj.account.ui.register.RegisterActivityV2"
      android:launchMode="singleTask"
      android:screenOrientation="portrait" />
    <activity
      android:name="com.bxkj.account.ui.login.LoginV2Activity"
      android:launchMode="singleTask"
      android:screenOrientation="portrait" />
    <activity
      android:name="com.bxkj.account.ui.jobselection.JobSelectionActivity"
      android:configChanges="keyboardHidden"
      android:screenOrientation="portrait"
      android:windowSoftInputMode="stateHidden" />
    <activity
      android:name="com.bxkj.account.ui.recoverpassword.RecoverPasswordActivity"
      android:configChanges="keyboardHidden"
      android:screenOrientation="portrait"
      android:windowSoftInputMode="adjustPan|stateHidden" />

  </application>

</manifest>