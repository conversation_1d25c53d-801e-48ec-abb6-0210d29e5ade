<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_match"
  android:background="@drawable/bg_ffffff">

  <include layout="@layout/common_include_title_bar" />

  <LinearLayout
    android:id="@+id/ll_job_search"
    style="@style/Layout.JobSearch"
    android:background="@drawable/account_bg_job_search"
    android:orientation="horizontal"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/title_bar">

    <ImageView
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_20"
      android:src="@drawable/common_ic_search" />

    <EditText
      android:id="@+id/et_job_search"
      style="@style/EditText.JobSearch" />
  </LinearLayout>

  <TextView
    style="@style/Text.15sp.333333"
    android:layout_marginTop="@dimen/dp_20"
    android:text="@string/account_checked_position"
    app:layout_constraintStart_toStartOf="@id/ll_job_search"
    app:layout_constraintTop_toBottomOf="@id/ll_job_search" />

  <TextView
    android:id="@+id/tv_checked_job"
    style="@style/Text.12sp.888888"
    android:layout_marginTop="@dimen/dp_22"
    android:text="@string/job_selection_default_num_format"
    app:layout_constraintEnd_toEndOf="@id/ll_job_search"
    app:layout_constraintTop_toBottomOf="@id/ll_job_search" />

  <LinearLayout
    android:id="@+id/ll_checked_position"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_77"
    android:gravity="center"
    app:layout_constraintTop_toBottomOf="@id/tv_checked_job">

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_checked_position"
      style="@style/match_match"
      android:overScrollMode="never"
      android:paddingTop="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_20"
      android:paddingBottom="@dimen/dp_14"
      android:visibility="gone" />

    <TextView
      style="@style/Text.JobSelectionPlaceHolder"
      android:text="@string/account_job_selection_placeholder" />
  </LinearLayout>

  <View
    android:id="@+id/v_line"
    style="@style/Line.Horizontal"
    android:layout_height="@dimen/dp_8"
    app:layout_constraintTop_toBottomOf="@id/ll_checked_position" />

  <com.bxkj.common.widget.pagestatuslayout.PageStatusLayout
    android:id="@+id/el_left"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    android:background="@drawable/bg_f8f8f8"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toStartOf="@id/el_right"
    app:layout_constraintHorizontal_weight="43"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/v_line">

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_first_class"
      style="@style/match_match"
      android:background="@drawable/bg_f8f8f8"
      android:overScrollMode="never" />
  </com.bxkj.common.widget.pagestatuslayout.PageStatusLayout>


  <com.bxkj.common.widget.pagestatuslayout.PageStatusLayout
    android:id="@+id/el_right"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintHorizontal_weight="57"
    app:layout_constraintStart_toEndOf="@id/el_left"
    app:layout_constraintTop_toBottomOf="@id/v_line">

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_second_class"
      style="@style/match_match"
      android:overScrollMode="never" />
  </com.bxkj.common.widget.pagestatuslayout.PageStatusLayout>

  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/recycler_search_job_class_result"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_0"
    android:background="@drawable/bg_ffffff"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/ll_job_search" />
</androidx.constraintlayout.widget.ConstraintLayout>