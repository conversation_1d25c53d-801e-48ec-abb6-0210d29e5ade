<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <androidx.legacy.widget.Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_16" />

    <FrameLayout
        style="@style/match_wrap">
        <com.bxkj.common.widget.PasswordEditText
            android:id="@+id/et_old_password"
            style="@style/EditText.Basic"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_30"
            android:hint="@string/account_please_input_old_password"
            android:imeOptions="actionNext"
            android:inputType="textPassword"
            android:visibility="gone" />
    </FrameLayout>

    <View
        android:id="@+id/v_line"
        style="@style/Line.Horizontal"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_30"
        android:visibility="gone" />

    <FrameLayout
        style="@style/match_wrap">
        <com.bxkj.common.widget.PasswordEditText
            android:id="@+id/et_new_password"
            style="@style/EditText.Basic"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_30"
            android:hint="@string/account_please_input_new_password"
            android:imeOptions="actionNext"
            android:inputType="textPassword" />
    </FrameLayout>

    <View
        style="@style/Line.Horizontal"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_30" />

    <FrameLayout
        style="@style/match_wrap">
        <com.bxkj.common.widget.PasswordEditText
            android:id="@+id/et_confirm_password"
            style="@style/EditText.Basic"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_30"
            android:hint="@string/account_please_confirm_new_password"
            android:imeOptions="actionDone"
            android:inputType="textPassword" />

    </FrameLayout>

    <View
        style="@style/Line.Horizontal"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_30" />


    <TextView
        android:id="@+id/tv_next"
        style="@style/Button.Basic"
        android:layout_marginEnd="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_30"
        android:enabled="false"
        android:text="@string/common_complete" />

</LinearLayout>