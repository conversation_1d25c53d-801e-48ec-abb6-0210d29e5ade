package com.bxkj.account.ui.jobselection;

import android.content.Context;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.accountmodule.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.jobselection
 * @Description: 职位大类适配器
 * @TODO: TODO
 * @date 2018/3/29
 */

public class JobFirstClassAdapter extends SuperAdapter<JobTypeData> {

    private int mSelectPosition = 0;

    public JobFirstClassAdapter(Context context, List<JobTypeData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, JobTypeData jobTypeData, int position) {
        TextView tvFirstClass = holder.findViewById(R.id.tv_first_class);
        tvFirstClass.setText(jobTypeData.getName());
        tvFirstClass.setSelected(position == mSelectPosition);

        holder.itemView.setOnClickListener(view -> {
            if (mSelectPosition == position) return;
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(holder.itemView, position);
            }
            mSelectPosition = position;
            notifyDataSetChanged();
        });
    }
}
