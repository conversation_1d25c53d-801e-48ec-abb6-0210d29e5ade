package com.bxkj.account.ui.recoverpassword;

import android.content.Intent;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.fragment.app.DialogFragment;
import com.bxkj.account.mvp.contract.SendSmsContract;
import com.bxkj.account.mvp.presenter.SendSmsPresenter;
import com.bxkj.account.ui.updatepassword.UpdatePasswordNavigation;
import com.bxkj.accountmodule.R;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.MyEditText;
import com.bxkj.common.widget.dialog.TipsDialog;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.recoverpassword
 * @Description: 找回密码
 * @TODO: TODO
 * @date 2018/5/8
 */

public class RecoverPasswordActivity extends BaseDaggerActivity
  implements RecoverPasswordContract.View, SendSmsContract.View {

  private static final int RECOVER_PASSWORD_BY_EMAIL = 1;
  private static final int RECOVER_PASSWORD_BY_MOBILE = 2;
  private static final int TO_UPDATE_PASSWORD_CODE = 1;

  @Inject
  RecoverPasswordPresenter mRecoverPasswordPresenter;
  @Inject
  SendSmsPresenter mSendSmsPresenter;

  private MyEditText etUserName;
  private MyEditText etEmail;
  private LinearLayout llRecoverPwdByEmail;
  private MyEditText etMobileNumber;
  private MyEditText etVerificationCode;
  private TextView tvGetVerificationCode;
  private LinearLayout llRecoverPwdByMobile;
  private TextView tvRecoverPasswordType;
  private TextView tvNext;

  private int mPageType = RECOVER_PASSWORD_BY_EMAIL;
  private String mMobile;
  private String mCode;

  @Override
  protected int getLayoutId() {
    return R.layout.account_activity_recover_password;
  }

  @Override
  protected void initPresenter() {
    mRecoverPasswordPresenter.attachView(this);
    mSendSmsPresenter.attachView(this);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.account_recover_password));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    etUserName.addTextChangedListener(textWatcher);
    etEmail.addTextChangedListener(textWatcher);

    etMobileNumber.addTextChangedListener(textWatcher);
    etVerificationCode.addTextChangedListener(textWatcher);
  }

  private TextWatcher textWatcher = new TextWatcher() {
    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
      changeNextBtnStatus();
    }

    @Override
    public void afterTextChanged(Editable editable) {

    }
  };

  /**
   * 改变下一步按钮状态
   */
  private void changeNextBtnStatus() {
    if (etUserName.length() > 0 && etEmail.length() > 0 && mPageType == RECOVER_PASSWORD_BY_EMAIL) {
      tvNext.setEnabled(true);
    } else if (etMobileNumber.length() > 0
      && etVerificationCode.length() > 0
      && mPageType == RECOVER_PASSWORD_BY_MOBILE) {
      tvNext.setEnabled(true);
    } else {
      tvNext.setEnabled(false);
    }
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_click_this) {
      resetPageStatus();
      if (mPageType == RECOVER_PASSWORD_BY_EMAIL) {
        mPageType = RECOVER_PASSWORD_BY_MOBILE;
        llRecoverPwdByEmail.setVisibility(View.GONE);
        llRecoverPwdByMobile.setVisibility(View.VISIBLE);
        tvRecoverPasswordType.setText(
          getString(R.string.account_change_to_recover_password_by_email));
      } else {
        mPageType = RECOVER_PASSWORD_BY_EMAIL;
        llRecoverPwdByMobile.setVisibility(View.GONE);
        llRecoverPwdByEmail.setVisibility(View.VISIBLE);
        tvRecoverPasswordType.setText(
          getString(R.string.account_change_to_recover_password_by_mobile));
      }
    } else if (view.getId() == R.id.tv_get_verification_code) {
      tvGetVerificationCode.setEnabled(false);
      mSendSmsPresenter.checkTheMobileNumberExists(etMobileNumber.getText().toString());
    } else {
      if (mPageType == RECOVER_PASSWORD_BY_EMAIL) {
        mRecoverPasswordPresenter.sendUpdatePasswordEmail(etUserName.getText().toString(),
          etEmail.getText().toString());
      } else {
        mRecoverPasswordPresenter.checkMobileAndVerificationCode(mMobile,
          etMobileNumber.getText().toString(), mCode, etVerificationCode.getText().toString());
      }
    }
  }

  /**
   * 重置页面状态
   */
  private void resetPageStatus() {
    etUserName.setText(CommonApiConstants.NO_TEXT);
    etEmail.setText(CommonApiConstants.NO_TEXT);
    etMobileNumber.setText(CommonApiConstants.NO_TEXT);
    etVerificationCode.setText(CommonApiConstants.NO_TEXT);
    tvNext.setEnabled(false);
  }

  @Override
  public void sendUpdatePasswordEmailSuccess(String msg) {
    new TipsDialog().setTitle(msg)
      .setOnConfirmClickListener((DialogFragment dialogFragment) -> finish());
  }

  @Override
  public void checkMobileAndCodeSuccess(String mobile) {
    UpdatePasswordNavigation.create(UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_NUMBER, mobile)
      .startForResult(this, TO_UPDATE_PASSWORD_CODE);
    //startActivityForResult(UpdatePasswordActivity.newIntent(this,
    //    RouterNavigation.UpdatePasswordActivity.RETRIEVE_PASSWORD_TYPE, mobile),
    //  TO_UPDATE_PASSWORD_CODE);
  }

  @Override
  public void mobileNumberNoExists(String mobileNumber) {
    showToast(getString(R.string.account_account_not_exists));
  }

  @Override
  public void mobileNumberExists(String mobileNumber) {
    mSendSmsPresenter.sendSMSVerificationCode(mobileNumber, 2);
  }

  @Override
  public void sendSMSVerificationCodeSuccess(String mobile, String code) {
    mMobile = mobile;
    mCode = code;
    mSendSmsPresenter.startSendSMSCountdown(60);
  }

  @Override
  public void sendSMSVerificationCodeError(String errMsg) {
    tvGetVerificationCode.setEnabled(true);
    showToast(errMsg);
  }

  @Override
  public void secondsToResend(long seconds) {
    tvGetVerificationCode.setText(
      getString(R.string.account_resend_countdown, String.valueOf(seconds)));
  }

  @Override
  public void resendCountDownOver() {
    tvGetVerificationCode.setText(R.string.account_get_verification_code);
    tvGetVerificationCode.setEnabled(true);
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK) {
      finish();
    }
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mRecoverPasswordPresenter.detachView();
    mSendSmsPresenter.detachView();
  }

  private void bindView(View bindSource) {
    etUserName = bindSource.findViewById(R.id.et_user_name);
    etEmail = bindSource.findViewById(R.id.et_email);
    llRecoverPwdByEmail = bindSource.findViewById(R.id.ll_recover_pwd_by_email);
    etMobileNumber = bindSource.findViewById(R.id.et_mobile_number);
    etVerificationCode = bindSource.findViewById(R.id.et_verification_code);
    tvGetVerificationCode = bindSource.findViewById(R.id.tv_get_verification_code);
    llRecoverPwdByMobile = bindSource.findViewById(R.id.ll_recover_pwd_by_mobile);
    tvRecoverPasswordType = bindSource.findViewById(R.id.tv_recover_password_type);
    tvNext = bindSource.findViewById(R.id.tv_next);
    bindSource.findViewById(R.id.tv_click_this).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_get_verification_code)
      .setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_next).setOnClickListener(v -> onViewClicked(v));
  }
}
