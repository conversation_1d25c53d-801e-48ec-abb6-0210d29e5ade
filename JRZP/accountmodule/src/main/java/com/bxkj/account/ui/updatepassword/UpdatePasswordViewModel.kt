package com.bxkj.account.ui.updatepassword

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.account.api.AccountApi
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.RegularUtils
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/7/18
 **/
class UpdatePasswordViewModel @Inject constructor(
  private val accountApi: AccountApi
) : BaseViewModel() {
  val oldPwd = MutableLiveData<String>()

  val newPwd = MutableLiveData<String>()
  val confirmPwd = MutableLiveData<String>()

  val updateWithNumberSuccessEvent = MutableLiveData<VMEvent<Unit>>()
  val updateWithOldPwdSuccessEvent = MutableLiveData<VMEvent<Unit>>()

  //修改按钮是否可点击
  val updateBtnEnable = MediatorLiveData<Boolean>().apply {
    fun checkUpdateBtnEnable(): Boolean {
      return if (updateType == UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_OLD_PWD) {
        !oldPwd.value.isNullOrEmpty() && !newPwd.value.isNullOrEmpty() && !confirmPwd.value.isNullOrEmpty()
      } else {
        !newPwd.value.isNullOrEmpty() && !confirmPwd.value.isNullOrEmpty()
      }
    }
    addSource(oldPwd) { value = checkUpdateBtnEnable() }
    addSource(newPwd) { value = checkUpdateBtnEnable() }
    addSource(confirmPwd) { value = checkUpdateBtnEnable() }
  }

  private var updateType: Int = UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_OLD_PWD
  private var retrievePwsMobile: String = ""

  fun start(extraUpdateType: Int, extraMobile: String) {
    updateType = extraUpdateType
    retrievePwsMobile = extraMobile
  }

  fun updatePassword() {
    if (updateType == UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_OLD_PWD && oldPwd.value.isNullOrEmpty()) {
      showToast("请输入旧密码")
      return
    }
    if (newPwd.value.isNullOrEmpty()) {
      showToast("请输入新密码")
      return
    }
    if (!RegularUtils.isPassword(newPwd.value)) {
      showToast("密码长度需为8-16，包含大小写字母和数字")
      return
    }
    if (confirmPwd.value.isNullOrEmpty()) {
      showToast("请再次输入新密码")
      return
    }
    if (newPwd.value != confirmPwd.value) {
      showToast("两次输入的密码不一致")
      return
    }
    if (updateType == UpdatePasswordNavigation.TYPE_UPDATE_PWD_WITH_OLD_PWD) {
      viewModelScope.launch {
        showLoading()
        accountApi.updatePwdWithOldPwdV2(getSelfUserID(), oldPwd.value!!, newPwd.value!!)
          .handleResult({
            updateWithOldPwdSuccessEvent.value = VMEvent(Unit)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    } else {
      viewModelScope.launch {
        showLoading()
        accountApi.updatePwdWithMobileNumberV2(retrievePwsMobile, newPwd.value!!)
          .handleResult({
            updateWithNumberSuccessEvent.value = VMEvent(Unit)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }
}