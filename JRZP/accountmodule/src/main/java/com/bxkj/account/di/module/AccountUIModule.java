package com.bxkj.account.di.module;

import com.bxkj.account.ui.jobselection.JobSelectionActivity;
import com.bxkj.account.ui.login.LoginV2Activity;
import com.bxkj.account.ui.recoverpassword.RecoverPasswordActivity;
import com.bxkj.account.ui.register.RegisterActivityV2;
import com.bxkj.account.ui.updatepassword.UpdatePasswordActivityV2;
import com.bxkj.common.di.scope.PerActivity;
import dagger.Module;
import dagger.android.ContributesAndroidInjector;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.di.module
 * @Description: activity绑定
 * @TODO: TODO
 * @date 2018/3/27
 */
@Module
public abstract class AccountUIModule {

  @PerActivity
  @ContributesAndroidInjector
  abstract UpdatePasswordActivityV2 updatePasswordActivityV2();

  @PerActivity
  @ContributesAndroidInjector
  abstract RegisterActivityV2 registerActivityV2();

  @PerActivity
  @ContributesAndroidInjector
  abstract JobSelectionActivity jobSelectionActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract RecoverPasswordActivity recoverPasswordActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract LoginV2Activity loginV2Activity();
}
