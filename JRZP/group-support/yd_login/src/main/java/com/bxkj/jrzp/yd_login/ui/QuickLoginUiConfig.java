package com.bxkj.jrzp.yd_login.ui;

import android.content.Context;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bxkj.common.util.DensityUtils;
import com.bxkj.jrzp.yd_login.R;
import com.netease.nis.quicklogin.helper.UnifyUiConfig;
import com.netease.nis.quicklogin.listener.ActivityLifecycleCallbacks;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/31
 */
public class QuickLoginUiConfig {

  public interface OnCustomViewClickListener {

    void onClick(View view);
  }

  public static UnifyUiConfig getUiConfig(final Context context,
      OnCustomViewClickListener onCustomViewClickListener,
      ActivityLifecycleCallbacks activityLifecycleCallbacks) {

    TextView tvOtherLoginMethod = new TextView(context);
    tvOtherLoginMethod.setText("其他方式登录");
    tvOtherLoginMethod.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
    tvOtherLoginMethod.setTextColor(Color.parseColor("#666666"));

    UnifyUiConfig.Builder uiConfig = new UnifyUiConfig.Builder()
        //状态栏
        .setStatusBarColor(Color.WHITE)
        .setStatusBarDarkColor(true)
        //设置导航栏
        .setNavigationTitle(" ")
        .setNavigationBackgroundColor(Color.WHITE)
        .setNavigationIcon("ic_big_close")
        .setHideNavigation(false)
        //设置logo
        .setLogoIconName("personal_ic_app_logo")
        .setLogoWidth(70)
        .setLogoHeight(70)
        .setLogoTopYOffset(50)
        .setHideLogo(false)
        //手机掩码
        .setMaskNumberColor(Color.parseColor("#333333"))
        .setMaskNumberSize(20)
        .setMaskNumberTopYOffset(140)
        //认证品牌
        .setSloganSize(12)
        .setSloganColor(Color.parseColor("#999999"))
        .setSloganBottomYOffset(18)
        //登录按钮
        .setLoginBtnText("本机号码一键登录")
        .setLoginBtnTextColor(Color.WHITE)
        .setLoginBtnBackgroundRes("yd_login_btn_login")
        .setLoginBtnWidth(315)
        .setLoginBtnHeight(48)
        .setLoginBtnTextSize(18)
        .setLoginBtnTopYOffset(DensityUtils.getScreenDPHeight(context) / 2 - 68)
        //隐私栏
        .setPrivacyTextStart("勾选即代表您已同意")
        .setProtocolText("《今日招聘隐私政策》")
        .setProtocolLink("https://jrzpapi2.jdzj.com/page/secrecy.html")
        .setPrivacyTextEnd("并使用本机号码登录")
        .setPrivacyTextColor(Color.parseColor("#333333"))
        .setPrivacyProtocolColor(Color.parseColor("#FE6600"))
        .setPrivacyMarginLeft(20)
        .setPrivacyMarginRight(20)
        .setPrivacyTextMarginLeft(4)
        .setPrivacyState(false)
        .setPrivacySize(12)
        .setPrivacyBottomYOffset(50)
        .setPrivacyTextGravityCenter(true)
        .setHidePrivacyCheckBox(false)
        .setCheckedImageName("ic_privacy_sel")
        .setUnCheckedImageName("ic_privacy_nor")
        // 协议详情页导航栏
        .setProtocolPageNavBackIcon("common_ic_back")
        .setProtocolPageNavColor(Color.WHITE)
        .addCustomView(tvOtherLoginMethod, "tv_other_login", UnifyUiConfig.POSITION_IN_BODY,
            (context1, activity, view) -> {
              if (onCustomViewClickListener != null) {
                onCustomViewClickListener.onClick(view);
              }
            });

    RelativeLayout.LayoutParams tvLoginOtherLayoutParams =
        new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT);
    tvLoginOtherLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
    tvLoginOtherLayoutParams.addRule(RelativeLayout.BELOW, R.id.yd_btn_oauth);
    tvLoginOtherLayoutParams.topMargin = DensityUtils.dp2px(context, 8f);
    tvOtherLoginMethod.setLayoutParams(tvLoginOtherLayoutParams);

    if (activityLifecycleCallbacks != null) {
      uiConfig.setActivityLifecycleCallbacks(activityLifecycleCallbacks);
    }
    return uiConfig.build(context);
  }
}
