package com.bxkj.share.ui

import android.view.View
import android.view.ViewGroup.LayoutParams
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.view.animation.OvershootInterpolator
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.widget.YUIWebView
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout
import com.bxkj.share.R
import com.bxkj.share.ShareOpID
import com.bxkj.share.ShareOption
import com.bxkj.share.ShareOptionsAdapter
import com.bxkj.share.ShareUtils
import com.hjq.permissions.Permission
import com.hjq.toast.Toaster
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req
import com.tencent.qcloud.ugckit.utils.FileUtils

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/22
 * @version: V1.0
 */
class ShareRecruitmentInfoDialog(private val builder: Builder) : ShareDialog() {

  private var mArrowAnim: Animation? = null

  class Builder : ShareDialog.Builder<Builder>() {

    internal var url: String = ""

    fun recruitmentInfoUrl(url: String): Builder = apply {
      this.url = url
    }

    override fun build(): ShareRecruitmentInfoDialog {
      return ShareRecruitmentInfoDialog(this)
    }

    override fun self(): Builder = this

  }

  private var mContentWebView: YUIWebView? = null

  override fun getRootViewId(): Int = R.layout.share_dialog_recruitment_info

  override fun layoutHeight(): Int {
    return LayoutParams.MATCH_PARENT
  }

  override fun initView() {
    super.initView()
    val pageStatusLayout = rootView.findViewById<PageStatusLayout>(R.id.psl_content)
//    pageStatusLayout.show(PageStatusConfigFactory.newLoadingConfig())

    mContentWebView = rootView.findViewById(R.id.web_content)
    mContentWebView?.let {
      it.webChromeClient = object : WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
          super.onProgressChanged(view, newProgress)
//          if (newProgress > 80) {
//            pageStatusLayout.hidden()
//          }
        }
      }
      it.loadUrl(builder.url)
    }

    rootView.findViewById<TextView>(R.id.tv_cancel).setOnClickListener {
      this.dismiss()
    }
    rootView.setOnClickListener {
      this.dismiss()
    }

    setupArrowAnim()

    setupShareOptions()

  }

  private fun setupArrowAnim() {
    val preArrow = rootView.findViewById<ImageView>(R.id.iv_pre)
    preArrow.setOnClickListener {
      mContentWebView?.execJS("javascript:changeup()", null)
    }
    val nextArrow = rootView.findViewById<ImageView>(R.id.iv_next)
    nextArrow.setOnClickListener {
      mContentWebView?.execJS("javascript:changedown()", null)
    }
    mArrowAnim = AlphaAnimation(0.2f, 0.8f).apply {
      duration = 800
      repeatCount = Animation.INFINITE
      repeatMode = Animation.REVERSE
    }
    preArrow.animation = mArrowAnim
    nextArrow.animation = mArrowAnim
    mArrowAnim?.start()
  }

  private fun setupShareOptions() {
    val optionItemEnterAnim =
      AnimationUtils.loadAnimation(activity, R.anim.anim_share_option_enter).apply {
        interpolator = OvershootInterpolator()
      }
    val optionItemEnterAnimController = LayoutAnimationController(optionItemEnterAnim).apply {
      order = LayoutAnimationController.ORDER_NORMAL
      delay = 0.3f
    }
    val shareOptions = getShareOptions()
    val shareOptionList = rootView.findViewById<RecyclerView>(R.id.recycler_share_options)
    shareOptionList.layoutAnimation = optionItemEnterAnimController
    shareOptionList.layoutManager = GridLayoutManager(activity, shareOptions.size)
    shareOptionList.adapter = ShareOptionsAdapter(context).apply {
      reset(shareOptions)
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val clickItem = data[position]
          if (
            builder.onShareItemClickListener?.onShareItemClick(
              this@ShareRecruitmentInfoDialog, clickItem
            ) == true
          ) {
            return
          } else {
            handleShareItemClick(clickItem)
          }
        }
      })
    }
  }

  private fun getShareOptions(): List<ShareOption> {
    return if (builder.shareOptions.isNullOrEmpty()) {
      getNormalShareOptions()
    } else {
      builder.shareOptions!!
    }
  }

  private fun getNormalShareOptions(): List<ShareOption> {
    return arrayListOf(
      ShareOption.get(R.drawable.ic_page_options_share_to_wechat, "微信", ShareOpID.SHARE_WECHAT),
      ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat_moment,
        "朋友圈",
        ShareOpID.SHARE_WECHAT_MOMENT
      ),
      ShareOption.get(R.drawable.share_ic_download_pic, "保存图片", ShareOpID.SHARE_COPY_LINK)
    )
  }

  private fun handleShareItemClick(clickItem: ShareOption) {
    when (clickItem.opID) {
      ShareOpID.SHARE_WECHAT -> {
        shareImgToWechat(Req.WXSceneSession)
      }
      ShareOpID.SHARE_WECHAT_MOMENT -> {
        shareImgToWechat(Req.WXSceneTimeline)
      }
      ShareOpID.SHARE_COPY_LINK -> {
        saveBusinessCardPic()
      }
    }
  }

  private fun shareImgToWechat(targetScene: Int) {
    PermissionUtils.requestPermission(
      activity,
      getString(R.string.permission_tips_title),
      getString(R.string.share_img_permission_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          FileUtils.captureWebView(mContentWebView)?.let {
            ShareUtils.shareImageToWechat(
              it,
              targetScene
            )
            dismiss()
          }
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          Toaster.show(getString(R.string.cancel_operation))
        }
      }, Permission.WRITE_EXTERNAL_STORAGE, Permission.READ_EXTERNAL_STORAGE
    )
  }

  /**
   * 保存名片图片
   */
  private fun saveBusinessCardPic() {
    PermissionUtils.requestPermission(
      activity,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_save_pic_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          if (ZPFileUtils.saveBitmapToGallery(
              context!!,
              FileUtils.captureWebView(mContentWebView)
            )
          ) {
            Toaster.show(getString(R.string.share_business_card_save_success))
            <EMAIL>()
          } else {
            Toaster.show(getString(R.string.share_business_card_save_failed))
            <EMAIL>()
          }
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          Toaster.show(getString(R.string.cancel_operation))
        }
      }, Permission.WRITE_EXTERNAL_STORAGE, Permission.READ_EXTERNAL_STORAGE
    )
  }

  override fun onPause() {
    super.onPause()
    mContentWebView?.onPause()
  }

  override fun onResume() {
    super.onResume()
    mContentWebView?.onResume()
  }

  override fun onDestroyView() {
    super.onDestroyView()
    mArrowAnim?.cancel()
    mContentWebView?.destroy()
  }

}