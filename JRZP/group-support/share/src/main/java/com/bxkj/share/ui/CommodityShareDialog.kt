package com.bxkj.share.ui

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.view.animation.OvershootInterpolator
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.widget.marquee.MarqueeView
import com.bxkj.common.widget.popup.IWheelOptions
import com.bxkj.share.PaymentNoticeAdapter
import com.bxkj.share.R
import com.bxkj.share.ShareCommodityParams
import com.bxkj.share.ShareOpID
import com.bxkj.share.ShareOption
import com.bxkj.share.ShareOptionsAdapter
import com.bxkj.share.ShareUtils
import com.hjq.toast.Toaster
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/27
 * @version: V1.0
 */
class CommodityShareDialog private constructor(private val builder: Builder) : ShareDialog() {

  override fun getRootViewId(): Int = R.layout.share_dialog

  class Builder : ShareDialog.Builder<Builder>() {

    internal var commodityParams: ShareCommodityParams? = null

    internal var commissionNotice: List<IWheelOptions>? = null

    internal var shareTipsTitle: String? = null

    internal var shareDesc: String? = null

    fun setCommodityParams(commodityParams: ShareCommodityParams): Builder = apply {
      this.commodityParams = commodityParams
    }

    fun setCommissionNotice(commissionNotice: List<IWheelOptions>?) = apply {
      this.commissionNotice = commissionNotice
    }

    fun setShareTipsTitle(title: String) = apply {
      this.shareTipsTitle = title
    }

    fun setShareTips(tips: String) = apply {
      this.shareDesc = tips
    }

    override fun build(): ShareDialog {
      return CommodityShareDialog(this)
    }

    override fun self(): Builder = this

  }

  override fun initView() {
    super.initView()
    setupCloseClickListener()
    setupShareCommodityParams()
    setupShareTips()
    setupPaymentNotice()
    setupShareOptions()
  }

  private fun setupCloseClickListener() {
    rootView.findViewById<TextView>(R.id.tv_cancel).setOnClickListener {
      dismiss()
    }
  }

  private fun setupShareCommodityParams() {
    builder.commodityParams?.let {
      ImageLoader.loadImage(
        activity,
        GlideLoadConfig.Builder().url(it.pic)
          .radius(DensityUtils.dp2px(activity, 10f))
          .into(rootView.findViewById(R.id.iv_share_pic)).build()
      )
      rootView.findViewById<TextView>(R.id.tv_course_name).text = it.title
      rootView.findViewById<TextView>(R.id.tv_course_price).text =
        HtmlUtils.fromHtml(getString(R.string.share_commodity_price_format, it.price))
      if (builder.shareDesc.isNullOrEmpty()) {
        rootView.findViewById<TextView>(R.id.tv_share_desc).text =
          HtmlUtils.fromHtml(getString(R.string.share_commodity_tips_desc_format, it.commission))
      }
    }
  }

  private fun setupShareTips() {
    builder.shareTipsTitle?.let {
      rootView.findViewById<TextView>(R.id.tv_share_title).text = it
    }

    builder.shareDesc?.let {
      rootView.findViewById<TextView>(R.id.tv_share_desc).text = it
    }
  }

  private fun setupPaymentNotice() {
    builder.commissionNotice?.let {
      val marqueePaymentNotice = rootView.findViewById<MarqueeView>(R.id.marquee_payment_notice)
      marqueePaymentNotice.setAdapter(PaymentNoticeAdapter(it))
      marqueePaymentNotice.start()
    }
  }

  private fun setupShareOptions() {
    val optionItemEnterAnim =
      AnimationUtils.loadAnimation(activity, R.anim.anim_share_option_enter).apply {
        interpolator = OvershootInterpolator()
      }
    val optionItemEnterAnimController = LayoutAnimationController(optionItemEnterAnim).apply {
      order = LayoutAnimationController.ORDER_NORMAL
      delay = 0.3f
    }
    val shareOptions = getShareOptions()
    val shareOptionList = rootView.findViewById<RecyclerView>(R.id.recycler_share_options)
    shareOptionList.layoutAnimation = optionItemEnterAnimController
    shareOptionList.layoutManager = GridLayoutManager(activity, shareOptions.size)
    shareOptionList.adapter = ShareOptionsAdapter(context).apply {
      reset(shareOptions)
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val clickItem = data[position]
          if (builder.onShareItemClickListener?.onShareItemClick(
              this@CommodityShareDialog,
              clickItem
            ) == true
          ) {
            return
          } else {
            handleShareItemClick(clickItem)
          }
          dismiss()
        }
      })
    }
  }

  private fun handleShareItemClick(clickItem: ShareOption) {
    when (clickItem.opID) {
      ShareOpID.SHARE_WECHAT -> {
        shareToWechat(clickItem.opID)
      }
      ShareOpID.SHARE_WECHAT_MOMENT -> {
        shareToWechat(clickItem.opID)
      }
      ShareOpID.SHARE_COPY_LINK -> {
        copyLinkText()
      }
    }
  }

  private fun copyLinkText() {
    val clipboardManager = activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val copyText = ClipData.newPlainText("Link", builder.shareUrl)
    clipboardManager.setPrimaryClip(copyText)
    Toaster.show("已复制到剪切板")
  }

  private fun shareToWechat(shareOpID: Int) {
    val finalShareUrl = "${builder.shareUrl}&fx=android"
    if (CheckUtils.isNullOrEmpty(builder.sharePic)) {
      ShareUtils.shareUrlToWechat(
        activity,
        builder.shareTitle,
        getShareContent(),
        finalShareUrl,
        null,
        getWechatShareScene(shareOpID)
      )
    } else {
      loadWebPic(builder.sharePic!!) { bitmap ->
        ShareUtils.shareUrlToWechat(
          activity,
          builder.shareTitle,
          getShareContent(),
          finalShareUrl,
          bitmap,
          getWechatShareScene(shareOpID)
        )
      }
    }
  }

  private fun getWechatShareScene(shareOpID: Int): Int {
    return if (shareOpID == ShareOpID.SHARE_WECHAT)
      Req.WXSceneSession
    else Req.WXSceneTimeline
  }

  private fun getShareContent(): String {
    return HtmlUtils.delHtmlTag(builder.shareContent)
  }

  private fun getShareOptions(): List<ShareOption> {
    return if (builder.shareOptions.isNullOrEmpty()) {
      getNormalShareOptions()
    } else {
      builder.shareOptions!!
    }
  }

  private fun getNormalShareOptions(): List<ShareOption> {
    return arrayListOf(
      ShareOption.get(R.drawable.ic_page_options_share_to_wechat, "微信", ShareOpID.SHARE_WECHAT),
      ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat_moment,
        "朋友圈",
        ShareOpID.SHARE_WECHAT_MOMENT
      ),
      ShareOption.get(R.drawable.ic_page_options_copy_link, "复制链接", ShareOpID.SHARE_COPY_LINK)
    )
  }

}