package com.bxkj.share.ui

import android.graphics.Color
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.view.animation.OvershootInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.share.R
import com.bxkj.share.ShareOpID
import com.bxkj.share.ShareOption
import com.bxkj.share.ShareOptionsAdapter
import com.bxkj.share.ShareUtils
import com.google.zxing.EncodeHintType
import com.google.zxing.EncodeHintType.CHARACTER_SET
import com.google.zxing.EncodeHintType.ERROR_CORRECTION
import com.google.zxing.EncodeHintType.MARGIN
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel.H
import com.hjq.permissions.Permission
import com.hjq.toast.Toaster
import com.king.zxing.util.CodeUtils
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req
import com.tencent.qcloud.ugckit.utils.FileUtils
import java.util.EnumMap

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/21
 * @version: V1.0
 */
class ShareBusinessCardDialog private constructor(private val builder: Builder) : ShareDialog() {

  class Builder : ShareDialog.Builder<Builder>() {

    internal var showJobCount: Boolean = false
    internal var jobCount: Int = 0
    internal var userName: String = ""
    internal var userAvatar: String = ""
    internal var noticeCount: Int = 0
    internal var likeCount: Int = 0
    internal var fansCount: Int = 0
    internal var userDesc: String = ""
    internal var userAuthTag: Int = 0

    fun showJobCount(showJobCount: Boolean) = apply {
      this.showJobCount = showJobCount
    }

    fun setJobCount(jobCount: Int) = apply {
      this.jobCount = jobCount
    }

    fun setUserName(userName: String) = apply {
      this.userName = userName
    }

    fun setUserAvatar(userAvatar: String) = apply {
      this.userAvatar = userAvatar
    }

    fun setUserAuthTagImg(tagImg: Int) = apply {
      userAuthTag = tagImg
    }

    fun setUserCount(notice: Int, like: Int, fans: Int) = apply {
      this.noticeCount = notice
      this.likeCount = like
      this.fansCount = fans
    }

    fun setUserDesc(desc: String) = apply {
      this.userDesc = desc
    }

    override fun build(): ShareBusinessCardDialog {
      return ShareBusinessCardDialog(this)
    }

    override fun self(): Builder {
      return this
    }
  }

  override fun getRootViewId(): Int = R.layout.share_dialog_business_card

  override fun initView() {
    super.initView()

    rootView.findViewById<TextView>(R.id.tv_cancel).setOnClickListener {
      this.dismiss()
    }

    rootView.findViewById<LinearLayout>(R.id.ll_job_count).visibility =
      if (builder.showJobCount) View.VISIBLE else View.GONE
    rootView.findViewById<TextView>(R.id.tv_job_count).text = builder.jobCount.toString()

    setupUserInfo()
    setupShareOptions()
  }

  private fun setupUserInfo() {
    rootView.findViewById<TextView>(R.id.tv_name).text = builder.userName.trim()
    rootView.findViewById<TextView>(R.id.tv_desc).text =
      if (CheckUtils.isNullOrEmpty(builder.userDesc)) "这个人很懒，什么都没留下。" else builder.userDesc
    rootView.findViewById<TextView>(R.id.tv_notice_count).text = builder.noticeCount.toString()
    rootView.findViewById<TextView>(R.id.tv_like_count).text = builder.likeCount.toString()
    rootView.findViewById<TextView>(R.id.tv_fans_count).text = builder.fansCount.toString()
    ImageLoader.loadImage(
      context,
      GlideLoadConfig.Builder()
        .url(builder.userAvatar)
        .error(R.drawable.ic_user_avatar_placeholder)
        .placeHolder(R.drawable.ic_user_avatar_placeholder)
        .circle()
        .into(rootView.findViewById<ImageView>(R.id.iv_avatar))
        .build()
    )

    val ivAuthTag = rootView.findViewById<ImageView>(R.id.iv_auth_tag)
//    if (builder.userAuthTag <= 0) {
//      ivAuthTag.visibility = View.GONE
//    } else {
//      ivAuthTag.visibility = View.VISIBLE
//      ivAuthTag.setImageResource(builder.userAuthTag)
//    }

    //配置参数
    val hints: MutableMap<EncodeHintType, Any> =
      EnumMap(com.google.zxing.EncodeHintType::class.java)
    hints[CHARACTER_SET] = "utf-8"
    //容错级别
    hints[ERROR_CORRECTION] = H
    //设置空白边距的宽度
    hints[MARGIN] = 0 //default is 4

    ImageLoader.loadImage(
      context,
      GlideLoadConfig.Builder().url(
        CodeUtils.createQRCode(
          builder.shareUrl,
          DensityUtils.dp2px(context, 60f), null, 0f, hints, Color.BLACK
        )
      ).into(rootView.findViewById(R.id.iv_qr_code)).build()
    )

  }

  private fun setupShareOptions() {
    val optionItemEnterAnim =
      AnimationUtils.loadAnimation(activity, R.anim.anim_share_option_enter).apply {
        interpolator = OvershootInterpolator()
      }
    val optionItemEnterAnimController = LayoutAnimationController(optionItemEnterAnim).apply {
      order = LayoutAnimationController.ORDER_NORMAL
      delay = 0.3f
    }
    val shareOptions = getShareOptions()
    val shareOptionList = rootView.findViewById<RecyclerView>(R.id.recycler_share_options)
    shareOptionList.layoutAnimation = optionItemEnterAnimController
    shareOptionList.layoutManager = GridLayoutManager(activity, shareOptions.size)
    shareOptionList.adapter = ShareOptionsAdapter(context).apply {
      reset(shareOptions)
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val clickItem = data[position]
          if (
            builder.onShareItemClickListener?.onShareItemClick(
              this@ShareBusinessCardDialog, clickItem
            ) == true
          ) {
            return
          } else {
            handleShareItemClick(clickItem)
          }
        }
      })
    }
  }

  private fun handleShareItemClick(clickItem: ShareOption) {
    when (clickItem.opID) {
      ShareOpID.SHARE_WECHAT -> {
        shareImgToWechat(Req.WXSceneSession)
      }
      ShareOpID.SHARE_WECHAT_MOMENT -> {
        shareImgToWechat(Req.WXSceneTimeline)
      }
      ShareOpID.SHARE_COPY_LINK -> {
        saveBusinessCardPic()
      }
    }
  }

  private fun shareImgToWechat(targetScene: Int) {
    PermissionUtils.requestPermission(
      activity,
      getString(R.string.permission_tips_title),
      getString(R.string.share_img_permission_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          ShareUtils.shareImageToWechat(
            FileUtils.captureView(rootView.findViewById<LinearLayout>(R.id.ll_business_card)),
            targetScene
          )
          dismiss()
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          Toaster.show(getString(R.string.cancel_operation))
        }
      }, Permission.WRITE_EXTERNAL_STORAGE, Permission.READ_EXTERNAL_STORAGE
    )
  }

  /**
   * 保存名片图片
   */
  private fun saveBusinessCardPic() {
    PermissionUtils.requestPermission(
      activity,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_save_pic_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          if (ZPFileUtils.saveBitmapToGallery(
              context!!,
              FileUtils.captureView(rootView.findViewById<LinearLayout>(R.id.ll_business_card))
            )
          ) {
            Toaster.show(getString(R.string.share_business_card_save_success))
            <EMAIL>()
          } else {
            Toaster.show(getString(R.string.share_business_card_save_failed))
            <EMAIL>()
          }
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          Toaster.show(getString(R.string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  private fun getShareOptions(): List<ShareOption> {
    return if (builder.shareOptions.isNullOrEmpty()) {
      getNormalShareOptions()
    } else {
      builder.shareOptions!!
    }
  }

  private fun getNormalShareOptions(): List<ShareOption> {
    return arrayListOf(
      ShareOption.get(R.drawable.ic_page_options_share_to_wechat, "微信", ShareOpID.SHARE_WECHAT),
      ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat_moment,
        "朋友圈",
        ShareOpID.SHARE_WECHAT_MOMENT
      ),
      ShareOption.get(R.drawable.share_ic_download_pic, "保存图片", ShareOpID.SHARE_COPY_LINK)
    )
  }
}