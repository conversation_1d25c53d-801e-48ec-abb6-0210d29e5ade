<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.feature.ui.selectaddress.AddressMultiSelectViewModel" />
    </data>

    <LinearLayout style="@style/match_match"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            style="@style/match_wrap"
            app:title="@string/select_multi_address_title" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1"
            android:orientation="horizontal">

            <include
                android:id="@+id/include_province_list"
                layout="@layout/include_mvvm_refresh_layout"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="2"
                app:listViewModel="@{viewModel.provinceListViewModel}" />

            <View style="@style/Line.Vertical.Light" />

            <include
                android:id="@+id/include_city_list"
                layout="@layout/include_mvvm_refresh_layout"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="3"
                app:listViewModel="@{viewModel.cityListViewModel}" />

            <View style="@style/Line.Vertical.Light" />

            <include
                android:id="@+id/include_area_list"
                layout="@layout/include_mvvm_refresh_layout"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_weight="4"
                app:listViewModel="@{viewModel.areaListViewModel}" />

        </LinearLayout>

        <View
            style="@style/Line.Horizontal.Light"
            android:visibility="@{viewModel.selectedAddress.size()==0?View.GONE:View.VISIBLE}" />

        <LinearLayout
            style="@style/match_wrap"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp_12"
            android:visibility="@{viewModel.selectedAddress.size()==0?View.GONE:View.VISIBLE}">

            <TextView
                style="@style/Text.14sp.333333"
                android:text="@string/select_address_selected" />

            <View
                style="@style/Line.Vertical.Light"
                android:layout_height="@dimen/dp_18"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_12" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_selected_address"
                style="@style/match_wrap"
                bind:items="@{viewModel.selectedAddress}" />
        </LinearLayout>

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/dp_8"
            android:paddingEnd="@dimen/dp_12"
            android:paddingStart="@dimen/dp_12"
            android:visibility="@{viewModel.selectedAddress.size()==0?View.GONE:View.VISIBLE}">

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Text.14sp.333333.Bold"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_4"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_12"
                android:paddingTop="@dimen/dp_12"
                android:text="@string/select_address_cancel" />

            <TextView
                android:id="@+id/tv_confirm"
                style="@style/Text.14sp.FE6600.Bold"
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_4"
                android:gravity="center"
                android:onClick="@{()->viewModel.save()}"
                android:paddingBottom="@dimen/dp_12"
                android:paddingTop="@dimen/dp_12"
                android:text="@{@string/select_address_selected_format(viewModel.selectedAddress.size(),viewModel.maxSelectNum)}" />

        </LinearLayout>

    </LinearLayout>
</layout>