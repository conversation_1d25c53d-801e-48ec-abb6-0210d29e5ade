<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.support.feature.data.AddressItemData" />
    </data>

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/Text.14sp.333333"
        android:layout_width="match_parent"
        android:background="@drawable/frame_cccccc_round"
        android:drawableEnd="@drawable/ic_word_close"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_6"
        android:text="@{data.showText}" />

</layout>