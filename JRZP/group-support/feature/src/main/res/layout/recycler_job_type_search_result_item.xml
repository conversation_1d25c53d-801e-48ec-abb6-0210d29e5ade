<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.common.data.JobTypeData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_height="@dimen/common_dp_42"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_12">

    <TextView
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_weight="1"
      android:text="@{data.fullTypeText}" />

    <ImageView
      style="@style/wrap_wrap"
      android:src="@drawable/common_ic_cb_checked"
      android:visibility="@{data.selected?View.VISIBLE:View.GONE}" />

  </LinearLayout>
</layout>