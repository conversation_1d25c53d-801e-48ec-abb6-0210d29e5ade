<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_e6202020"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/indicator"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/common_dp_44" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_22"
            android:layout_marginTop="@dimen/dp_22"
            android:src="@drawable/ic_close_white" />

    </LinearLayout>
</layout>