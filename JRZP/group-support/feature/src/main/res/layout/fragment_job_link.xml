<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.support.feature.ui.sharejobdialog.JobLinkViewModel" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/dp_36"
      android:layout_marginEnd="@dimen/dp_36"
      android:background="@drawable/enterprise_bg_job_link_info"
      android:paddingTop="@dimen/dp_77"
      android:paddingBottom="@dimen/dp_77">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_hr_avatar"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
        bind:imgUrl="@{viewModel.jobPosterInfo.hrPhoto}" />

      <TextView
        android:id="@+id/tv_hr_name"
        style="@style/Text.12sp.333333"
        android:layout_marginEnd="@dimen/dp_12"
        android:text="@{viewModel.jobPosterInfo.comName}"
        app:layout_constraintEnd_toStartOf="@id/iv_hr_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_hr_avatar" />

      <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_52"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_4"
        android:background="@drawable/enterprise_bg_job_link_talk"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_0"
        app:layout_constraintEnd_toStartOf="@id/iv_hr_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_hr_name">

        <LinearLayout
          android:id="@+id/ll_app_info"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:gravity="center_vertical"
          android:orientation="horizontal"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent">

          <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@mipmap/ic_launcher"
            app:shapeAppearance="@style/roundedCornerImageStyle" />

          <TextView
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_4"
            android:text="@string/enterprise_job_link_app_name" />
        </LinearLayout>

        <LinearLayout
          android:id="@+id/ll_job_content"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/dp_0"
          android:background="@drawable/bg_ffffff"
          android:orientation="vertical"
          android:paddingTop="@dimen/dp_12"
          app:layout_constraintBottom_toTopOf="@id/v_bottom_line"
          app:layout_constraintDimensionRatio="200:165"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/ll_app_info">

          <com.bxkj.common.widget.ZPTextView
            android:id="@+id/tv_job_name"
            style="@style/Text.16sp.333333.Bold"
            android:text="@{viewModel.jobPosterInfo.name}" />

          <TextView
            android:id="@+id/tv_job_salary"
            style="@style/Text.14sp.FE6600"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@{viewModel.jobPosterInfo.money}" />

          <TextView
            android:id="@+id/tv_job_desc"
            style="@style/Text.12sp.666666"
            android:layout_marginTop="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{viewModel.jobPosterInfo.jobDesc}" />

        </LinearLayout>

        <View
          android:id="@+id/v_bottom_line"
          style="@style/Line.Horizontal.Light"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/ll_job_content" />

        <TextView
          style="@style/Text.12sp.999999"
          android:layout_width="@dimen/dp_0"
          android:layout_marginTop="@dimen/dp_4"
          android:layout_marginBottom="@dimen/dp_4"
          android:drawableStart="@drawable/enterprise_ic_wechat_mini_program"
          android:drawablePadding="@dimen/dp_4"
          android:gravity="center_vertical"
          android:text="小程序"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/v_bottom_line" />

      </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.legacy.widget.Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/dp_36"
      android:layout_marginTop="@dimen/dp_22"
      android:layout_marginEnd="@dimen/dp_36"
      android:layout_marginBottom="@dimen/dp_22"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_share_to_wechat"
        style="@style/Text.14sp.FFFFFF"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/ic_page_options_share_to_wechat"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_horizontal"
        android:onClick="@{onClickListener}"
        android:text="@string/share_wechat" />

      <TextView
        android:id="@+id/tv_share_to_wechat_moment"
        style="@style/Text.14sp.FFFFFF"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/ic_page_options_share_to_wechat_moment"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_horizontal"
        android:onClick="@{onClickListener}"
        android:text="@string/share_wechat_moment" />

      <TextView
        android:id="@+id/tv_copy_link"
        style="@style/Text.14sp.FFFFFF"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:drawableTop="@drawable/ic_page_options_copy_link"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_horizontal"
        android:onClick="@{onClickListener}"
        android:text="@string/share_copy_link" />

    </LinearLayout>
  </LinearLayout>

</layout>