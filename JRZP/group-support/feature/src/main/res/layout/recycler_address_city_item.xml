<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.common.data.AddressOptionData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:orientation="horizontal"
        android:padding="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_city"
            style="@style/Text.15sp.333333"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@{data.name}"
            android:textColor="@color/cl_333333_to_fe6600_selector" />

        <ImageView
            style="@style/wrap_wrap"
            android:src="@drawable/ic_select_sel"
            android:visibility="@{data.selected?View.VISIBLE:View.GONE}" />

    </LinearLayout>
</layout>