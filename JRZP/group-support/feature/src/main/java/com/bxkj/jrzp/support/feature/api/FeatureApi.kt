package com.bxkj.jrzp.support.feature.api

import com.bxkj.common.data.AddressOptionData
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.data.UserResumeData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/4
 * @version: V1.0
 */
interface FeatureApi {

    @POST("/JobApply/AddJobApply/")
    suspend fun submitResume(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/ReleaseJob/GetFenxiangJob/")
    suspend fun getJobShareInfo(@Body zpRequestBody: ZPRequestBody): BaseResponse<JobData>

    @POST("/Area/GetAreaList/")
    suspend fun getAreaList(@Body requestBody: ZPRequestBody): BaseResponse<List<AddressOptionData>>

    @POST("/Area/GetAreaList/")
    suspend fun getAreaListV1(@Body requestBody: ZPRequestBody): BaseResponse<List<AreaOptionsData>>

    @POST("/Job/GetJobListByName/")
    suspend fun searchJobTypeByName(@Body requestBody: ZPRequestBody): BaseResponse<List<JobTypeData>>

    @POST("/Job/GetJobList/")
    suspend fun getJobType(@Body requestBody: ZPRequestBody): BaseResponse<List<JobTypeData>>

    @POST("/Resume/GetResumeList/")
    suspend fun getUserResumeList(@Body requestBody: ZPRequestBody): BaseResponse<List<UserResumeData>>

    @POST("/Zhibo/ZhiboRz/")
    suspend fun checkPersonalAuthStatus(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

    @POST("/UserBasicInfo/isInfoFinished/")
    suspend fun checkPersonalInfoIsCompleted(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Company/GetCompanyBaseInfoISFinish/")
    suspend fun checkCompanyInfoCompleted(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/News/VerifyPublish/")
    suspend fun checkEnterpriseCertificated(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/UserBasicInfo/IsHFNameAndTxInfo/")
    suspend fun checkUserAvatarAndNickName(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>
}