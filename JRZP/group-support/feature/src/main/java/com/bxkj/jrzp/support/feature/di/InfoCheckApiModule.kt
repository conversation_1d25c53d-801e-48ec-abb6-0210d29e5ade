package com.bxkj.jrzp.support.feature.di

import com.bxkj.jrzp.support.feature.api.FeatureApi
import dagger.Module
import dagger.Provides
import retrofit2.Retrofit

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/4
 * @version: V1.0
 */
@Module
class InfoCheckApiModule {
  @Provides
  fun provideInfoCheckApi(retrofit: Retrofit): FeatureApi {
    return retrofit.create(FeatureApi::class.java)
  }
}