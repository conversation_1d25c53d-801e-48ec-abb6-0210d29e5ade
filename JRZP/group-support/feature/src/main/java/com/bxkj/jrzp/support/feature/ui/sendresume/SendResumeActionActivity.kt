package com.bxkj.jrzp.support.feature.ui.sendresume

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant

/**
 * Description:投递简历操作
 * Author:Sanjin
 **/
class SendResumeActionActivity : BaseDBActivity<Nothing, SendResumeActionViewModel>() {

    companion object {

        private const val EXTRA_JOB_ID = "JOB_ID"
        private const val EXTRA_COMPANY_USER_ID = "COMPANY_USER_ID"

        const val RESULT_JOB_ID = "RESULT_SUCCESS_RESUME_ID"

        fun newIntent(context: Context, jobId: Int, companyUserId: Int): Intent {
            return Intent(context, SendResumeActionActivity::class.java).apply {
                putExtra(EXTRA_JOB_ID, jobId)
                putExtra(EXTRA_COMPANY_USER_ID, companyUserId)
            }
        }
    }

    private val jobId by lazy { intent.getIntExtra(EXTRA_JOB_ID, 0) }
    private val companyUserId by lazy { intent.getIntExtra(EXTRA_COMPANY_USER_ID, 0) }

    override fun getViewModelClass(): Class<SendResumeActionViewModel> = SendResumeActionViewModel::class.java

    override fun getLayoutId(): Int = CommonApiConstants.NO_ID

    override fun initPage(savedInstanceState: Bundle?) {
        statusBarManager.transparentStatusBar().transparentNavigationBar().init()

        subscribeViewModelEvent()

        viewModel.start(jobId, companyUserId)
    }

    private fun subscribeViewModelEvent() {
        viewModel.toCreateResumeCommand.observe(this, EventObserver {
            MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
        })

        viewModel.submitSuccessEvent.observe(this, EventObserver {
            setResult(RESULT_OK, Intent().apply {
                putExtra(RESULT_JOB_ID, it)
            })
        })

        viewModel.pageFinishCommand.observe(this,EventObserver{
            finish()
        })
    }
}