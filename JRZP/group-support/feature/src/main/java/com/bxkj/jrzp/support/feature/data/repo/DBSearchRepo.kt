package com.bxkj.jrzp.support.feature.data.repo

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.exception.ExceptionCode
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.jrzp.support.db.AppDatabase
import com.bxkj.jrzp.support.db.entry.SearchRecord
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchType
import io.reactivex.SingleObserver
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 **/
class DBSearchRepo @Inject constructor(
    private val appDatabase: AppDatabase
):BaseRepo() {
    fun insertSearchHistory(searchRecord: SearchRecord): ReqResponse<Nothing> {
        val oldRecord =
            appDatabase.searchRecordDao().checkHasItem(searchRecord.type, searchRecord.keyword)
        if (oldRecord != null) {
            appDatabase.searchRecordDao().deleteItemById(oldRecord.id)
        }
        appDatabase.searchRecordDao().inset(searchRecord)
        return ReqResponse.Success()
    }

    suspend fun getHistoryByType(searchType: Int): ReqResponse<List<SearchRecord>> {
        val result = appDatabase.searchRecordDao().getCoroutineAllByType(searchType)
        return if (result != null) {
            ReqResponse.Success(result)
        } else {
            ReqResponse.Failure(RespondThrowable.getDefault())
        }
    }

    /**
     * 根据[searchType]清除搜索历史
     */
    suspend fun clearHistoryByType(searchType: Int): ReqResponse<Nothing> {
        return if (appDatabase.searchRecordDao().clearByType(searchType) > 0) {
            ReqResponse.Success()
        } else {
            ReqResponse.Failure(RespondThrowable.getDefault())
        }
    }

    fun clearHistoryByType(searchType: SearchType, callBack: ResultCallBack) {
        if (appDatabase.searchRecordDao().clearByType(searchType.value) > 0) {
            callBack.onSuccess()
        } else {
            callBack.onError(RespondThrowable(null, ExceptionCode.UNKNOWN, ExceptionCode.UNKNOWN_TEXT))
        }
    }

    fun deleteById(id: Int) {
        appDatabase.searchRecordDao().deleteItemById(id)
    }

    /**
     * 根据[id]删除搜索记录
     */
    suspend fun deleteHistoryById(id: Int): ReqResponse<Nothing> {
        return if (appDatabase.searchRecordDao().deleteItemById(id) > 0) {
            ReqResponse.Success()
        } else {
            ReqResponse.Failure(RespondThrowable.getDefault())
        }
    }

    fun getAllByType(
        searchType: Int,
        callBack: ResultDataCallBack<List<SearchRecord>>,
    ) {
        appDatabase.searchRecordDao().getAllByType(searchType)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : SingleObserver<List<SearchRecord>> {
                override fun onSuccess(list: List<SearchRecord>) {
                    callBack.onSuccess(list)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(e: Throwable) {
                    callBack.onError(
                        RespondThrowable(
                            null,
                            ExceptionCode.UNKNOWN,
                            ExceptionCode.UNKNOWN_TEXT
                        )
                    )
                }
            })
    }
}