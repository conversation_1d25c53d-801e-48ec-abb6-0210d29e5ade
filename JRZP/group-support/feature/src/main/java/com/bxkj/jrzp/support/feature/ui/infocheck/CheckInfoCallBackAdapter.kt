package com.bxkj.jrzp.support.feature.ui.infocheck

import com.bxkj.jrzp.support.feature.data.CheckErrMsg
import com.bxkj.jrzp.support.feature.data.CheckResultMsg

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/6/21
 * @version: V1.0
 */
abstract class CheckInfoCallBackAdapter :
    CheckInfoCallback {
  override fun onAllCheckSuccess(result: CheckResultMsg) {
  }

  override fun onCheckFailed(checkErrorMsg: CheckErrMsg) {
  }
}