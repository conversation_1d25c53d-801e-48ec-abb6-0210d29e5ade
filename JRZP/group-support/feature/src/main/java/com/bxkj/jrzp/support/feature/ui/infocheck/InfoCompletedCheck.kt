package com.bxkj.jrzp.support.feature.ui.infocheck

import android.content.Context
import com.bxkj.jrzp.support.feature.data.InfoCheckItem

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/6/21
 * @version: V1.0
 */
class InfoCompletedCheck {

  private var mContext: Context? = null

  private var mCheckInfoCallBack: CheckInfoCallback? = null

  private var failedCheckTipsNextStep: FailedCheckTipsNextStep? = null

  private var mNeedCheckItem = ArrayList<InfoCheckItem>()

  private var mShowLoading = true

  companion object {
    fun with(context: Context): InfoCompletedCheck {
      return InfoCompletedCheck(context)
    }
  }

  private constructor(context: Context) {
    mContext = context
  }

  fun showLoading(show: Boolean): InfoCompletedCheck = apply {
    mShowLoading = show
  }

  fun checkItem(vararg item: InfoCheckItem): InfoCompletedCheck =
    apply {
      mNeedCheckItem.addAll(item.toMutableList())
    }

  fun checkItem(items: List<InfoCheckItem>) {
    mNeedCheckItem.addAll(items)
  }

  fun setCheckInfoCallBack(checkInfoCallback: CheckInfoCallback): InfoCompletedCheck = apply {
    mCheckInfoCallBack = checkInfoCallback
  }

  fun setFailedCheckTipsNextStep(failedCheckTipsNextStep: FailedCheckTipsNextStep): InfoCompletedCheck =
    apply {
      this.failedCheckTipsNextStep = failedCheckTipsNextStep
    }

  fun start() {
    mContext?.let { context ->
      CheckActivity.startCheck(
        context,
        mNeedCheckItem,
        mCheckInfoCallBack,
        failedCheckTipsNextStep,
        mShowLoading
      )
    }
  }
}