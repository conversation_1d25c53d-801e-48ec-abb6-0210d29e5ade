package com.bxkj.jrzp.support.feature.ui.filepreview

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Base64
import android.webkit.WebChromeClient
import android.webkit.WebView
import com.bxkj.common.base.BaseActivity
import com.bxkj.common.widget.YUIWebView
import com.bxkj.common.widget.YUIWebView.OnUrlLoadingListener
import com.bxkj.jrzp.support.feature.R

/**
 * Description: 文件预览
 * Author:Sanjin
 **/
class FilePreviewActivity : BaseActivity() {

    private val webContent by lazy { findViewById<YUIWebView>(R.id.web_view) }

    override fun getLayoutId(): Int = R.layout.activity_file_preview

    override fun initPage() {

        webContent.setSupportZoom(true)

        showLoading("简历加载中")

        webContent.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, newProgress: Int) {
                if (newProgress > 90) {
                    hiddenLoading()
                }
            }
        }

        intent.getStringExtra(EXTRA_FILE_URL)?.let { fileUrl ->
            val fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1)
            when {
                fileName.endsWith(".jpg") || fileName.endsWith(".png") || fileName.endsWith(".jpeg") -> {
                    webContent.loadRichText("<img style=\"display: block; margin: 0 auto;\" src=\"$fileUrl\"/>")
                }

                fileName.endsWith(".pdf") -> {
                    webContent.loadUrl("file:///android_asset/index.html?$fileUrl")
                }

                fileName.endsWith(".doc") || fileName.endsWith(".docx")-> {
                    webContent.loadUrl(
                        "http://124.160.65.83:8012/onlinePreview?url=${
                            Base64.encodeToString(
                                fileUrl.toByteArray(),
                                Base64.DEFAULT
                            )
                        }"
                    )
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        webContent.onPause()
        webContent.pauseTimers()
    }

    override fun onResume() {
        super.onResume()
        webContent.onResume()
        webContent.resumeTimers()
    }

    override fun onDestroy() {
        webContent.stopLoading()
        super.onDestroy()
    }

    companion object {

        private const val EXTRA_FILE_URL = "FILE_URL"
        private const val EXTRA_TITLE = "TITLE"

        @JvmOverloads
        fun newIntent(context: Context, fileUrl: String = "", title: String = ""): Intent {
            return Intent(context, FilePreviewActivity::class.java)
                .apply {
                    putExtra(EXTRA_FILE_URL, fileUrl)
                    putExtra(EXTRA_TITLE, title)
                }
        }
    }
}