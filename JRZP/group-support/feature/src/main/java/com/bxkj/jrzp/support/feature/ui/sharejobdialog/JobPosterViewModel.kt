package com.bxkj.jrzp.support.feature.ui.sharejobdialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.feature.api.FeatureRepository
import com.bxkj.jrzp.user.data.JobData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 **/
class JobPosterViewModel @Inject constructor(
    private val featureRepo: FeatureRepository
) : BaseViewModel() {

    val jobShareInfo = MutableLiveData<JobData>()

    fun start(jobId: Int,userId: Int) {
        viewModelScope.launch {
            showLoading()
            featureRepo.getJobShareInfo(userId, jobId)
                .handleResult({
                    jobShareInfo.value = it
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }
}