package com.bxkj.jrzp.support.feature.ui.infocheck

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import androidx.lifecycle.Observer
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.BaseDialogFragment.OnDismissListener
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.support.feature.R
import com.bxkj.jrzp.support.feature.data.CheckErrMsg
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem
import com.bxkj.jrzp.user.data.UserAuthStatusData
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.jrzp.user.ui.jobproveauth.JobProveAuthNavigation
import com.bxkj.jrzp.user.ui.selectauthtype.SelectAuthTypeNavigation
import com.bxkj.personal.ui.activity.userbasicinfo.FillInfoNext.NEXT_COMPLETE_JOB_PROVE
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoNavigation

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/6/21
 * @version: V1.0
 */
class CheckActivity : BaseDBActivity<Nothing, CheckViewModel>() {

  companion object {

    const val TO_COMPLETED_INFO_CODE = 1
    const val EXTRA_NEED_CHECK_ITEM = "NEED_CHECK_ITEM"
    const val EXTRA_SHOW_LOADING = "SHOW_LOADING"

    var mCheckInfoCallback: CheckInfoCallback? = null

    var failedCheckTipsNextStep: FailedCheckTipsNextStep? = null

    var useCount: Int = 0

    fun startCheck(
      context: Context,
      needCheckItemItem: ArrayList<InfoCheckItem>,
      checkInfoCallback: CheckInfoCallback?,
      failedCheckTipsNextStep: FailedCheckTipsNextStep? = null,
      showLoading: Boolean = true
    ) {
      mCheckInfoCallback = checkInfoCallback
      this.failedCheckTipsNextStep = failedCheckTipsNextStep
      useCount += 1

      val intent = Intent(context, CheckActivity::class.java)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      intent.putParcelableArrayListExtra(EXTRA_NEED_CHECK_ITEM, needCheckItemItem)
      intent.putExtra(EXTRA_SHOW_LOADING, showLoading)
      context.startActivity(intent)
    }
  }

  override fun getViewModelClass(): Class<CheckViewModel> = CheckViewModel::class.java

  override fun getLayoutId(): Int = CommonApiConstants.NO_ID

  override fun initPage(savedInstanceState: Bundle?) {
    statusBarManager.statusBarDarkFont(true, 0.4f).init()

    subscribeViewModelEvent()

    viewModel.startCheck(
      intent.getParcelableArrayListExtra(EXTRA_NEED_CHECK_ITEM),
      intent.getBooleanExtra(EXTRA_SHOW_LOADING, true)
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.checkErrorMsg.observe(this, Observer { errMsg ->
      if (errMsg.checkItem.onlyCheck) { //只检查
        mCheckInfoCallback?.onCheckFailed(errMsg)
        finish()
      } else {
        when (errMsg.checkItem.checkInfoTag) {
          CheckInfoTag.CHECK_COMPANY_INFO_COMPLETED -> {
            BusinessBasicInfoNavigation.navigate(true).start()
            finish()
          }

          CheckInfoTag.CHECK_COMPANY_CERTIFICATED -> {
            handleCertificateStatus(errMsg)
          }

          CheckInfoTag.CHECK_COMPANY_IS_ORG -> {
            handleCertificateStatus(errMsg, 1)
          }

          CheckInfoTag.CHECK_COMPANY_AUTH_NEW -> {
            if (errMsg.error.errCode == 30004) {
              SelectAuthTypeNavigation.navigate().start()
            } else {
              showToast(errMsg.error.errMsg)
            }
            failedCheckTipsNextStep?.invoke()
            finish()
          }

          CheckInfoTag.CHECK_PERSONAL_AUTH -> {
            handlePersonalFailedStatus(errMsg)
          }
        }
      }
    })

    viewModel.checkResultMsg.observe(this, Observer {
      if (it.checkItem?.checkInfoTag == CheckInfoTag.CHECK_COMPANY_AUTH_NEW) {
        val authStatusList: List<UserAuthStatusData> = CheckUtils.cast(it.result)
        if (authStatusList.isEmpty()) {
          failedCheckTipsNextStep?.invoke()
          SelectAuthTypeNavigation.navigate().start()
          finish()
        } else {
          val currentAuth = authStatusList[0]
          if (AuthenticationType.isPersonal(currentAuth.type)) {
            failedCheckTipsNextStep?.invoke()
            SelectAuthTypeNavigation.navigate().start()
            finish()
          } else {
            when {
              currentAuth.authProcessing() -> {
                TipsDialog()
                  .setTitle(getString(R.string.check_certificate))
                  .setContent(getString(R.string.check_certificating))
                  .setOnOverrideDismissListener(object : OnDismissListener {
                    override fun onDismiss(dialogInterface: DialogInterface?) {
                      failedCheckTipsNextStep?.invoke()
                      finish()
                    }
                  })
                  .setOnConfirmClickListener {
                    failedCheckTipsNextStep?.invoke()
                    finish()
                  }.show(supportFragmentManager)
              }

              currentAuth.authFailed() -> {
                ActionDialog.Builder()
                  .setTitle(getString(R.string.check_certificate))
                  .setCancelable(false)
                  .setContent(
                    getString(
                      R.string.check_certificate_error_format,
                      currentAuth.shResult
                    )
                  ).setOnConfirmClickListener {
                    if (currentAuth.type == AuthenticationType.SCHOOL) {
                      SchoolInfoNavigation.navigate(SchoolInfoNavigation.NEXT_STEP_AUTH)
                        .start()
                    } else if (currentAuth.type == AuthenticationType.SELF_EMPLOYED) {
                      IDCardValidationNavigation.create().start()
                    } else {
                      BusinessBasicInfoNavigation.navigate(true, currentAuth.type)
                        .start()
                    }
                    failedCheckTipsNextStep?.invoke()
                    finish()
                  }
                  .setOnCancelClickListener {
                    failedCheckTipsNextStep?.invoke()
                    finish()
                  }.build().show(supportFragmentManager)
              }

              currentAuth.authSuccess() -> {
                if (currentAuth.type == AuthenticationType.ENTERPRISE) {
                  val authErrorTips = it.checkItem.checkTipsContent
                  if (authErrorTips != CommonApiConstants.NO_ID) {
                    TipsDialog()
                      .setTitle(getString(R.string.check_certificate))
                      .setContent(getString(authErrorTips))
                      .setOnOverrideDismissListener(object : OnDismissListener {
                        override fun onDismiss(dialogInterface: DialogInterface?) {
                          finish()
                        }
                      })
                      .setOnConfirmClickListener {
                        finish()
                      }.show(supportFragmentManager)
                    return@Observer
                  }
                }
                mCheckInfoCallback?.onAllCheckSuccess(it)
                finish()
              }

              else -> {
                finish()
              }
            }
          }
        }
      }
    })

    viewModel.checkCompletedEvent.observe(this, Observer {
      mCheckInfoCallback?.onAllCheckSuccess(CheckResultMsg())
      finish()
    })

    viewModel.exitCheckCommand.observe(this, Observer {
      finish()
    })
  }

  private fun handlePersonalFailedStatus(errMsg: CheckErrMsg) {
    when (errMsg.error.errCode) {
      20001, 20004 -> {  //未认证
        showToast(getString(R.string.check_personal_auth_tips))
        UserBasicInfoNavigation.create(nextStep = NEXT_COMPLETE_JOB_PROVE).start()
        finish()
      }

      20002 -> {  //认证中
        showCertificatingTips(getString(R.string.check_personal_certificating))
      }

      20003 -> {  //审核失败
        showAuthFailedTips(errMsg.error.errMsg) {
          UserBasicInfoNavigation.create(nextStep = NEXT_COMPLETE_JOB_PROVE).start()
          finish()
        }
      }

      20005, 20008 -> {  //未进行在职证明
        showToast(getString(R.string.check_job_prove_auth_tips))
        JobProveAuthNavigation.navigate().start()
        finish()
      }

      20006 -> {  //在职证明审核中
        showCertificatingTips(getString(R.string.check_job_prove_certificating))
      }

      20007 -> {  //在职证明审核失败
        showAuthFailedTips(errMsg.error.errMsg) {
          JobProveAuthNavigation.navigate().start()
          finish()
        }
      }
    }
  }

  private fun showAuthFailedTips(failedMsg: String, confirmAction: () -> Unit) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.check_certificate))
      .setCancelable(false)
      .setContent(getString(R.string.check_certificate_error_format, failedMsg))
      .setOnConfirmClickListener {
        confirmAction.invoke()
      }
      .setOnCancelClickListener {
        finish()
      }.build().show(supportFragmentManager)
  }

  private fun handleCertificateStatus(it: CheckErrMsg, type: Int = 0) {
    when (it.error.errCode) {
      30005 -> {  //认证中
        showCertificatingTips(getString(R.string.check_certificating))
      }

      30006 -> {  //认证失败
        ActionDialog.Builder()
          .setCancelable(false)
          .setTitle(getString(R.string.check_certificate))
          .setContent(getString(R.string.check_certificate_error_format, it.error.errMsg))
          .setOnConfirmClickListener {
            BusinessBasicInfoNavigation.navigate(true).startForResult(
              this,
              TO_COMPLETED_INFO_CODE
            )
            finish()
            failedCheckTipsNextStep?.invoke()
          }
          .setOnCancelClickListener {
            finish()
            failedCheckTipsNextStep?.invoke()
          }.build().show(supportFragmentManager)
      }

      30009 -> {
        TipsDialog()
          .setTitle(getString(R.string.check_certificate))
          .setContent(getString(R.string.check_not_agency))
          .setOnOverrideDismissListener(object : OnDismissListener {
            override fun onDismiss(dialogInterface: DialogInterface?) {
              finish()
              failedCheckTipsNextStep?.invoke()
            }
          })
          .setOnConfirmClickListener {
            finish()
            failedCheckTipsNextStep?.invoke()
          }.show(supportFragmentManager)
      }

      else -> { //未认证
        ActionDialog.Builder()
          .setCancelable(false)
          .setTitle(getString(it.checkItem.checkTipsTitle))
          .setContent(getString(it.checkItem.checkTipsContent))
          .setOnConfirmClickListener {
            BusinessBasicInfoNavigation.navigate(true).startForResult(
              this,
              TO_COMPLETED_INFO_CODE
            )
            finish()
            failedCheckTipsNextStep?.invoke()
          }
          .setOnCancelClickListener {
            finish()
          }.build().show(supportFragmentManager)
      }
    }
  }

  private fun showCertificatingTips(tipsContent: String) {
    TipsDialog()
      .setTitle(getString(R.string.check_certificate))
      .setContent(tipsContent)
      .setOnOverrideDismissListener(object : OnDismissListener {
        override fun onDismiss(dialogInterface: DialogInterface?) {
          finish()
          failedCheckTipsNextStep?.invoke()
        }
      })
      .setOnConfirmClickListener {
        finish()
        failedCheckTipsNextStep?.invoke()
      }.show(supportFragmentManager)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_COMPLETED_INFO_CODE) {
      viewModel.reCheck()
    }
  }

  override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
    return if (keyCode == KeyEvent.KEYCODE_BACK) {
      true
    } else super.onKeyDown(keyCode, event)
  }

  override fun finish() {
    useCount -= 1
    if (useCount == 0) {
      mCheckInfoCallback = null
      failedCheckTipsNextStep = null
    }
    super.finish()
  }
}