package com.bxkj.jrzp.support.comment.ui

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.jrzp.support.comment.R
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.databinding.CommentLayoutCommentListBinding
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.donkingliang.consecutivescroller.IConsecutiveScroller
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import com.sanjindev.pagestatelayout.PageStateLayout
import com.scwang.smartrefresh.layout.SmartRefreshLayout

/**
 *
 * @author: sanjin
 * @date: 2022/9/2
 */
class CommentLayout @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defStyle: Int = 0
) : PageStateLayout(context, attributeSet, defStyle), IConsecutiveScroller {

    private var viewBinding: CommentLayoutCommentListBinding =
      CommentLayoutCommentListBinding.inflate(LayoutInflater.from(context), this, true)

  private var _loadPageIndex = 1
    private var _onLoadCommentListener: OnLoadCommentListener? = null
    private var _commentListAdapter: SimpleDBListAdapter<CommentItemData>? = null

    private var _containerRefreshLayout: SmartRefreshLayout? = null
    private var _onCommentOptionClickListener: OnCommentOptionClickListener? = null

    private var _commentPageSize = 0

    init {

      setupCommentListAdapter()
    }

    private fun setupCommentListAdapter() {
        _commentListAdapter =
            CommentListAdapter(context, R.layout.comment_recycler_item).apply {
                setOnItemClickListener(object :
                    SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        when (v.id) {
                            R.id.iv_avatar, R.id.tv_nick_name -> {
                                UserHomeNavigation.navigate(
                                    data[position].uid,
                                    AuthenticationType.QUERY_HIGHER_AUTH
                                ).start()
                            }
                            R.id.tv_like -> {
                                _onCommentOptionClickListener?.onLikeOrUnLike(data[position])
                            }
                            R.id.ll_reply -> {
                                _onCommentOptionClickListener?.onReplyListClick(
                                    position,
                                    data[position]
                                )
                            }
                            else -> {
                                _onCommentOptionClickListener?.onReplyClick(data[position])
                            }
                        }
                    }
                }, R.id.iv_avatar, R.id.tv_nick_name, R.id.tv_like, R.id.ll_reply)
            }
        viewBinding.recyclerComment.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = _commentListAdapter
        }
    }

    fun init(pageSize: Int) {
        _commentPageSize = pageSize
        refresh()
    }

    fun refresh() {
        _loadPageIndex = 1
        doLoadAction()
    }

    fun loadMore() {
        _loadPageIndex += 1
        doLoadAction()
    }

    fun noMoreData() {
        if (_loadPageIndex == 1) {
            show(EmptyPageState::class.java, object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                    pageState.apply {
                        setLayoutHeight(dip(160))
                        setImage(R.drawable.ic_no_content_small)
                        setContent("还没有评论，快来评论吧")
                        setOnLayoutClickListener {

                        }
                    }
                }
            })
            _containerRefreshLayout?.setEnableLoadMore(false)
        } else {
            loadIndexRollback()
        }
        _containerRefreshLayout?.setNoMoreData(true)
        finishContainerLoadState()
    }

    fun loadError() {
        if (_loadPageIndex == 1) {
            show(EmptyPageState::class.java, object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                    pageState.apply {
                        setLayoutHeight(dip(160))
                        setImage(R.drawable.ic_no_content_small)
                        setContent("还没有评论，快来评论吧")
                        setOnLayoutClickListener {

                        }
                    }
                }
            })
            _containerRefreshLayout?.setEnableLoadMore(false)
        } else {
            loadIndexRollback()
        }
        finishContainerLoadState()
    }

    fun setContainerRefreshLayout(smartRefreshLayout: SmartRefreshLayout) {
        _containerRefreshLayout = smartRefreshLayout
        smartRefreshLayout.setEnableLoadMore(false)
    }

    fun loadComments(comments: List<CommentItemData>) {
        _containerRefreshLayout?.setEnableLoadMore(true)
        _containerRefreshLayout?.setNoMoreData(false)
        hidden()
        _commentListAdapter?.let {
            if (_loadPageIndex == 1) {
                it.clear()
            }
            it.addAll(comments)
        }
        finishContainerLoadState()
    }

    fun addComment(comment: CommentItemData) {
        hidden()
        _commentListAdapter?.let {
            if (_commentPageSize > 0) {
                if (it.data.size >= _commentPageSize) {
                    //清楚最后一项，避免加载重复
                    it.removeAt(it.data.size - 1)
                }
            }
            it.add(0, comment)
        }
    }

    fun setOnLoadDataListener(onLoadDataListener: OnLoadCommentListener) {
        _onLoadCommentListener = onLoadDataListener
    }

    private fun finishContainerLoadState() {
        _containerRefreshLayout?.let {
            it.finishRefresh()
            it.finishLoadMore()
        }
    }

    private fun loadIndexRollback() {
        if (_loadPageIndex > 1) {
            _loadPageIndex -= 1
        }
    }

    private fun doLoadAction() {
        _onLoadCommentListener?.onLoadData(_loadPageIndex)
    }

    override fun getCurrentScrollerView(): View {
        return viewBinding.recyclerComment
    }

    override fun getScrolledViews(): List<View> {
        return listOf<View>(viewBinding.recyclerComment)
    }

    interface OnLoadCommentListener {
        fun onLoadData(index: Int)
    }

    fun setCommentOptionClickListener(onCommentOptionClickListener: OnCommentOptionClickListener) {
        _onCommentOptionClickListener = onCommentOptionClickListener
    }

    fun replaceComment(index: Int, comment: CommentItemData?) {
        _commentListAdapter?.replace(index, comment)
    }

    fun removeComment(index: Int) {
        _commentListAdapter?.removeAt(index)
    }

    interface OnCommentOptionClickListener {
        fun onLikeOrUnLike(comment: CommentItemData)

        fun onReplyListClick(commentPosition: Int, comment: CommentItemData)

        fun onReplyClick(parentComment: CommentItemData)
    }

}