package com.bxkj.jrzp.support.comment.ui.replay

import android.widget.TextView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.support.comment.BR
import com.bxkj.jrzp.support.comment.R
import com.bxkj.jrzp.support.comment.data.CommentItemData

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.commentreply
 * @Description:
 * <AUTHOR>
 * @date 2019/10/24
 * @version V1.0
 */
class ReplyCommentViewBinder constructor(private val mCommentReplyViewModel: CommentReplyViewModel) :
  DefaultViewBinder<CommentItemData>(R.layout.comment_recycler_reply_item, BR.data, true) {
  override fun onBindViewHolder(
    holder: <PERSON>ViewHolder,
    item: CommentItemData,
    position: Int
  ) {
    super.onBindViewHolder(holder, item, position)
    holder.findViewById<TextView>(R.id.tv_like).setOnClickListener {
      mCommentReplyViewModel.likeOrUnlikeComment(item)
    }
    holder.itemView.setOnClickListener {
      mCommentReplyViewModel.reply(item)
    }
  }

}