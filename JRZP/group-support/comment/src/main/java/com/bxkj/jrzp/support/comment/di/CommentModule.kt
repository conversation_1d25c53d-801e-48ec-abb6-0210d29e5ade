package com.bxkj.jrzp.support.comment.di

import com.bxkj.jrzp.support.comment.ui.BottomSheetCommentDialog
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/19
 * @version: V1.0
 */
@Module(includes = [CommentApiModule::class, CommentVMModule::class])
abstract class CommentModule {

  @ContributesAndroidInjector
  abstract fun bottomSheetCommentDialog(): BottomSheetCommentDialog
}