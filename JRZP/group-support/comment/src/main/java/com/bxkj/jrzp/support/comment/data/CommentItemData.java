package com.bxkj.jrzp.support.comment.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.jrzp.support.comment.BR;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/10/22
 */
public class CommentItemData extends BaseObservable implements Parcelable {

    private int pid;
    private int uid;
    private String content;
    private String date;
    private int zanCount;
    private int hfCount;
    private String nickName;
    private String photo;
    private String nickName2;
    private int isZan;
    private boolean isParent;
    private int replyUserId;
    private List<CommentItemData> nesPinglun2;

    protected CommentItemData(Parcel in) {
        pid = in.readInt();
        uid = in.readInt();
        content = in.readString();
        date = in.readString();
        zanCount = in.readInt();
        hfCount = in.readInt();
        nickName = in.readString();
        photo = in.readString();
        nickName2 = in.readString();
        isZan = in.readInt();
        replyUserId = in.readInt();
        nesPinglun2 = in.createTypedArrayList(CommentItemData.CREATOR);
    }

    public static CommentItemData fromUserCreate(int parentId, int userId, String content, String date, String nickName, String userHeader, int replyUserId, String replyNickName) {
        return new CommentItemData(parentId, userId, content, date, nickName, userHeader, replyUserId, replyNickName);
    }

    public CommentItemData(int pid, int uid, String content, String date, String nickName, String userHeader, int replyUserId, String replyNickName) {
        this.pid = pid;
        this.uid = uid;
        this.content = content;
        this.date = date;
        this.nickName = nickName;
        this.photo = userHeader;
        this.replyUserId = replyUserId;
        this.nickName2 = replyNickName;
    }

    public static final Creator<CommentItemData> CREATOR = new Creator<CommentItemData>() {
        @Override
        public CommentItemData createFromParcel(Parcel in) {
            return new CommentItemData(in);
        }

        @Override
        public CommentItemData[] newArray(int size) {
            return new CommentItemData[size];
        }
    };

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Bindable
    public int getZanCount() {
        return zanCount;
    }

    public void setZanCount(int zanCount) {
        this.zanCount = zanCount;
        notifyPropertyChanged(BR.zanCount);
    }

    @Bindable
    public int getHfCount() {
        return hfCount;
    }

    public void setHfCount(int hfCount) {
        this.hfCount = hfCount;
        notifyPropertyChanged(BR.hfCount);
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getNickName2() {
        return nickName2;
    }

    public void setNickName2(String nickName2) {
        this.nickName2 = nickName2;
    }

    @Bindable
    public int getIsZan() {
        return isZan;
    }

    public void setIsZan(int isZan) {
        this.isZan = isZan;
        notifyPropertyChanged(BR.isZan);
    }

    public boolean isParent() {
        return isParent;
    }

    public void setParent(boolean parent) {
        isParent = parent;
    }

    //增加点赞
    public void addLike() {
        setIsZan(1);
        setZanCount(zanCount + 1);
    }

    //取消点赞
    public void removeLike() {
        setIsZan(0);
        setZanCount(zanCount - 1);
    }

    //添加回复
    public void addReply(CommentItemData comment) {
        if (nesPinglun2 == null) nesPinglun2 = new ArrayList<>();
        if (nesPinglun2.size() >= 2) {
            nesPinglun2.remove(nesPinglun2.size() - 1);
        }
        nesPinglun2.add(0, comment);
        setHfCount(hfCount + 1);
        setNesPinglun2(nesPinglun2);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(pid);
        dest.writeInt(uid);
        dest.writeString(content);
        dest.writeString(date);
        dest.writeInt(zanCount);
        dest.writeInt(hfCount);
        dest.writeString(nickName);
        dest.writeString(photo);
        dest.writeString(nickName2);
        dest.writeInt(isZan);
        dest.writeInt(replyUserId);
        dest.writeTypedList(nesPinglun2);
    }

    public static class NoCommentData {
        public static NoCommentData getInstance() {
            return new NoCommentData();
        }
    }

    @Bindable
    public List<CommentItemData> getNesPinglun2() {
        return nesPinglun2;
    }

    public void setNesPinglun2(List<CommentItemData> nesPinglun2) {
        if (nesPinglun2 != null) {
            this.nesPinglun2 = new ArrayList<>(nesPinglun2);
            notifyPropertyChanged(BR.nesPinglun2);
        }
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) return true;
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof CommentItemData)) {
            return false;
        }
        CommentItemData comment = (CommentItemData) obj;
        return this.uid == comment.uid && CheckUtils.equalsStr(this.content, comment.content) && CheckUtils.equalsStr(this.date, comment.date);
    }

    public static class ItemDiffCallBack extends DiffUtil.ItemCallback<CommentItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull CommentItemData oldItem, @NonNull
                CommentItemData newItem) {
            return oldItem.equals(newItem);
        }

        @Override
        public boolean areContentsTheSame(@NonNull CommentItemData oldItem, @NonNull
                CommentItemData newItem) {
            return oldItem.content.equals(newItem.content) && oldItem.date.equals(newItem.date);
        }
    }
}
