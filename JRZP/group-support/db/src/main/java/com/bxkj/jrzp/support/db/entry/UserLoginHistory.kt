package com.bxkj.jrzp.support.db.entry

import androidx.recyclerview.widget.DiffUtil
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/19
 * @version: V1.0
 */
@Entity(tableName = "user_login_history")
data class UserLoginHistory(
  @PrimaryKey
  @ColumnInfo(name = "user_id")
  var userID: Int,
  var token: String? = "",
  var avatar: String? = "",
  var nickname: String? = "",
  var identity: Int
) {
  companion object {

    @JvmStatic
    fun get(userID: Int, identity: Int): UserLoginHistory {
      return UserLoginHistory(userID, identity = identity)
    }
  }

  class DiffCallback : DiffUtil.ItemCallback<UserLoginHistory>() {
    override fun areItemsTheSame(oldItem: UserLoginHistory, newItem: UserLoginHistory): Boolean {
      return oldItem.userID == newItem.userID
    }

    override fun areContentsTheSame(oldItem: UserLoginHistory, newItem: UserLoginHistory): Boolean {
      return oldItem.userID == newItem.userID && oldItem.avatar == newItem.avatar && oldItem.nickname == newItem.nickname
    }

  }
}
