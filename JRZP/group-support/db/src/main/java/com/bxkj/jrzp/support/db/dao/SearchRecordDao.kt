package com.bxkj.jrzp.support.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.bxkj.jrzp.support.db.entry.SearchRecord
import io.reactivex.Single

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data.db
 * @Description:
 * @date 2019/11/18
 */
@Dao
interface SearchRecordDao {
  @Query("SELECT * FROM table_search_record WHERE type=:type")
  fun getAllByType(type: Int): Single<List<SearchRecord>>

  @Query("SELECT * FROM table_search_record WHERE type=:type")
  suspend fun getCoroutineAllByType(
    type: Int,
  ): List<SearchRecord>?

  @Insert(onConflict = OnConflictStrategy.REPLACE)
  fun inset(searchRecord: SearchRecord)

  @Query("SELECT * FROM table_search_record WHERE type=:type AND keyword=:keyword")
  fun checkHasItem(type: Int, keyword: String?): SearchRecord?

  @Query("DELETE FROM table_search_record WHERE id=:id ")
  fun deleteItemById(id: Int): Int

  @Query("DELETE FROM table_search_record WHERE type=:type")
  fun clearByType(type: Int): Int
}