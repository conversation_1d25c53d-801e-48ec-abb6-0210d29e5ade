package com.bxkj.jrzp.support.db.entry;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.math.BigDecimal;

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/3
 * @version: V1.0
 */
@Entity(tableName = "table_user_action_record")
public class UserActionRecord {

    @PrimaryKey(autoGenerate = true)
    private int id;
    private int type;
    private String key;
    private float value;

    public static UserActionRecord fromType(int type) {
        return new UserActionRecord(type);
    }

    public static UserActionRecord fromKey(String key) {
        return new UserActionRecord(key);
    }

    private UserActionRecord(int type) {
        this.type = type;
        this.value = 0.1f;
    }

    private UserActionRecord(String key) {
        this.key = key;
        this.value = 0.1f;
    }

    public UserActionRecord(int type, String key, float value) {
        this.type = type;
        this.key = key;
        this.value = value;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public float getValue() {
        return new BigDecimal(value).setScale(1, BigDecimal.ROUND_DOWN).floatValue();
    }

    public void setValue(float value) {
        this.value = value;
    }

    public void addWeight() {
        value = value + 0.1f;
    }
}
