package com.bxkj.jrzp.support.db.entry;

import androidx.room.Entity;
import androidx.room.PrimaryKey;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data.db
 * @Description: 附件简历上传记录
 * @TODO: TODO
 * @date 2018/12/3
 */
@Entity(tableName = "upload_attached_resume_record_table")
public class UploadAttachedResumeRecord {
    @PrimaryKey
    private int resumeId;
    private String attachedResumeWebUrl;
    private String attachedResumeFilePath;

    public UploadAttachedResumeRecord(int resumeId, String attachedResumeWebUrl, String attachedResumeFilePath) {
        this.resumeId = resumeId;
        this.attachedResumeWebUrl = attachedResumeWebUrl;
        this.attachedResumeFilePath = attachedResumeFilePath;
    }

    public int getResumeId() {
        return resumeId;
    }

    public void setResumeId(int resumeId) {
        this.resumeId = resumeId;
    }

    public String getAttachedResumeWebUrl() {
        return attachedResumeWebUrl;
    }

    public void setAttachedResumeWebUrl(String attachedResumeWebUrl) {
        this.attachedResumeWebUrl = attachedResumeWebUrl;
    }

    public String getAttachedResumeFilePath() {
        return attachedResumeFilePath;
    }

    public void setAttachedResumeFilePath(String attachedResumeFilePath) {
        this.attachedResumeFilePath = attachedResumeFilePath;
    }

    @Override
    public String toString() {
        return "UploadAttachedResumeRecord{" +
            "resumeId=" + resumeId +
            ", attachedResumeWebUrl='" + attachedResumeWebUrl + '\'' +
            ", attachedResumeFilePath='" + attachedResumeFilePath + '\'' +
            '}';
    }
}
