<?xml version="1.0" encoding="utf-8"?>
<resources>

  <style name="YUIAppBaseTheme" parent="Theme.MaterialComponents.Light.NoActionBar" />

  <style name="YUI" parent="YUIAppBaseTheme">

    <!--============== Global ==============-->
    <item name="yui_global_normal_text_color">@color/yui_333333</item>
    <item name="yui_global_hint_text_color">@color/yui_999999</item>

    <!--============== YUICommonListItemView ==============-->
    <!--  Size  -->
    <item name="yui_list_item_first_item_margin_left">12dp</item>
    <!-- 标题 -->
    <item name="yui_list_item_title_text_size">15sp</item>
    <item name="yui_list_item_title_text_color">?attr/yui_global_normal_text_color</item>
    <!-- 内容 -->
    <item name="yui_list_item_content_hint_color">?attr/yui_global_hint_text_color</item>
    <item name="yui_list_item_content_text_color">?attr/yui_global_normal_text_color</item>
    <item name="yui_list_item_content_text_size">15sp</item>
    <!-- Accessory -->
    <item name="yui_list_item_accessory_margin_right">12dp</item>

    <item name="YUICommonListItemViewStyle">@style/YUI.CommonListItemView</item>
  </style>

  <style name="YUI.ImageViewerActivity">
    <item name="colorPrimaryDark">@color/yui_black</item>
    <item name="android:windowBackground">@color/yui_tran</item>
    <item name="android:windowIsTranslucent">true</item>
  </style>

</resources>