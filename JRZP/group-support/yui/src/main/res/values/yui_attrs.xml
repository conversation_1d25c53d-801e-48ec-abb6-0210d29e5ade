<?xml version="1.0" encoding="utf-8"?>
<resources>

  <attr name="postSplashScreenTheme" format="reference" />
  <attr name="splashScreenIconSize" format="dimension" />
  <attr name="windowSplashScreenAnimatedIcon" format="reference" />
  <attr name="windowSplashScreenAnimationDuration" format="integer" />
  <attr name="windowSplashScreenBackground" format="color" />
  <attr name="windowSplashScreenIconBackgroundColor" format="color" />
  <dimen name="splashscreen_icon_mask_size_no_background">410dp</dimen>
  <dimen name="splashscreen_icon_mask_size_with_background">342dp</dimen>
  <dimen name="splashscreen_icon_mask_stroke_no_background">109dp</dimen>
  <dimen name="splashscreen_icon_mask_stroke_with_background">92dp</dimen>
  <dimen name="splashscreen_icon_size">?splashScreenIconSize</dimen>
  <dimen name="splashscreen_icon_size_no_background">288dp</dimen>
  <dimen name="splashscreen_icon_size_with_background">240dp</dimen>
  <integer name="default_icon_animation_duration">10000</integer>

  <style name="Base.Theme.SplashScreen" parent="Base.v21.Theme.SplashScreen" />

  <style name="Base.Theme.SplashScreen.DayNight" parent="Base.Theme.SplashScreen.Light" />

  <style name="Base.Theme.SplashScreen.Light" parent="Base.v21.Theme.SplashScreen.Light" />

  <style name="Base.v21.Theme.SplashScreen" parent="android:Theme.DeviceDefault.NoActionBar"></style>

  <style name="Base.v21.Theme.SplashScreen.Light" parent="android:Theme.DeviceDefault.Light.NoActionBar"></style>

  <style name="Theme.SplashScreen" parent="Theme.SplashScreen.Common">
    <item name="postSplashScreenTheme">?android:attr/theme</item>
    <item name="windowSplashScreenAnimationDuration">
      @integer/default_icon_animation_duration
    </item>
    <item name="windowSplashScreenBackground">?android:colorBackground</item>
    <item name="windowSplashScreenAnimatedIcon">@android:drawable/sym_def_app_icon</item>

  </style>

  <style name="Theme.SplashScreen.Common" parent="Base.Theme.SplashScreen.DayNight">
    <item name="android:windowActionBar">false</item>
    <item name="android:windowNoTitle">true</item>
    <item name="android:windowBackground">
      @drawable/compat_splash_screen_no_icon_background
    </item>
    <item name="android:opacity">opaque</item>
    <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    <item name="android:fitsSystemWindows">false</item>
    <item name="android:statusBarColor">@android:color/transparent</item>
    <item name="android:navigationBarColor">@android:color/transparent</item>
    <item name="splashScreenIconSize">@dimen/splashscreen_icon_size_no_background</item>
  </style>

  <style name="Theme.SplashScreen.IconBackground" parent="Theme.SplashScreen">
    <item name="android:windowBackground">@drawable/compat_splash_screen</item>
    <item name="splashScreenIconSize">@dimen/splashscreen_icon_size_with_background</item>
  </style>

  <!--============== Global ==============-->
  <attr name="yui_global_normal_text_color" format="color" />
  <attr name="yui_global_hint_text_color" format="color" />

  <!--============== YUICommonListItemView ==============-->
  <!-- Icon -->
  <attr name="yui_list_item_first_item_margin_left" format="dimension" />
  <!-- 标题 -->
  <attr name="yui_list_item_title_text_size" format="dimension" />
  <attr name="yui_list_item_title_text_color" format="color" />
  <!-- 内容 -->
  <attr name="yui_list_item_content_hint_color" format="color" />
  <attr name="yui_list_item_content_text_color" format="color" />
  <attr name="yui_list_item_content_text_size" format="dimension" />
  <!-- Accessory -->
  <attr name="yui_list_item_accessory_chevron_icon" format="reference" />
  <attr name="yui_list_item_accessory_margin_right" format="dimension" />

  <declare-styleable name="YUICommonListItemView">
    <!--  图标  -->
    <attr name="yui_icon" format="reference" />
    <!--  标题  -->
    <attr name="yui_title" format="string" />
    <!-- 内容 -->
    <attr name="android:inputType" />
    <attr name="android:digits" />
    <attr name="yui_content" format="string" />
    <attr name="yui_content_hint" format="string" />
    <attr name="yui_content_lines" format="integer" />
    <!-- 内容类型 -->
    <attr name="yui_content_type" format="enum">
      <enum name="none" value="0" />
      <enum name="text" value="1" />
      <enum name="edit" value="2" />
    </attr>
    <!--  附加  -->
    <attr name="yui_accessory_type" format="enum">
      <enum name="none" value="0" />
      <enum name="chevron" value="1" />
      <enum name="switcher" value="2" />
      <enum name="custom" value="3" />
    </attr>
    <!--  边框  -->
    <attr name="yui_border_margin_left" format="dimension" />
    <attr name="yui_border_margin_right" format="dimension" />
    <attr name="yui_border_bg" format="reference" />
  </declare-styleable>

  <!-- 默认样式 -->
  <attr name="YUICommonListItemViewStyle" format="reference" />

</resources>