package com.sanjindev.mui.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.transition.TransitionInflater
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.SharedElementCallback
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.bumptech.glide.Glide
import com.sanjindev.mui.R
import com.sanjindev.mui.ui.ImageViewerActivity.ImageGalleryPagerAdapter.ItemViewHolder
import com.sanjindev.mui.weiget.DragCloseLayout
import com.sanjindev.mui.weiget.DragCloseLayout.OnDragEventListener

/**
 *
 * @author: sanjin
 * @date: 2021/7/21
 */

private const val TAG = "ImageViewerActivity"

class ImageViewerActivity : AppCompatActivity() {

    companion object {

        var currentPosition = 0

        private const val EXTRA_IMG_DATA = "IMG_DATA"
        private const val EXTRA_POSITION = "POSITION"

        fun newIntent(context: Context, imgData: ArrayList<String>, position: Int): Intent {
            return Intent(context, ImageViewerActivity::class.java)
                .apply {
                    putExtra(EXTRA_IMG_DATA, imgData)
                    putExtra(EXTRA_POSITION, position)
                }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.yui_activity_image_viewer)

        setupImageViewPager()

        prepareSharedElementTransition()

        findViewById<ImageView>(R.id.iv_back).setOnClickListener {
            if (Build.VERSION.SDK_INT > 21) {
                finishAfterTransition()
            } else {
                finish()
            }
        }
    }

    private fun setupImageViewPager() {
        getExtraImageData()?.let {
            val vpGallery = findViewById<ViewPager2>(R.id.vp_image_viewer)
            vpGallery.adapter = ImageGalleryPagerAdapter(this, it)
            vpGallery.setCurrentItem(getExtraImagePosition(), false)
            vpGallery.registerOnPageChangeCallback(object : OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    currentPosition = position
                }
            })
        }
    }

    private fun getExtraImageData(): List<String>? {
        return intent.getStringArrayListExtra(EXTRA_IMG_DATA)
    }

    private fun prepareSharedElementTransition() {
        val transition =
            TransitionInflater.from(this)
                .inflateTransition(R.transition.image_shared_element_transition)

        if (Build.VERSION.SDK_INT > 21) {
            window.sharedElementEnterTransition = transition
        }

        setEnterSharedElementCallback(object : SharedElementCallback() {
            override fun onMapSharedElements(
                names: MutableList<String>?,
                sharedElements: MutableMap<String, View>?,
            ) {
                Log.d(TAG, "onMapSharedElements: $names")
                (findViewById<ViewPager2>(R.id.vp_image_viewer).getChildAt(0) as RecyclerView).findViewHolderForAdapterPosition(
                    getExtraImagePosition()
                )?.itemView?.let {
                    sharedElements?.put(names?.get(0) ?: "", it.findViewById(R.id.iv_img))
                }
            }
        })
    }

    private fun getExtraImagePosition(): Int {
        return intent.getIntExtra(EXTRA_POSITION, 0)
    }

    class ImageGalleryPagerAdapter constructor(
        val activity: Activity,
        private val urls: List<String>,
    ) :
        RecyclerView.Adapter<ItemViewHolder>() {

        class ItemViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var ivItem: ImageView = itemView.findViewById(R.id.iv_img)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
            return ItemViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.yui_recycler_image_viewer_item, parent, false)
            )
        }

        override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
            if (Build.VERSION.SDK_INT > 21) {
                holder.ivItem.transitionName = urls[position]
            }
            Glide.with(holder.ivItem).load(urls[position]).into(holder.ivItem)
            (holder.itemView as DragCloseLayout).setOnDragEventListener(object :
                OnDragEventListener {
                override fun onClose() {
                    if (Build.VERSION.SDK_INT > 21) {
                        activity.finishAfterTransition()
                    }
                }
            })
        }

        override fun getItemCount(): Int {
            return urls.size
        }
    }
}