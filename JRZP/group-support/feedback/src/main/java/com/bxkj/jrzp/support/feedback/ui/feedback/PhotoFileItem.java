package com.bxkj.jrzp.support.feedback.ui.feedback;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.data.GalleryItem;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.releasenews
 * @Description:
 * @date 2019/11/22
 */
public class PhotoFileItem implements GalleryItem, Serializable {

  private int fileType;
  private String filePath;
  private String fileUrl;
  private boolean isVideo;
  private boolean isAddItem;

  public static PhotoFileItem fromPathAndUrl(String path, String url, boolean isVideo) {
    return new PhotoFileItem(path, url, false, isVideo);
  }

  public static PhotoFileItem getAddItem() {
    return new PhotoFileItem(true);
  }

  private PhotoFileItem(boolean isAddItem) {
    this.isAddItem = isAddItem;
  }

  public PhotoFileItem(String filePath, String url, boolean isAddItem, boolean isVideo) {
    this.filePath = filePath;
    this.fileUrl = url;
    this.isAddItem = isAddItem;
    this.isVideo = isVideo;
  }

  public String getFilePath() {
    return filePath;
  }

  public void setFilePath(String filePath) {
    this.filePath = filePath;
  }

  public boolean isAddItem() {
    return isAddItem;
  }

  public void setAddItem(boolean addItem) {
    isAddItem = addItem;
  }

  public int getFileType() {
    return fileType;
  }

  public void setFileType(int fileType) {
    this.fileType = fileType;
  }

  public String getFileUrl() {
    return fileUrl;
  }

  public void setFileUrl(String fileUrl) {
    this.fileUrl = fileUrl;
  }

  public boolean isVideo() {
    return isVideo;
  }

  public void setVideo(boolean video) {
    isVideo = video;
  }

  private static final String TAG = "MomentFileItem";

  @NotNull @Override public String getItemUrl() {
    return fileUrl;
  }

  public static class ItemDiffCallBack extends DiffUtil.ItemCallback<PhotoFileItem> {

    @Override
    public boolean areItemsTheSame(@NonNull PhotoFileItem oldItem,
        @NonNull PhotoFileItem newItem) {
      return false;
    }

    @Override
    public boolean areContentsTheSame(@NonNull PhotoFileItem oldItem,
        @NonNull PhotoFileItem newItem) {
      return oldItem.fileUrl != null && newItem.fileUrl != null && oldItem.fileUrl.equals(
          newItem.fileUrl);
    }
  }
}
