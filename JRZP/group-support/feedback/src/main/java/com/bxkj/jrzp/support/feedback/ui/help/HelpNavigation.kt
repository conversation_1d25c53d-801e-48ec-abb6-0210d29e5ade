package com.bxkj.jrzp.support.feedback.ui.help

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.support.feedback.FeedbackConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/21
 * @version: V1.0
 */
class HelpNavigation {
  companion object{
    const val PATH="${FeedbackConstants.DIRECTORY}/help"

    fun navigate():RouterNavigator{
      return Router.getInstance().to(PATH)
    }
  }
}