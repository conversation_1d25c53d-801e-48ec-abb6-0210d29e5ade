package com.bxkj.jrzp.support.feedback.ui.conversation

import android.content.Context
import android.view.View
import android.widget.TextView
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.TimeUtils
import com.bxkj.jrzp.support.feedback.R
import com.bxkj.jrzp.support.feedback.data.FeedbackConversationMsgItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/21
 * @version: V1.0
 */
class FeedbackConversationListAdapter constructor(context: Context) : MultiTypeAdapter(context) {

  override fun convert(holder: SuperViewHolder, viewType: Int, item: Any?, position: Int) {
    val timeTag = holder.findViewById<TextView>(R.id.tv_time_tag)
    item?.let {
      if (item is FeedbackConversationMsgItemData) {
        checkNeedShowTimeTag(timeTag, position, item)
      }
    }
    super.convert(holder, viewType, item, position)
  }

  private fun checkNeedShowTimeTag(
    timeTag: TextView?,
    position: Int,
    item: FeedbackConversationMsgItemData
  ) {
    if (timeTag != null) {
      if (position == 0) {
        timeTag.visibility = View.VISIBLE
        timeTag.text = item.date
      } else {
        val previousItem = data[position - 1]
        if (previousItem is FeedbackConversationMsgItemData) {
          previousItem.let {
            if (TimeUtils.getSecondDifference(
                TimeUtils.string2Date(previousItem.date, "yyyy-MM-dd HH:mm"),
                TimeUtils.string2Date(item.date, "yyyy-MM-dd HH:mm")
              ) > 60
            ) {
              timeTag.visibility = View.VISIBLE
              timeTag.text = item.date
            } else {
              timeTag.visibility = View.GONE
            }
          }
        }
      }
    }
  }
}