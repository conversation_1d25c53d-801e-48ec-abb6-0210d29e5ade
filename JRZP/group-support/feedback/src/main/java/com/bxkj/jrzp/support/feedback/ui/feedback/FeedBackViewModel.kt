package com.bxkj.jrzp.support.feedback.ui.feedback

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.jrzp.support.feedback.repository.FeedbackRepository
import com.bxkj.support.upload.data.FileItem
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.entity.LocalMedia
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.feedback
 * @Description:
 * @TODO: TODO
 * @date 2019/4/4
 */
class FeedBackViewModel @Inject constructor(
    private val mFeedbackRepository: FeedbackRepository,
    private val mUploadRepository: UploadRepository
) : BaseViewModel() {

    companion object {
        const val MAX_SELECT_COUNT = 3
    }

    val feedbackContent = MutableLiveData<String>()
    val submitSuccess = LiveEvent<Void>()

    private var _photoList = ArrayList<FileItem>()

    private var _photoAdapter: SimpleDBListAdapter<FileItem>? = null

    private var mFeedbackType = 1

    fun submitFeedback() {
        viewModelScope.launch {
            showLoading()
            mFeedbackRepository.submitFeedback(
                getSelfUserID(),
                mFeedbackType,
                CheckUtils.checkNullReturnString(feedbackContent.value, ""),
                getFeedbackPics()
            ).handleResult({
                submitSuccess.call()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    private fun getFeedbackPics(): String {
        val pStringBuilder = StringBuilder()
        _photoList.forEach { pic ->
            pStringBuilder.append(pic.fileUrl).append(",")
        }
        return if (pStringBuilder.isEmpty()) {
            ""
        } else {
            pStringBuilder.substring(0, pStringBuilder.length - 1)
        }
    }

    fun setFeedbackType(type: Int) {
        mFeedbackType = type
    }

    fun getPhotoAdapter(): SimpleDBListAdapter<FileItem>? {
        return _photoAdapter
    }

    fun setPhotoAdapter(adapter: SimpleDBListAdapter<FileItem>) {
        adapter.add(FileItem.addItem)
        _photoAdapter = adapter
    }

    fun deletePhoto(index: Int) {
        _photoList.removeAt(index)
        _photoAdapter?.removeAt(index)
        if (_photoList.size == MAX_SELECT_COUNT - 1) {
            _photoAdapter?.add(FileItem.addItem)
        }
    }

    fun uploadFileList(list: List<LocalMedia>) {
        showLoading()
        if (!CheckUtils.isNullOrEmpty(list)) {
            val tempList = ArrayList<FileItem>()
            val currentUploadIndex = 0
            uploadFile(list, tempList, currentUploadIndex)
        }
    }

    /**
     * 上传文件
     */
    private fun uploadFile(
        list: List<LocalMedia>,
        tempList: ArrayList<FileItem>,
        currentUploadIndex: Int
    ) {
        //当前文件
        val file = list[currentUploadIndex]
        //是否是视频
        val fileIsVideo = ZPFileUtils.isVideoFile(file.mimeType)
        //最终使用的路径
        val finalPath = ZPFileUtils.getPictureSelectorPath(file)
        viewModelScope.launch {
            mUploadRepository.uploadFile(
                finalPath, UploadFileRequestParams.fromFileType(
                    getSelfUserID(),
                    if (fileIsVideo) UploadFileRequestParams.TYPE_VIDEO else UploadFileRequestParams.TYPE_IMG
                )
            ).handleResult({
                it?.let {
                    tempList.add(FileItem.fromPathAndUrl(finalPath, it, fileIsVideo))
                    if (currentUploadIndex < list.size - 1) {   //未上传完，继续上传下一个
                        val nextIndex = currentUploadIndex + 1
                        uploadFile(list, tempList, nextIndex)
                    } else {
                        uploadSuccess(tempList)
                    }
                }
            }, {
                showToast(it.errMsg)
                hideLoading()
            })
        }
    }

    private fun uploadSuccess(tempList: List<FileItem>) {
        hideLoading()
        _photoList.addAll(tempList)
        _photoAdapter?.let {
            it.addAll(it.data.size - 1, tempList)
            if (getAvailableImgNum() <= 0) {
                it.removeAt(it.data.lastIndex)
            }
        }
    }

    fun getAvailableImgNum(): Int {
        return MAX_SELECT_COUNT - _photoList.size
    }
}