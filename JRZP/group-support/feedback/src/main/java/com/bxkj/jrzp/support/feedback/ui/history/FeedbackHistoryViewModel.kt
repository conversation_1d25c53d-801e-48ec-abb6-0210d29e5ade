package com.bxkj.jrzp.support.feedback.ui.history

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.feedback.repository.FeedbackRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/21
 * @version: V1.0
 */
class FeedbackHistoryViewModel @Inject constructor(private val mFeedbackRepository: FeedbackRepository) :
  BaseViewModel() {

  val listViewModel = RefreshListViewModel()

  init {
    setupFeedbackHistoryListViewModel()
  }

  private fun setupFeedbackHistoryListViewModel() {
    listViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mFeedbackRepository.getFeedbackHistoryList(
          getSelfUserID(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          listViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            listViewModel.noMoreData()
          } else {
            listViewModel.loadError()
          }
        })
      }
    }
  }

  fun start() {
    listViewModel.refresh()
  }

}