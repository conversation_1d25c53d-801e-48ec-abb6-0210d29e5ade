package com.bxkj.jrzp.support.feedback.di

import com.bxkj.common.di.scope.PerActivity
import com.bxkj.jrzp.support.feedback.ui.conversation.FeedbackConversationActivity
import com.bxkj.jrzp.support.feedback.ui.feedback.NewFeedbackActivity
import com.bxkj.jrzp.support.feedback.ui.help.HelpActivity
import com.bxkj.jrzp.support.feedback.ui.history.FeedbackHistoryActivity
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/20
 * @version: V1.0
 */
@Module(includes = [FeedbackApiModule::class, FeedbackVMModule::class])
abstract class FeedbackModule {

  @PerActivity
  @ContributesAndroidInjector
  abstract fun feedbackActivity(): NewFeedbackActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun feedbackHistoryActivity(): FeedbackHistoryActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun feedbackConversationActivity(): FeedbackConversationActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun helpActivity(): HelpActivity

}