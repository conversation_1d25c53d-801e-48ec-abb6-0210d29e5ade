<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.feedback.data.FeedbackHistoryItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:padding="@dimen/dp_16">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.14sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{@string/feedback_history_title_format(data.typeName,data.content)}"
      app:layout_constraintEnd_toStartOf="@id/tv_date"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.12sp.666666"
      android:text="@{data.edate}"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_state"
      style="@style/Text.12sp.FF7647"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.feedbackHandleState}"
      android:textColor="@{data.hasReply()?@color/common_666666:@color/cl_ff7405}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      style="@style/Text.10sp.FFFFFF"
      android:background="@drawable/bg_ff7405_radius_4"
      android:gravity="center"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_2"
      android:text="@string/feedback_history_new_msg_tips"
      android:visibility="@{data.hasNewMsg()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_date" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>