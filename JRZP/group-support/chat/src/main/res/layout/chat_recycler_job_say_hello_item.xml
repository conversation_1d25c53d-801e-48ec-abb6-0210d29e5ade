<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_14"
        android:paddingStart="@dimen/dp_14"
        android:paddingTop="@dimen/dp_8">

        <TextView
            android:id="@+id/tv_job_name"
            style="@style/Text.12sp.888888"
            android:layout_width="match_parent"
            android:drawableEnd="@drawable/ic_edit_info_item"
            android:gravity="center_vertical"
            android:text="@{data.relName}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/Text.14sp.333333"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@{data.content}" />

    </LinearLayout>
</layout>