<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="data"
            type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
    </data>


    <LinearLayout
        style="@style/match_wrap"
        android:layout_marginStart="@dimen/common_dp_60"
        android:layout_marginEnd="@dimen/common_dp_60"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/chat_bg_feature_item"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:padding="@dimen/dp_12">

        <TextView
            android:id="@+id/tv_content"
            style="@style/Text.16sp.333333.Bold"
            android:layout_width="match_parent"
            android:drawableStart="@{data.agreeExchangeWXMsg?@drawable/chat_ic_msg_exchange_wechat:@drawable/chat_ic_msg_exchange_phone}"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_wechat_copy"
            style="@style/Text.14sp.333333"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f4f4f4_radius_10"
            android:gravity="center"
            android:paddingTop="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_8"
            android:text="@string/chat_wechat_copy"
            android:textColor="@color/chat_button_text_selector"
            android:visibility="@{data.agreeExchangeWXMsg?View.VISIBLE:View.GONE}" />

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center"
            android:visibility="@{data.agreeExchangePhoneMsg?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tv_send_sms"
                style="@style/Text.14sp.333333"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_10"
                android:gravity="center"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:text="@string/chat_phone_send_sms"
                android:textColor="@color/chat_button_text_selector" />

            <TextView
                android:id="@+id/tv_call"
                style="@style/Text.14sp.333333"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_10"
                android:gravity="center"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:text="@string/chat_phone_call"
                android:textColor="@color/chat_button_text_selector" />

            <TextView
                android:id="@+id/tv_phone_copy"
                style="@style/Text.14sp.333333"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:background="@drawable/bg_f4f4f4_radius_10"
                android:gravity="center"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:text="@string/chat_phone_copy"
                android:textColor="@color/chat_button_text_selector" />
        </LinearLayout>
    </LinearLayout>
</layout>