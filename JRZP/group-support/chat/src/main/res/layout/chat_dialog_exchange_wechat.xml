<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.chat.widget.exchangewechatdialog.ExchangeWechatViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ffffff_radius_10"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_22">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333.Bold"
            android:layout_width="match_parent"
            android:layout_marginBottom="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:text="@{viewModel.showSetupWechatLayout?@string/chat_exchange_wechat_setup_title:@string/chat_exchange_wechat_tips_title}" />

        <EditText
            style="@style/Text.12sp.333333"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:background="@drawable/bg_f4f4f4_radius_4"
            android:gravity="center_vertical"
            android:paddingEnd="@dimen/dp_8"
            android:text="@={viewModel.wechatNumber}"
            android:paddingStart="@dimen/dp_8"
            android:visibility="@{viewModel.showSetupWechatLayout?View.VISIBLE:View.GONE}" />

        <TextView
            style="@style/Text.14sp.333333"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:text="@string/chat_exchange_wechat_tips_content"
            android:visibility="@{viewModel.showSetupWechatLayout?View.GONE:View.VISIBLE}" />

        <View
            style="@style/Line.Horizontal.Light"
            android:layout_marginTop="@dimen/dp_22" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Text.16sp.888888"
                android:layout_width="@dimen/dp_0"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="@{onClickListener}"
                android:paddingBottom="@dimen/dp_12"
                android:paddingTop="@dimen/dp_12"
                android:text="@string/common_cancel" />

            <View style="@style/Line.Vertical.Light" />

            <TextView
                android:id="@+id/tv_confirm"
                style="@style/Text.16sp.ff7647"
                android:layout_width="@dimen/dp_0"
                android:layout_weight="1"
                android:width="@dimen/dp_0"
                android:gravity="center"
                android:onClick="@{()->viewModel.confirm()}"
                android:paddingBottom="@dimen/dp_12"
                android:paddingTop="@dimen/dp_12"
                android:text="@string/common_confirm" />

        </LinearLayout>
    </LinearLayout>
</layout>