<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.chat.ui.chatreplysetting.ChatReplySettingViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            app:title="@string/chat_reply_setting_page_title" />

        <include
            android:id="@+id/include_reply_list"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.replyListViewModel}" />

        <TextView
            android:id="@+id/tv_add"
            style="@style/Button.Basic.Round"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_18"
            android:onClick="@{onClickListener}"
            android:text="@string/chat_reply_add_text" />
    </LinearLayout>
</layout>