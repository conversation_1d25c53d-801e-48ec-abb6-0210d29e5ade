<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.chat.ui.chatsayhellosetting.GreetingSettingViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/include_say_hello_list"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.sayHelloListViewModel}" />

        <TextView
            android:id="@+id/tv_add"
            style="@style/Button.Basic.Round"
            android:layout_marginBottom="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_14"
            android:text="@string/chat_job_reply_setting_add_text" />
    </LinearLayout>
</layout>