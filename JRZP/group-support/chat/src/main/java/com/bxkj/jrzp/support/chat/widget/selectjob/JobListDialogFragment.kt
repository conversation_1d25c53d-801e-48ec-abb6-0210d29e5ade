package com.bxkj.jrzp.support.chat.widget.selectjob

import android.os.Bundle
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.setHeight
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.common.widget.pagestatuslayout.v2.LoadingPageState
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.databinding.EnterpriseDialogJobListBinding
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckInfoCallBackAdapter
import com.bxkj.jrzp.support.feature.ui.infocheck.InfoCompletedCheck
import com.sanjindev.pagestatelayout.OnStateSetUpListener

/**
 * Description: 职位列表Dialog
 * Author:45457
 **/
class JobListDialogFragment internal constructor(
  builder: Builder,
) : BaseDBDialogFragment<EnterpriseDialogJobListBinding, JobListDialogViewModel>() {
  private var _title: String? = builder.title
  private var _selectedJobID: Int = builder.selectedJobID
  private var _onSelectedListener: ((positionItemBean: PositionItemBean) -> Unit)? =
    builder.onSelectedListener
  private var _addAllPlaceholder: Boolean = builder.addAllPlaceholder
  private var _onDataReturnListener: ((data: List<PositionItemBean>) -> Unit)? =
    builder.onDataReturnListener
  private var _isLoadOnlineJobList: Boolean = builder.isLoadOnlineJobList
  private var _data: List<PositionItemBean>? = builder.data

  class Builder(
    internal var title: String? = "",
    internal var selectedJobID: Int = 0,
    internal var onSelectedListener: ((positionItemBean: PositionItemBean) -> Unit)? = null,
    internal var addAllPlaceholder: Boolean = false,
    internal var onDataReturnListener: ((data: List<PositionItemBean>) -> Unit)? = null,
    internal var isLoadOnlineJobList: Boolean = false,
    internal var data: List<PositionItemBean>? = null,
  ) {
    fun setTitle(title: String) = apply { this.title = title }

    fun setAddAllPlaceholder(addAllPlaceholder: Boolean) =
      apply { this.addAllPlaceholder = addAllPlaceholder }

    fun setOnDataReturnListener(onDataReturnListener: ((data: List<PositionItemBean>) -> Unit)? = null) =
      apply { this.onDataReturnListener = onDataReturnListener }

    fun setSelectedJobId(selectedJobID: Int) = apply { this.selectedJobID = selectedJobID }

    fun setOnSelectedListener(onSelectedListener: ((positionItemBean: PositionItemBean) -> Unit)? = null) =
      apply { this.onSelectedListener = onSelectedListener }

    fun loadOnlineJobList(loadOnlineJobList: Boolean) =
      apply { this.isLoadOnlineJobList = loadOnlineJobList }

    fun setData(data: List<PositionItemBean>?) = apply { this.data = data }

    fun build() = JobListDialogFragment(this)
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialog)
  }

  override fun enableBottomSheet(): Boolean = true

  override fun getViewModelClass(): Class<JobListDialogViewModel> =
    JobListDialogViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_dialog_job_list

  override fun initPage() {
    setPeekHeight(dip(300))

    _title?.let {
      viewBinding.tvTitle.text = it
    }

    viewBinding.viewModel = viewModel

    viewBinding.ivClose.setOnClickListener {
      dismiss()
    }

    setupJobList()

    viewBinding.pslContent.show(
      LoadingPageState::class.java,
      object : OnStateSetUpListener<LoadingPageState> {
        override fun onStateSetUp(pageState: LoadingPageState) {
          pageState.view?.setHeight(dip(250))
        }
      },
    )

    subscribeViewModelEvent()

    viewModel.start(_addAllPlaceholder, _data, _isLoadOnlineJobList)
  }

  private fun subscribeViewModelEvent() {
    viewModel.jobList.observe(this) {
      _onDataReturnListener?.invoke(it)
      viewBinding.pslContent.hidden()
    }

    viewModel.showNoJobLayout.observe(this) {
      viewBinding.pslContent.show(
        ErrorPageState::class.java,
        object : OnStateSetUpListener<ErrorPageState> {
          override fun onStateSetUp(pageState: ErrorPageState) {
            pageState.setLayoutHeight(LayoutParams.WRAP_CONTENT)
            pageState.setImage(R.drawable.ic_no_content_small)
            pageState.setContent("暂无职位")
            pageState.setNextOptionText("去发布")
            pageState.setNextOptionClickListener {
              dismissAllowingStateLoss()
              InfoCompletedCheck
                .with(parentActivity)
                .checkItem(
                  InfoCheckItem
                    .Builder()
                    .checkInfoTag(CheckInfoTag.CHECK_COMPANY_AUTH_NEW)
                    .onlyCheck(false)
                    .build(),
                ).setCheckInfoCallBack(
                  object : CheckInfoCallBackAdapter() {
                    override fun onAllCheckSuccess(result: CheckResultMsg) {
                      PostJobNavigation.navigate(PostJobNavigation.TYPE_FULL_TIME).start()
                    }
                  },
                ).start()
            }
          }
        },
      )
    }
  }

  private fun setupJobList() {
    viewBinding.recyclerJobList.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration
          .Builder()
          .divider(getResDrawable(R.drawable.divider_f4f4f4))
          .margin(dip(18))
          .drawFoot(false)
          .build(),
      )
      adapter =
        object :
          SimpleDiffListAdapter<PositionItemBean>(
            R.layout.enterprise_recycler_dialog_job_item,
            PositionItemBean.DiffCallback(),
          ) {
          private var selectJobID = _selectedJobID

          override fun onBindViewHolder(
            holder: SuperViewHolder,
            position: Int,
          ) {
            super.onBindViewHolder(holder, position)
            val item = getData()?.get(position)
            val tvName = holder.findViewById<TextView>(R.id.tv_name)
            val tvAbout = holder.findViewById<TextView>(R.id.tv_about)
            val ivSelect = holder.findViewById<ImageView>(R.id.iv_select)
            tvAbout.visibility =
              if (item?.positionAbout?.isBlank() == true) View.GONE else View.VISIBLE
            if (_isLoadOnlineJobList) {
              ivSelect.visibility = View.GONE
              val tvSettingTag = holder.findViewById<TextView>(R.id.tv_setting_tag)
              item?.let {
                if (item.isZidinyi > 0) {
                  tvName.setTextColor(getResColor(R.color.common_b5b5b5))
                  tvAbout.setTextColor(getResColor(R.color.common_b5b5b5))
                  tvSettingTag.visibility = View.VISIBLE
                } else {
                  tvName.setTextColor(getResColor(R.color.cl_333333))
                  tvAbout.setTextColor(getResColor(R.color.cl_333333))
                  tvSettingTag.visibility = View.GONE
                }
              }
            } else {
              tvName.isSelected = selectJobID == item?.id
              tvAbout.isSelected = selectJobID == item?.id
              ivSelect.visibility =
                if (selectJobID == item?.id) ImageView.VISIBLE else ImageView.GONE
            }

            holder.itemView.setOnClickListener {
              if (_isLoadOnlineJobList) {
                if (item?.isZidinyi.getOrDefault() > 0) {
                  return@setOnClickListener
                } else {
                  _onSelectedListener?.invoke(item ?: return@setOnClickListener)
                  dismiss()
                }
              } else {
                selectJobID = item?.id ?: 0
                notifyDataSetChanged()
                _onSelectedListener?.invoke(item ?: return@setOnClickListener)
                dismiss()
              }
            }
          }
        }
    }
  }
}
