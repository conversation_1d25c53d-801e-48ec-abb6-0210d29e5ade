package com.bxkj.jrzp.support.chat.ui.updatechatcontactway

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.databinding.ChatActivityUpdateContactWayBinding

/**
 * Description: 修改联系方式
 * Author:45457
 **/
class UpdateChatContactWayActivity :
    BaseDBActivity<ChatActivityUpdateContactWayBinding, UpdateChatContactWayViewModel>() {

    companion object {

        const val EXTRA_CONTACT_WAY_TYPE = "CONTACT_WAY_TYPE"

        fun newIntent(context: Context, contactWayType: Int): Intent {
            return Intent(context, UpdateChatContactWayActivity::class.java).apply {
                putExtra(EXTRA_CONTACT_WAY_TYPE, contactWayType)
            }
        }
    }

    override fun getViewModelClass(): Class<UpdateChatContactWayViewModel> = UpdateChatContactWayViewModel::class.java

    override fun getLayoutId(): Int = R.layout.chat_activity_update_contact_way

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        subscribeViewModelEvent()

        viewModel.start(intent.getIntExtra(EXTRA_CONTACT_WAY_TYPE, 0))

        viewBinding.titleBar.setRightOptionClickListener {
            viewModel.confirm()
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.updateSuccessEvent.observe(this, EventObserver {
            showToast("修改成功")
            finish()
        })
    }
}