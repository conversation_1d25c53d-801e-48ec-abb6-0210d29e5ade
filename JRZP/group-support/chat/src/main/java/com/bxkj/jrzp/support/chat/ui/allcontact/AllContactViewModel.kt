package com.bxkj.jrzp.support.chat.ui.allcontact

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.data.repository.ConversationRepository
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.enums.ChatRole.Companion.Role
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.R.drawable
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.ContactFeatureBean
import com.bxkj.jrzp.support.chat.data.ContactTopChatBean
import com.bxkj.jrzp.support.chat.data.NoReplyInfoBean
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class AllContactViewModel @Inject constructor(
  private val _commonConversationRepository: ConversationRepository,
  private val _chatRepo: ChatRepo,
) : BaseViewModel() {

  val contactListViewModel = RefreshListViewModel()

  private var _contractTopChatBean: ContactTopChatBean? = null

  private var _chatRole = ChatRole.ALL

  private var _filterJobId = 0

  private var _contactType: Int = AllContactFragment.NORMAL

  //临时存储数据
  private val _tempList = ArrayList<Any>()

  //未回复的好友
  private var _noReplyFriendBean: NoReplyInfoBean? = null

  //联系人列表
  private var _contactList: ArrayList<ContactBodyBean>? = null

  //标记的联系人
  private var _markContactBean: ContactFeatureBean? = null

  init {
    if (_contactType == AllContactFragment.NORMAL) {
      contactListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    }
    contactListViewModel.setOnLoadDataListener {
      viewModelScope.launch {
        if (_contactType == AllContactFragment.NORMAL) {
          loadNormalContactList()
        } else {
          loadUnsuitableContactList(it)
        }
      }
    }
  }

  fun setPageParams(contactType: Int?) {
    this._contactType = contactType.getOrDefault(AllContactFragment.NORMAL)
    if (contactType != AllContactFragment.NORMAL) {
      contactListViewModel.refresh()
    }
  }

  fun switchRole(@Role role: Int) {
    _chatRole = role
    contactListViewModel.refresh(true)
  }

  fun filterChatByJobId(jobId: Int) {
    _filterJobId = jobId
    contactListViewModel.refresh(true)
  }

  fun setAllMsgHasRead() {
    viewModelScope.launch {
      showLoading()
      _commonConversationRepository.setupMsgHasRead(getSelfUserID(), 0)
        .handleResult({
          contactListViewModel.refresh(false)
          showToast("设置成功")
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun upOrCancelUpMsg(item: ContactBodyBean) {
    viewModelScope.launch {
      showLoading()
      _commonConversationRepository.upOrCancelUpMsg(
        item.id,
        ChatRole.getConvertLocalRole()
      ).handleResult({
        contactListViewModel.hiddenPageStatusLayout()
        if (!item.isOnTop()) { //未置顶
          contactListViewModel.remove(item)
          item.top()
          if (_contractTopChatBean == null) {
            _contractTopChatBean = ContactTopChatBean()
          }
          if (contactListViewModel.data.find { it is ContactTopChatBean } == null) {
            contactListViewModel.add(
              getTopContactLayoutPosition(),
              _contractTopChatBean
            )
          }
          _contractTopChatBean?.addChat(item)
        } else {
          if (_contractTopChatBean?.removeChat(item) == 0) {
            contactListViewModel.remove(_contractTopChatBean)
            contactListViewModel.add(getTopContactLayoutPosition(), item)
          } else {
            contactListViewModel.add(getTopContactLayoutPosition() + 1, item)
          }
          item.cancelTop()
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  private fun getTopContactLayoutPosition(): Int {
    if (UserUtils.isPersonalRole()) {
      return 1
    } else {
      if (contactListViewModel.data[0] is NoReplyInfoBean) {
        return 1
      } else {
        return 0
      }
    }
  }

  fun deleteMsg(
    item: ContactBodyBean,
    isTopChat: Boolean = false,
  ) {
    viewModelScope.launch {
      showLoading()
      _commonConversationRepository.deleteConversation(
        item.id,
        ChatRole.getConvertLocalRole()
      ).handleResult({
        if (contactListViewModel.remove(item) == 0) {
          contactListViewModel.refresh()
        }
        if (isTopChat) {
          if (_contractTopChatBean?.removeChat(item) == 0) {
            contactListViewModel.remove(getTopContactLayoutPosition())
          }
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  /**
   * 刷新置顶的联系人列表
   */
  private fun refreshTopContactList() {
    viewModelScope.launch {
      if (_contractTopChatBean == null) {
        _commonConversationRepository.getTopContactList(
          getSelfUserID(),
          ChatRole.getConvertLocalRole(),
          _chatRole
        ).handleResult({
          _contractTopChatBean = ContactTopChatBean().apply {
            chatList = ArrayList(it)
          }
        }, {
          if (!it.isNoDataError) {
            _contractTopChatBean = null
          }
        })
      }
      refreshNoReplyInfo()
    }
  }

  private fun refreshNoReplyInfo() {
    if (!UserUtils.isPersonalRole()) {
      viewModelScope.launch {
        _chatRepo.getNoReplyInfo(getSelfUserID())
          .handleResult({
            if (contactListViewModel.data.isNotEmpty()) {
              _noReplyFriendBean = it
            } else {
              _noReplyFriendBean = null
            }
          }, {
            if (contactListViewModel.data.isNotEmpty()) {
              _noReplyFriendBean = null
            }
          }, {
            refreshAllTypeInfo()
          })
      }
    } else {
      refreshAllTypeInfo()
    }
  }

  private fun refreshMarkChat() {
    viewModelScope.launch {
      if (UserUtils.isPersonalRole()) {
        _chatRepo.getGeekMarkChatCount().handleResult({
          if (it?.count.getOrDefault() > 0) {
            _markContactBean = ContactFeatureBean(
              drawable.chat_ic_mark_improper,
              "不感兴趣的企业",
              "${it?.count}个企业",
              ContactFeatureBean.JUMP_TAG_UNSUITABLE
            )
          } else {
            _markContactBean = null
          }
        })
      } else {
        _chatRepo.getBusinessMarkChatCount().handleResult({
          if (it?.count.getOrDefault() > 0) {
            _markContactBean = ContactFeatureBean(
              drawable.chat_ic_mark_improper,
              "不合适的人才",
              "${it?.count}个联系人",
              ContactFeatureBean.JUMP_TAG_UNSUITABLE
            )
          } else {
            _markContactBean = null
          }
        })
      }
      refreshAllTypeInfo()
    }
  }

  /**
   * 加载不合适的联系人列表
   */
  private fun loadUnsuitableContactList(currentPage: Int) {
    if (UserUtils.isPersonalRole()) {
      viewModelScope.launch {
        _commonConversationRepository.getConversationList(
          getSelfUserID(),
          ChatRole.getConvertLocalRole(),
          _chatRole,
          currentPage,
          20,
          _filterJobId,
          true
        ).handleResult({
          contactListViewModel.autoAddAll(it)
        }, {
          if (it.isNetworkError) {
            contactListViewModel.showLoadErrorPage()
          } else {
            contactListViewModel.noMoreData()
          }
        })
      }
    } else {
      viewModelScope.launch {
        _chatRepo.getBusinessMarkChatList(
          getSelfUserID(),
          ChatRole.getConvertLocalRole(),
          currentPage,
          20
        ).handleResult({
          contactListViewModel.autoAddAll(it)
        }, {
          if (it.isNetworkError) {
            contactListViewModel.showLoadErrorPage()
          } else {
            contactListViewModel.noMoreData()
          }
        })
      }
    }
  }

  /**
   * 加载普通的联系人列表
   */
  private fun loadNormalContactList() {
    viewModelScope.launch {
      _commonConversationRepository.getConversationList(
        getSelfUserID(),
        ChatRole.getConvertLocalRole(),
        _chatRole,
        1,
        200,
        _filterJobId
      ).handleResult({
        it?.let {
          _contactList = ArrayList(it)
        }
        // refreshAllTypeInfo()
        refreshMarkChat()
      }, {
        if (it.isNetworkError) {
          contactListViewModel.showLoadErrorPage()
        } else {
          if (!UserUtils.isPersonalRole()) {
            contactListViewModel.noMoreData()
          }
        }
      }, {
        refreshTopContactList()
      })
    }
  }

  private fun refreshAllTypeInfo() {
    _tempList.clear()
    if (UserUtils.isPersonalRole()) {
      _tempList.add(
        ContactFeatureBean(
          drawable.chat_ic_feature_latest_job,
          "最新职位",
          "为您推荐最新职位",
          ContactFeatureBean.JUMP_TAG_0
        )
      )
    }

    if (!UserUtils.isPersonalRole()) {
      _noReplyFriendBean?.let {
        _tempList.add(it)
      }
    }

    _contractTopChatBean?.let {
      if (it.hasTopContact()) {
        _tempList.add(it)
      }
    }

    _contactList?.let {
      _tempList.addAll(it)
    }

    _markContactBean?.let {
      _tempList.add(it)
    }

    contactListViewModel.reset(_tempList)
    if (_tempList.isEmpty()) {
      contactListViewModel.noMoreData()
    }
  }
}