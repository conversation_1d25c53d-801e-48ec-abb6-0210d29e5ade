package com.bxkj.jrzp.support.chat

/**
 * Description:
 * Author:45457
 **/
class ChatMsgType {

    companion object {

        const val STANDARD_TEXT = 200

        const val LOCATION = 22
        const val IMAGE = 23

        const val GEEK_ACCEPT_INTERVIEW = 7
        const val GEEK_REFUSE_INTERVIEW = 9

        const val REQ_EXCHANGE_PHONE = 12
        const val REQ_EXCHANGE_WECHAT = 13

        const val CANCEL_INTERVIEW = 14

        const val AGREE_EXCHANGE_PHONE = 15
        const val AGREE_EXCHANGE_WECHAT = 16

        const val REFUSE_EXCHANGE_PHONE = 18
        const val REFUSE_EXCHANGE_WECHAT = 19

        const val SWITCH_JOB = 17
    }
}