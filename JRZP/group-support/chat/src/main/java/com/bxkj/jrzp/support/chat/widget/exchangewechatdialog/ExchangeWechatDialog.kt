package com.bxkj.jrzp.support.chat.widget.exchangewechatdialog

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.appcompat.app.AppCompatDialog
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.R.style
import com.bxkj.jrzp.support.chat.databinding.ChatDialogExchangeWechatBinding

/**
 * Description: 交换微信号
 * Author:45457
 **/
class ExchangeWechatDialog constructor(
    private val wechatNumber: String?,
    private val onConfirm: (() -> Unit)? = null
) : BaseDBDialogFragment<ChatDialogExchangeWechatBinding, ExchangeWechatViewModel>(), OnClickListener {

    override fun getViewModelClass(): Class<ExchangeWechatViewModel> = ExchangeWechatViewModel::class.java

    override fun getLayoutId(): Int = R.layout.chat_dialog_exchange_wechat

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AppCompatDialog(requireContext(), style.BaseDialogFragmentStyle)
    }

    override fun initPage() {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        subscribeViewModelEvent()

        viewModel.setWechatNumber(wechatNumber)
    }

    private fun subscribeViewModelEvent() {
        viewModel.confirmCommand.observe(this, EventObserver {
            dismiss()
            onConfirm?.invoke()
        })
    }

    override fun onClick(v: View?) {
        v?.let {
            if (v.id == R.id.tv_cancel) {
                dismiss()
            }
        }
    }
}