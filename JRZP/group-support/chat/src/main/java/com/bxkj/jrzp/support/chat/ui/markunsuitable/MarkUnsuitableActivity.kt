package com.bxkj.jrzp.support.chat.ui.markunsuitable

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.databinding.ChatActivityMarkUnsuitableBinding
import com.bxkj.jrzp.support.chat.ui.allcontact.AllContactFragment

/**
 * Description:不合适的联系人
 * Author:45457
 **/
class MarkUnsuitableActivity : BaseDBActivity<ChatActivityMarkUnsuitableBinding, BaseViewModel>() {

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, MarkUnsuitableActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.chat_activity_mark_unsuitable

    override fun initPage(savedInstanceState: Bundle?) {
        setupContentFragment()

        viewBinding.ctlTitle.setTitle(if (UserUtils.isPersonalRole()) "不感兴趣的企业" else "不合适的人才")
    }

    private fun setupContentFragment() {
        supportFragmentManager.beginTransaction()
            .add(R.id.fl_container, AllContactFragment.newInstance(AllContactFragment.UNSUITABLE))
            .commitNow()
    }
}