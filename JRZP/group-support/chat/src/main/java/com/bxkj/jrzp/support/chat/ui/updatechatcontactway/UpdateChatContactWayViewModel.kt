package com.bxkj.jrzp.support.chat.ui.updatechatcontactway

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.startCountDown
import com.bxkj.jrzp.user.api.OpenUserApiConstants.ContactType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description: 修改联系方式
 * Author:45457
 **/
class UpdateChatContactWayViewModel @Inject constructor(
    private val _commonRepo: CommonRepository,
    private val _openUserRepo: OpenUserRepository
) : BaseViewModel() {

    val contactInfoText = MutableLiveData<String>()
    val originPhoneNumber=MutableLiveData<String>()

    val smsCode = MutableLiveData<String>()

    val smdCodeCountdown = MutableLiveData<Int>().apply { value = 0 }

    val updateSuccessEvent = MutableLiveData<VMEvent<Unit>>()

    private var _serviceCode: String? = null
    private var _receivedSmsCodePhoneNum: String = ""

    private val _contactInfoType =
        MutableLiveData<Int>().apply { value = ContactType.PHONE.value }

    val isUpdatePhoneMode = MediatorLiveData<Boolean>().apply {
        addSource(_contactInfoType) {
            value = it == ContactType.PHONE.value
        }
    }

    fun start(updateMode: Int) {
        _contactInfoType.value = updateMode
        viewModelScope.launch {
            showLoading()
            _openUserRepo.getUserContactInfo(if (updateMode == ContactType.PHONE.value) ContactType.PHONE else ContactType.WECHAT)
                .handleResult({ result ->
                    originPhoneNumber.value=result?.contactWay.getOrDefault()
                    contactInfoText.value = result?.contactWay.getOrDefault()
                    if (updateMode == ContactType.PHONE.value) {
                        _receivedSmsCodePhoneNum = result?.contactWay.getOrDefault()
                    }
                }, { err ->
                    showToast(err.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun requestSmsCode() {
        val phoneNumber = contactInfoText.value
        if (phoneNumber.isNullOrBlank()) {
            showToast("请填写手机号后发送")
        } else {
            viewModelScope.launch {
                showLoading()
                _commonRepo.requestSmsCode(phoneNumber).handleResult({
                    _serviceCode = it
                    _receivedSmsCodePhoneNum = phoneNumber
                    startCountDown(60, {
                        smdCodeCountdown.value = it
                    })
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
            }
        }
    }

    fun confirm() {
        if (isUpdatePhoneMode.value == true) {
            val phoneNumber = contactInfoText.value
            val smsCode = smsCode.value
            if (phoneNumber.isNullOrBlank()) {
                showToast("请填写手机号")
                return
            }
            if (smsCode.isNullOrBlank()) {
                showToast("请填写验证码")
                return
            }
            if (smsCode != _serviceCode) {
                showToast("验证码错误")
                return
            }
            if (phoneNumber != _receivedSmsCodePhoneNum) {
                showToast("手机号与接收验证码的手机号不一致")
                return
            }
            viewModelScope.launch {
                showLoading()
                _openUserRepo.updateUserContactPhone(phoneNumber, smsCode)
                    .handleResult({
                        updateSuccessEvent.value = VMEvent(Unit)
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        } else {
            val wechatNumber = contactInfoText.value
            if (wechatNumber.isNullOrBlank()) {
                showToast("请填写微信号")
                return
            }

            viewModelScope.launch {
                showLoading()
                _openUserRepo.updateUserWechat(wechatNumber)
                    .handleResult({
                        updateSuccessEvent.value = VMEvent(Unit)
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }
}