package com.bxkj.jrzp.support.chat.ui.chatsayhellosetting

import android.app.Activity
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean
import com.bxkj.jrzp.support.chat.databinding.ChatFragmentCommonSayHelloSettingBinding
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation

class CommonChatSayHelloSettingFragment :
    BaseDBFragment<ChatFragmentCommonSayHelloSettingBinding, CommonChatSayHelloSettingViewModel>() {

    companion object {

        fun newInstance(): CommonChatSayHelloSettingFragment {
            return CommonChatSayHelloSettingFragment()
        }
    }

    private val _editCustomQuickMsgLauncher = registerForActivityResult(StartActivityForResult()) {
        if (it.resultCode == Activity.RESULT_OK) {
            viewModel.editCustomQuickMsg(it.data?.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT).getOrDefault())
        }
    }

    override fun getViewModelClass(): Class<CommonChatSayHelloSettingViewModel> =
        CommonChatSayHelloSettingViewModel::class.java

    override fun getLayoutId(): Int = R.layout.chat_fragment_common_say_hello_setting

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupMsgTypeList()

        setupQuickMsgList()

        viewModel.start()
    }

    private fun setupQuickMsgList() {
        viewBinding.includeMsgList.recyclerContent.apply {
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecoration(LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4)).build())
        }

        viewModel.quickMsgListViewModel.setAdapter(object :
            SimpleDBListAdapter<ChatQuickMsgBean>(requireContext(), R.layout.chat_recycler_say_hello_msg_item) {
        }.apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    data.get(position)?.let {
                        if (it.default == 0) {
                            viewModel.setDefaultQuickMsg(it)
                        }
                    }
                }
            })
        })

        viewBinding.recyclerCustomQuickMsgList.apply {
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecoration(LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4)).build())
            adapter = SimpleDiffListAdapter(
                R.layout.chat_recycler_say_hello_msg_item,
                ChatQuickMsgBean.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.get(position)?.let {
                            if (it.default == 0) {
                                viewModel.setDefaultQuickMsg(it)
                            }
                        }
                    }
                })
            }
        }

        viewModel.editQuickMsgCommand.observe(this, EventObserver {
            _editCustomQuickMsgLauncher.launch(
                EditInfoNavigation.navigate("编辑打招呼语", "编辑打招呼语", it, 100).createIntent(requireContext())
            )
        })
    }

    private fun setupMsgTypeList() {
        viewBinding.recyclerMsgType.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = object :
                SuperAdapter<Pair<Int, String>>(
                    requireContext(),
                    R.layout.chat_recycler_msg_type_item
                ) {

                override fun openSingleSelect(): Boolean = true

                override fun convert(
                    holder: SuperViewHolder,
                    viewType: Int,
                    item: Pair<Int, String>?,
                    position: Int
                ) {
                    item?.let {
                        val tvItem = holder.findViewById<TextView>(R.id.tv_item)
                        tvItem.text = item.second
                        tvItem.isSelected = selectedIndex == position
                    }
                }
            }.apply {
                selectedIndex = 0
                data = arrayListOf(
                    Pair(0, "常规"),
                    Pair(1, "幽默"),
                    Pair(2, "礼貌"),
                    Pair(3, "诚恳"),
                    Pair(4, "自定义")
                )
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        data.get(position)?.let { item ->
                            viewModel.switchQuickMsgList(item.first)
                        }
                    }
                })
            }
        }
    }
}