<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <SurfaceView
            android:id="@+id/surfaceView"
            style="@style/match_match"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.king.zxing.ViewfinderView
            android:id="@+id/viewfinderView"
            style="@style/match_match"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/ll_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:layout_marginStart="@dimen/dp_10"
                android:onClick="@{onClickListener}"
                android:src="@drawable/scan_ic_page_back" />

            <Space
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_select_pic"
                style="@style/wrap_wrap"
                android:layout_margin="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_10"
                android:onClick="@{onClickListener}"
                android:src="@drawable/scan_ic_photoalbum" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_torch"
            style="@style/wrap_wrap"
            android:layout_marginBottom="@dimen/dp_50"
            android:onClick="@{onClickListener}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/iv_torch"
                style="@style/wrap_wrap"
                android:layout_gravity="center"
                android:src="@drawable/scan_ic_torch_selector" />

            <TextView
                android:id="@+id/tv_torch_status"
                style="@style/Text.14sp.FFFFFF"
                android:layout_marginTop="@dimen/dp_4"
                android:text="@string/scan_torch_off" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_temp"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="100dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>