<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_dialog">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/common_dp_10"
        android:layout_marginStart="@dimen/common_dp_10"
        android:layout_marginTop="@dimen/common_dp_16"
        style="@style/common_Text.18sp.333333"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_dialog_content"
        android:layout_width="@dimen/common_dp_0"
        app:layout_goneMarginTop="@dimen/common_dp_16"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/common_dp_20"
        android:layout_marginStart="@dimen/common_dp_20"
        android:layout_marginTop="@dimen/common_dp_5"
        android:lineSpacingExtra="@dimen/dp_3"
        style="@style/common_Text.16sp.333333"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_title" />

    <EditText
        android:id="@+id/et_content"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="@dimen/dp_40"
        android:layout_marginEnd="@dimen/common_dp_16"
        android:layout_marginStart="@dimen/common_dp_16"
        android:layout_marginTop="@dimen/common_dp_16"
        android:background="@drawable/common_bg_dialog_edit"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingTop="@dimen/common_dp_4"
        android:paddingBottom="@dimen/common_dp_4"
        android:paddingEnd="@dimen/common_dp_12"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5"
        android:textSize="@dimen/common_sp_16"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_content" />

    <View
        android:id="@+id/v_h_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="16dp"
        android:background="@color/common_e8e8e8"
        app:layout_constraintTop_toBottomOf="@id/et_content" />

    <View
        android:id="@+id/v_v_line"
        android:layout_width="1px"
        android:layout_height="48dp"
        android:background="#E8E8E8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_h_line" />

    <TextView
        android:id="@+id/tv_dialog_cancel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/common_cancel"
        android:textColor="@color/cl_888888"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/v_v_line"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_h_line" />

  <FrameLayout
    android:id="@+id/fl_confirm"
    android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toEndOf="@id/v_v_line"
    app:layout_constraintTop_toBottomOf="@id/v_h_line">

    <TextView
      android:id="@+id/tv_dialog_confirm"
      style="@style/wrap_wrap"
      android:layout_gravity="center"
      android:gravity="center"
      android:text="@string/common_confirm"
      android:textColor="@color/common_49C280"
      android:textSize="16dp" />
  </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>