package com.bxkj.ecommon.base

import android.os.Bundle
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.bxkj.common.base.mvvm.BaseNActivity
import com.bxkj.common.util.ActivityManager
import com.bxkj.common.util.CustomDensityManager
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.LoginActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.LoadingDialog
import com.bxkj.common.widget.dialog.LoadingDialog.Builder
import com.bxkj.ecommon.R
import com.gyf.immersionbar.ImmersionBar
import com.hjq.toast.Toaster
import dagger.android.AndroidInjection
import dagger.android.AndroidInjector
import dagger.android.DispatchingAndroidInjector
import dagger.android.HasAndroidInjector
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.mvvm
 * @Description:
 * @TODO: TODO
 * @date 2019/4/3
 */
abstract class EBaseNActivity : FragmentActivity(),
    HasAndroidInjector {
    @Inject
    lateinit var androidInjector: DispatchingAndroidInjector<Any>
    private var mLoadingDialog: LoadingDialog? = null
    lateinit var statusBarManager: ImmersionBar
        private set

    private var mAfterLoginAction: (() -> Unit)? = null

    private var mCompositeDisposable: CompositeDisposable? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        AndroidInjection.inject(this)
        super.onCreate(savedInstanceState)
        initGlobalConfig()
        mCompositeDisposable = CompositeDisposable()
        subscribeLoginSuccessEvent()
    }

    private fun subscribeLoginSuccessEvent() {
        addDisposable(RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
            if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS || it.code == RxMsgCode.ENTERPRISE_LOGIN_SUCCESS) {
                mAfterLoginAction?.invoke()
            }
        }
        )
    }

    private fun initGlobalConfig() {
        //屏幕适配换算
        CustomDensityManager.setCustomDensity(this, application)
        //状态栏管理
        statusBarManager = ImmersionBar.with(this).navigationBarColor(R.color.white)
        //将activity压入栈中
        ActivityManager.getInstance().pushActivity(this)
    }

    protected open fun addDisposable(disposable: Disposable) {
        mCompositeDisposable?.add(disposable)
    }

    @JvmOverloads
    fun showLoading(@StringRes textId: Int = com.bxkj.common.api.CommonApiConstants.NO_ID) {
        if (mLoadingDialog == null) {
            mLoadingDialog = Builder(this).build()
        }
        if (textId != 0) {
            mLoadingDialog!!.setText(getString(textId))
        } else {
            mLoadingDialog!!.clearLoadingText()
        }
        mLoadingDialog!!.show()
    }

    fun hiddenLoading() {
        if (mLoadingDialog != null && mLoadingDialog!!.isShowing) {
            mLoadingDialog!!.dismiss()
        }
    }

    fun showToast(text: String?) {
        Toaster.show(text)
    }

    fun getResColor(@ColorRes colorResId: Int): Int {
        return ContextCompat.getColor(this, colorResId)
    }

    val localUserId: Int
        get() = UserUtils.getUserId()

    fun checkToLogin(): Boolean {
        if (!UserUtils.logged()) {
            jumpToLogin()
            return false
        }
        return true
    }

    /**
     * 检查登录后执行
     */
    fun afterLogin(afterAction: () -> Unit) {
        if (checkToLogin()) {
            afterAction.invoke()
        } else {
            mAfterLoginAction = afterAction
        }
    }

    private fun jumpToLogin() {
        Router.getInstance().to(LoginActivity.URL)
            .withBoolean(LoginActivity.EXTRA_BACK_AFTER_LOGIN, true)
            .startForResult(this, BaseNActivity.LOGIN_REQUEST_CODE)
    }

    override fun androidInjector(): AndroidInjector<Any> {
        return androidInjector
    }

    override fun finish() {
        SystemUtil.hideSoftKeyboard(this)
        super.finish()
    }

    override fun onStop() {
        super.onStop()
        hiddenLoading()
    }

    override fun onDestroy() {
        mCompositeDisposable?.clear()
        ActivityManager.getInstance().popActivity(this)
        super.onDestroy()
    }

    companion object {
        const val LOGIN_REQUEST_CODE = 99
    }
}