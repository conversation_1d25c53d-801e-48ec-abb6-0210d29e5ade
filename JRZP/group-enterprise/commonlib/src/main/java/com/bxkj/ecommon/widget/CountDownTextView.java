package com.bxkj.ecommon.widget;

import android.content.Context;
import android.util.AttributeSet;

import com.bxkj.common.util.TimeUtils;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget
 * @Description:
 * @TODO: TODO
 * @date 2019/6/13
 */
public class CountDownTextView extends androidx.appcompat.widget.AppCompatTextView {

    private Disposable mCountdownDisposable;

    public CountDownTextView(Context context) {
        this(context, null);
    }

    public CountDownTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CountDownTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void startCountdown(long second) {
        if (second <= 0) {
            return;
        }
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(second + 1)
                .map(aLong -> second - aLong)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                        mCountdownDisposable = d;
                    }

                    @Override
                    public void onNext(Long aLong) {
                        setText(TimeUtils.second2MinutesAndSeconds(aLong,"时","分","秒后开始"));
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                        setText("正在进行中");
                    }
                });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopCountdown();
    }

    private void stopCountdown() {
        if (mCountdownDisposable != null && !mCountdownDisposable.isDisposed()) {
            mCountdownDisposable.dispose();
            mCountdownDisposable = null;
        }
    }
}
