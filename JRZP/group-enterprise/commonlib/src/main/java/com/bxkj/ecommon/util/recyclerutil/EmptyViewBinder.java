package com.bxkj.ecommon.util.recyclerutil;

import com.bxkj.common.R;
import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.EmptyData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util.recyclerutil
 * @Description:
 * @TODO: TODO
 * @date 2018/12/11
 */
public class EmptyViewBinder implements ItemViewBinder<EmptyData> {
  @Override
  public void onBindViewHolder(SuperViewHolder holder, EmptyData item, int position) {

  }

  @Override
  public int getLayoutId() {
    return R.layout.layout_recycler_empty;
  }
}
