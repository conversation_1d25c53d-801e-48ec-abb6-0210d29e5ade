package com.bxkj.ecommon.network;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.ZPRequestBody;
import io.reactivex.Observable;
import java.util.List;
import retrofit2.Retrofit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.network
 * @Description:
 * @TODO: TODO
 * @date 2018/3/31
 */

public class ECommonApi {

    protected ECommonServices mCommonServices;

    public ECommonApi(Retrofit retrofit) {
        mCommonServices = retrofit.create(ECommonServices.class);
    }

    /**
     * 获取用户选中的期望职位
     *
     * @param userId
     * @return
     */
    public Observable<BaseResponse<List<JobTypeData>>> getUserCheckedJobList(int userId) {
        ZPRequestBody ZPRequestBody = new ZPRequestBody();
        ZPRequestBody.put("uid", userId);
        return mCommonServices.getUserCheckedJobList(ZPRequestBody);
    }

    /**
     * 获取职位分类列表
     *
     * @param type         1为查询大类，2为查询小类
     * @param firstClassId 大类id，查询大类时为0
     * @return
     */
    public Observable<BaseResponse<List<JobTypeData>>> getJobClassList(int type, int firstClassId) {
        ZPRequestBody ZPRequestBody = new ZPRequestBody();
        ZPRequestBody.put("type", type);
        ZPRequestBody.put("pid", firstClassId);
        return mCommonServices.getJobClassList(ZPRequestBody);
    }

    /**
     * 获取地区列表
     *
     * @param type 查询类型（1、省2、市3、区县4、街道）
     * @param pid  父类编号（查询省时为0）
     * @return
     */
    public Observable<BaseResponse<List<AreaOptionsData>>> getAreaList(int type, int parentId) {
        ZPRequestBody ZPRequestBody = new ZPRequestBody();
        ZPRequestBody.put("type", type);
        ZPRequestBody.put("pid", parentId);
        return mCommonServices.getAreaList(ZPRequestBody);
    }

}
