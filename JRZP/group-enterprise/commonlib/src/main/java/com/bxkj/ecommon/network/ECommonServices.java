package com.bxkj.ecommon.network;

import com.bxkj.common.network.ZPRequestBody;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.network.BaseResponse;

import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib.network
 * @Description: post请求
 * @TODO: TODO
 * @date 2018/3/28
 */

public interface ECommonServices {

    @POST
    Observable<BaseResponse> basePost(@Url String url, @Body ZPRequestBody ZPRequestBody);

    @POST(ECommonApiConstants.I_GET_USER_CHECKED_JOB)
    Observable<BaseResponse<List<JobTypeData>>> getUserCheckedJobList(@Body ZPRequestBody ZPRequestBody);

    @POST(ECommonApiConstants.I_GET_JOB_CLASS_LIST)
    Observable<BaseResponse<List<JobTypeData>>> getJobClassList(@Body ZPRequestBody ZPRequestBody);

    @POST(ECommonApiConstants.I_GET_AREA_LIST)
    Observable<BaseResponse<List<AreaOptionsData>>> getAreaList(@Body ZPRequestBody ZPRequestBody);


}
