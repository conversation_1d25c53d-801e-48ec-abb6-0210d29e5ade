package com.bxkj.ecommon.constants;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib
 * @Description:
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ECommonApiConstants {

  //接口统一地址
  public static final String BASE_URL = "https://jrzpapi.jdzj2.com";

  //默认头像男
  public static final String DEFAULT_MAN_HEADER = "images_server/comm/nan.png";

  //默认头像女
  public static final String DEFAULT_WOMAN_HEADER = "images_server/comm/nv.png";

  //图片上传前缀
  public static final String IMG_UPLOAD_PREFIX = "data:image/jpeg;base64,";

  //获取用户选中的期望职位
  public static final String I_GET_USER_CHECKED_JOB = "/UserJob/GetUserJobList/";

  //获取职位分类list
  public static final String I_GET_JOB_CLASS_LIST = "/Job/GetJobList/";

  //获取区域列表
  public static final String I_GET_AREA_LIST = "/Area/GetAreaList/";

  //默认分页大小
  public static final int DEFAULT_PAGE_SIZE = 15;

  //无数据默认值
  public static final int NO_DATA = -1;

  //无id默认值
  public static final int NO_ID = 0;

  //无内容默认值
  public static final String NO_TEXT = "";

  //省类别编号
  public static final int GET_PROVINCE_TYPE = 1;

  //市类别编号
  public static final int GET_CITY_TYPE = 2;

  //区类别编号
  public static final int GET_AREA_TYPE = 3;

  //街道类别编号
  public static final int GET_STREET_TYPE = 4;

  //注册类型
  public static final int APP_ORIGIN = -20;

  public static String getDefaultHeader(int sex) {
    if (sex == 0) {
      return DEFAULT_MAN_HEADER;
    } else {
      return DEFAULT_WOMAN_HEADER;
    }
  }

  /*****************Chat(聊天)*****************/
  //获取聊天对象信息
  public static final String I_GET_CONVERSATION_USER_INFO = "/Conversation/GetConUserInfoV2/";

  //检查是否存在会话
  public static final String I_CHECK_HAS_CONVERSATION = "/Conversation/MatchConInfo/";

  //获取会话列表
  public static final String I_GET_CONVERSATION_LIST = "/Conversation/GetConversationListByPageV2/";

  //删除会话
  public static final String I_DELETE_CONVERSATION = "/Conversation/DeleteConversation/";

  //举报
  public static final String I_REPORT_CONVERSATION = "/Report/Report/";

  //设置消息已读
  public static final String I_SETUP_MSG_HAS_READ = "/Conversation/SetAllRead/";
}
