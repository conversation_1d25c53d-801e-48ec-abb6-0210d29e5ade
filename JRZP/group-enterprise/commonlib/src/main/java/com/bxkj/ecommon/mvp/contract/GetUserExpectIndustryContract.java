package com.bxkj.ecommon.mvp.contract;

import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.mvp
 * @Description: GetUserCheckedJob
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface GetUserExpectIndustryContract {
    interface View extends BaseView {
        void getUserExpectIndustryListSuccess(List<JobTypeData> userExpectIndustryList);

        void noExpectIndustry();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getUserCheckedJobList(int userId);
    }
}
