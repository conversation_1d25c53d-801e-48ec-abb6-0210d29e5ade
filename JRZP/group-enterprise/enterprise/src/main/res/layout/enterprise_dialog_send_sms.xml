<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.bxkj.common.util.HtmlUtils" />

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.weight.sendsms.SendSmsViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginStart="@dimen/dialog_lr_margin"
            android:layout_marginTop="@dimen/dialog_top_margin"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text.DialogTitle"
                android:layout_weight="1"
                android:text="@string/enterprise_send_sms_title" />

            <ImageView
                android:id="@+id/iv_close"
                style="@style/wrap_wrap"
                android:src="@drawable/ic_big_close" />
        </LinearLayout>

        <TextView
            style="@style/Text.12sp.888888"
            android:layout_marginStart="18dp"
            android:text="@string/enterprise_send_sms_tips" />

        <TextView
            android:id="@+id/et_content"
            style="@style/Text.16sp.333333"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/bg_f4f4f4_radius_10"
            android:gravity="start|top"
            android:lines="5"
            android:padding="@dimen/dp_8"
            android:text="@{viewModel.smsBalance.content}" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_sms_package"
            style="@style/match_wrap"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_12"
            android:visibility="@{viewModel.smsBalance.hasBalance()?View.GONE:View.VISIBLE}"
            bind:items="@{viewModel.smsPackageList}" />

        <View
            style="@style/Line.Horizontal.Light"
            android:layout_marginTop="@dimen/dp_30" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_balance"
                style="@style/Text.14sp.333333"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_18"
                android:layout_weight="1"
                android:text="@{HtmlUtils.fromHtml(@string/enterprise_send_sms_balance_format(viewModel.smsBalance.smsCount))}"
                android:visibility="@{viewModel.smsBalance.hasBalance()?View.VISIBLE:View.GONE}" />

            <TextView
                android:id="@+id/tv_price"
                style="@style/Text.14sp.333333"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_18"
                android:layout_weight="1"
                android:text="@{@string/enterprise_send_sms_price_format(viewModel.selectSmsPackage.price)}"
                android:visibility="@{viewModel.smsBalance.hasBalance()?View.GONE:View.VISIBLE}" />

            <TextView
                android:id="@+id/tv_confirm"
                android:onClick="@{()->viewModel.confirm()}"
                style="@style/common_Text.16sp.FFFFFF"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:layout_marginEnd="@dimen/dp_18"
                android:background="@drawable/bg_fe6600_radius_6"
                android:gravity="center"
                android:paddingEnd="@dimen/dp_12"
                android:paddingStart="@dimen/dp_12"
                android:text="@{viewModel.smsBalance.hasBalance()?@string/enterprise_send_sms_confirm:@string/enterprise_send_sms_buy_package}" />

        </LinearLayout>

    </LinearLayout>
</layout>