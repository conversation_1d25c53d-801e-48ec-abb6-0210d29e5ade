<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.selectsayhellojob.SelectSayHelloJobViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical"
        android:paddingStart="@dimen/common_dp_20"
        android:paddingEnd="@dimen/common_dp_20">

        <LinearLayout
            android:id="@+id/ll_title_bar"
            style="@style/match_wrap"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/common_Text.20sp.333333.Bold"
                android:layout_marginTop="@dimen/common_dp_30"
                android:layout_marginBottom="@dimen/common_dp_30"
                android:layout_weight="1"
                android:text="@string/select_say_hello_page_title" />

            <ImageView
                android:id="@+id/iv_close"
                style="@style/common_wrap_wrap"
                android:src="@drawable/common_ic_close" />
        </LinearLayout>

        <include
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.jobListViewModel}" />

    </LinearLayout>
</layout>