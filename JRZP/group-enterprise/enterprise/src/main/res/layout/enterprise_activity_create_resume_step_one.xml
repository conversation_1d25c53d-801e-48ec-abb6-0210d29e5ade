<?xml version="1.0" encoding="utf-8"?>
<layout>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_title_bar" />

    <ScrollView style="@style/match_wrap">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <TextView
          style="@style/common_Text.18sp.333333"
          android:layout_width="match_parent"
          android:paddingStart="@dimen/common_dp_12"
          android:paddingTop="@dimen/common_dp_16"
          android:paddingEnd="@dimen/common_dp_12"
          android:paddingBottom="@dimen/common_dp_16"
          android:text="@string/create_resume_tips" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_height="@dimen/common_dp_8" />

        <LinearLayout
          style="@style/common_Layout.InfoItem.Padding12"
          android:orientation="horizontal">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/create_resume_name_hint" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_name"
            style="@style/common_EditText.Basic.RightAlignment"
            android:layout_marginStart="@dimen/common_dp_10"
            android:layout_weight="1"
            android:drawablePadding="@dimen/common_dp_10"
            android:gravity="end|center_vertical"
            android:hint="@string/create_resume_name_hint"
            android:imeOptions="actionDone"
            android:inputType="textWebEmailAddress" />

          <RadioGroup
            android:id="@+id/rg_sex"
            style="@style/common_wrap_match"
            android:layout_marginStart="@dimen/common_dp_20"
            android:layout_marginEnd="@dimen/common_dp_16"
            android:orientation="horizontal">

            <RadioButton
              android:id="@+id/rb_male"
              style="@style/RadioButton.GenderSelector"
              android:checked="true"
              android:drawableStart="@drawable/ic_drawable_start_gender_man"
              android:text="@string/gender_male" />

            <RadioButton
              android:id="@+id/rb_female"
              style="@style/RadioButton.GenderSelector"
              android:layout_marginStart="@dimen/common_dp_12"
              android:drawableStart="@drawable/ic_drawable_start_gender_lady"
              android:text="@string/gender_female" />

          </RadioGroup>

        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
          android:id="@+id/ll_birthday"
          style="@style/common_Layout.InfoItem.Padding12"
          android:orientation="horizontal">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/account_birthday" />

          <TextView
            android:id="@+id/tv_birthday"
            style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
          android:id="@+id/ll_address"
          style="@style/common_Layout.InfoItem.Padding12"
          android:orientation="horizontal">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/account_address" />

          <TextView
            android:id="@+id/tv_address"
            style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
          android:id="@+id/ll_education"
          style="@style/common_Layout.InfoItem.Padding12"
          android:orientation="horizontal">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/account_degree" />

          <TextView
            android:id="@+id/tv_education"
            style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/account_graduation_time" />

          <TextView
            android:id="@+id/tv_graduation_time"
            style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/create_resume_before_position" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_before_position"
            style="@style/common_EditText.Basic.RightAlignment"
            android:hint="@string/create_resume_before_position_hint" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/create_resume_expect_work" />

          <TextView
            android:id="@+id/tv_expect_position"
            style="@style/common_Text.InfoItem.Select" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/create_resume_specific_position" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_specific_position"
            style="@style/common_EditText.Basic.RightAlignment"
            android:hint="@string/create_resume_specific_position" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <Button
          android:id="@+id/btn_confirm"
          style="@style/common_Button.Basic"
          android:layout_marginStart="@dimen/common_dp_30"
          android:layout_marginTop="@dimen/dp_44"
          android:layout_marginEnd="@dimen/common_dp_30"
          android:layout_marginBottom="@dimen/common_dp_30"
          android:text="@string/common_complete" />

      </LinearLayout>

    </ScrollView>

  </LinearLayout>
</layout>