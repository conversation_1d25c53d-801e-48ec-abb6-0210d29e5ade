<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_match"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            app:title="@string/mine_company_home" />

        <androidx.core.widget.NestedScrollView style="@style/match_wrap">

            <LinearLayout
                style="@style/match_wrap"
                android:orientation="vertical">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/iv_logo"
                    style="@style/roundedCornerImageStyle.Avatar"
                    android:layout_width="72dp"
                    android:layout_height="72dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/common_dp_20"
                    android:onClick="@{onClickListener}"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ps_ic_placeholder"
                    bind:imgUrl="@{viewModel.companyInfo.logoUrl}" />

                <TextView
                    android:id="@+id/tv_logo"
                    style="@style/common_Text.14sp.888888"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/common_dp_10"
                    android:layout_marginBottom="@dimen/common_dp_20"
                    android:onClick="@{onClickListener}"
                    android:text="@string/company_logo" />

                <View
                    style="@style/Line.Horizontal.Light"
                    android:layout_height="@dimen/common_dp_8" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_name"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{()->viewModel.toEditName()}"
                    app:yui_content="@{viewModel.companyInfo.name}"
                    app:yui_content_hint="@string/please_enter"
                    app:yui_content_type="text"
                    app:yui_enabled="@{viewModel.authFailed}"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_name" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_short_name"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{()->viewModel.toEditShortName()}"
                    app:yui_content="@{viewModel.companyInfo.name2}"
                    app:yui_content_hint="@string/please_enter"
                    app:yui_content_type="text"
                    app:yui_title="@string/company_for_short" />

                <View
                    style="@style/Line.Horizontal.Light"
                    android:layout_height="@dimen/common_dp_8" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_address"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{()->viewModel.toEditAddress()}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.appendAddress}"
                    app:yui_content_hint="@string/common_please_select"
                    app:yui_content_type="text"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_details_address" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_desc"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{()->viewModel.toEditDesc()}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.info}"
                    app:yui_content_hint="@string/please_enter"
                    app:yui_content_type="text"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_about" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_industry"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{onClickListener}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.tradeName}"
                    app:yui_content_hint="@string/common_please_select"
                    app:yui_content_type="text"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_industry" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_nature"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{onClickListener}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.proName}"
                    app:yui_content_hint="@string/common_please_select"
                    app:yui_content_type="text"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_nature" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_size"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{onClickListener}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.sizeName}"
                    app:yui_content_hint="@string/common_please_select"
                    app:yui_content_type="text"
                    app:yui_icon="@drawable/common_ic_required"
                    app:yui_title="@string/company_size" />

                <View
                    style="@style/Line.Horizontal.Light"
                    android:layout_height="@dimen/common_dp_8" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_web_url"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{onClickListener}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.url}"
                    app:yui_content_hint="@string/please_enter"
                    app:yui_content_type="text"
                    app:yui_title="@string/company_web" />

                <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
                    android:id="@+id/item_traffic"
                    style="@style/JrzpCommonListItemView"
                    android:onClick="@{onClickListener}"
                    app:yui_accessory_type="chevron"
                    app:yui_content="@{viewModel.companyInfo.traffic}"
                    app:yui_content_hint="@string/please_enter"
                    app:yui_content_type="text"
                    app:yui_title="@string/company_traffic" />

                <TextView
                    android:id="@+id/tv_submit"
                    style="@style/Button.Basic.Round"
                    android:layout_margin="@dimen/dp_30"
                    android:onClick="@{()->viewModel.save()}" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>