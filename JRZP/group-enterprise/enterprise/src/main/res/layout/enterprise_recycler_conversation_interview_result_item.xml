<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
    </data>

    <LinearLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_marginBottom="@dimen/dp_8"
        android:orientation="vertical">

        <include layout="@layout/enterprise_layout_conversation_time_tag" />

        <LinearLayout
            style="@style/wrap_wrap"
            android:orientation="horizontal"
            android:visibility="@{data.sender?View.GONE:View.VISIBLE}">

            <include
                android:id="@+id/include_avatar"
                layout="@layout/enterprise_layout_conversation_msg_avatar"
                app:avatar="@{data.friendAvatar}" />

            <TextView
                android:id="@+id/tv_geek_result"
                style="@style/Text.16sp.333333"
                android:layout_marginEnd="@dimen/common_dp_60"
                android:layout_marginStart="@dimen/common_dp_6"
                android:background="@drawable/bg_conversation_msg_left"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center_vertical"
                android:paddingBottom="@dimen/dp_8"
                android:paddingEnd="12dp"
                android:paddingStart="12dp"
                android:paddingTop="@dimen/dp_8"
                android:text="@{data.convertContent}"
                bind:compoundDrawableStart="@{data.optionsIcon}" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/match_wrap"
            android:gravity="end"
            android:orientation="horizontal"
            android:visibility="@{data.sender?View.VISIBLE:View.GONE}">

            <LinearLayout
                android:id="@+id/ll_content"
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/common_dp_6"
                android:layout_marginStart="@dimen/common_dp_60"
                android:background="@drawable/bg_conversation_company"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingBottom="@dimen/dp_8"
                android:paddingEnd="@dimen/common_dp_12"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingTop="@dimen/dp_8"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toStartOf="@id/iv_sender_avatar"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    style="@style/Text.16sp.333333"
                    android:layout_gravity="center_vertical"
                    android:drawablePadding="@dimen/dp_4"
                    android:text="@{data.convertContent}"
                    bind:compoundDrawableEnd="@{data.optionsIcon}" />

            </LinearLayout>

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/iv_sender_avatar"
                android:layout_width="@dimen/conversation_msg_avatar_size"
                android:layout_height="@dimen/conversation_msg_avatar_size"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                bind:imgUrl="@{data.myAvatar}" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</layout>