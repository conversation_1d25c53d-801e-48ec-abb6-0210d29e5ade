<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match">

    <ImageView
        android:id="@+id/iv_picture"
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:background="@color/common_f1f3f6"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/common_Text.15sp.333333"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginStart="@dimen/common_dp_12"
        android:layout_marginTop="@dimen/common_dp_12"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:background="@color/common_f1f3f6"
        android:ellipsize="end"
        android:lineSpacingExtra="@dimen/dp_3"
        android:maxLines="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_picture" />

    <TextView
        android:id="@+id/tv_follow_number"
        style="@style/common_Text.12sp.767676"
        android:layout_marginTop="@dimen/common_dp_5"
        android:layout_marginEnd="@dimen/common_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_start_and_end_date"
        style="@style/common_Text.12sp.767676"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginStart="@dimen/common_dp_12"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:background="@color/common_f1f3f6"
        android:ellipsize="end"
        android:lines="1"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_follow_number"
        app:layout_constraintEnd_toStartOf="@id/tv_follow_number"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/v_line1"
        style="@style/common_Line.Horizontal"
        android:layout_marginTop="@dimen/common_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_start_and_end_date" />

    <TextView
        android:id="@+id/tv_venue"
        style="@style/common_Text.12sp.767676"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginStart="@dimen/common_dp_12"
        android:layout_marginTop="@dimen/common_dp_10"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:background="@color/common_f1f3f6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line1" />

    <TextView
        android:id="@+id/tv_address"
        style="@style/common_Text.12sp.767676"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginStart="@dimen/common_dp_12"
        android:layout_marginTop="@dimen/common_dp_5"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:background="@color/common_f1f3f6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_venue" />

    <View
        android:id="@+id/v_line2"
        style="@style/common_Line.Horizontal"
        android:layout_marginTop="@dimen/common_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_address" />

    <FrameLayout
        android:id="@+id/tv_school"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_54"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingEnd="@dimen/common_dp_12"
        app:layout_constraintTop_toBottomOf="@id/v_line2">

        <TextView
            style="@style/common_Text.14sp"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:background="@color/common_f1f3f6" />
    </FrameLayout>

    <View
        android:id="@+id/v_line3"
        style="@style/common_Line.Horizontal"
        android:layout_height="@dimen/common_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_school" />

    <FrameLayout
        android:id="@+id/tv_recruit_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_52"
        android:paddingStart="@dimen/common_dp_12"
        app:layout_constraintTop_toBottomOf="@id/v_line3">

        <TextView
            style="@style/common_Text.16sp"
            android:layout_width="80dp"
            android:layout_gravity="center_vertical"
            android:background="@color/common_f1f3f6" />
    </FrameLayout>

    <View
        android:id="@+id/v_line4"
        style="@style/common_Line.Horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_recruit_title" />

    <FrameLayout
        android:id="@+id/web_details"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/common_f1f3f6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line4" />

</androidx.constraintlayout.widget.ConstraintLayout>
