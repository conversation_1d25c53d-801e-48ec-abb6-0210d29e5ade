<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.positiontopkeywords.PositionTopKeywordsViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/ll_content"
        style="@style/match_match"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/position_top_title" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_keyword"
            style="@style/match_wrap" />

        <TextView
            style="@style/common_Text.12sp.767676"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginTop="@dimen/common_dp_12"
            android:layout_marginEnd="@dimen/common_dp_12"
            android:text="@string/position_top_keywords_note" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_0"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_save"
            style="@style/common_Button.Basic"
            android:layout_marginStart="@dimen/common_dp_30"
            android:layout_marginEnd="@dimen/common_dp_30"
            android:layout_marginBottom="@dimen/common_dp_30"
            android:onClick="@{onClickListener}"
            android:text="@string/save" />
    </LinearLayout>
</layout>