<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_f8f8f8"
        android:orientation="vertical">

        <LinearLayout
            style="@style/match_wrap"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp_4">

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/indicator_type"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/common_dp_28"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_filter"
                style="@style/common_Text.15sp.767676"
                android:layout_marginEnd="@dimen/dp_10"
                android:drawablePadding="@dimen/dp_4"
                android:drawableStart="@drawable/personal_ic_job_filter"
                android:onClick="@{onClickListener}"
                android:text="@string/enterprise_home_filter" />

        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_content"
            style="@style/match_match" />

    </LinearLayout>
</layout>