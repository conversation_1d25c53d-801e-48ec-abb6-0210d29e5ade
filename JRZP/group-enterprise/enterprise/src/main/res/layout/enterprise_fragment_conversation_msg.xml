<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.fragment.conversationmsg.ConversationMsgViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:showBackIcon="false"
      app:title="@string/enterprise_main_tab_message" />

    <include
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.msgListViewModel}" />

  </LinearLayout>
</layout>