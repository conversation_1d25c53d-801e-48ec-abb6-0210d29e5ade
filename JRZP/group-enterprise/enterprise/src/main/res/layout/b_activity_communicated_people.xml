<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools"
    >

  <data>
    <variable
        name="onClickListener"
        type="android.view.View.OnClickListener"
        />

    <variable
        name="viewModel"
        type="com.bxkj.enterprise.ui.activity.communicatedpeople.CommunicatedPeopleViewModel"
        />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:background="@drawable/bg_f4f4f4"
      android:orientation="vertical"
      >

    <com.bxkj.common.widget.CommonTitleBar
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        app:title="@string/b_communicated_people_title"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@drawable/bg_ffffff"
        android:layout_height="wrap_content"
        >

      <EditText
          android:id="@+id/et_search"
          style="@style/Text.14sp.333333"
          android:layout_width="match_parent"
          android:layout_marginEnd="@dimen/dp_8"
          android:singleLine="true"
          android:layout_marginStart="@dimen/dp_8"
          android:text="@={viewModel.searchContent}"
          android:imeOptions="actionSearch"
          android:background="@drawable/bg_f4f4f4_radius_4"
          android:drawablePadding="@dimen/dp_4"
          android:drawableStart="@drawable/ic_search"
          android:hint="@string/b_communicated_people_search_hint"
          android:paddingBottom="@dimen/dp_8"
          android:paddingEnd="@dimen/dp_12"
          android:paddingStart="@dimen/dp_12"
          android:paddingTop="@dimen/dp_8"
          />
      <LinearLayout
          android:id="@+id/ll_filter_bar"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:orientation="horizontal"
          android:paddingBottom="@dimen/dp_10"
          android:paddingEnd="@dimen/dp_8"
          android:paddingStart="@dimen/dp_8"
          android:paddingTop="@dimen/dp_10"
          >

        <TextView
            android:id="@+id/tv_filter_job"
            style="@style/Text.12sp.333333"
            android:onClick="@{onClickListener}"
            android:background="@drawable/bg_f4f4f4_radius_4"
            android:drawableEnd="@drawable/ic_expand"
            android:drawablePadding="@dimen/dp_2"
            android:paddingBottom="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_8"
            android:textColor="@color/cl_333333_to_fe6600_selector"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_2"
            android:text="@string/b_communicated_people_filter_job"
            />
        <TextView
            android:id="@+id/tv_filter_date"
            style="@style/Text.12sp.333333"
            android:onClick="@{onClickListener}"
            android:layout_marginStart="@dimen/dp_8"
            android:background="@drawable/bg_f4f4f4_radius_4"
            android:drawableEnd="@drawable/ic_expand"
            android:drawablePadding="@dimen/dp_2"
            android:textColor="@color/cl_333333_to_fe6600_selector"
            android:paddingBottom="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_8"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_2"
            android:text="@string/b_communicated_people_filter"
            />
        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="0dp"
            />
        <TextView
            android:drawablePadding="@dimen/dp_4"
            android:onClick="@{()->viewModel.toggleSort()}"
            android:text="@{viewModel.sort==0?@string/b_communicated_people_list_asc:@string/b_communicated_people_list_desc}"
            android:drawableEnd="@{viewModel.sort==0?@drawable/b_ic_list_desc:@drawable/b_ic_list_asc}"
            style="@style/Text.12sp.333333"
            />

      </LinearLayout>

    </LinearLayout>


    <include
        android:id="@+id/include_people_list"
        layout="@layout/include_mvvm_refresh_layout"
        bind:listViewModel="@{viewModel.peopleListViewModel}"
        />
  </LinearLayout>

</layout>