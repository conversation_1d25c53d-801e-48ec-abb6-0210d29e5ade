<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.myvideomanagement.MyVideoManagementViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:rightOptionClickListener="@{()->viewModel.openOrCloseEdit()}"
      app:rightText="@{viewModel.openEdit?@string/common_cancel:@string/common_edit}"
      app:title="@string/mine_video_management" />

    <FrameLayout
      android:id="@+id/fl_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />
  </LinearLayout>
</layout>