<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
        android:id="@+id/iv_date_tag"
        android:layout_width="@dimen/common_dp_5"
        android:layout_height="@dimen/common_dp_5"
        android:layout_marginTop="@dimen/dp_22"
        android:src="@drawable/shape_round_ff865d"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/common_Line.Vertical"
        android:layout_height="@dimen/common_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_date_tag"
        app:layout_constraintStart_toStartOf="@id/iv_date_tag"
        app:layout_constraintTop_toBottomOf="@id/iv_date_tag" />

    <TextView
        android:id="@+id/tv_work_time"
        style="@style/common_Text.14sp.888888"
        android:layout_marginStart="@dimen/common_dp_10"
        app:layout_constraintBottom_toBottomOf="@id/iv_date_tag"
        app:layout_constraintStart_toEndOf="@id/iv_date_tag"
        app:layout_constraintTop_toTopOf="@id/iv_date_tag" />

    <TextView
        android:id="@+id/tv_company_and_position"
        style="@style/Text.14sp.333333"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginTop="@dimen/common_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_work_time"
        app:layout_constraintTop_toBottomOf="@id/tv_work_time" />

    <TextView
        android:id="@+id/tv_job_responsibility_title"
        style="@style/common_Text.14sp.888888"
        android:layout_marginTop="@dimen/common_dp_10"
        android:text="@string/resume_details_job_responsibility"
        app:layout_constraintStart_toStartOf="@id/tv_company_and_position"
        app:layout_constraintTop_toBottomOf="@id/tv_company_and_position" />

    <TextView
        android:id="@+id/tv_job_responsibility"
        style="@style/common_Text.14sp.888888"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginTop="@dimen/common_dp_10"
        android:lineSpacingExtra="@dimen/common_dp_2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_job_responsibility_title"
        app:layout_constraintTop_toBottomOf="@id/tv_job_responsibility_title" />
</androidx.constraintlayout.widget.ConstraintLayout>