<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
        android:id="@+id/iv_selector"
        style="@style/common_wrap_wrap"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/common_dp_12"
        android:src="@drawable/bg_resume_receive_mailbox_item_selector" />

    <TextView
        android:id="@+id/tv_item"
        style="@style/common_Text.15sp.333333"
        android:layout_width="match_parent"
        android:layout_centerVertical="true"
        android:layout_marginBottom="@dimen/common_dp_16"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:layout_marginStart="@dimen/common_dp_8"
        android:layout_marginTop="@dimen/common_dp_16"
        android:layout_toEndOf="@id/iv_selector" />

    <View
        android:id="@+id/v_line"
        style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
        android:layout_alignParentBottom="true"
        app:layout_constraintBottom_toBottomOf="parent" />
</RelativeLayout>