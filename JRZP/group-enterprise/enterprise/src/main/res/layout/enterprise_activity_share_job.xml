<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.sharejob.ShareJobViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1">

      <com.google.android.material.appbar.AppBarLayout
        style="@style/match_wrap"
        android:background="@drawable/bg_ffffff"
        android:orientation="vertical"
        app:elevation="@dimen/dp_0">

        <com.google.android.material.appbar.CollapsingToolbarLayout
          style="@style/match_wrap"
          app:layout_scrollFlags="scroll|exitUntilCollapsed">

          <LinearLayout
            style="@style/match_wrap"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
              android:id="@+id/tv_option_tips"
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_24"
              android:drawableStart="@drawable/enterprise_ic_edit_job_success"
              android:drawablePadding="@dimen/dp_6"
              android:gravity="center_vertical" />

            <LinearLayout
              android:id="@+id/btn_share"
              style="@style/match_wrap"
              android:layout_marginStart="62dp"
              android:layout_marginTop="@dimen/dp_14"
              android:layout_marginEnd="62dp"
              android:background="@drawable/frame_fe6600_round"
              android:clipToPadding="false"
              android:gravity="center"
              android:onClick="@{()->viewModel.getShareInfo()}"
              android:orientation="horizontal"
              android:paddingTop="@dimen/dp_12"
              android:paddingBottom="@dimen/dp_12">

              <ImageView
                android:id="@+id/iv_share"
                style="@style/wrap_wrap"
                android:src="@drawable/enterprise_ic_share_job" />

              <TextView
                style="@style/Text.16sp.ff7647"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/enterprise_share_job_share" />
            </LinearLayout>

            <View
              style="@style/Line.Horizontal.Light"
              android:layout_height="@dimen/dp_8"
              android:layout_marginTop="@dimen/dp_24" />

          </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <LinearLayout
          style="@style/match_wrap"
          android:orientation="horizontal"
          android:paddingStart="@dimen/dp_14"
          android:paddingTop="@dimen/dp_10"
          android:paddingEnd="@dimen/dp_14"
          android:paddingBottom="@dimen/dp_10">

          <TextView
            style="@style/Text.ContentText"
            android:text="@string/enterprise_share_job_recommend_resume" />

          <Space
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1" />

          <TextView
            style="@style/Text.Tertiary"
            android:drawableStart="@drawable/enterprise_ic_refresh_recommend_resume"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center_vertical"
            android:onClick="@{()->viewModel.refreshRecommendResumeList()}"
            android:text="@string/enterprise_share_job_refresh" />

        </LinearLayout>

      </com.google.android.material.appbar.AppBarLayout>

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_recommend_resume"
        style="@style/match_match"
        bind:items="@{viewModel.recommendResumeList}"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <View style="@style/Line.Horizontal" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_56">

      <ImageView
        android:id="@+id/iv_all_selected"
        style="@style/wrap_wrap"
        bind:selected="@{viewModel.recommendResumeList.size()==viewModel.selectResumeList.size()}"
        android:layout_marginStart="@dimen/dp_14"
        android:onClick="@{()->viewModel.allSelect()}"
        android:src="@drawable/ic_invoice_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_all_selected"
        style="@style/Text.Tertiary.Dark"
        android:layout_marginStart="@dimen/dp_8"
        android:onClick="@{()->viewModel.allSelect()}"
        android:text="@string/enterprise_share_job_all_selected"
        app:layout_constraintBottom_toTopOf="@id/tv_selected_count"
        app:layout_constraintStart_toEndOf="@id/iv_all_selected"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

      <TextView
        android:id="@+id/tv_selected_count"
        style="@style/common_Text.10sp.999999"
        android:text="@{@string/enterprise_share_job_selected_count_format(viewModel.selectResumeList.size,viewModel.recommendResumeList.size)}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_all_selected"
        app:layout_constraintTop_toBottomOf="@id/tv_all_selected" />

      <TextView
        style="@style/Button.Basic.Round"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_22"
        android:layout_marginEnd="@dimen/dp_14"
        android:enabled="@{viewModel.selectResumeList.size()!=0}"
        android:onClick="@{()->viewModel.invite()}"
        android:text="@{@string/enterprise_select_resume_count_format(viewModel.inviteBalance)}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_all_selected"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

  </LinearLayout>
</layout>