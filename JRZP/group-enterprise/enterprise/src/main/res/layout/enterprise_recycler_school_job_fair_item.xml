<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="jobFairItem"
            type="com.bxkj.enterprise.data.SchoolJobFairItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingTop="@dimen/common_dp_12"
        android:paddingEnd="@dimen/common_dp_12"
        android:paddingBottom="@dimen/common_dp_10">

        <TextView
            android:id="@+id/tv_title"
            style="@style/common_Text.15sp.333333"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginEnd="@dimen/dp_18"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{jobFairItem.title}"
            app:layout_constraintEnd_toStartOf="@id/tv_sign_up"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_status"
            style="@style/common_Text.12sp.767676"
            android:layout_marginTop="@dimen/common_dp_4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_sign_up"
            style="@style/common_Text.14sp"
            android:background="@{jobFairItem.baomingKey==1?@drawable/frame_primary_radius_4:@drawable/frame_e8e8e8_radius_4}"
            android:enabled="@{jobFairItem.baomingKey==1}"
            android:paddingStart="@dimen/common_dp_16"
            android:paddingTop="@dimen/common_dp_4"
            android:paddingEnd="@dimen/common_dp_16"
            android:paddingBottom="@dimen/common_dp_4"
            android:text="@{jobFairItem.baomingKey==1?@string/school_recruit_sign_up:@string/school_recruit_sign_up_end}"
            android:textColor="@{jobFairItem.baomingKey==1?@color/common_49C280:@color/common_767676}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
