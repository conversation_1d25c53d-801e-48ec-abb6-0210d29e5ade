<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <LinearLayout
        android:id="@+id/ll_header"
        android:layout_width="match_parent"
        android:layout_height="74dp"
        android:gravity="center_vertical"
        android:paddingEnd="@dimen/common_dp_12"
        android:paddingStart="@dimen/common_dp_12">

        <include layout="@layout/enterprise_include_asterisk" />

        <TextView
            style="@style/common_Text.15sp.333333"
            android:layout_weight="1"
            android:text="@string/header" />

        <ImageView
            android:id="@+id/iv_header"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_marginEnd="@dimen/common_dp_10" />

        <ImageView
            style="@style/common_wrap_wrap"
            android:src="@drawable/common_ic_next" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

        <include layout="@layout/enterprise_include_asterisk" />

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/name" />

        <EditText
            android:id="@+id/et_name"
            style="@style/common_EditText.Basic.RightAlignment" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

        <include layout="@layout/enterprise_include_asterisk" />

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/hr_position" />

        <EditText
            android:id="@+id/et_position"
            style="@style/common_EditText.Basic.RightAlignment" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout
        style="@style/match_wrap"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingTop="@dimen/common_dp_16">

        <include layout="@layout/enterprise_include_asterisk" />

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/team_member_introduction" />

        <EditText
            android:id="@+id/et_introduction"
            android:layout_width="@dimen/common_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/common_dp_16"
            android:layout_marginEnd="@dimen/common_dp_12"
            android:layout_marginStart="@dimen/common_dp_10"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="top"
            android:hint="@string/team_member_introduction_hint"
            android:lines="5"
            android:textColorHint="@color/common_b5b5b5"
            android:textSize="15sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_0"
        android:layout_weight="1"
        android:background="@drawable/bg_f4f4f4" />
</LinearLayout>