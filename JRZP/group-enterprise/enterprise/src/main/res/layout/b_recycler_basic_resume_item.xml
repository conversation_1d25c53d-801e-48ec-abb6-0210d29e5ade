<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="name"
      type="String" />

    <variable
      name="avatar"
      type="String" />

    <variable
      name="resumeOnTop"
      type="Boolean" />

    <variable
      name="desc"
      type="String" />

    <variable
      name="expectJob"
      type="String[]" />

    <variable
      name="hasChat"
      type="Boolean" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_marginStart="@dimen/dp_8"
    android:layout_marginEnd="@dimen/dp_8"
    android:background="@drawable/bg_ffffff_radius_10"
    android:padding="@dimen/dp_16">

    <ImageView
      android:id="@+id/iv_select"
      style="@style/wrap_wrap"
      android:src="@drawable/enterprise_ic_cliam_company_selector"
      android:visibility="gone"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.17sp.000000.Bold"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginEnd="@dimen/dp_2"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{name}"
      app:layout_constrainedWidth="true"
      app:layout_constraintEnd_toStartOf="@id/tv_top_tag"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintHorizontal_chainStyle="packed"
      app:layout_constraintStart_toEndOf="@id/iv_select"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_goneMarginEnd="@dimen/dp_12"
      app:layout_goneMarginStart="@dimen/dp_0" />

    <ImageView
      android:id="@+id/tv_top_tag"
      style="@style/wrap_wrap"
      android:src="@drawable/b_ic_resume_top_tag"
      android:visibility="@{resumeOnTop?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toStartOf="@id/iv_avatar"
      app:layout_constraintStart_toEndOf="@id/tv_name"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_about"
      style="@style/Text.12sp.888888"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{desc}"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_expect_prefix"
      style="@style/Text.14sp.888888"
      android:text="@string/recommend_resume_expect_prefix"
      app:layout_constraintBottom_toBottomOf="@id/tv_conversation"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_about"
      app:layout_constraintTop_toTopOf="@id/tv_conversation" />

    <com.bxkj.common.widget.labellayout.LabelLayout
      style="@style/wrap_wrap"
      android:layout_marginEnd="@dimen/dp_12"
      app:itemBackground="@drawable/bg_f4f4f4_radius_4"
      app:itemMargin="@dimen/dp_4"
      app:itemPaddingLR="@dimen/dp_6"
      app:itemPaddingTB="@dimen/dp_1"
      app:itemTextColor="@color/cl_333333"
      app:itemTextSize="@dimen/dp_14"
      app:layout_constrainedWidth="true"
      app:layout_constraintBottom_toBottomOf="@id/tv_conversation"
      app:layout_constraintEnd_toStartOf="@id/tv_conversation"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintStart_toEndOf="@id/tv_expect_prefix"
      app:layout_constraintTop_toTopOf="@id/tv_conversation"
      app:maxShowTab="2"
      bind:items="@{expectJob}" />

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_52"
      android:layout_height="@dimen/dp_52"
      android:padding="0.5dp"
      app:layout_constraintEnd_toEndOf="@id/tv_conversation"
      app:layout_constraintStart_toStartOf="@id/tv_conversation"
      app:layout_constraintTop_toTopOf="parent"
      app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
      app:strokeColor="@color/common_f4f4f4"
      app:strokeWidth="@dimen/dp_1"
      bind:imgUrl="@{avatar}" />

    <TextView
      android:id="@+id/tv_conversation"
      style="@style/Text.12sp.FFFFFF"
      android:layout_width="70dp"
      android:layout_marginTop="@dimen/dp_8"
      android:background="@drawable/bg_fe6600_round"
      android:gravity="center"
      android:paddingTop="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{hasChat?@string/recommend_resume_chat:@string/recommend_resume_invite}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>