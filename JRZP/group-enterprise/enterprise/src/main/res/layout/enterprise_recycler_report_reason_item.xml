<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.ui.activity.reportreason.EnterprsieReportReasonItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingStart="@dimen/common_dp_14"
        android:paddingEnd="@dimen/common_dp_14">

        <TextView
            android:id="@+id/tv_title"
            style="@style/common_Text.16sp.333333.Bold"
            android:layout_marginTop="@dimen/common_dp_16"
            android:text="@{data.title}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            style="@style/common_wrap_wrap"
            android:src="@drawable/common_ic_next"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title" />

        <TextView
            style="@style/common_Text.12sp.888888"
            android:layout_marginTop="@dimen/common_dp_4"
            android:layout_marginBottom="@dimen/common_dp_16"
            android:text="@{data.desc}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <View
            style="@style/common_Line.Horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>