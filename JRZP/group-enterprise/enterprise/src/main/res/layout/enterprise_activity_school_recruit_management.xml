<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.schoolrecruitmanagement.SchoolRecruitManagementViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:rightText="@{viewModel.showSelectAll?@string/school_recruit_management_cancel:@string/school_recruit_management_delete}"
      app:show_right_text_bg="false"
      app:title="@string/school_recruit_management_title" />

    <net.lucode.hackware.magicindicator.MagicIndicator
      android:id="@+id/indicator"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_36" />

    <View style="@style/Line.Horizontal.Light" />

    <androidx.viewpager2.widget.ViewPager2
      android:id="@+id/vp_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      android:id="@+id/tv_publish"
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_16"
      android:onClick="@{()->viewModel.releasePreCheck()}"
      android:text="@string/school_recruit_management_publish"
      android:visibility="@{viewModel.showSelectAll?View.GONE:View.VISIBLE}" />
  </LinearLayout>
</layout>