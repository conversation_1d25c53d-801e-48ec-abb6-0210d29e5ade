<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.fragment.homev2.BusinessHomeViewModelV3" />
  </data>


  <com.scwang.smartrefresh.layout.SmartRefreshLayout
    android:id="@+id/refresh_layout"
    style="@style/match_match"
    app:srlEnableLoadMore="false">

    <LinearLayout
      style="@style/match_match"
      android:orientation="vertical">

      <LinearLayout
        android:id="@+id/ll_title_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@drawable/bg_ffdbc3"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
          style="@style/Text.24sp.333333"
          android:text="@string/enterprise_home_title" />

      </LinearLayout>

      <androidx.coordinatorlayout.widget.CoordinatorLayout
        style="@style/match_match"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
          style="@style/match_wrap"
          app:elevation="@dimen/dp_0"
          android:background="@drawable/bg_f8f8f8">

          <FrameLayout
            style="@style/match_wrap"
            android:visibility="@{viewModel.showPostJobTips?View.VISIBLE:View.GONE}"
            app:layout_scrollFlags="scroll">

            <View
              android:layout_width="match_parent"
              android:layout_height="@dimen/dp_30"
              android:background="@drawable/bg_ffdbc3_to_f4f4f4" />

            <ImageView
              android:id="@+id/iv_post_job"
              style="@style/match_wrap"
              android:layout_marginStart="@dimen/dp_8"
              android:layout_marginTop="@dimen/dp_8"
              android:layout_marginEnd="@dimen/dp_8"
              android:adjustViewBounds="true"
              android:onClick="@{onClickListener}"
              android:src="@drawable/enterprise_img_free_post_job" />

          </FrameLayout>

          <com.youth.banner.Banner
            android:id="@+id/banner_ad"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            android:background="@drawable/bg_f8f8f8"
            android:minHeight="@dimen/dp_0"
            android:src="@drawable/img_map_placeholder"
            app:banner_indicator_gravity="center"
            app:banner_indicator_height="@dimen/dp_2"
            app:banner_indicator_normal_color="@color/common_f6f6f6"
            app:banner_indicator_normal_width="@dimen/dp_6"
            app:banner_indicator_selected_color="@color/cl_ff7405"
            app:banner_indicator_selected_width="@dimen/dp_10"
            app:banner_indicator_space="@dimen/dp_2"
            app:banner_loop_time="5000"
            app:banner_radius="@dimen/dp_8"
            app:layout_scrollFlags="scroll" />

          <LinearLayout
            style="@style/match_wrap"
            android:visibility="@{viewModel.showPostJobTips?View.GONE:View.VISIBLE}"
            android:orientation="horizontal">

            <net.lucode.hackware.magicindicator.MagicIndicator
              android:id="@+id/indicator_job_list"
              android:layout_width="0dp"
              android:layout_height="38dp"
              android:layout_weight="1"
              android:minHeight="38dp"
              android:paddingStart="@dimen/dp_8"
              android:paddingEnd="@dimen/dp_8"
              app:layout_scrollFlags="scroll|exitUntilCollapsed" />

            <ImageView
              android:id="@+id/iv_rank"
              android:layout_width="38dp"
              android:layout_height="38dp"
              android:onClick="@{onClickListener}"
              android:scaleType="centerInside"
              android:src="@drawable/b_ic_resume_list_rank" />

          </LinearLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager2.widget.ViewPager2
          android:id="@+id/vp_content"
          style="@style/match_match"
          app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

      </androidx.coordinatorlayout.widget.CoordinatorLayout>

    </LinearLayout>

  </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</layout>