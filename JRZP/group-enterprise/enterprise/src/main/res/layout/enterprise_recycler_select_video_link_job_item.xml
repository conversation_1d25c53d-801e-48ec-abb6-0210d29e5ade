<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.enterprise.data.PositionItemBean" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingTop="@dimen/dp_18"
    android:paddingBottom="@dimen/dp_18">

    <ImageView
      android:id="@+id/iv_select"
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_18"
      android:src="@drawable/ic_select_selector"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.18sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_20"
      android:layout_marginEnd="@dimen/dp_18"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.name}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/iv_select"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.12sp.888888"
      android:layout_marginTop="@dimen/dp_4"
      android:text="@{data.edate1}"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      style="@style/Text.12sp.888888"
      android:layout_marginStart="@dimen/dp_20"
      android:text="@{@string/enterprise_select_video_link_job_views_count_format(data.count)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_time"
      app:layout_constraintStart_toEndOf="@id/tv_time" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>