<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.BeanUsageRecordBean" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginStart="@dimen/dp_8"
        android:background="@drawable/bg_ffffff_radius_6"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_8"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_12">

        <TextView
            style="@style/Text.16sp.333333"
            android:text="@{data.des}" />

        <TextView
            style="@style/Text.14sp.888888"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@{data.ecdate}" />
    </LinearLayout>
</layout>