<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.hredit.HREditViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/save"
      app:title="@string/enterprise_hr_edit_title" />

    <LinearLayout
      android:id="@+id/ll_avatar"
      android:layout_width="match_parent"
      android:layout_height="74dp"
      android:gravity="center_vertical"
      android:onClick="@{onClickListener}"
      android:paddingStart="@dimen/common_dp_12"
      android:paddingEnd="@dimen/common_dp_12">

      <include layout="@layout/enterprise_include_asterisk" />

      <TextView
        style="@style/common_Text.15sp.333333"
        android:layout_weight="1"
        android:text="@string/header" />

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_header"
        style="@style/roundedCornerImageStyle.Avatar"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginEnd="@dimen/common_dp_10"
        bind:imgUrl="@{viewModel.showAvatar}" />

      <ImageView
        style="@style/common_wrap_wrap"
        android:src="@drawable/common_ic_next" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/yui_name"
      style="@style/JrzpCommonListItemView"
      app:yui_content="@={viewModel.hrInfo.name}"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/enterprise_hr_edit_name" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/yui_gender"
      style="@style/JrzpCommonListItemView"
      android:onClick="@{onClickListener}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.hrInfo.sexText}"
      app:yui_content_type="text"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/enterprise_hr_edit_gender" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/yui_position"
      style="@style/JrzpCommonListItemView"
      app:yui_content="@={viewModel.hrInfo.position}"
      app:yui_content_hint="@string/enterprise_hr_edit_position"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/enterprise_hr_edit_position" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/yui_phone"
      style="@style/JrzpCommonListItemView"
      android:inputType="phone"
      app:yui_content="@={viewModel.hrInfo.mobile}"
      app:yui_content_hint="@string/enterprise_hr_edit_phone"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/enterprise_hr_edit_phone" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/yui_email"
      style="@style/JrzpCommonListItemView"
      android:inputType="textEmailAddress"
      app:yui_content="@={viewModel.hrInfo.email}"
      app:yui_content_hint="@string/enterprise_hr_edit_email"
      app:yui_content_type="edit"
      app:yui_title="@string/enterprise_hr_edit_email" />

  </LinearLayout>
</layout>