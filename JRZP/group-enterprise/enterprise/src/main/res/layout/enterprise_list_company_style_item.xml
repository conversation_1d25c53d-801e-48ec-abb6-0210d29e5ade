<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.enterprise.data.CompanyStyleItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:background="@drawable/frame_f4f4f4_radius_4_dotted"
        android:orientation="vertical">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_photo"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:scaleType="centerCrop"
            android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="w,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/ImageView.Radius4"
            bind:imgUrl="@{data.realImgUrl}" />

        <ImageView
            android:id="@+id/iv_delete"
            style="@style/wrap_wrap"
            android:src="@drawable/ic_delete_photo"
            android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:scaleType="center"
            android:src="@drawable/ic_news_photo_add"
            android:visibility="@{data.isAddItem?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="w,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>