<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.ui.activity.beanmall.BeanMallFunctionItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ffffff_radius_10"
        android:paddingBottom="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:imgUrl="@{data.icon}" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.16sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@{data.title}"
            app:layout_constraintBottom_toTopOf="@id/tv_desc"
            app:layout_constraintEnd_toStartOf="@id/iv_more"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="@id/iv_icon"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_desc"
            style="@style/Text.14sp.888888"
            android:layout_width="0dp"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.desc}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/iv_icon"
            app:layout_constraintEnd_toStartOf="@id/iv_more"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <ImageView
            android:id="@+id/iv_more"
            style="@style/wrap_wrap"
            android:src="@drawable/common_ic_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>