<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.recruitmentdata.RecruitmentDataViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:title="@string/b_recruitment_data_title" />

    <FrameLayout style="@style/match_wrap">

      <ImageView
        style="@style/match_wrap"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@drawable/b_ic_recruitment_data_top_bg" />

      <TextView
        style="@style/Text.22sp.333333"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_14"
        android:text="@string/b_recruitment_data_top_text" />

    </FrameLayout>

    <!--    <LinearLayout-->
    <!--      style="@style/match_wrap"-->
    <!--      android:background="@drawable/b_bg_recruitment_data_indicator"-->
    <!--      android:orientation="horizontal">-->

    <!--      <net.lucode.hackware.magicindicator.MagicIndicator-->
    <!--        android:id="@+id/indicator_job_list"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="@dimen/dp_40"-->
    <!--        android:layout_marginStart="@dimen/dp_14"-->
    <!--        android:layout_marginEnd="@dimen/dp_14"-->
    <!--        android:layout_weight="1" />-->
    <!--    </LinearLayout>-->

    <LinearLayout
      style="@style/match_wrap"
      android:layout_height="@dimen/dp_40"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_12">

      <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/indicator_data_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

      <androidx.legacy.widget.Space
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

      <TextView
        android:id="@+id/tv_date_filter"
        style="@style/Text.10sp.333333"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:drawableEnd="@drawable/b_ic_recruitment_data_date_expand"
        android:drawablePadding="@dimen/dp_4"
        android:onClick="@{()->viewModel.showDateFilterDialog()}"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_4"
        android:text="@{viewModel.dateFilterText}" />

    </LinearLayout>

    <View style="@style/Line.Horizontal.Light" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_recruitment_data"
      style="@style/match_wrap"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginTop="@dimen/dp_14"
      android:layout_marginEnd="@dimen/dp_14"
      android:visibility="@{viewModel.showChart?View.GONE:View.VISIBLE}"
      bind:items="@{viewModel.recruitmentDataList}" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginTop="@dimen/dp_14"
      android:layout_marginEnd="@dimen/dp_14"
      android:orientation="vertical"
      android:visibility="@{viewModel.showChart?View.VISIBLE:View.GONE}">

      <RadioGroup
        android:id="@+id/rb_month_data_cate"
        style="@style/match_wrap"
        android:orientation="horizontal">

        <RadioButton
          android:id="@+id/tv_cate_1"
          style="@style/Text.10sp.FE6600"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:checked="true"
          android:gravity="center"
          android:paddingTop="@dimen/dp_4"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/b_recruitment_data_tab1"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/tv_cate_2"
          style="@style/Text.10sp.FE6600"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:gravity="center"
          android:paddingTop="@dimen/dp_4"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/b_recruitment_data_tab2"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/tv_cate_3"
          style="@style/Text.10sp.FE6600"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:gravity="center"
          android:paddingTop="@dimen/dp_4"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/b_recruitment_data_tab3"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/tv_cate_4"
          style="@style/Text.10sp.FE6600"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:gravity="center"
          android:paddingTop="@dimen/dp_4"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/b_recruitment_data_tab4"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/tv_cate_5"
          style="@style/Text.10sp.FE6600"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:gravity="center"
          android:paddingTop="@dimen/dp_4"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/b_recruitment_data_tab5"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

      </RadioGroup>

      <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/chart_month_bulletin"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginTop="@dimen/dp_12" />

    </LinearLayout>

    <TextView
      style="@style/Text.15sp.333333"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_14"
      android:text="@string/b_recruitment_data_recommend" />

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_14"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_14">

      <LinearLayout
        android:id="@+id/ll_ad_1"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@drawable/b_ic_insufficient_number_of_chat"
        android:onClick="@{onClickListener}"
        android:orientation="vertical">

        <TextView
          style="@style/Text.14sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginTop="@dimen/dp_8"
          android:text="@string/b_recruitment_data_chat_ad_title" />

        <TextView
          style="@style/Text.10sp.888888"
          android:layout_marginStart="@dimen/dp_8"
          android:text="@string/b_recruitment_data_chat_ad_content" />

      </LinearLayout>

      <LinearLayout
        android:id="@+id/ll_ad_2"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_weight="1"
        android:background="@drawable/b_ic_insufficient_of_resume_package"
        android:onClick="@{onClickListener}"
        android:orientation="vertical">

        <TextView
          style="@style/Text.14sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginTop="@dimen/dp_8"
          android:text="@string/b_recruitment_data_resume_ad_title" />

        <TextView
          style="@style/Text.10sp.888888"
          android:layout_marginStart="@dimen/dp_8"
          android:text="@string/b_recruitment_data_resume_ad_content" />

      </LinearLayout>

      <LinearLayout
        android:id="@+id/ll_ad_3"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_weight="1"
        android:background="@drawable/b_ic_insufficient_of_exposures"
        android:onClick="@{onClickListener}"
        android:orientation="vertical">

        <TextView
          style="@style/Text.14sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginTop="@dimen/dp_8"
          android:text="@string/b_recruitment_data_exposures_ad_title" />

        <TextView
          style="@style/Text.10sp.888888"
          android:layout_marginStart="@dimen/dp_8"
          android:text="@string/b_recruitment_data_exposures_ad_content" />

      </LinearLayout>
    </LinearLayout>

    <ImageView
      android:id="@+id/iv_upgrad_member"
      style="@style/match_wrap"
      android:layout_margin="@dimen/dp_14"
      android:adjustViewBounds="true"
      android:onClick="@{onClickListener}"
      android:scaleType="fitXY"
      android:src="@drawable/b_ic_upgrade_member"
      android:visibility="@{viewModel.showUpgradeMember?View.VISIBLE:View.GONE}" />

  </LinearLayout>
</layout>