<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <TextView
        style="@style/Text.Tips"
        android:text="@string/resume_receive_select_mailbox_tips" />

    <LinearLayout
        style="@style/common_Layout.InfoItem.Padding12"
        android:background="@drawable/bg_ffffff">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/resume_receive_setting" />

        <TextView
            style="@style/common_Text.15sp.767676"
            android:layout_weight="1"
            android:gravity="end"
            android:text="@string/resume_receive_forward_to_mailbox" />

    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_0"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_mailbox"
            style="@style/match_wrap"
            android:background="@drawable/bg_ffffff"
            android:overScrollMode="never" />
    </FrameLayout>

    <View style="@style/common_Line.Horizontal" />

    <FrameLayout
        android:id="@+id/fl_add_mailbox"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_52"
        android:background="@drawable/bg_ffffff">

        <TextView
            style="@style/common_Text.14sp.888888"
            android:layout_gravity="center"
            android:drawableStart="@drawable/ic_resume_receive_mailbox_add"
            android:gravity="center"
            android:text="@string/resume_receive_add_mailbox" />
    </FrameLayout>
</LinearLayout>