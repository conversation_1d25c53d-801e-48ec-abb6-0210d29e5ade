package com.bxkj.enterprise.weight

import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import com.bxkj.common.util.CheckUtils
import com.bxkj.ecommon.widget.dialogfragment.BaseDialogFragment
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.PositionPreviewData

/**
 * @Project: ejrzp
 * @Description:
 * @author:45457
 * @date: 2020/7/13
 * @version: V1.0
 */
class ChatInviteSendResumeDialog constructor(
  private val jobDetails: PositionPreviewData,
  private var mOnOptionClickListener: OnOptionClickListener? = null
) : BaseDialogFragment() {

  override fun getRootViewId(): Int {
    return R.layout.enterprise_dialog_chat_invite_send_resume
  }

  override fun initView() {
    rootView.findViewById<TextView>(R.id.tv_job).text = jobDetails.name
    rootView.findViewById<TextView>(R.id.tv_location).text =
      if (CheckUtils.isNullOrEmpty(jobDetails.quName)) "未完善" else jobDetails.quName
    rootView.findViewById<TextView>(R.id.tv_salary).text = jobDetails.money

    rootView.findViewById<TextView>(R.id.tv_select_other).setOnClickListener {
      mOnOptionClickListener?.onSelectOther()
      dismiss()
    }

    rootView.findViewById<TextView>(R.id.tv_confirm).setOnClickListener {
      mOnOptionClickListener?.confirm()
      dismiss()
    }

    rootView.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
      dismiss()
    }
  }

  interface OnOptionClickListener {
    fun onSelectOther()

    fun confirm()
  }

  fun show(manager: FragmentManager) {
    this.show(manager, TAG)
  }

  companion object {
    private const val TAG = "ChatInviteSendResumeDia"
  }
}