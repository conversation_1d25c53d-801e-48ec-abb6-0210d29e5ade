package com.bxkj.enterprise.ui.activity.selectrealtejob

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.throttleAction
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.drawable
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.databinding.EnterpriseActivitySelectRelateJobBinding
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation

/**
 * @Project: VideoRecruitment
 * @Description: 选择视频关联简历
 * @author:45457
 * @date: 2020/6/18
 * @version: V1.0
 */
@Route(path = SelectRelateJobNavigation.PATH)
class SelectRelateJobActivity :
  BaseDBActivity<EnterpriseActivitySelectRelateJobBinding, SelectRelateJobViewModel>(),
  OnClickListener {

  private var mSelectVideoLinkJobListAdapter: SelectRelateJobListAdapter? = null

  override fun getViewModelClass(): Class<SelectRelateJobViewModel> =
    SelectRelateJobViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_select_relate_job

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupSelectVideoLinkJobListAdapter()

    subscribeViewModelEvent()

    viewModel.start()
  }

  override fun onResume() {
    super.onResume()
    viewModel.refresh()
  }

  private fun subscribeViewModelEvent() {
    viewModel.resultSelectedItemCommand.observe(this, Observer {
      val resultIntent = Intent()
      resultIntent.putParcelableArrayListExtra(
        SelectRelateJobNavigation.EXTRA_SELECTED_ITEMS,
        it
      )
      setResult(Activity.RESULT_OK, resultIntent)
      finish()
    })

    viewModel.toPostJobCommand.observe(this, Observer {
      PostJobNavigation.navigate(PostJobNavigation.TYPE_FULL_TIME, PostJobNavigation.NEXT_BIND_VIDEO).start()
    })

    viewModel.searchKeyword.observe(this, Observer {
      viewBinding.etKeyword.throttleAction(1000) { _ ->
        viewModel.startSearch()
      }
    })

    viewModel.setupSelectedJobEvent.observe(this, Observer {
      intent.getParcelableArrayListExtra<PositionItemBean>(SelectRelateJobNavigation.EXTRA_SELECTED_ITEMS)
        ?.let {
          mSelectVideoLinkJobListAdapter?.selectedItems = it
        }
    })
  }

  private fun setupSelectVideoLinkJobListAdapter() {
    mSelectVideoLinkJobListAdapter = SelectRelateJobListAdapter(this, viewModel)
    val videoLinkJobList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    videoLinkJobList.layoutManager = LinearLayoutManager(this)
    videoLinkJobList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          drawable.divider_f4f4f4_8
        ), LinearLayoutManager.VERTICAL
      )
    )

    viewModel.jobListViewModel.setAdapter(mSelectVideoLinkJobListAdapter)
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_select_all) {
        mSelectVideoLinkJobListAdapter?.switchSelectAll()
      }
    }
  }

}