package com.bxkj.enterprise.ui.activity.collection;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import com.bxkj.common.util.DensityUtils;
import com.bxkj.enterprise.R;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.collection
 * @Description:
 * @TODO: TODO
 * @date 2018/8/13
 */
public class CollectionTabLayoutAdapter extends CommonNavigatorAdapter {

    private ViewPager mViewPager;

    public CollectionTabLayoutAdapter(ViewPager viewPager) {
        mViewPager = viewPager;
    }

    @Override
    public int getCount() {
        return mViewPager.getAdapter().getCount();
    }

    @Override
    public IPagerTitleView getTitleView(Context context, int index) {
        ColorTransitionPagerTitleView colorTransitionPagerTitleView = new ColorTransitionPagerTitleView(context);
        colorTransitionPagerTitleView.setNormalColor(ContextCompat.getColor(mViewPager.getContext(), R.color.cl_333333));
        colorTransitionPagerTitleView.setSelectedColor(ContextCompat.getColor(mViewPager.getContext(), R.color.common_49C280));
        colorTransitionPagerTitleView.setText(mViewPager.getAdapter().getPageTitle(index));
        colorTransitionPagerTitleView.setOnClickListener(view -> mViewPager.setCurrentItem(index));
        return colorTransitionPagerTitleView;
    }

    @Override
    public IPagerIndicator getIndicator(Context context) {
        LinePagerIndicator linePagerIndicator = new LinePagerIndicator(context);
        linePagerIndicator.setMode(LinePagerIndicator.MODE_EXACTLY);
        linePagerIndicator.setColors(ContextCompat.getColor(context,R.color.common_49C280));
        linePagerIndicator.setLineWidth(DensityUtils.dp2px(context, 10));
        linePagerIndicator.setLineHeight(DensityUtils.dp2px(context, 3));
        return linePagerIndicator;
    }
}
