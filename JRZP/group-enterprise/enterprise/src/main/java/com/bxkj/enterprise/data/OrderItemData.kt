package com.bxkj.enterprise.data

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/10/12
 * @version V1.0
 */
data class OrderItemData(
    var id: Int,
    var type: Int,
    var content: String,
    var price: String,
    var time: String,
    var ispay: Int,
    var iseffect: Int,
    var invoiceID: Int,
    var isCanInvoice: Int,
    var typeName: String
) {
    companion object {
        const val PAID = 1
        const val UNPAID = 2
    }

    fun getOrderType(): String {
        var orderType = ""
        when (type) {
            1 -> {
                orderType = "购买会员"
            }
            2 -> {
                orderType = "续费"
            }
            3 -> {
                orderType = "购买积分"
            }
            4 -> {
                orderType = "购买短信"
            }
            5 -> {
                orderType = "RPO服务"
            }
            6 -> {
                orderType = "账户充值"
            }
            7 -> {
                orderType = "简历置顶"
            }
            8 -> {
                orderType = "购买招聘豆"
            }
            9 -> {
                orderType = "简历加油包"
            }
        }
        return orderType
    }

    fun getOrderTitle(): String {
        var orderTitle = ""
        when (type) {
            1 -> {
                orderTitle = if (content == "401") {
                    "购买半年会员"
                } else {
                    "购买一年会员"
                }
            }
            2 -> {
                orderTitle = if (content == "401") {
                    "续费半年会员"
                } else {
                    "续费一年会员"
                }
            }
            3 -> {
                orderTitle = "购买" + content + "积分"
            }
            4 -> {
                orderTitle = "购买" + content + "条短信"
            }
            8 -> {
                orderTitle = "购买" + content + "招聘豆"
            }
            9 -> {
                orderTitle = "购买" + content + "简历加油包"
            }
        }
        return orderTitle
    }
}