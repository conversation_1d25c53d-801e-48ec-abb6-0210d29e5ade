package com.bxkj.enterprise.ui.activity.positiontop;

import androidx.annotation.NonNull;

import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.PositionTopCityItemData;
import com.bxkj.enterprise.data.PositionTopData;
import com.bxkj.enterprise.data.PositionTopKeywordData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.positiontop
 * @Description: PositionTop
 * @TODO: TODO
 * @date 2018/3/27
 */

public class PositionTopPresenter extends PositionTopContract.Presenter {

  private static final String TAG = PositionTopPresenter.class.getSimpleName();
  private BusinessApi mBusinessApi;

  @Inject
  public PositionTopPresenter(BusinessApi businessApi) {
    mBusinessApi = businessApi;
  }

  @Override
  void getPositionTopInfo(int userId, int positionId) {
    mView.showLoading();
    mBusinessApi.getPositionTopInfo(userId, positionId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(BaseResponse baseResponse) {
          mView.getPositionTopInfoSuccess((PositionTopData) baseResponse.getData());
        }

        @Override
        protected void onError(RespondThrowable respondThrowable) {
          mView.hiddenLoading();
          mView.onError(respondThrowable.getErrMsg());
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }

        @Override
        public void onComplete() {
          mView.hiddenLoading();
        }
      });
  }

  @Override
  void getPositionTopCost(int userId, int location) {
    mBusinessApi.getPositionTopCost(userId, location)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(BaseResponse baseResponse) {
          mView.getPositionTopCostSuccess(CheckUtils.cast(baseResponse.getData()));
        }

        @Override
        protected void onError(RespondThrowable respondThrowable) {
          mView.onError(respondThrowable.getErrMsg());
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }
      });
  }

  @Override
  void getTopDaysBalance(int userId) {
    mBusinessApi.getPositionTopDaysBalance(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(@NonNull BaseResponse baseResponse) {
          mView.getTopDayBalanceSuccess(Integer.parseInt(baseResponse.getMsg()));
        }

        @Override
        protected void onError(@NonNull RespondThrowable respondThrowable) {
          mView.onError(respondThrowable.getErrMsg());
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }
      });
  }

  @Override
  void getToppedCityList(int userId, int positionId) {
    mBusinessApi.getPositionTopCityList(userId, positionId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(@NonNull BaseResponse baseResponse) {
          List<PositionTopCityItemData> toppedArea = CheckUtils.cast(baseResponse.getDataList());
          if (toppedArea.isEmpty()) {
            return;
          }
          StringBuilder toppedAreaText = new StringBuilder();
          for (int i = 0; i < toppedArea.size(); i++) {
            toppedAreaText.append(toppedArea.get(i).getCityName()).append(",");
          }
          mView.getToppedAreaSuccess(
            toppedAreaText.substring(0, toppedAreaText.lastIndexOf(",")));
        }

        @Override
        protected void onError(@NonNull RespondThrowable respondThrowable) {
          if (respondThrowable.getErrCode() != 30001) {
            mView.onError(respondThrowable.getErrMsg());
          }
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }
      });
  }

  @Override
  void addPositionTop(int userId, int location, int positionId, int days, int paymentMethod,
    int city, List<PositionTopKeywordData> keywords) {
    if (CheckUtils.isNullOrEmpty(keywords)) {
      mView.onError("未填写关键词");
      return;
    }
    StringBuilder keywordsText = new StringBuilder();
    for (int i = 0; i < keywords.size(); i++) {
      keywordsText.append(keywords.get(i).getKeyword()).append("|");
    }
    showLoading();
    mBusinessApi.addPositionTop(userId, location, positionId, days, paymentMethod, city,
        keywordsText.substring(0, keywordsText.lastIndexOf("|")))
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(new CustomObserver() {
        @Override
        protected void onSuccess(@NonNull BaseResponse baseResponse) {
          hiddenLoading();
          mView.addPositionTopSuccess();
        }

        @Override
        protected void onError(@NonNull RespondThrowable respondThrowable) {
          hiddenLoading();
          if (respondThrowable.getErrCode() == 30012 || respondThrowable.getErrCode() == 30010) {
            mView.positionTopFailed(respondThrowable.getErrMsg());
          } else {
            mView.onError(respondThrowable.getErrMsg());
          }
        }

        @Override
        public void onSubscribe(Disposable d) {
          mCompositeDisposable.add(d);
        }
      });
  }
}
