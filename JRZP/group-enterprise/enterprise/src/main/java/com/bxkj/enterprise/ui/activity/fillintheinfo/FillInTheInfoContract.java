package com.bxkj.enterprise.ui.activity.fillintheinfo;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.EnterpriseInfoData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.fillintheinfo
 * @Description: FillTheInfo
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface FillInTheInfoContract {
    interface View extends BaseView {

        void updateEnterpriseInfoSuccess(String companyName);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {

        abstract void updateEnterpriseInfo(EnterpriseInfoData enterpriseInfoData);
    }
}
