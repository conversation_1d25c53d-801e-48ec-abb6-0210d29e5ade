package com.bxkj.enterprise.ui.fragment.inviterecord

import android.os.Bundle
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.enterprise.data.InviteRecordItemData
import com.bxkj.enterprise.data.source.InviteFriendsRepo
import javax.inject.Inject

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.fragment.inviterecord
 * @Description:
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
class InviteRecordViewModel @Inject constructor(private val mInviteFriendsRepo: InviteFriendsRepo) :
  BaseViewModel() {

  val listViewModel = RefreshListViewModel()

  init {
    listViewModel.refreshLayoutViewModel.enableLoadMore(false)
  }

  fun start(params: Bundle?) {
    params?.let {
      val pageType = params.get(InviteRecordFragment.EXTRA_PAGE_TYPE)
      if (pageType == InviteRecordFragment.TYPE_COMPLETED) {
        setupCompletePage()
      } else {
        setupUndonePage()
      }
      listViewModel.refresh()
    }
  }

  private fun setupCompletePage() {
    listViewModel.setOnLoadDataListener {
      mInviteFriendsRepo.getInviteRecordList(getSelfUserID(), object :
          ResultListCallBack<List<InviteRecordItemData>> {
        override fun onSuccess(data: List<InviteRecordItemData>?) {
          listViewModel.reset(data)
        }

        override fun onNoMoreData() {
          listViewModel.noMoreData()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          listViewModel.loadError()
        }
      })
    }
  }

  private fun setupUndonePage() {
    listViewModel.setOnLoadDataListener {
      mInviteFriendsRepo.getInviteUndoneList(getSelfUserID(), object :
          ResultListCallBack<List<InviteRecordItemData>> {
        override fun onSuccess(data: List<InviteRecordItemData>?) {
          listViewModel.reset(data)
        }

        override fun onNoMoreData() {
          listViewModel.noMoreData()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          listViewModel.loadError()
        }
      })
    }
  }

  override fun onCleared() {
    super.onCleared()
    mInviteFriendsRepo.clear()
  }
}