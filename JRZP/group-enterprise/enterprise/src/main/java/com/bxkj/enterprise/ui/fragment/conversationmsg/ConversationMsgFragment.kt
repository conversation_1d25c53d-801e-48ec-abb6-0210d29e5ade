package com.bxkj.enterprise.ui.fragment.conversationmsg

import android.content.Intent
import android.view.View
import androidx.core.app.NotificationManagerCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.constants.PushConstants
import com.bxkj.common.data.EmptyData
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.ecommon.base.EBaseDBFragment
import com.bxkj.ecommon.util.recyclerutil.EmptyViewBinder
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.EUnreadMsgGroupData
import com.bxkj.enterprise.data.UnreadVideoMsgData
import com.bxkj.enterprise.databinding.EnterpriseFragmentConversationMsgBinding
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentActivity
import com.bxkj.enterprise.ui.activity.notice.NoticeActivity
import com.bxkj.enterprise.ui.activity.systemmsg.SystemMsgActivity
import com.bxkj.enterprise.ui.activity.videosignupmsg.VideoSignUpMsgActivity
import com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuRecyclerView

/**
 * @Project: gzgk
 * @Description: 消息
 * @author:45457
 * @date: 2020/7/4
 * @version: V1.0
 */
//@Route(path = EnterpriseMsgNavigation.PATH)
class ConversationMsgFragment :
    EBaseDBFragment<EnterpriseFragmentConversationMsgBinding, ConversationMsgViewModel>() {

    companion object {
        const val TO_MSG_NOTICE_CODE = 1
        const val TO_SUB_MES_CODE = 2
        const val TO_SEW_ME_CODE = 3
        const val TO_SYSTEM_MSG_CODE = 4
        const val TO_UNREAD_VIDEO_MSG_CODE = 5

        const val TO_CONVERSATION_CODE = 6

        const val EXTRA_CHAT_ID = "CHAT_ID"

        fun newInstance(): ConversationMsgFragment {
            return ConversationMsgFragment()
        }
    }

    override fun getViewModelClass(): Class<ConversationMsgViewModel> =
        ConversationMsgViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_fragment_conversation_msg

    override fun initPage() {
        dataBinding.viewModel = viewModel

        subscribeReceiveNewMsg()

        subscribeViewModelEvent()

        setupMsgListAdapter()
    }

    override fun onResume() {
        super.onResume()
        initImmersionBar()
        viewModel.start()
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            initImmersionBar()
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.checkNoticePermissionCommand.observe(this) {
            checkNoticePermission()
        }
    }

    private fun initImmersionBar() {
        statusBarManager.titleBar(dataBinding.titleBar).statusBarDarkFont(true, 0.4f)
            .navigationBarColor(R.color.common_white).init()
    }

    private fun subscribeReceiveNewMsg() {
        addDisposable(
            RxBus.get().toObservable(RxBus.Message::class.java)
                .subscribe {
                    if (it.code == RxMsgCode.ACTION_RECEIVE_NEW_MSG) {
                        NotificationManagerCompat.from(parentActivity)
                            .cancel(PushConstants.MSG_TYPE_CONVERSATION_MSG.toInt())
                        viewModel.start()
                    }
                }
        )
    }

    private fun checkNoticePermission() {
        viewModel.setupNoticePermissionStatus(
            NotificationManagerCompat.from(parentActivity).areNotificationsEnabled()
        )
    }

    private fun setupMsgListAdapter() {
        val msgListAdapter = MultiTypeAdapter(parentActivity, null)
        msgListAdapter.register(
            EUnreadMsgGroupData::class.java,
            DefaultViewBinder<EUnreadMsgGroupData>(
                R.layout.enterprise_layout_message_header,
                BR.data,
                true
            ).apply {
                setOnItemClickListener(object :
                    DefaultViewBinder.OnItemClickListener<EUnreadMsgGroupData> {
                    override fun onItemClicked(
                        v: View,
                        position: Int,
                        item: EUnreadMsgGroupData
                    ) {
                        when (v.id) {
                            R.id.tv_notice_permission -> {
                                SystemUtil.toSettingPage(parentActivity)
                            }
                            R.id.fl_notice -> startActivityForResult(
                                NoticeActivity.newIntent(parentActivity),
                                TO_MSG_NOTICE_CODE
                            )
                            R.id.fl_system ->
                                startActivityForResult(
                                    SystemMsgActivity.newIntent(parentActivity),
                                    TO_SYSTEM_MSG_CODE
                                )
                        }
                    }
                }, R.id.tv_notice_permission, R.id.fl_notice, R.id.fl_see, R.id.fl_system)
            }
        )

        val videoUnReadMsgBinder = DefaultViewBinder<UnreadVideoMsgData>(
            R.layout.enterprise_recycler_unread_video_msg_item,
            BR.data,
            true
        ).apply {
            setOnItemClickListener(object :
                DefaultViewBinder.OnItemClickListener<UnreadVideoMsgData> {
                override fun onItemClicked(v: View, position: Int, item: UnreadVideoMsgData) {
                    when (v.id) {
                        R.id.fl_msg_type -> {
                            showSelectMsgInitiatorPopup()
                        }
                        else -> {
                            startActivityForResult(
                                VideoSignUpMsgActivity.newIntent(parentActivity),
                                TO_UNREAD_VIDEO_MSG_CODE
                            )
                        }
                    }
                }
            }, R.id.fl_msg_type)
        }
        msgListAdapter.register(UnreadVideoMsgData::class.java, videoUnReadMsgBinder)

        msgListAdapter.register(
            ContactBodyBean::class.java,
            DefaultViewBinder<ContactBodyBean>(
                R.layout.chat_recycler_contact_item,
                BR.data,
                true
            ).apply {
                setOnItemClickListener(object :
                    DefaultViewBinder.OnItemClickListener<ContactBodyBean> {
                    override fun onItemClicked(v: View, position: Int, item: ContactBodyBean) {
                        when (v.id) {
                            R.id.tv_up -> {
                                dataBinding.root.findViewById<SwipeMenuRecyclerView>(R.id.recycler_content).smoothCloseMenu()
                                viewModel.upOrCancelUpMsg(item)
                            }
                            R.id.tv_delete -> {
                                dataBinding.root.findViewById<SwipeMenuRecyclerView>(R.id.recycler_content).smoothCloseMenu()
                                viewModel.deleteMsg(item)
                            }
                            else -> {
                                startActivityForResult(
                                    BusinessChatContentActivity.newIntent(
                                        parentActivity,
                                        ChatRole.BUSINESS,
                                        item.userID
                                    ).apply {
                                        putExtra(EXTRA_CHAT_ID, item.id)
                                    }, TO_CONVERSATION_CODE
                                )
                            }
                        }
                    }
                }, R.id.tv_up, R.id.tv_delete)
            }
        )

        msgListAdapter.register(
            EmptyData::class.java,
            EmptyViewBinder()
        )

        val msgList = dataBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        msgList.layoutManager = LinearLayoutManager(parentActivity)
        viewModel.msgListViewModel.setAdapter(msgListAdapter)
    }

    private fun showSelectMsgInitiatorPopup() {
        val msgInitiators = resources.getStringArray(R.array.conversation_msg_type)
        MenuPopup.Builder(parentActivity)
            .setData(msgInitiators)
            .setSelected(viewModel.getUnreadVideoMsg().msgType)
            .setOnItemClickListener { _, position ->
                viewModel.setupMsgInitiator(position, msgInitiators[position])
            }.build().show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        viewModel.handleActivityResult(requestCode, resultCode, data)
    }

}