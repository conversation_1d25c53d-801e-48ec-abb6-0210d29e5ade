package com.bxkj.enterprise.mvp.presenter;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.mvp.contract.CheckCompanyInfoFillContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.presenter
 * @Description: CheckCompanyInfoFill
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CheckCompanyInfoFillPresenter extends CheckCompanyInfoFillContract.Presenter {

    private static final String TAG = CheckCompanyInfoFillPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public CheckCompanyInfoFillPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void checkCompanyInfoFill(int userId) {
        mView.showLoading();
        mBusinessApi.checkCompanyInfoFill(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.companyInfoFill();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        if (respondThrowable.isNetworkError()) {
                            mView.onError(respondThrowable.getErrMsg());
                        } else {
                            mView.companyInfoNotFill(respondThrowable.getErrMsg());
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
