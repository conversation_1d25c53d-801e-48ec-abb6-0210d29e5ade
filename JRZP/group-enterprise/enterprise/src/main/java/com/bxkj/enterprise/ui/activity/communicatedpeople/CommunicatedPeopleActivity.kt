package com.bxkj.enterprise.ui.activity.communicatedpeople

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.common.util.recyclerutil.TextItemDecoration
import com.bxkj.common.util.recyclerutil.TextItemDecoration.SectionItemCallback
import com.bxkj.common.widget.DropDownMenuView
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup
import com.bxkj.common.widget.filterpopup.FilterUtils
import com.bxkj.common.widget.filterpopup.FilterView
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.CommunicatedPeopleData
import com.bxkj.enterprise.databinding.BActivityCommunicatedPeopleBinding
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2
import java.util.Calendar

/**
 * Description:
 * Author:Sanjin
 * Date:2024/3/8
 **/
@Route(path = CommunicatedPeopleNavigation.PATH)
class CommunicatedPeopleActivity :
  BaseDBActivity<BActivityCommunicatedPeopleBinding, CommunicatedPeopleViewModel>(),
  OnClickListener {

  private var filterDropDownPopup: DropDownPopup? = null
  private var jobListDropDownMenu: DropDownMenuView? = null
  private var dateFilterView: FilterView? = null

  override fun getViewModelClass(): Class<CommunicatedPeopleViewModel> =
    CommunicatedPeopleViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_communicated_people

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    viewBinding.etSearch.setOnEditorActionListener { v, actionId, event ->
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        viewModel.startSearch()
      }
      return@setOnEditorActionListener false
    }

    subscribeViewModelEvent()

    setupPeopleListAdapter()

    initFilterDropDownPopup()

    viewModel.start()
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.tv_filter_job) {
        filterDropDownPopup?.showItemAsDropDown(0)
      } else {
        filterDropDownPopup?.showItemAsDropDown(1)
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.onlineJobList.observe(this, Observer {
      jobListDropDownMenu?.setData(it)
    })
  }

  private fun initFilterDropDownPopup() {
    jobListDropDownMenu = DropDownMenuView(this).apply {
      setOnItemClickListener { _, position ->
        filterDropDownPopup?.close()
        viewModel.filterByJob(position)
      }
    }

    val dateOptions = getResArray(R.array.b_communicated_people_date_filter_options)
    dateFilterView = FilterView.Builder(this)
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .setOnFilterConfirmListener {
        filterDropDownPopup?.close()
        viewModel.filterByDate(it[FilterOptionData.PUBLISH_DATE].getOrDefault())
      }
      .build()
      .apply {
        setData(
          listOf(
            FilterOptionsGroup(
              FilterOptionData.PUBLISH_DATE,
              FilterUtils.parseFilterOptions(*dateOptions)
            )
          )
        )
      }

    filterDropDownPopup = DropDownPopup(this, viewBinding.llFilterBar).apply {
      addContentViews(jobListDropDownMenu, dateFilterView)
      setOnItemExpandStatusChangeListener { index, opened ->
        if (index == 0) {
          viewBinding.tvFilterJob.isSelected = opened
        } else {
          viewBinding.tvFilterDate.isSelected = opened
        }
      }
    }
  }

  private fun setupPeopleListAdapter() {
    val peopleListAdapter = SimpleDBListAdapter<CommunicatedPeopleData>(
      this, R.layout.b_recycler_communicated_people_item
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          data.get(position)?.let {
            startActivity(
              ApplicantResumeDetailActivityV2.newIntent(
                this@CommunicatedPeopleActivity,
                it.resID,
                it.userID
              )
            )
          }
        }
      })
    }

    viewBinding.includePeopleList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(this@CommunicatedPeopleActivity)
      addItemDecoration(
        TextItemDecoration.Builder()
          .setTextColor(R.color.cl_333333)
          .setTextHeight(dip(32))
          .setTextSize(14)
          .setNormalDividerHeight(dip(8))
          .setPaddingStart(dip(8).toFloat())
          .setTextTitleCreateHandler(object : SectionItemCallback {
            override fun getSectionHeader(position: Int): String {
              val date = TimeUtils.string2Date(
                peopleListAdapter.data[position].editDate,
                "yyyy-MM-dd"
              )
              var datePattern = "MM月dd日"
              if (Calendar.getInstance().get(Calendar.YEAR) != Calendar.getInstance()
                  .apply { time = date }
                  .get(Calendar.YEAR)
              ) {
                datePattern = "yyyy年MM月dd日"
              }
              return TimeUtils.formatDate(date, datePattern)
            }

            override fun isSection(previousPosition: Int, position: Int): Boolean {
              val data = peopleListAdapter.data
              return TimeUtils.string2Date(
                data[previousPosition].editDate,
                "yyyy-MM-dd"
              ).after(
                TimeUtils.string2Date(
                  data[position].editDate, "yyyy-MM-dd"
                )
              )
            }
          })
          .build()
      )
    }
    viewModel.peopleListViewModel.setAdapter(peopleListAdapter)
  }

  companion object {

    fun newIntent(context: Context): Intent {
      return Intent(context, CommunicatedPeopleActivity::class.java)
    }
  }
}