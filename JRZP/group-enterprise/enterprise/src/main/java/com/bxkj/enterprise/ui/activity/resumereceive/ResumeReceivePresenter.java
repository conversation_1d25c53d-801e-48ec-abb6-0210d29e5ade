package com.bxkj.enterprise.ui.activity.resumereceive;


import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.ResumeReceiveMailboxItemData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.resumereceive
 * @Description: ResumeReceive
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ResumeReceivePresenter extends ResumeReceiveContract.Presenter {

    private static final String TAG = ResumeReceivePresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public ResumeReceivePresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void getResumeReceiveMailboxList(int userId) {
        mBusinessApi.getResumeReceiveMailboxList(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getResumeReceiveMailboxListSuccess((List<ResumeReceiveMailboxItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30002) {
                            mView.noResumeReceiveMailBoxList();
                        } else {
                            mView.getResumeReceiveListError();
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void deleteAndAddResumeReceiveMailbox(int jobOrTemplate, int jobOrTemplateId, List<ResumeReceiveMailboxItemData> receiveMailboxItemDataList) {
        Observable.just(receiveMailboxItemDataList)
                .flatMap((Function<List<ResumeReceiveMailboxItemData>, ObservableSource<BaseResponse>>) resumeReceiveMailboxItemData -> {
                    StringBuffer emailBuffer = new StringBuffer();
                    for (int i = 0; i < resumeReceiveMailboxItemData.size(); i++) {
                        emailBuffer.append(resumeReceiveMailboxItemData.get(i).getEmail()).append(",");
                    }
                    return mBusinessApi.addResumeReceiveMailbox(jobOrTemplate, jobOrTemplateId, emailBuffer.substring(0, emailBuffer.lastIndexOf(",")));
                }).compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.deleteAndAddResumeReceiveMailboxSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
