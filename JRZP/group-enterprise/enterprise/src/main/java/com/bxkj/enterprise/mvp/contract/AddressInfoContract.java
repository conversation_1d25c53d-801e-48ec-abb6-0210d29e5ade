package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: AddressId
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface AddressInfoContract {
    interface View extends BaseView {
        void getAddressInfoSuccess(AreaOptionsData areaOptionsData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getAddressInfoByAddressName(int addressType, String addressName);
    }
}
