package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.ecommon.util.map.LngLat;
import com.bxkj.enterprise.ui.activity.selectaddress.AddressData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 企業信息
 * @TODO: TODO
 * @date 2018/7/20
 */
public class EnterpriseInfoData implements Parcelable {

  /**
   * id : 69692
   * uid : 0
   * name : 杭州滨兴科技有限公司
   * name2 :
   * logo :
   * address : 浦沿街道
   * phone : 0571-12345678
   * lxr : 刘双锋
   * fax : null
   * qq : null
   * Info : 商铺推广服务，网站设计与制作等
   * proid : 7
   * proName : 国有企业
   * tradeid : 5
   * tradeName : 互联网·电子商务
   * sizeid : 7
   * sizeName : 200-499人
   * comUrl : null
   * province : 28009
   * provinceName : 浙江
   * city : 28590
   * cityName : 杭州
   * county : 28615
   * countyName : 滨江区
   * town : 28616
   * townName : 浦沿街道
   * count : 0
   * date : null
   * coordinate : null
   * traffic : null
   */

  private int id;
  private int uid;
  private String name;
  private String name2;
  private String logo;
  private String address;
  private String phone;
  private String lxr;
  private Object fax;
  private String qq;
  private String Info;
  private int proid;
  private String proName;
  private int tradeid;
  private String tradeName;
  private int sizeid;
  private String sizeName;
  private Object comUrl;
  private int province;
  private String provinceName;
  private int city;
  private String cityName;
  private int county;
  private String countyName;
  private int town;
  private String townName;
  private int count;
  private Object date;
  private String coordinate;
  private Object traffic;

  public static EnterpriseInfoData getEmpty(int userId) {
    EnterpriseInfoData emptyInfo = new EnterpriseInfoData();
    emptyInfo.setUid(userId);
    if (!CheckUtils.isNullOrEmpty(UserUtils.getUserRegisterPhone())) {
      emptyInfo.setPhone(UserUtils.getUserRegisterPhone());
    }
    return emptyInfo;
  }

  public EnterpriseInfoData() {
  }

  protected EnterpriseInfoData(Parcel in) {
    id = in.readInt();
    uid = in.readInt();
    name = in.readString();
    name2 = in.readString();
    logo = in.readString();
    address = in.readString();
    phone = in.readString();
    lxr = in.readString();
    Info = in.readString();
    proid = in.readInt();
    proName = in.readString();
    tradeid = in.readInt();
    tradeName = in.readString();
    sizeid = in.readInt();
    sizeName = in.readString();
    province = in.readInt();
    provinceName = in.readString();
    city = in.readInt();
    cityName = in.readString();
    county = in.readInt();
    countyName = in.readString();
    town = in.readInt();
    townName = in.readString();
    count = in.readInt();
  }

  public static final Creator<EnterpriseInfoData> CREATOR = new Creator<EnterpriseInfoData>() {
    @Override
    public EnterpriseInfoData createFromParcel(Parcel in) {
      return new EnterpriseInfoData(in);
    }

    @Override
    public EnterpriseInfoData[] newArray(int size) {
      return new EnterpriseInfoData[size];
    }
  };

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName2() {
    return name2;
  }

  public void setName2(String name2) {
    this.name2 = name2;
  }

  public String getLogo() {
    return logo;
  }

  public void setLogo(String logo) {
    this.logo = logo;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getLxr() {
    return lxr;
  }

  public void setLxr(String lxr) {
    this.lxr = lxr;
  }

  public Object getFax() {
    return fax;
  }

  public void setFax(Object fax) {
    this.fax = fax;
  }

  public String getQq() {
    return qq;
  }

  public void setQq(String qq) {
    this.qq = qq;
  }

  public String getInfo() {
    return Info;
  }

  public void setInfo(String Info) {
    this.Info = Info;
  }

  public int getProid() {
    return proid;
  }

  public void setProid(int proid) {
    this.proid = proid;
  }

  public String getProName() {
    return proName;
  }

  public void setProName(String proName) {
    this.proName = proName;
  }

  public int getTradeid() {
    return tradeid;
  }

  public void setTradeid(int tradeid) {
    this.tradeid = tradeid;
  }

  public String getTradeName() {
    return tradeName;
  }

  public void setTradeName(String tradeName) {
    this.tradeName = tradeName;
  }

  public int getSizeid() {
    return sizeid;
  }

  public void setSizeid(int sizeid) {
    this.sizeid = sizeid;
  }

  public String getSizeName() {
    return sizeName;
  }

  public void setSizeName(String sizeName) {
    this.sizeName = sizeName;
  }

  public Object getComUrl() {
    return comUrl;
  }

  public void setComUrl(Object comUrl) {
    this.comUrl = comUrl;
  }

  public int getProvince() {
    return province;
  }

  public void setProvince(int province) {
    this.province = province;
  }

  public String getProvinceName() {
    return provinceName;
  }

  public void setProvinceName(String provinceName) {
    this.provinceName = provinceName;
  }

  public int getCity() {
    return city;
  }

  public void setCity(int city) {
    this.city = city;
  }

  public String getCityName() {
    return cityName;
  }

  public void setCityName(String cityName) {
    this.cityName = cityName;
  }

  public int getCounty() {
    return county;
  }

  public void setCounty(int county) {
    this.county = county;
  }

  public String getCountyName() {
    return countyName;
  }

  public void setCountyName(String countyName) {
    this.countyName = countyName;
  }

  public int getTown() {
    return town;
  }

  public void setTown(int town) {
    this.town = town;
  }

  public String getTownName() {
    return townName;
  }

  public void setTownName(String townName) {
    this.townName = townName;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public Object getDate() {
    return date;
  }

  public void setDate(Object date) {
    this.date = date;
  }

  public String getCoordinate() {
    return coordinate;
  }

  public void setCoordinate(String coordinate) {
    this.coordinate = coordinate;
  }

  public Object getTraffic() {
    return traffic;
  }

  public void setTraffic(Object traffic) {
    this.traffic = traffic;
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel parcel, int i) {
    parcel.writeInt(id);
    parcel.writeInt(uid);
    parcel.writeString(name);
    parcel.writeString(name2);
    parcel.writeString(logo);
    parcel.writeString(address);
    parcel.writeString(phone);
    parcel.writeString(lxr);
    parcel.writeString(Info);
    parcel.writeInt(proid);
    parcel.writeString(proName);
    parcel.writeInt(tradeid);
    parcel.writeString(tradeName);
    parcel.writeInt(sizeid);
    parcel.writeString(sizeName);
    parcel.writeInt(province);
    parcel.writeString(provinceName);
    parcel.writeInt(city);
    parcel.writeString(cityName);
    parcel.writeInt(county);
    parcel.writeString(countyName);
    parcel.writeInt(town);
    parcel.writeString(townName);
    parcel.writeInt(count);
  }

  public AreaOptionsData getProvinceData() {
    return new AreaOptionsData(province, provinceName);
  }

  public AreaOptionsData getCityData() {
    return new AreaOptionsData(city, cityName);
  }

  public AreaOptionsData getAreaData() {
    return new AreaOptionsData(county, countyName);
  }

  public AreaOptionsData getStreetData() {
    return new AreaOptionsData(town, townName);
  }

  public LngLat getLngLat() {
    if (!CheckUtils.isNullOrEmpty(coordinate)) {
      String[] stringLngLat = coordinate.split(",");
      return new LngLat(Double.parseDouble(stringLngLat[0]), Double.parseDouble(stringLngLat[1]));
    }
    return null;
  }

  /**
   * 设置地址信息
   */
  public void setAddressData(AddressData addressData) {
    this.province = addressData.getProvinceId();
    this.provinceName = addressData.getProvinceName();
    this.city = addressData.getCityId();
    this.cityName = addressData.getCityName();
    this.county = addressData.getAreaId();
    this.countyName = addressData.getAreaName();
    this.town = addressData.getStreetId();
    this.townName = addressData.getStreetName();
  }
}
