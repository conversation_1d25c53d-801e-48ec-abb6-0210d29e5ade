package com.bxkj.enterprise.ui.activity.schoolrecruiteditor

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.Observer
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.applyCustomConfig
import com.bxkj.common.util.kotlin.toDate
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.JobItem
import com.bxkj.enterprise.databinding.EnterpriseActivitySchoolRecruitEditorBinding
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.SchoolRecruitJobActivity
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.SchoolRecruitJobNavigation
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation
import com.bxkj.personal.ui.activity.editrichtext.EditRichTextNavigation
import java.text.SimpleDateFormat
import java.util.*

/**
 *
 * @author: sanjin
 * @date: 2022/4/18
 */
class SchoolRecruitEditorActivity :
    BaseDBActivity<EnterpriseActivitySchoolRecruitEditorBinding, SchoolRecruitEditorViewModel>() {

    companion object {

        private const val TO_EDIT_CONTENT_CODE = 1

        private const val EXTRA_SCHOOL_RECRUIT_ID = "SCHOOL_RECRUIT_ID"

        fun newIntent(context: Context, schoolRecruitId: Int = 0): Intent {
            return Intent(context, SchoolRecruitEditorActivity::class.java)
                .apply {
                    putExtra(EXTRA_SCHOOL_RECRUIT_ID, schoolRecruitId)
                }
        }
    }

    private val editJobLauncher =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) {
            if (it.resultCode == RESULT_OK) {
                it.data?.let { data ->
                    val resultJobList =
                        data.getParcelableArrayListExtra<JobItem>(SchoolRecruitJobNavigation.EXTRA_JOB_LIST)
                    resultJobList?.let {
                        viewModel.setJobList(resultJobList)
                    }
                }
            }
        }


    override fun getViewModelClass(): Class<SchoolRecruitEditorViewModel> =
        SchoolRecruitEditorViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_school_recruit_editor

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        viewBinding.titleBar.setRightOptionClickListener {
            viewModel.save()
        }

        subscribeViewModelEvent()

        viewModel.start(getIntentSchoolRecruitId())
    }

    private fun subscribeViewModelEvent() {
        viewModel.showExpirationPickerCommand.observe(this) {
            SystemUtil.hideSoftKeyboard(this)
            TimePickerBuilder(this) { date, _ ->
                val expiration =
                    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date)
                viewModel.setExpiration(expiration)
            }.setType(booleanArrayOf(true, true, true, false, false, false))
                .setDate(
                    if (it.isNullOrBlank()) Calendar.getInstance()
                        .apply { add(Calendar.MONTH, 3) } else Calendar.getInstance()
                        .apply {
                            time = it.toDate("yyyy-MM-dd")
                        })
                .setRangDate(
                    Calendar.getInstance(), Calendar.getInstance()
                        .apply {
                            add(Calendar.YEAR, 2)
                            set(Calendar.MONTH, 6)
                        })
                .applyCustomConfig().build().show()
        }

        viewModel.toEditSchoolRecruitJobCommand.observe(this, Observer {
            editJobLauncher.launch(SchoolRecruitJobActivity.newIntent(this, it))
        })

        viewModel.toEditContentCommand.observe(this, Observer {
            EditRichTextNavigation.create(
                getString(R.string.school_recruit_editor_details),
                getString(R.string.school_recruit_editor_details_hint),
                it
            ).startForResult(this, TO_EDIT_CONTENT_CODE)
        })

        viewModel.showMemberPrivilegeTipsCommand.observe(this) {
            ActionDialog.Builder()
                .setContent(it)
                .setConfirmText("升级会员")
                .setOnConfirmClickListener {
                    MemberCenterWebNavigation.create().start()
                }.build().show(supportFragmentManager)
        }

        viewModel.editSuccessEvent.observe(this) {
            RxBus.get()
                .post(RxBus.Message.fromCode(RxMsgCode.ACTION_SCHOOL_RECRUIT_INFO_CHANGE_SUCCESS))
            finish()
        }

        viewModel.showFailedMsgCommand.observe(this, Observer {
            TipsDialog().setTitle("审核失败原因")
                .setContent(if (it.isNullOrBlank()) "请联系客服" else it)
                .show(supportFragmentManager)
        })
    }

    private fun getIntentSchoolRecruitId(): Int {
        return intent.getIntExtra(EXTRA_SCHOOL_RECRUIT_ID, 0)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == TO_EDIT_CONTENT_CODE && resultCode == RESULT_OK && data != null) {
            val resultContent = data.getStringExtra(EditRichTextNavigation.EXTRA_RESULT_CONTENT)
            viewModel.setContent(resultContent)
        }
    }

}