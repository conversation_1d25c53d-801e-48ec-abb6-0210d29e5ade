package com.bxkj.enterprise.ui.activity.jobfairregistration;

import android.content.Context;
import androidx.annotation.NonNull;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.BR;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.RecruitInfoItemData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.jobfairregistration
 * @Description:
 * @TODO: TODO
 * @date 2019/6/16
 */
public class RecruitInfoListAdapter extends SuperAdapter<RecruitInfoItemData> {
    public RecruitInfoListAdapter(Context context, int layoutId) {
        super(context, layoutId);
    }

    @Override
    protected void convert(@NonNull SuperViewHolder holder, int viewType, RecruitInfoItemData recruitInfoItemData, int position) {
        holder.bind(BR.recruitInfoItem, recruitInfoItemData);
        setOnChildClickListener(position, holder.findViewById(R.id.iv_edit));
    }
}
