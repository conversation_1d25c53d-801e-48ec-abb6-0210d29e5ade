package com.bxkj.enterprise.ui.activity.invitationinterview

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.enterprise.ui.activity.interviewremark.InterviewRemarkActivity
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/3/16
 */
class InviteInterviewViewModel @Inject constructor(
    private val mMyJobRepo: MyJobRepo
) : BaseViewModel() {

    val inviteUserName = MutableLiveData<String>()

    val interviewJobInfo = MutableLiveData<PositionItemBean>()

    val contactMethod = MutableLiveData<Int>().apply { value = 0 }

    val inviteSuccessEvent = LiveEvent<Void>()

    //面试时间
    val interviewTime = MutableLiveData<String>()

    //面试地点
    val interviewAddress = MutableLiveData<String>()

    //面试HR
    val interviewHR = MutableLiveData<String>()

    //手机号
    val contactMobileNumber = MutableLiveData<String>()

    //区号
    val contactPhoneAreaCode = MutableLiveData<String>()

    //电话号
    val contactPhone = MutableLiveData<String>()

    //备注
    val remark = MutableLiveData<String>()

    private var openSmsNotice = false

    private var inviteJobId: Int = 0
    private var inviteResumeId: Int = 0

    //选中的备注
    private val selectedRemarkTips = ArrayList<String>()

    //额外的备注
    private var otherRemark: String = ""

    //面试类型
    private var interviewType: Int = InviteInterviewNavigation.INTERVIEW_NORMAL

    fun start(jobId: Int, resumeId: Int, resumeUserName: String, interviewType: Int) {
        inviteJobId = jobId
        inviteResumeId = resumeId

        this.interviewType = interviewType

        inviteUserName.value = resumeUserName

        viewModelScope.launch {
            mMyJobRepo.getOnlineJobInfo(getSelfUserID(), jobId)
                .handleResult({
                    it?.let {
                        interviewJobInfo.value = it
                        interviewAddress.value = it.shiName + it.quName + it.jieName + it.address
                        interviewHR.value = it.hrLxr
                        contactMobileNumber.value = it.hrMobile
                    }
                }, {
                    interviewJobInfo.value = PositionItemBean()
                })
        }
    }

    fun setupInterviewTime(time: String) {
        interviewTime.value = time
    }

    /**
     * 打开关闭短信通知
     */
    fun switchContactMethod(method: Int) {
        contactMethod.value = method
    }

    /**
     * 开关短信通知
     */
    fun openSmsNotice(open: Boolean) {
        openSmsNotice = open
    }

    fun getSelectedRemarkTips(): List<String> {
        return selectedRemarkTips
    }

    fun getOtherRemark(): String {
        return otherRemark
    }

    fun sendInvite() {
        var contactNumber = ""
        if (interviewTime.value.isNullOrBlank()) {
            showToast("请选择面试时间")
            return
        }
        if (interviewType == InviteInterviewNavigation.INTERVIEW_NORMAL && interviewAddress.value.isNullOrBlank()) {
            showToast("请填写面试地址")
            return
        }
        if (interviewHR.value.isNullOrBlank()) {
            showToast("请填写联系人")
            return
        }
        if (contactMethod.value == 0) {
            if (contactMobileNumber.value.isNullOrBlank()) {
                showToast("请填写联系手机")
                return
            } else {
                contactNumber = contactMobileNumber.value.getOrDefault()
            }
        } else {
            if (contactPhoneAreaCode.value.isNullOrBlank() || contactPhone.value.isNullOrBlank()) {
                showToast("请填写联系电话")
                return
            } else {
                contactNumber =
                    "${contactPhoneAreaCode.value.getOrDefault()}-${contactPhone.value.getOrDefault()}"
            }
        }

        val strRemark = if (interviewType == InviteInterviewNavigation.INTERVIEW_NORMAL) {
            "经过我公司HR的初步筛选，认为你与我们的职位要求很匹配，现诚邀你来我公司面谈。请准时出席，${remark.value.getOrDefault()}如时间有变化也尽快与我们联系。"
        } else {
            "经过我公司HR的初步筛选，认为你与我们的职位要求很匹配，现诚邀你进行视频面试。如接受面试请保持面试时间APP在线，以免错过面试。"
        }


        viewModelScope.launch {
            showLoading()
            mMyJobRepo.inviteInterview(
                getSelfUserID(),
                inviteJobId,
                inviteResumeId,
                interviewTime.value.getOrDefault(),
                interviewAddress.value.getOrDefault(),
                interviewHR.value.getOrDefault(),
                contactNumber,
                openSmsNotice,
                strRemark,
                interviewType
            ).handleResult({
                inviteSuccessEvent.call()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == InviteInterviewActivity.TO_EDIT_REMARK_CODE && resultCode == Activity.RESULT_OK && data != null) {
            selectedRemarkTips.clear()
            data.getStringArrayListExtra(InterviewRemarkActivity.REMARK_TIPS)?.let {
                selectedRemarkTips.addAll(it)
            }
            data.getStringExtra(InterviewRemarkActivity.OTHER_REMARK)?.let {
                otherRemark = it
            }
            val remarkText = selectedRemarkTips.appendItem()
            remark.value =
                if (remarkText.isBlank()) otherRemark else "${selectedRemarkTips.appendItem()},${otherRemark}"
        }
    }

}