package com.bxkj.enterprise.ui.fragment.collection;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.bxkj.common.base.BaseListFragment;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.api.parameters.GetReceivedResumeListParameters;
import com.bxkj.enterprise.data.ResumeItemData;
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2;
import com.bxkj.enterprise.ui.fragment.receivedresume.ReceiveResumeListContract;
import com.bxkj.enterprise.ui.fragment.receivedresume.ReceiveResumeListPresenter;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.collection
 * @Description:
 * @TODO: TODO
 * @date 2018/8/13
 */
public class ECollectionFragment extends BaseListFragment
  implements CollectionContract.View, ReceiveResumeListContract.View {

  @Inject
  CollectionPresenter mCollectionPresenter;
  @Inject
  ReceiveResumeListPresenter mReceiveResumeListPresenter;

  private static final String PAGE_TAG = "page_tag";
  public static final int TAG_COLLECTION = 1;
  public static final int TAG_DOWNLOAD = 2;

  private CollectionListAdapter mCollectionListAdapter;
  private GetReceivedResumeListParameters mGetReceivedResumeListParameters;

  private int mPageTag;

  public static ECollectionFragment newInstance(int tag) {
    Bundle args = new Bundle();
    ECollectionFragment fragment = new ECollectionFragment();
    args.putInt(PAGE_TAG, tag);
    fragment.setArguments(args);
    return fragment;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mCollectionPresenter);
    presenters.add(mReceiveResumeListPresenter);
    return presenters;
  }

  @Override
  public int getLayoutId() {
    return R.layout.enterprise_fragment_collection;
  }

  @Override
  public void initPage() {
    super.initPage();
    mPageTag = getArguments().getInt(PAGE_TAG);
    if (mPageTag == TAG_DOWNLOAD) {
      mGetReceivedResumeListParameters = new GetReceivedResumeListParameters();
      mGetReceivedResumeListParameters.setResumeTypes("3,4");
    }
    mCollectionListAdapter = new CollectionListAdapter(getContext(), null,
      R.layout.enterprise_recycler_collection_resume_item);
    mCollectionListAdapter.setOnItemClickListener((view, position) -> {
      if (view.getId() == R.id.iv_delete) {
        new EActionDialog.Builder()
          .setContent(getString(R.string.collection_delete_confirm))
          .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            mCollectionPresenter.deleteCollection(getUserId(),
              mCollectionListAdapter.getData().get(position).getId(), position);
          }).build().show(getChildFragmentManager(), EActionDialog.TAG);
      } else {
        ResumeItemData resumeItemData = mCollectionListAdapter.getData().get(position);
        startActivity(
          ApplicantResumeDetailActivityV2.newIntent(getActivity(), resumeItemData.getId(),
            resumeItemData.getUbInfo().getUid(), 0, resumeItemData.getJaid()));
      }
    });
    getRecyclerView().setLayoutManager(new LinearLayoutManager(getContext()));
    getRecyclerView().addItemDecoration(
      new RecycleViewDivider(getContext(), LinearLayoutManager.VERTICAL, DensityUtils
        .dp2px(getContext(), 8), getMColor(R.color.common_f4f4f4), true));
    getRecyclerView().setAdapter(mCollectionListAdapter);
  }

  @Override
  protected void fetchData() {
    if (mPageTag == TAG_COLLECTION) {
      mCollectionPresenter.getCollectionResumeList(getUserId(),
        getRefreshLayoutManager().getCurrentPage(), ECommonApiConstants.DEFAULT_PAGE_SIZE);
    } else {
      mReceiveResumeListPresenter.getReceiveResumeList(getUserId(),
        mGetReceivedResumeListParameters, getRefreshLayoutManager().getCurrentPage(),
        ECommonApiConstants.DEFAULT_PAGE_SIZE);
    }
  }

  @Override
  public void getResumeListSuccess(List<ResumeItemData> resumeItemDataList) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    if (getRefreshLayoutManager().currentFirstPage()) {
      mCollectionListAdapter.reset(resumeItemDataList);
      return;
    }
    mCollectionListAdapter.addAll(resumeItemDataList);
  }

  @Override
  public void deleteCollectionSuccess(int position) {
    mCollectionListAdapter.removeAt(position);
  }
}
