package com.bxkj.enterprise.ui.activity.joinmembership;

import com.bxkj.common.di.module.ApplicationModule;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.MemberServicesData;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.joinmembership
 * @Description: JoinMemberShip
 * @TODO: TODO
 * @date 2018/3/27
 */

public class JoinMemberShipPresenter extends JoinMemberShipContract.Presenter {

    private static final String TAG = JoinMemberShipPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;
    private int mUserId;

    @Inject
    public JoinMemberShipPresenter(@Named(ApplicationModule.USER_ID) int userId, BusinessApi businessApi) {
        mUserId = userId;
        mBusinessApi = businessApi;
    }

    @Override
    void getMemberServicesList() {
        mBusinessApi.getMemberServicesList()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getMemberServicesListSuccess((List<MemberServicesData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void createVipOrder(int vipOrderType, int vipOrderLevel, int price) {
        mView.showLoading();
        mBusinessApi.createVipOrder(mUserId, vipOrderType, vipOrderLevel)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.createOrderSuccess(MTextUtils.hexToString(baseResponse.getMsg()), price);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        mView.hiddenLoading();
                    }
                });
    }
}
