package com.bxkj.enterprise.ui.activity.positiontopkeywords

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.enterprise.R
import com.bxkj.enterprise.api.EnterpriseApiConstants
import com.bxkj.enterprise.data.PositionTopKeywordData
import com.bxkj.enterprise.data.source.MyJobRepo
import javax.inject.Inject

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.positiontopkeywords
 * @Description:
 * <AUTHOR>
 * @date 2019/10/26
 * @version V1.0
 */
class PositionTopKeywordsViewModel @Inject constructor(private val mMyJobRepo: MyJobRepo) :
    BaseViewModel() {

    private var mTopCity: Int = 0
    private var mTopDays: Int = 0
    val initKeywords = MutableLiveData<List<PositionTopKeywordData>>()
    val backCommend = LiveEvent<Void>()

    fun start(intent: Intent?) {
        intent?.let {
            mTopCity = intent.getIntExtra(PositionTopKeywordsActivity.EXTRA_TOP_CITY, 0)
            mTopDays = intent.getIntExtra(PositionTopKeywordsActivity.EXTRA_TOP_DAYS, 0)
            val keywords =
                intent.getParcelableArrayListExtra<PositionTopKeywordData>(
                    PositionTopKeywordsActivity.EXTRA_KEYWORDS
                )
            if (CheckUtils.isNullOrEmpty(keywords)) {
                val tempKeywords = ArrayList<PositionTopKeywordData>()
                for (i in 1..3) {
                    tempKeywords.add(PositionTopKeywordData.getDefault())
                }
                initKeywords.value = tempKeywords
            } else {
                initKeywords.value = keywords
            }
        }
    }

    fun getKeywordTopEffectiveDate(keyword: PositionTopKeywordData, needBack: Boolean) {
        keyword.keyword.replace("|", "")
        mMyJobRepo.getJobTopKeywordEffectiveDate(getSelfUserID(),
            EnterpriseApiConstants.POSITION_TOP_FOR_PC,
            mTopCity,
            keyword.keyword,
            mTopDays,
            object :
                ResultDataCallBack<String> {
                override fun onSuccess(data: String?) {
                    keyword.effectiveDate = data
                    if (needBack) {
                        backCommend.call()
                    }
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    showToast(respondThrowable.errMsg)
                    keyword.keyword = ""
                }
            })
    }

    /**
     * 检查是否有未提交的数据
     */
    fun checkHasNoSubmitData(data: List<PositionTopKeywordData>) {
        if (dataIsEmpty(data)) {
            showToast(R.string.position_least_one_keyword)
            return
        }
        for (item in data) {
            if (!item.keyword.isNullOrBlank() && item.effectiveDate.isNullOrBlank()) {
                getKeywordTopEffectiveDate(item, true)
                return
            }
        }
        backCommend.call()
    }

    private fun dataIsEmpty(data: List<PositionTopKeywordData>): Boolean {
        for (item in data) {
            if (!CheckUtils.isNullOrEmpty(item.keyword)) {
                return false
            }
        }
        return true
    }
}