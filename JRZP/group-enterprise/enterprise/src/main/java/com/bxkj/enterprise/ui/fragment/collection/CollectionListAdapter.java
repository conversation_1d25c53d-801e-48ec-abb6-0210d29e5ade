package com.bxkj.enterprise.ui.fragment.collection;

import android.content.Context;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.collection
 * @Description:
 * @TODO: TODO
 * @date 2018/8/13
 */
public class CollectionListAdapter extends SuperAdapter<ResumeItemData> {
  public CollectionListAdapter(Context context, List<ResumeItemData> list, int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, ResumeItemData resumeItemData,
      int position) {
    ResumeItemData.UbInfoBean itemInfo = resumeItemData.getUbInfo();
    holder.setText(R.id.tv_name, itemInfo.getName());
    holder.setText(R.id.tv_about, resumeItemData.getAbout());
    holder.setText(R.id.tv_position,
        String.format(mContext.getResources().getString(R.string.position_format),
            resumeItemData.getDetailsName2()));
    ImageView ivHeader = holder.findViewById(R.id.iv_header);
    ImageLoader.loadImage(mContext,
        new GlideLoadConfig.Builder().url(MTextUtils.appendImgUrl(itemInfo.getDomain()
            , CheckUtils.isNullOrEmpty(itemInfo.getTx()) ? ECommonApiConstants.getDefaultHeader(
                itemInfo.getSex()) : itemInfo.getTx())).into(ivHeader).build());

    setOnChildClickListener(position, holder.findViewById(R.id.iv_delete), holder.itemView);
  }
}
