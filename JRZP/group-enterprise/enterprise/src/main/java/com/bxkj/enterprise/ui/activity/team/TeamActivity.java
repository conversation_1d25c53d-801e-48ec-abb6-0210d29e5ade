package com.bxkj.enterprise.ui.activity.team;

import android.content.Context;
import android.content.Intent;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.TeamItemData;
import com.bxkj.enterprise.ui.activity.teamedit.TeamEditActivity;
import com.therouter.router.Route;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.team
 * @Description: 团队
 * @TODO: TODO
 * @date 2018/8/9
 */
@Route(path = TeamManagementNavigation.PATH)
public class TeamActivity extends BaseDaggerActivity implements TeamContract.View {

  private static final int TO_EDIT_TEAM_MEMBER_CODE = 1;
  @Inject
  TeamPresenter mTeamPresenter;

  RecyclerView recyclerContent;

  private TeamListAdapter mTeamListAdapter;
  private PageStatusLayout mEmptyLayout;

  public static void start(Context context) {
    Intent starter = new Intent(context, TeamActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mTeamPresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_team;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.mine_team_management))
      .setRightText(getString(R.string.team_member_add))
      .setRightOptionClickListener(
        view -> startActivityForResult(TeamEditActivity.newIntent(TeamActivity.this, null),
          TO_EDIT_TEAM_MEMBER_CODE));
  }

  @Override
  protected void initPage() {
    recyclerContent = findViewById(R.id.recycler_content);

    mEmptyLayout = PageStatusLayout.wrap(recyclerContent);

    mTeamListAdapter = new TeamListAdapter(this, null, R.layout.enterprise_recycler_team_item);
    recyclerContent.setLayoutManager(new LinearLayoutManager(this));
    recyclerContent.addItemDecoration(new RecycleViewDivider(this, LinearLayoutManager.VERTICAL
      , DensityUtils.dp2px(this, 8), ContextCompat.getColor(this, R.color.common_f4f4f4), true));
    recyclerContent.setAdapter(mTeamListAdapter);
    mTeamListAdapter.setOnItemClickListener((view, position) -> {
      TeamItemData teamItem = mTeamListAdapter.getData().get(position);
      if (view.getId() == R.id.tv_delete) {
        new EActionDialog.Builder()
          .setTitle(getString(R.string.confirm_delete))
          .setOnConfirmClickListener((actionDialog, inputText) -> {
              actionDialog.dismiss();
              mTeamPresenter.deleteTeamMember(getMUserID(), teamItem.getId());
            }
          )
          .build()
          .show(getSupportFragmentManager(), EActionDialog.TAG);
      } else {
        startActivityForResult(TeamEditActivity.newIntent(this, teamItem),
          TO_EDIT_TEAM_MEMBER_CODE);
      }
    });
    mTeamPresenter.getTeamMemberList(getMUserID());
  }

  @Override
  public void getTeamMemberListSuccess(List<TeamItemData> teamItemDataList) {
    mEmptyLayout.hidden();
    mTeamListAdapter.reset(teamItemDataList);
  }

  @Override
  public void noPersonal() {
    mEmptyLayout.show(PageStatusConfigFactory.newEmptyConfig());
  }

  @Override
  public void deleteTeamMemberSuccess() {
    showToast(getString(R.string.common_delete_success));
    mTeamPresenter.getTeamMemberList(getMUserID());
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_EDIT_TEAM_MEMBER_CODE && resultCode == RESULT_OK) {
      mTeamPresenter.getTeamMemberList(getMUserID());
    }
  }
}
