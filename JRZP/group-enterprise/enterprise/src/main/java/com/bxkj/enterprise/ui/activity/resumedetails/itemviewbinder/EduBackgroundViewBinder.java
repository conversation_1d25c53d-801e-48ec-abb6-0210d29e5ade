package com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder;

import android.app.Activity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeEduBackgroundData;
import com.bxkj.enterprise.ui.activity.resumedetails.adapter.EduBackgroundAdapter;

/**
 * @date 2018/5/9
 */

public class EduBackgroundViewBinder implements ItemViewBinder<ResumeEduBackgroundData> {

    private final Activity mActivity;

    public EduBackgroundViewBinder(Activity activity) {
        mActivity = activity;
    }

    @Override
    public void onBindViewHolder(SuperViewHolder holder, ResumeEduBackgroundData item, int position) {
        holder.setText(R.id.tv_tag, mActivity.getString(R.string.resume_details_education_exp));
        RecyclerView recyclerView = holder.findViewById(R.id.recycler);
        recyclerView.setVisibility(
            CheckUtils.isNullOrEmpty(item.getEduBackgroundItemDataList()) ? View.GONE : View.VISIBLE);
        EduBackgroundAdapter eduBackgroundAdapter = new EduBackgroundAdapter(mActivity, item.getEduBackgroundItemDataList(), R.layout.enterprise_recycler_resume_education_item);
        recyclerView.setLayoutManager(new LinearLayoutManager(mActivity));
        recyclerView.setAdapter(eduBackgroundAdapter);
    }

    @Override
    public int getLayoutId() {
        return R.layout.b_layout_resume_has_list_item;
    }
}
