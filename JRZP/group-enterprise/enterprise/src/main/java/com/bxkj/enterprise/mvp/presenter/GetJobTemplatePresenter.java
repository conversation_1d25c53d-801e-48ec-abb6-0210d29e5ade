package com.bxkj.enterprise.mvp.presenter;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.JobTemplateItemData;
import com.bxkj.enterprise.mvp.contract.GetJobTemplateContract;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.jobtemplate
 * @Description: JobTemplate
 * @TODO: TODO
 * @date 2018/3/27
 */

public class GetJobTemplatePresenter extends GetJobTemplateContract.Presenter {

    private static final String TAG = GetJobTemplatePresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public GetJobTemplatePresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getJobTemplateList(int userId) {
        mBusinessApi.getJobTemplateList(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getJobTemplateListSuccess((List<JobTemplateItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30002) {
                            mView.noJobTemplate();
                            return;
                        }
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
