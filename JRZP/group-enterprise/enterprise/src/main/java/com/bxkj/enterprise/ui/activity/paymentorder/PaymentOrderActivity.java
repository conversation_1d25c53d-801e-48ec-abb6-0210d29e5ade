package com.bxkj.enterprise.ui.activity.paymentorder;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alipay.sdk.app.PayTask;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.ui.activity.paymentresult.PaymentResultActivity;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.paymentway
 * @Description: 付款方式
 * @TODO: TODO
 * @date 2018/10/15
 */
public class PaymentOrderActivity extends BaseDaggerActivity implements PaymentOrderContract.View {

  private static final String PRICE = "price";
  private static final String ORDER_ID = "order_id";

  private static final int TO_PAYMENT_RESULT_CODE = 1;

  @Inject
  PaymentOrderPresenter mPaymentOrderPresenter;

  private TextView tvPaymentCount;
  private ImageView ivAlipay;

  private String mPaymentPrice;
  private int mOrderId;

  /**
   * 创建订单付款
   *
   * @param price 订单金额
   */
  public static Intent newIntent(Context context, int orderId, String price) {
    Intent starter = new Intent(context, PaymentOrderActivity.class);
    starter.putExtra(ORDER_ID, orderId);
    starter.putExtra(PRICE, price);
    return starter;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mPaymentOrderPresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_payment_way;
  }

  @Override
  protected void initIntent(Intent intent) {
    mPaymentPrice = intent.getStringExtra(PRICE);
    mOrderId = intent.getIntExtra(ORDER_ID, ECommonApiConstants.NO_DATA);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.payment_order));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    tvPaymentCount.setText(
      MTextUtils.fromHtml(getString(R.string.payment_count_format, mPaymentPrice)));
    ivAlipay.setSelected(true);
  }

  private void onViewClicked() {
    if (mOrderId != ECommonApiConstants.NO_DATA) {       //订单id不为空获取支付信息
      mPaymentOrderPresenter.getAlipayOrderInfo(String.valueOf(mOrderId));
    }
  }

  @Override
  public void getAlipayOrderInfoSuccess(String alipayOrderInfo, String orderId) {
    mPaymentOrderPresenter.payOfAlipay(new PayTask(this), alipayOrderInfo, orderId);
  }

  @Override
  public void payForAlipaySuccess(String orderId) {
    startActivityForResult(PaymentResultActivity.newIntent(this, orderId), TO_PAYMENT_RESULT_CODE);
  }

  @Override
  public void payOnAlipayResultUnknown(String orderId) {
    startActivityForResult(PaymentResultActivity.newIntent(this, orderId), TO_PAYMENT_RESULT_CODE);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_PAYMENT_RESULT_CODE && resultCode == RESULT_OK) {
      finish();
    }
  }

  private void bindView(View bindSource) {
    tvPaymentCount = bindSource.findViewById(R.id.tv_payment_count);
    ivAlipay = bindSource.findViewById(R.id.iv_alipay);
    bindSource.findViewById(R.id.tv_payment_confirm).setOnClickListener(v -> onViewClicked());
  }
}
