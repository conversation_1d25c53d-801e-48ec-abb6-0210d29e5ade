package com.bxkj.enterprise.ui.activity.schoolrecruitdetails;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.webkit.WebChromeClient;
import android.webkit.WebView;

import com.bxkj.ecommon.base.EBaseDBActivity;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.databinding.EnterpriseActivitySchoolRecruitDetailsBinding;
import com.bxkj.enterprise.ui.activity.jobfairregistration.JobFairRegistrationActivity;
import com.bxkj.enterprise.ui.activity.schoolhome.SchoolHomeActivity;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolrecruitdetails
 * @Description: 校园招聘详情
 * @TODO: TODO
 * @date 2019/6/13
 */
public class ESchoolRecruitDetailsActivity extends
    EBaseDBActivity<EnterpriseActivitySchoolRecruitDetailsBinding, SchoolRecruitDetailsViewModel> {

  private static final String EXTRA_SCHOOL_RECRUIT_ID = "SCHOOL_RECRUIT_ID";

  public static final int TO_JOB_FAIR_REGISTRATION = 1;

  public static Intent newIntent(Context context, int schoolRecruitId) {
    Intent intent = new Intent(context, ESchoolRecruitDetailsActivity.class);
    intent.putExtra(EXTRA_SCHOOL_RECRUIT_ID, schoolRecruitId);
    return intent;
  }

  @Override
  protected Class<SchoolRecruitDetailsViewModel> getViewModelClass() {
    return SchoolRecruitDetailsViewModel.class;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_school_recruit_details;
  }

  @Override
  protected void initPage(final Bundle savedInstanceState) {
    getViewBinding().setViewModel(getViewModel());

    setupSkeletonScreen();

    subscribeDetailsChange();
    getViewModel().getSchoolRecruitDetails(getIntent().getIntExtra(EXTRA_SCHOOL_RECRUIT_ID, 0));
    getViewModel().checkSchoolRecruitStatus(getLocalUserId(),
        getIntent().getIntExtra(EXTRA_SCHOOL_RECRUIT_ID, 0));
    subscribeViewModelCommand();
  }

  private void setupSkeletonScreen() {
    SkeletonScreen skeletonScreen = Skeleton.bind(getViewBinding().scrollContent)
        .load(R.layout.enterprise_activity_school_recruit_skeleton)
        .color(R.color.common_f7f9fb)
        .duration(1500)
        .show();

    getViewBinding().webDetails.setWebChromeClient(new WebChromeClient() {
      @Override
      public void onProgressChanged(WebView view, int newProgress) {
        if (newProgress == 100) {
          skeletonScreen.hide();
        }
      }
    });
  }

  private void subscribeViewModelCommand() {
    getViewModel().getToSchoolHomeCommand().observe(this, schoolId -> {
      if (schoolId != null) {
        startActivity(SchoolHomeActivity.newIntent(this, schoolId));
      }
    });

    getViewModel().getToSignUpCommand().observe(this, schoolRecruitId -> {
      if (schoolRecruitId != null) {
        startActivityForResult(JobFairRegistrationActivity.newIntent(
            ESchoolRecruitDetailsActivity.this, schoolRecruitId), TO_JOB_FAIR_REGISTRATION);
      }
    });
  }

  private void subscribeDetailsChange() {
    getViewModel().getSchoolRecruitDetailsData().observe(this, schoolRecruitDetailsData -> {
      if (schoolRecruitDetailsData != null) {
        getViewBinding().webDetails.loadDataWithBaseURL(null, schoolRecruitDetailsData.getContent(),
            "text/html", "utf-8", null);
      }
    });
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_JOB_FAIR_REGISTRATION && resultCode == RESULT_OK) {
      getViewModel().checkSchoolRecruitStatus(getLocalUserId(),
          getIntent().getIntExtra(EXTRA_SCHOOL_RECRUIT_ID, 0));
    }
  }

  @Override
  protected void onResume() {
    super.onResume();
    getViewBinding().webDetails.resumeTimers();
    getViewBinding().webDetails.onResume();
  }

  @Override
  protected void onPause() {
    super.onPause();
    getViewBinding().webDetails.pauseTimers();
    getViewBinding().webDetails.onPause();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    if (getViewBinding().webDetails != null) {
      getViewBinding().webDetails.destroy();
    }
  }
}
