package com.bxkj.enterprise.data.source;

import androidx.annotation.NonNull;

import com.bxkj.common.base.mvvm.callback.ResultCallBack;
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.BaseRepo;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.CompanyInfoDataV2;
import com.bxkj.enterprise.data.SchoolRecruitDetailsData;
import com.bxkj.enterprise.data.source.datasource.SchoolRecruitDetailsSource;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source
 * @Description:
 * @TODO: TODO
 * @date 2019/6/13
 */
public class SchoolRecruitDetailsRepo extends BaseRepo implements SchoolRecruitDetailsSource {

    private BusinessApi mBusinessApi;

    @Inject
    public SchoolRecruitDetailsRepo(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getSchoolRecruitDetails(int schoolRecruitId, ResultDataCallBack<SchoolRecruitDetailsData> callBack) {
        mBusinessApi.getSchoolRecruitDetails(schoolRecruitId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        callBack.onSuccess((SchoolRecruitDetailsData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        callBack.onError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void jobFairRegistration(int userId, int jobFairId, CompanyInfoDataV2 companyInfoData, String jobs, ResultDataCallBack<BaseResponse> callBack) {
        mBusinessApi.jobFairRegistration(userId, jobFairId, companyInfoData, jobs)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        callBack.onSuccess(baseResponse);
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        callBack.onError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void checkSchoolRecruitStatus(int userId, int schoolRecruitId, ResultCallBack callBack) {
        mBusinessApi.checkSchoolRecruitRegisterStatus(userId, schoolRecruitId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        callBack.onSuccess();
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        callBack.onError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
