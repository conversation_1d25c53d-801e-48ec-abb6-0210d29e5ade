package com.bxkj.enterprise.ui.activity.seenmeapplicant

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.layout
import com.bxkj.enterprise.data.MessageData
import com.bxkj.enterprise.databinding.ActivitySeenMeApplicantBinding
import com.bxkj.enterprise.ui.activity.invitingdelivery.InvitationDeliveryNavigation
import com.bxkj.enterprise.ui.activity.postjob.PostJobV2Activity
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2

/**
 * @Project: VideoRecruitment
 * @Description: 看过我的求职者
 * @author:45457
 * @date: 2020/6/4
 * @version: V1.0
 */
class SewMeGeekFragment :
  BaseDBFragment<ActivitySeenMeApplicantBinding, SeenMeApplicantViewModel>() {

  companion object {

    fun newInstance(): Fragment {
      return SewMeGeekFragment()
    }
  }

  override fun getViewModelClass(): Class<SeenMeApplicantViewModel> =
    SeenMeApplicantViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_seen_me_applicant

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    subscribeViewModelEvent()

    setupSeenMeApplicantList()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.toInviteSendResume.observe(this, Observer {
      InvitationDeliveryNavigation.create(
        intArrayOf(it.userData.otherID),
        it.userData.nickName
      ).start()
    })

    viewModel.showNoJobTipsDialog.observe(this, Observer {
      EActionDialog.Builder()
        .setContent(getString(R.string.no_position_tips))
        .setConfirmText(getString(R.string.go_to_post))
        .setOnConfirmClickListener { actionDialog: EActionDialog, _: String? ->
          actionDialog.dismiss()
          startActivity(PostJobV2Activity.newIntent(requireContext()))
        }
        .build().show(childFragmentManager, EActionDialog.TAG)
    })
  }

  private fun setupSeenMeApplicantList() {
    val seenMeApplicantListAdapter =
      SimpleDBListAdapter<MessageData>(
        requireContext(), layout.recycler_seen_me_applicant_item, BR.data
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            val item = data[position]
            item.updateViewState(true)
            viewModel.setupMsgRead(item)
            startActivity(
              ApplicantResumeDetailActivityV2.newIntent(
                requireContext(),
                item.userData.otherID,
                item.userData.id,
                jobID = item.relId,
                jobName = item.relName
              )
            )
          }
        })
      }
    val seenMeApplicantList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    seenMeApplicantList.layoutManager = LinearLayoutManager(requireContext())
    seenMeApplicantList.addItemDecoration(
      LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4_8))
        .drawHeader(true)
        .drawFoot(true)
        .build()
    )
    viewModel.seenMeApplicantListViewModel.setAdapter(seenMeApplicantListAdapter)
  }
}