package com.bxkj.enterprise.ui.activity.permissionmanagement

import androidx.recyclerview.widget.DiffUtil

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/3
 * @version: V1.0
 */
data class PermissionItem private constructor(
  var permissionTitle: String,
  var permissionUrlTips: String,
  var permissionDescUrl: String,
  var opened: Boolean
) {
  companion object {
    fun create(
      permissionName: String,
      permissionUrlTips: String,
      permissionDescUrl: String,
      opened: Boolean
    ): PermissionItem {
      return PermissionItem(permissionName, permissionUrlTips, permissionDescUrl, opened)
    }
  }

  class DiffCallBack : DiffUtil.ItemCallback<PermissionItem>() {
    override fun areItemsTheSame(oldItem: PermissionItem, newItem: PermissionItem): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(oldItem: PermissionItem, newItem: PermissionItem): Boolean {
      return (oldItem.permissionTitle == newItem.permissionTitle) && (oldItem.opened == newItem.opened)
    }

  }
}