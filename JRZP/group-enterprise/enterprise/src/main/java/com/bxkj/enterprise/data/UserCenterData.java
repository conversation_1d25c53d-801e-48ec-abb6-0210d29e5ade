package com.bxkj.enterprise.data;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.enterprise.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 用户个人中心信息
 * @TODO: TODO
 * @date 2018/8/7
 */
public class UserCenterData {
    private int id;
    private String name;
    private String name2;
    private String logo;
    private String domain;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return CheckUtils.isNullOrEmpty(name) ? "请前往公司主页完善资料" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName2() {
        return name2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }

    public Object getLogo() {
        return !CheckUtils.isNullOrEmpty(logo) ? MTextUtils.appendImgUrl(domain, logo) : R.drawable.ic_company_no_logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
