package com.bxkj.enterprise.ui.activity.selectaddressbymap

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.lifecycle.lifecycleScope
import com.baidu.location.BDLocation
import com.baidu.mapapi.map.BaiduMap.OnMapStatusChangeListener
import com.baidu.mapapi.map.MapStatus
import com.baidu.mapapi.map.MapStatusUpdateFactory
import com.baidu.mapapi.model.LatLng
import com.baidu.mapapi.search.core.SearchResult
import com.baidu.mapapi.search.geocode.*
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.textChangeFlow
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.widget.adresspickerdialog.AddressPickerDialogFragment
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.CompanyAddressData
import com.bxkj.enterprise.databinding.EnterpriseActivitySelectAddressByMapBinding
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

/**
 *
 * @author: sanjin
 * @date: 2022/11/2
 */
class SelectAddressByMapActivity :
    BaseDBActivity<EnterpriseActivitySelectAddressByMapBinding, SelectAddressByMapViewModel>(),
    OnClickListener {

    companion object {

        const val RESULT_ADDRESS_CHANGE = RESULT_FIRST_USER + 1

        const val EXTRA_ADDRESS_DATA = "ADDRESS_DATA"
        private const val EXTRA_ONLY_EDIT_VALUE = "ONLY_EDIT_VALUE"

        fun newIntent(
            context: Context,
            companyAddress: CompanyAddressData? = null,
            onlyEditValue: Boolean = false
        ): Intent {
            return Intent(context, SelectAddressByMapActivity::class.java).apply {
                companyAddress?.let {
                    putExtra(EXTRA_ADDRESS_DATA, companyAddress)
                }
                putExtra(EXTRA_ONLY_EDIT_VALUE, onlyEditValue)
            }
        }
    }

    private val _selectCityLauncher = registerForActivityResult(StartActivityForResult()) {
        viewModel.handleSelectCityResult(it)
    }

    private val _geoCoder = GeoCoder.newInstance().apply {
        setOnGetGeoCodeResultListener(object : OnGetGeoCoderResultListener {
            override fun onGetGeoCodeResult(result: GeoCodeResult?) {
                result?.let {
                    if (it.error == SearchResult.ERRORNO.NO_ERROR) {
                        viewBinding.mapAddress.map.animateMapStatus(
                            MapStatusUpdateFactory.newLatLng(
                                it.location
                            )
                        )
                    } else {
                        showToast(getString(R.string.select_address_not_search_position))
                    }
                }
            }

            override fun onGetReverseGeoCodeResult(p0: ReverseGeoCodeResult?) {
            }
        })
    }

    private val _extraOnlyEditValue by lazy { intent.getBooleanExtra(EXTRA_ONLY_EDIT_VALUE, false) }

    override fun getViewModelClass(): Class<SelectAddressByMapViewModel> =
        SelectAddressByMapViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_select_address_by_map

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        setupViewEvent()

        setupMapView()

        subscribeViewModelEvent()

        viewModel.start(intent.getParcelableExtra(EXTRA_ADDRESS_DATA), _extraOnlyEditValue)
    }

    override fun onClick(v: View?) {
        v?.let {
            if (it.id == R.id.item_city) {
                _selectCityLauncher.launch(CityPickerActivity.newIntent(this, false))
            }
        }
    }

    private fun setupViewEvent() {
        if (!_extraOnlyEditValue) {
            viewBinding.titleBar.setRightText(R.string.common_delete)
            viewBinding.titleBar.setRightOptionClickListener {
                ActionDialog.Builder()
                    .setContent("确认删除该地址？")
                    .setOnConfirmClickListener {
                        viewModel.deleteThisAddress()
                    }.build().show(supportFragmentManager)
            }
        }

        viewBinding.tvSave.setOnClickListener {
            viewBinding.mapAddress.map.mapStatus.target.let {
                viewModel.save(it.longitude, it.latitude)
            }
        }

        lifecycleScope.launch {
            viewBinding.etDetailsAddress.getContentView()?.textChangeFlow()
                ?.debounce(1000)
                ?.collect {
                    geoCodeSelectedAddress()
                }
        }
    }

    private fun geoCodeSelectedAddress() {
        val userEnterAddress = viewBinding.etDetailsAddress.getContentView()?.text
        _geoCoder.geocode(GeoCodeOption().apply {
            city(viewModel.getSelectedCity())
            address(viewModel.getSelectedArea() + (if (userEnterAddress.isNullOrEmpty()) "中心" else userEnterAddress))
        })
    }

    private fun setupMapView() {
        viewBinding.mapAddress.showZoomControls(false)
        viewBinding.mapAddress.map.apply {
            setMaxAndMinZoomLevel(13f, 13f)
            uiSettings.apply {
                setAllGesturesEnabled(false)
            }
            setOnMapStatusChangeListener(object : OnMapStatusChangeListener {
                override fun onMapStatusChangeStart(p0: MapStatus?) {
                    viewBinding.ivPosition.animate().translationY(-15f)
                    viewBinding.ivPositionShadow.animate().scaleX(2.0f)
                    viewBinding.ivPositionShadow.animate().scaleY(2.0f)
                }

                override fun onMapStatusChangeStart(p0: MapStatus?, p1: Int) {
                }

                override fun onMapStatusChange(p0: MapStatus?) {
                }

                override fun onMapStatusChangeFinish(p0: MapStatus?) {
                    viewBinding.ivPosition.animate().translationY(0f)
                    viewBinding.ivPositionShadow.animate().scaleX(1.0f)
                    viewBinding.ivPositionShadow.animate().scaleY(1.0f)
                }
            })
            setOnMapLoadedCallback {
                viewModel.initLnglat()
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.showDelete.observe(this) {
            viewBinding.titleBar.setShowRight(it)
        }

        viewModel.reGeoCommand.observe(this, EventObserver {
            geoCodeSelectedAddress()
        })

        viewModel.setupInitLocationCommand.observe(this, EventObserver {
            if (it.hasLnglat()) {
                viewBinding.mapAddress.map.animateMapStatus(
                    MapStatusUpdateFactory.newLatLng(
                        LatLng(
                            it.lat!!.toDouble(),
                            it.lng!!.toDouble()
                        )
                    )
                )
            }
        })

        viewModel.startLocationCommand.observe(this, EventObserver {
            LocationManager.getLocationInfo(
                this,
                "权限说明",
                "地图选地址需要开启定位权限",
                object : LocationManager.OnLocationListener {
                    override fun onSuccess(location: BDLocation) {
                        updateMapStatus(location.longitude, location.latitude)
                        if (!location.city.isNullOrBlank()) {
                            viewModel.autoCompleteProvinceAndCity(
                                location.city.substring(0, location.city.length - 1)
                            )
                        }
                    }

                    override fun onFailed() {
                        showToast("定位失败，定位至默认坐标")
                        updateMapStatus(116.405419, 39.912057)
                    }
                })
        })

        viewModel.showAreaPickerCommand.observe(this, EventObserver { address ->
            AddressPickerDialogFragment().apply {
                setStartLevel(AddressPickerDialogFragment.AREA_LEVEL, address.cityId.getOrDefault())
                setEndLevel(AddressPickerDialogFragment.AREA_LEVEL)
                setOnSelectedListener { _, _, area, _ ->
                    viewModel.setArea(area)
                    geoCodeSelectedAddress()
                }
            }.show(supportFragmentManager)
        })

        viewModel.addressInfoChangeEvent.observe(this, EventObserver {
            setResult(RESULT_ADDRESS_CHANGE)
            finish()
        })

        viewModel.saveAndBackCommand.observe(this, EventObserver {
            setResult(Activity.RESULT_OK, Intent().apply {
                putExtra(EXTRA_ADDRESS_DATA, it)
            })
            finish()
        })
    }

    private fun updateMapStatus(lng: Double, lat: Double) {
        viewBinding.mapAddress.map.animateMapStatus(
            MapStatusUpdateFactory.newLatLng(LatLng(lat, lng))
        )
    }

    override fun onResume() {
        super.onResume()
        viewBinding.mapAddress.onResume()
    }

    override fun onPause() {
        super.onPause()
        viewBinding.mapAddress.onPause()
    }

    override fun onDestroy() {
        viewBinding.mapAddress.onDestroy()
        super.onDestroy()
    }
}