package com.bxkj.enterprise.ui.fragment.homev2

import android.content.Context
import androidx.core.content.ContextCompat
import com.bxkj.common.R
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator

/**
 *
 * @author: sanjin
 * @date: 2022/7/29
 */
open class HomeOnlineJobTabAdapter constructor(titles: Array<String?>) :
    MagicIndicatorAdapter(titles) {

    override fun getTitleView(context: Context, index: Int): IPagerTitleView {
        val pageTitleView =
            ScalePagerTitleView(context, 0.1f).apply {
                maxEms = 6
            }
        pageTitleView.setPadding(context.dip(12), 0, context.dip(12), 0)
        pageTitleView.textSize = 17f
        pageTitleView.normalColor = ContextCompat.getColor(context, R.color.cl_999999)
        pageTitleView.selectedColor = ContextCompat.getColor(context, R.color.cl_333333)
        pageTitleView.text = getTitles()[index]
        pageTitleView.setOnClickListener { view ->
            getOnTabClickListener()?.onTabClicked(view, index)
        }
        return pageTitleView
    }

    override fun getIndicator(context: Context): IPagerIndicator {
        return LinePagerIndicator(context).apply {
            setColors(ContextCompat.getColor(context, R.color.cl_transparent))
        }
    }
}