package com.bxkj.enterprise.ui.activity.invitingdelivery

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.ecommon.data.HasStateOnlinePositionData
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.enterprise.data.source.ResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class InvitationDeliveryViewModel @Inject constructor(
    private val _myJobRepo: MyJobRepo,
    private val _resumeRepo: ResumeRepo
) : BaseViewModel() {

    val selectedJob = MutableLiveData<HasStateOnlinePositionData>()

    val jobList = MutableLiveData<List<HasStateOnlinePositionData>>()

    val inviteSuccessEvent = MutableLiveData<VMEvent<Unit>>()

    val inviteNoBalanceEvent = MutableLiveData<VMEvent<String>>()

    val noJobEvent = MutableLiveData<VMEvent<Unit>>()

    private var _resumeIDs: IntArray? = null

    fun start(resumeIDs: IntArray?) {
        _resumeIDs = resumeIDs
        getInviteAvailableJobList()
    }

    fun setSelectedJob(job: HasStateOnlinePositionData) {
        selectedJob.value = job
    }

    /**
     * 邀请
     */
    fun invite(greetingText: String) {
        selectedJob.value?.let { job ->
            showLoading()
            viewModelScope.launch {
                val idBuilder = StringBuilder()
                _resumeIDs?.forEach {
                    idBuilder.append(it).append(",")
                }
                _resumeRepo.sendSayHelloMsg(
                    getSelfUserID(),
                    job.id,
                    idBuilder.toString(),
                    greetingText
                ).handleResult({
                    inviteSuccessEvent.value = VMEvent(Unit)
                }, {
                    if (it.errCode == 30004 || it.errCode == 30005) {
                        inviteNoBalanceEvent.value = VMEvent(it.errMsg)
                    } else {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
            }
        } ?: showToast("请选择职位")
    }

    /**
     * 获取可用邀请职位列表
     */
    private fun getInviteAvailableJobList() {
        val checkResumeID = _resumeIDs?.let {
            it[0]
        } ?: let {
            0
        }
        viewModelScope.launch {
            showLoading()
            _myJobRepo.getInviteAvailableJobList(getSelfUserID(), checkResumeID)
                .handleResult({
                    it?.let {
                        jobList.value = it
                        selectedJob.value = it[0]
                    }
                }, {
                    if (it.isNoDataError) {
                        noJobEvent.value = VMEvent(Unit)
                    } else {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
        }
    }
}