package com.bxkj.enterprise.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.BR
import com.bxkj.common.util.recyclerutil.ListSection

/**
 *
 * @author: sanjin
 * @date: 2022/4/20
 */
data class SchoolRecruitCityData(
    var name: String
) : BaseObservable(), ListSection {

    var pinyin: String = ""

    @get:Bindable
    var checked: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.checked)
        }

    class DiffCallback : DiffUtil.ItemCallback<SchoolRecruitCityData>() {
        override fun areItemsTheSame(
            oldItem: SchoolRecruitCityData,
            newItem: SchoolRecruitCityData
        ): Boolean {
            return oldItem.name == newItem.name
        }

        override fun areContentsTheSame(
            oldItem: SchoolRecruitCityData,
            newItem: SchoolRecruitCityData
        ): Boolean {
            return oldItem.name == newItem.name
        }

    }

    override fun getSection(): String {
        if (pinyin.isEmpty()) {
            return ""
        } else {
            return pinyin.first().toString()
        }
    }
}