package com.bxkj.enterprise.ui.activity.invitationinterview

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.lifecycle.Observer
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.dialog.timeselect.TimeSelectDialog
import com.bxkj.common.widget.dialog.timeselect.TimeSelectDialog.OnConfirmClickListener
import com.bxkj.common.widget.zpcalenderview.ZPCalenderView.OnDateClickListener
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.CompanyAddressData
import com.bxkj.enterprise.databinding.EnterpriseActivityInviteInterviewV2Binding
import com.bxkj.enterprise.ui.activity.selectaddressbymap.SelectAddressByMapActivity
import com.bxkj.enterprise.weight.hrlist.HRListDialogFragment
import com.bxkj.jrzp.support.chat.widget.selectjob.JobListDialogFragment
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Description:
 * Author:45457
 **/
@Route(path = InviteInterviewNavigation.PATH)
class InviteInterviewActivityV2 :
    BaseDBActivity<EnterpriseActivityInviteInterviewV2Binding, InviteInterviewViewModelV2>(), OnClickListener {

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, InviteInterviewActivityV2::class.java)
        }
    }

    private val _jobID by lazy { intent.getIntExtra(InviteInterviewNavigation.EXTRA_JOB_ID, 0) }
    private val _resumeID by lazy { intent.getIntExtra(InviteInterviewNavigation.EXTRA_RESUME_ID, 0) }
    private val _inviteUserName by lazy { intent.getStringExtra(InviteInterviewNavigation.EXTRA_INVITE_USER_NAME) }
    private val _interviewId by lazy { intent.getIntExtra(InviteInterviewNavigation.EXTRA_INTERVIEW_ID, 0) }

    private val _editAddressLauncher = registerForActivityResult(StartActivityForResult()) {
        if (it.resultCode == RESULT_OK) {
            val result =
                it.data?.getParcelableExtra<CompanyAddressData>(SelectAddressByMapActivity.EXTRA_ADDRESS_DATA)
            viewModel.updateInterviewAddress(result ?: CompanyAddressData())
        }
    }

    override fun getViewModelClass(): Class<InviteInterviewViewModelV2> = InviteInterviewViewModelV2::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_invite_interview_v2

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        setupCalenderDateClickListener()

        subscribeViewModelEvent()

        if (_interviewId > 0) {
            viewBinding.tvConversation.text = getString(R.string.enterprise_invite_interview_update_btn_text)
        } else {
            viewBinding.tvConversation.text = getString(R.string.enterprise_invite_interview_send_btn_text)
        }

        viewModel.setPageParams(_jobID, _resumeID, _inviteUserName, _interviewId)
    }

    override fun onClick(v: View?) {
        v?.let {
            if (v.id == R.id.item_interview_time) {
                showInterviewTimeSelectDialog()
            }
        }
    }

    private fun setupCalenderDateClickListener() {
        viewBinding.calenderDate.setOnDateClickListener(object : OnDateClickListener {
            override fun onDateClick(date: LocalDate) {
                viewModel.updateInterviewDate(date.toString())
                showInterviewTimeSelectDialog()
            }
        })
    }

    private fun showInterviewTimeSelectDialog() {
        TimeSelectDialog("选择面试时间", object : OnConfirmClickListener {
            override fun onConfirm(selected: String) {
                viewModel.updateInterviewTime(selected)
            }
        }).show(supportFragmentManager)
    }

    private fun subscribeViewModelEvent() {
        viewModel.interviewInfo.observe(this, Observer {
            if (it.jiViewDate.isNotBlank()) {
                viewBinding.calenderDate.setSelectedDate(
                    LocalDate.parse(
                        it.jiViewDate.split(" ")[0].getOrDefault(),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd")
                    )
                )
            }
        })

        viewModel.showJobListDialogCommand.observe(this, EventObserver {
            JobListDialogFragment.Builder()
                .setSelectedJobId(it)
                .setTitle("选择面试职位")
                .setOnSelectedListener { selected ->
                    viewModel.updateInterviewJobInfo(selected)
                }
                .build()
                .show(supportFragmentManager)
        })

        viewModel.toEditInterviewAddressCommand.observe(this, EventObserver {
            _editAddressLauncher.launch(SelectAddressByMapActivity.newIntent(this, it, true))
        })

        viewModel.showHRListDialogCommand.observe(this, EventObserver {
            HRListDialogFragment(it) { selected ->
                viewModel.updateInterviewHR(selected)
            }.show(supportFragmentManager)
        })

        viewModel.inviteSuccessEvent.observe(this, EventObserver {
            setResult(Activity.RESULT_OK)
            finish()
        })
    }
}