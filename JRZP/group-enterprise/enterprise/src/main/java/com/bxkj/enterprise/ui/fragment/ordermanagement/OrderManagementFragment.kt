package com.bxkj.enterprise.ui.fragment.ordermanagement

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.BaseListFragment
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.OrderItemData
import com.bxkj.enterprise.mvp.contract.GetOrderListContract
import com.bxkj.enterprise.mvp.presenter.GetOrderListPresenter
import com.bxkj.enterprise.ui.activity.applyinvoice.ApplyInvoiceActivity
import com.bxkj.enterprise.ui.activity.invoicedetails.InvoiceDetailsActivity
import com.bxkj.enterprise.ui.activity.paymentorder.PaymentOrderActivity
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.ordermanagement
 * @Description: 訂單管理
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/10/12
 * @version V1.0
 */
@Route(path = OrderManagementNavigation.PATH)
class OrderManagementFragment : BaseListFragment(), OrderManagementContract.View,
  GetOrderListContract.View {

  @Inject
  lateinit var mOrderManagementPresenter: OrderManagementPresenter

  @Inject
  lateinit var mGetOrderListPresenter: GetOrderListPresenter

  private lateinit var mOrderManagementListAdapter: OrderManagementListAdapter

  companion object {

    private const val TO_APPLY_INVOICE_CODE: Int = 1
    private const val TO_PAYMENT_ORDER_CODE: Int = 2

    fun newInstance(type: Int): OrderManagementFragment {
      var bundle = Bundle()
      bundle.putInt(OrderManagementNavigation.EXTRA_PAGE_TYPE, type)
      val orderManagementFragment = OrderManagementFragment()
      orderManagementFragment.arguments = bundle
      return orderManagementFragment
    }
  }

  override fun getLayoutId(): Int = R.layout.enterprise_fragment_order_management

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>?): MutableList<BasePresenter<BaseView>> {
    presenters!!.add(CheckUtils.cast(mOrderManagementPresenter))
    presenters.add(CheckUtils.cast(mGetOrderListPresenter))
    return presenters
  }

  override fun onResume() {
    super.onResume()
    statusBarManager.statusBarDarkFont(true, 0.4f).init()
  }

  override fun initPage() {
    super.initPage()
    pageStatusLayout.show(PageStatusConfigFactory.newLoadingConfig())
    mOrderManagementListAdapter =
      OrderManagementListAdapter(context, null, R.layout.enterprise_recycler_order_item)
    recyclerView.adapter = mOrderManagementListAdapter
    recyclerView.layoutManager = LinearLayoutManager(context)

    mOrderManagementListAdapter.setOnItemClickListener(object : SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        val orderItemData = mOrderManagementListAdapter.data[position]
        if (v.id == R.id.tv_order_option_one) {
          if (orderItemData.ispay == 1) {
            mOrderManagementPresenter.activateOrder(orderItemData.id, position)
          } else {
            EActionDialog.Builder()
              .setTitle(getString(R.string.order_delete_tips))
              .setContent(getString(R.string.order_delete_tips_content))
              .setOnConfirmClickListener { actionDialog, _ ->
                actionDialog.dismiss()
                mOrderManagementPresenter.deleteOrderById(
                  orderItemData.id,
                  position
                )
              }.build().show(childFragmentManager, EActionDialog.TAG)
          }
        } else {
          if (orderItemData.ispay == 1) {    //已支付
            when (orderItemData.isCanInvoice) {
              1 -> startActivityForResult(
                ApplyInvoiceActivity.newIntent(
                  context!!,
                  orderItemData.id.toString(),
                  false
                ), TO_APPLY_INVOICE_CODE
              )

              2 -> startActivityForResult(
                ApplyInvoiceActivity.newIntent(
                  context!!,
                  orderItemData.invoiceID.toString(),
                  true
                ), TO_APPLY_INVOICE_CODE
              )

              else -> InvoiceDetailsActivity.start(context!!, orderItemData.invoiceID)
            }
          } else {
            startActivityForResult(
              PaymentOrderActivity.newIntent(
                context,
                orderItemData.id,
                orderItemData.price
              ), TO_PAYMENT_ORDER_CODE
            )
          }
        }
      }
    })
  }

  override fun fetchData() {
    mGetOrderListPresenter.getOrderList(
      0,
      if (requireArguments().getInt(OrderManagementNavigation.EXTRA_PAGE_TYPE) == OrderManagementNavigation.TYPE_PAID) 1 else 0,
      ECommonApiConstants.NO_DATA,
      ECommonApiConstants.NO_DATA,
      ECommonApiConstants.DEFAULT_PAGE_SIZE,
      refreshLayoutManager.currentPage
    )
  }

  override fun getOrderListSuccess(orderList: List<OrderItemData>) {
    pageStatusLayout.hidden()
    refreshLayoutManager.finishRefreshOrLoadMore()
    if (refreshLayoutManager.currentFirstPage())
      mOrderManagementListAdapter.reset(orderList)
    else
      mOrderManagementListAdapter.addAll(orderList)
  }

  override fun activateOrderSuccess(position: Int) {
    mOrderManagementListAdapter.data[position].iseffect = 1
    mOrderManagementListAdapter.notifyItemChanged(position)
    showToast(getString(R.string.order_activate_successful))
  }

  override fun deleteOrderSuccess(position: Int) {
    mOrderManagementListAdapter.removeAt(position)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      refreshLayoutManager.refreshPage()
    }
  }
}