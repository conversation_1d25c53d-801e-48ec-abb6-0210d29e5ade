package com.bxkj.enterprise.ui.fragment.linvoice

import com.bxkj.common.mvp.mvp.BaseMvpPresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.enterprise.data.InvoiceData

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.linvoice
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/10/15
 * @version V1.0
 */
interface InvoiceContract {
  interface View : BaseView {
    fun applyInvoiceSuccess()

    fun getCompanyNameSuccess(companyName: String)
  }

  abstract class Presenter : BaseMvpPresenter<View>() {
    abstract fun applyInvoice(orderIds: String, invoiceData: InvoiceData)

    abstract fun reapplyInvoice(orderId: String, invoiceData: InvoiceData)

    abstract fun getCompanyNameById()
  }
}