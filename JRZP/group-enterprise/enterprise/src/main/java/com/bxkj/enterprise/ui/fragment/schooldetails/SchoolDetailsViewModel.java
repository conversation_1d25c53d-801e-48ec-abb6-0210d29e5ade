package com.bxkj.enterprise.ui.fragment.schooldetails;

import android.app.Application;
import androidx.lifecycle.MutableLiveData;
import android.os.Bundle;

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.schooldetails
 * @Description:
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolDetailsViewModel extends BaseViewModel {

    private final MutableLiveData<String>mContent=new MutableLiveData<>();

    @Inject
    public SchoolDetailsViewModel(@NotNull Application application) {
        super();
    }

    public void initPage(Bundle bundle){

    }
}
