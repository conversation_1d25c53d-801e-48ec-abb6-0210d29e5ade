package com.bxkj.enterprise.data;

import com.bxkj.common.util.TimeUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2018/8/24
 */
public class PositionAutoRefreshData {
    private String dates;
    private String times;

    public String getDates() {
        return dates;
    }

    public void setDates(String dates) {
        this.dates = dates;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }

    public List<Calendar> getDateCalendars() {
        List<Calendar> calendars = new ArrayList<>();
        String[] stringDates = dates.split("\\|");
        for (String stringDate : stringDates) {
            calendars.add(TimeUtils.stringToCalendar("yyyy-MM-dd", stringDate));
        }
        return calendars;
    }

    public List<String> getTimeList() {
        return Arrays.asList(times.split("\\|"));
    }
}
