package com.bxkj.enterprise.ui.activity.integralreward

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.bxkj.enterprise.R

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.integralreward
 * @Description:
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
class IntegralRewardActivity : FragmentActivity() {

  companion object {
    private const val EXTRA_INTEGRAL_REWARD = "integral_reward"

    fun newIntent(context: Context, integral: Int): Intent {
      val intent = Intent(context, IntegralRewardActivity::class.java)
      intent.putExtra(EXTRA_INTEGRAL_REWARD, integral)
      return intent
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.enterprise_dialog_integral_reward)
    setupIntegral()
    findViewById<ImageView>(R.id.iv_close).setOnClickListener { finish() }
  }

  private fun setupIntegral() {
    val integral = intent.getIntExtra(EXTRA_INTEGRAL_REWARD, 0)
    if (integral > 0) {
      findViewById<TextView>(R.id.tv_integral).text =
        getString(R.string.integral_reward_format, integral)
    } else {
      finish()
    }
  }
}