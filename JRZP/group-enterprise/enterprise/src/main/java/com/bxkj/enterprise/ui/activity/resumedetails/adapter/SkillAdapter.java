package com.bxkj.enterprise.ui.activity.resumedetails.adapter;

import android.content.Context;
import android.view.View;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeSkillData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 专业技能适配器
 * @TODO: TODO
 * @date 2018/5/11
 */

public class SkillAdapter extends SuperAdapter<ResumeSkillData> {
    public SkillAdapter(Context context, List<ResumeSkillData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, ResumeSkillData professionalSkillItemData, int position) {
        holder.setText(R.id.tv_name_and_level, professionalSkillItemData.getName() + "|" + professionalSkillItemData.getDegree());
        holder.findViewById(R.id.tv_desc).setVisibility(
            CheckUtils.isNullOrEmpty(professionalSkillItemData.getRemark()) ? View.GONE : View.VISIBLE);
        holder.setText(R.id.tv_desc, professionalSkillItemData.getRemark());
    }
}
