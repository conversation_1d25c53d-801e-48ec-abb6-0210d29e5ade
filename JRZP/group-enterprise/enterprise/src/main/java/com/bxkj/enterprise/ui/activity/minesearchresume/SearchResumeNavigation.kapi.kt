package com.bxkj.enterprise.ui.activity.minesearchresume

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/27
 * @version: V1.0
 */
class SearchResumeNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/search"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}