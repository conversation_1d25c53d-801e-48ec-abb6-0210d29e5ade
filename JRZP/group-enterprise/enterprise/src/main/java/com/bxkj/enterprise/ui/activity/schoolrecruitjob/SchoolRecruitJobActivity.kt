package com.bxkj.enterprise.ui.activity.schoolrecruitjob

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.JobItem
import com.bxkj.enterprise.databinding.EnterpriseActivitySchoolRecruitJobBinding
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.SchoolRecruitJobNavigation.Companion.EXTRA_JOB_LIST
import com.bxkj.enterprise.ui.activity.schoolrecruitjob.editor.SchoolRecruitJobEditorActivity
import com.sanjindev.pagestatelayout.OnStateSetUpListener

/**
 *
 * @author: sanjin
 * @date: 2022/4/19
 */
@Route(path = SchoolRecruitJobNavigation.PATH)
class SchoolRecruitJobActivity :
    BaseDBActivity<EnterpriseActivitySchoolRecruitJobBinding, SchoolRecruitJobViewModel>() {

    companion object {

        fun newIntent(context: Context, jobList: List<JobItem>?): Intent {
            return Intent(context, SchoolRecruitJobActivity::class.java).apply {
                putParcelableArrayListExtra(EXTRA_JOB_LIST, jobList?.let { ArrayList(it) })
            }
        }
    }

    private val editJobLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            viewModel.handleActivityResult(it)
        }

    override fun getViewModelClass(): Class<SchoolRecruitJobViewModel> =
        SchoolRecruitJobViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_school_recruit_job

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupJobList()
        subscribeViewModelEvent()

        viewModel.start(getIntentJobList())
    }

    private fun getIntentJobList(): ArrayList<JobItem>? {
        return intent.getParcelableArrayListExtra(EXTRA_JOB_LIST)
    }

    private fun setupJobList() {
        val jobListAdapter =
            SimpleDiffListAdapter(
                R.layout.enterprise_recycler_school_recruit_job_item,
                JobItem.DiffCallback()
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.let {
                            val item = it[position]
                            if (v.id == R.id.iv_delete) {
                                viewModel.deleteJob(item)
                            } else {
                                editJobLauncher.launch(
                                    SchoolRecruitJobEditorActivity.newIntent(
                                        this@SchoolRecruitJobActivity,
                                        SchoolRecruitJobEditorActivity.TYPE_EDIT,
                                        item,
                                        position
                                    )
                                )
                            }
                        }
                    }
                }, R.id.iv_edit, R.id.iv_delete)
            }

        viewBinding.recyclerJobList.apply {
            layoutManager = LinearLayoutManager(this@SchoolRecruitJobActivity)
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f4f4f4_6))
                    .drawFoot(true)
                    .build()
            )
            adapter = jobListAdapter
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.showEmptyPageCommand.observe(this) {
            viewBinding.pslContent.show(object : OnStateSetUpListener<EmptyPageState> {
                override fun onStateSetUp(pageState: EmptyPageState) {
                    pageState.setImage(R.drawable.enterprise_img_no_school_recruit_job)
                    pageState.setContent("暂无招聘职位信息")
                }
            })
        }

        viewModel.jobList.observe(this) {
            if (!it.isNullOrEmpty()) {
                viewBinding.pslContent.hidden()
            }
        }

        viewModel.saveAndBackCommand.observe(this) {
            setResult(
                RESULT_OK,
                Intent().putParcelableArrayListExtra(EXTRA_JOB_LIST, ArrayList(it))
            )
            finish()
        }

        viewModel.toAddJobCommand.observe(this) {
            toAddJob(it)
        }

        viewModel.showDuplicateNameAlertCommand.observe(this) { jobInfo ->
            ActionDialog.Builder()
                .setTitle(getString(R.string.tips))
                .setContent("存在重复职位名【${jobInfo.jobName}】，请重新发布")
                .setConfirmText("重新发布")
                .setOnConfirmClickListener {
                    toAddJob(jobInfo)
                }.build().show(supportFragmentManager)
        }
    }

    private fun toAddJob(it: JobItem?) {
        editJobLauncher.launch(
            SchoolRecruitJobEditorActivity.newIntent(
                this,
                SchoolRecruitJobEditorActivity.TYPE_ADD,
                it
            )
        )
    }


}