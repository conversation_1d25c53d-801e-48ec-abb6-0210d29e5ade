package com.bxkj.enterprise.ui.activity.searchresume;

import android.app.Activity;
import android.content.Intent;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bxkj.common.base.BaseActivity;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.ecommon.widget.ClearEditText;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.ui.activity.searchresumeresult.SearchResultActivity;
import com.zaaach.citypicker.CityPicker;
import com.zaaach.citypicker.adapter.OnPickListener;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.LocatedCity;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.searchjob
 * @Description: 搜索职位
 * @TODO: TODO
 * @date 2018/4/10
 */

public class SearchResumeActivity extends BaseActivity {

  private static final String IS_NEED_BACK = "isNeedBack";
  private static final String TABLE_NAME = "tableName";
  public static final String SEARCH_TEXT = "searchText";

  private TextView tvLocation;
  private ClearEditText etSearchJob;
  private LinearLayout llSearchTool;
  private RecyclerView recyclerSearchRecord;

  private List<String> mSearchRecordList;
  private SearchSqlLiteHelper mSearchSqlLiteHelper;
  private SearchResumeRecordListAdapter mSearchResumeRecordListAdapter;

  private CityPicker mCityPicker;
  private String mTableName;
  private boolean isNeedBack;

  public static Intent newIntent(Activity activity, boolean needBack, String tableName) {
    Intent intent = new Intent(activity, SearchResumeActivity.class);
    intent.putExtra(IS_NEED_BACK, needBack);
    intent.putExtra(TABLE_NAME, tableName);
    return intent;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_search_resume;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    initSearchDbHelper();
    initCityPicker();
    initLocation();
    handleSearch();
  }

  @Override
  protected void onResume() {
    super.onResume();
    getRecordListData();
  }

  private void initSearchDbHelper() {
    mTableName = getIntent().getStringExtra(TABLE_NAME);
    mSearchSqlLiteHelper = new SearchSqlLiteHelper(this,
      CheckUtils.isNullOrEmpty(mTableName) ? SearchSqlLiteHelper.SEARCH_RESUME_RECORD_TABLE_NAME
        : mTableName);
    getRecordListData();
  }

  @Override
  protected void initIntent(Intent intent) {
    isNeedBack = intent.getBooleanExtra(IS_NEED_BACK, false);
  }

  private void initCityPicker() {
    mCityPicker = new CityPicker()
      .setLocatedCity(
        new LocatedCity(UserUtils.getUserLocateCityName(), ECommonApiConstants.NO_TEXT,
          String.valueOf(UserUtils.getUserLocateCityId())))
      .setFragmentManager(getSupportFragmentManager())
      .enableAnimation(true)
      .setAnimationStyle(R.style.common_RightPopupAnim)
      .setOnPickListener(new OnPickListener() {
        @Override
        public void onPick(int position, City data) {
          if (data != null) {
            tvLocation.setText(data.getName());
            UserUtils.saveUserSelectedCityInfo(Integer.parseInt(data.getCode()), data.getName());
          }
        }

        @Override
        public void onLocate() {

        }
      });
  }

  private void initLocation() {
    tvLocation.setText(
      !CheckUtils.isNullOrEmpty(UserUtils.getUserSelectedCityName())
        ? UserUtils.getUserSelectedCityName() : "请选择");
  }

  private void handleSearch() {
    etSearchJob.setOnEditorActionListener((textView, i, keyEvent) -> {
      if (i == EditorInfo.IME_ACTION_SEARCH) {
        toSearchResultActivity(etSearchJob.getText().toString());
        return true;
      }
      return false;
    });
  }

  //跳转到搜索结果页面
  private void toSearchResultActivity(String searchText) {
    if (!CheckUtils.isNullOrEmpty(searchText)) {
      //关键字中的特殊字符转义
      String sqlKeyword = CheckUtils.sqliteEscape(searchText);
      if (mSearchSqlLiteHelper.hasData(sqlKeyword)) {
        mSearchSqlLiteHelper.deleteItemList(sqlKeyword);
      }
      mSearchSqlLiteHelper.addItemList(sqlKeyword);
    }
    if (isNeedBack) {
      Intent intent = new Intent();
      intent.putExtra(SEARCH_TEXT, searchText);
      setResult(RESULT_OK, intent);
    } else {
      if (mTableName.equals(SearchSqlLiteHelper.SEARCH_RESUME_RECORD_TABLE_NAME)) {
        SearchResumeParameters searchResumeParameters = new SearchResumeParameters();
        searchResumeParameters.setTitle(searchText);
        SearchResultActivity.start(this, searchResumeParameters, true);
      }
    }
    finish();
  }

  private void getRecordListData() {
    mSearchRecordList = mSearchSqlLiteHelper.getAllData();
    if (CheckUtils.isNullOrEmpty(mSearchRecordList)) {
      llSearchTool.setVisibility(View.GONE);
    } else {
      llSearchTool.setVisibility(View.VISIBLE);
    }
    Collections.reverse(mSearchRecordList);
    //        mSearchResumeRecordListAdapter = new SearchResumeRecordListAdapter(this, mSearchRecordList, R.layout.enterprise_recycler_search_resume_record_item);
    recyclerSearchRecord.setLayoutManager(new LinearLayoutManager(this));
    recyclerSearchRecord.setAdapter(mSearchResumeRecordListAdapter);

    mSearchResumeRecordListAdapter.setOnItemClickListener((view, position) -> {
      if (view.getId() == R.id.tv_search_record_item) {
        toSearchResultActivity(mSearchRecordList.get(position));
      } else {
        new EActionDialog.Builder()
          .setTitle("删除")
          .setContent("删除历史记录" + "“" + mSearchRecordList.get(position) + "”")
          .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            mSearchSqlLiteHelper.deleteItemList(mSearchRecordList.get(position));
            mSearchRecordList.remove(position);
            mSearchResumeRecordListAdapter.notifyDataSetChanged();
          }).build().show(getSupportFragmentManager(), EActionDialog.TAG);
      }
    });
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_location) {
      mCityPicker.show();
    } else if (view.getId() == R.id.tv_cancel_search) {
      SystemUtil.hideSoftKeyboard(this);
      finish();
    } else {
      if (!CheckUtils.isNullOrEmpty(mSearchRecordList)) {
        new EActionDialog.Builder()
          .setTitle(getString(R.string.common_delete))
          .setContent(getString(R.string.common_confirm_clear_search_record))
          .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            mSearchSqlLiteHelper.deleteAllData();
            mSearchRecordList.clear();
            mSearchResumeRecordListAdapter.notifyDataSetChanged();
          }).build().show(getSupportFragmentManager(), EActionDialog.TAG);
      }
    }
  }

  private void bindView(View bindSource) {
    tvLocation = bindSource.findViewById(R.id.tv_location);
    etSearchJob = bindSource.findViewById(R.id.et_search_job);
    llSearchTool = bindSource.findViewById(R.id.ll_search_tool);
    recyclerSearchRecord = bindSource.findViewById(R.id.recycler_search_record);
    bindSource.findViewById(R.id.tv_location).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_cancel_search).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.iv_clear_search_record).setOnClickListener(v -> onViewClicked(v));
  }
}
