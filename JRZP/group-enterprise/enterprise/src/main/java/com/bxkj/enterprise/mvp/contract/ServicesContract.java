package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.CustomServiceData;

/**
 * @Project: ejrzp
 * @Description:
 * @author:45457
 * @date: 2020/6/6
 * @version: V1.0
 */
public interface ServicesContract {

    interface View extends BaseView {
        void getServicesInfoSuccess(CustomServiceData customServiceData);
    }

    public abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getServicesInfo();
    }
}
