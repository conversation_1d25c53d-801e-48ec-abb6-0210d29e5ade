package com.bxkj.enterprise.ui.fragment.receivedresume;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.api.parameters.GetReceivedResumeListParameters;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.resumelist
 * @Description: ResumeList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ReceiveResumeListPresenter extends ReceiveResumeListContract.Presenter {

    private static final String TAG = ReceiveResumeListPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public ReceiveResumeListPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getReceiveResumeList(int userId, GetReceivedResumeListParameters getReceivedResumeListParameters, int pageIndex, int pageSize) {
        mBusinessApi.getReceiveResumeList(userId, getReceivedResumeListParameters, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getResumeListSuccess((List<ResumeItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30002)
                            mView.onResultNoData();
                        else
                            mView.onRequestError(respondThrowable);

                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
