package com.bxkj.enterprise.ui.activity.schoolrecruitjob.editor

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.RegularUtils
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.common.util.kotlin.clearAllBlank
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.data.JobItem
import com.bxkj.enterprise.data.SchoolRecruitCityData
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/4/19
 */
class SchoolRecruitJobEditorViewModel @Inject constructor() : BaseViewModel() {

    val jobInfo = MutableLiveData<JobItem>()

    val showCityPickerCommand = LiveEvent<List<String>?>()

    val addSuccessEvent = LiveEvent<JobItem>()
    val editSuccessEvent = LiveEvent<JobItem>()

    private var _editType = SchoolRecruitJobEditorActivity.TYPE_ADD
    val isEditMode = ObservableBoolean(false)

    fun start(editType: Int, jobInfo: JobItem?) {
        this._editType = editType
        if (editType == SchoolRecruitJobEditorActivity.TYPE_EDIT) {
            isEditMode.set(true)
        }
        jobInfo?.let {
            if (editType == SchoolRecruitJobEditorActivity.TYPE_ADD) {
                it.jobName = ""
            }
            this.jobInfo.value = it
        } ?: let { this.jobInfo.value = JobItem() }
    }

    fun showCityPicker() {
        jobInfo.value?.let {
            showCityPickerCommand.setValue(it.cityName.getOrDefault().split(","))
        }
    }

    fun setJobCity(selectedCity: List<SchoolRecruitCityData>) {
        jobInfo.value?.let { job ->
            job.updateCityName(selectedCity.map { it.name }.appendItem())
        }
    }

    fun save() {
        jobInfo.value?.let {
            if (it.hasEmpty()) {
                showToast("请完善必填信息")
            } else {
                if (!RegularUtils.isEmail(it.email.clearAllBlank())) {
                    showToast("投递邮箱格式有误")
                    return
                }
                if (_editType == SchoolRecruitJobEditorActivity.TYPE_ADD) {
                    addSuccessEvent.value = it
                } else {
                    editSuccessEvent.value = it
                }
            }
        }
    }
}