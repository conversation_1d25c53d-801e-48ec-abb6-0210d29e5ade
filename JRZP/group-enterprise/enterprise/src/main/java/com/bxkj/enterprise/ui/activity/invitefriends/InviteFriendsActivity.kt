package com.bxkj.enterprise.ui.activity.invitefriends

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.lifecycle.Observer
import com.bxkj.common.widget.popup.CustomPopup
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityInviteFriendsBinding

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.invitefriends
 * @Description: 邀请好友
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/8/3
 * @version V1.0
 */
class InviteFriendsActivity :
  EBaseDBActivity<EnterpriseActivityInviteFriendsBinding, InviteFriendsViewModel>(),
  View.OnClickListener {
  companion object {

    fun newIntent(context: Context): Intent {
      return Intent(context, InviteFriendsActivity::class.java)
    }
  }

  private lateinit var mSharePopup: CustomPopup

  override fun getViewModelClass(): Class<InviteFriendsViewModel> =
    InviteFriendsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_invite_friends

  override fun initPage(savedInstanceState: Bundle?) {
    setupSharePopup()
    viewBinding.onClickListener = this
    subscribeShareTargetUrlChange()
    viewModel.start()
  }

  private fun subscribeShareTargetUrlChange() {
    viewModel.targetUrl.observe(this, Observer { targetUrl ->
      targetUrl?.let {
//        Observable.just(targetUrl)
//          .map {
//            QRCodeEncoder.syncEncodeQRCode(
//              it,
//              DensityUtils.dp2px(this, 150f),
//              Color.BLACK,
//              BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher)
//            )
//          }
//          .subscribeOn(Schedulers.io())
//          .observeOn(AndroidSchedulers.mainThread())
//          .subscribe { bitmap ->
//            dataBinding.ivQrCode.setImageBitmap(bitmap)
//          }
        setSharePopupClickListener(targetUrl)
      }
    })
  }

  private fun setSharePopupClickListener(targetUrl: String) {
    mSharePopup.findViewById<FrameLayout>(R.id.fl_share_to_wechat)
      .setOnClickListener(getSharePopupClickListener(targetUrl))
    mSharePopup.findViewById<FrameLayout>(R.id.fl_share_to_wechat_moments)
      .setOnClickListener(getSharePopupClickListener(targetUrl))
    mSharePopup.findViewById<FrameLayout>(R.id.fl_share_to_qq)
      .setOnClickListener(getSharePopupClickListener(targetUrl))
    mSharePopup.findViewById<FrameLayout>(R.id.fl_share_to_qq_moments)
      .setOnClickListener(getSharePopupClickListener(targetUrl))
    mSharePopup.findViewById<TextView>(R.id.tv_cancel)
      .setOnClickListener(getSharePopupClickListener(targetUrl))
  }

  private fun setupSharePopup() {
    mSharePopup = CustomPopup.Builder(this)
      .setLayoutResId(R.layout.enterprise_popup_share_app)
      .setAnimStyle(R.style.common_BottomPopupAnim)
      .build()
  }

  private fun getSharePopupClickListener(targetUrl: String): View.OnClickListener {
    return View.OnClickListener {
//      when (it.id) {
//        R.id.fl_share_to_wechat -> {
//          ShareUtils.shareUrlToWechat(
//            applicationContext,
//            getString(R.string.invite_friends_share_title),
//            getString(R.string.invite_friends_share_content),
//            targetUrl,
//            null,
//            SendMessageToWX.Req.WXSceneSession
//          )
//        }
//        R.id.fl_share_to_wechat_moments -> {
//          ShareUtils.shareUrlToWechat(
//            applicationContext,
//            getString(R.string.invite_friends_share_title),
//            getString(R.string.invite_friends_share_content),
//            targetUrl,
//            null,
//            SendMessageToWX.Req.WXSceneTimeline
//          )
//        }
//        R.id.fl_share_to_qq -> {
//          ShareUtils.shareToQQ(
//            this,
//            QQShare.SHARE_TO_QQ_TYPE_DEFAULT,
//            getString(R.string.invite_friends_share_title),
//            getString(R.string.invite_friends_share_content),
//            targetUrl,
//            getShareToQQUiListener()
//          )
//        }
//        R.id.fl_share_to_qq_moments -> {
//          ShareUtils.shareToQzone(
//            this,
//            getString(R.string.invite_friends_share_title),
//            getString(R.string.invite_friends_share_content),
//            targetUrl,
//            getShareToQQUiListener()
//          )
//        }
//        R.id.tv_cancel -> {
//          mSharePopup.dismiss()
//        }
//      }
    }
  }

//  private fun getShareToQQUiListener(): IUiListener {
//    return object : IUiListener {
//      override fun onComplete(o: Any) {
//      }
//
//      override fun onError(uiError: UiError) {
//      }
//
//      override fun onCancel() {
//      }
//    }
//  }

  override fun onClick(v: View) {
    if (v.id == R.id.tv_share) {
      mSharePopup.showBottom()
    }
  }

}