package com.bxkj.enterprise.ui.activity.hredit

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.util.imageloader.UCropEngine
import com.bxkj.common.util.kotlin.applyDefaultConfig
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.ecommon.util.BitmapUtils
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.HrItemData
import com.bxkj.enterprise.databinding.EnterpriseActivityHrEditV2Binding
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.yalantis.ucrop.UCrop.Options

/**
 *
 * @author: sanjin
 * @date: 2022/11/19
 */
class HREditActivityV2 : BaseDBActivity<EnterpriseActivityHrEditV2Binding, HREditViewModel>(),
  OnClickListener {

  companion object {

    private const val EXTRA_HR_INFO = "HR_INFO"

    @JvmStatic
    fun newIntent(context: Context, hrInfo: HrItemData? = null): Intent {
      return Intent(context, HREditActivityV2::class.java).apply {
        putExtra(EXTRA_HR_INFO, hrInfo)
      }
    }
  }

  private val _extraHrInfo by lazy { intent.getParcelableExtra<HrItemData>(EXTRA_HR_INFO) }

  override fun getViewModelClass(): Class<HREditViewModel> = HREditViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_hr_edit_v2

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    setupTitleBarRightOptionClickListener()

    viewModel.start(_extraHrInfo)
  }

  private fun subscribeViewModelEvent() {
    viewModel.submitSuccessEvent.observe(this, EventObserver {
      showToast(R.string.save_success)
      setResult(RESULT_OK)
      finish()
    })
  }

  private fun setupTitleBarRightOptionClickListener() {
    viewBinding.titleBar.setRightOptionClickListener {
      viewModel.submit()
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.ll_avatar -> {
          PermissionUtils.requestPermission(
            this,
            getString(R.string.permission_tips_title),
            getString(R.string.permission_select_img_tips),
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                PictureSelector.create(this@HREditActivityV2)
                  .openGallery(SelectMimeType.ofImage())
                  .applyDefaultConfig()
                  .setSelectionMode(SelectModeConfig.SINGLE)
                  .isDirectReturnSingle(true)
                  .setCropEngine(UCropEngine(Options().apply {
                    withAspectRatio(1f, 1f)
                    setCircleDimmedLayer(true)
                  }))
                  .forResult(PictureConfig.CHOOSE_REQUEST)
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE
          )
        }

        R.id.yui_gender -> {
          MenuPopup.Builder(this)
            .setData(arrayOf("男", "女"))
            .setSelected(viewBinding.yuiGender.getContentView()?.text.toString())
            .setOnItemClickListener { _, position ->
              viewModel.setHRGender(position)
            }
            .build().show()
        }
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST && resultCode == RESULT_OK && data != null) {
      val resultImgPath = data.getSelectedFirstMediaPath()
      ImageLoader.loadImage(
        this,
        GlideLoadConfig.Builder().url(resultImgPath).into(viewBinding.ivHeader).build()
      )
      viewModel.setHRAvatarBase64(
        ECommonApiConstants.IMG_UPLOAD_PREFIX + BitmapUtils.bitmapToString(
          resultImgPath,
          0.1f
        )
      )
    }
  }
}