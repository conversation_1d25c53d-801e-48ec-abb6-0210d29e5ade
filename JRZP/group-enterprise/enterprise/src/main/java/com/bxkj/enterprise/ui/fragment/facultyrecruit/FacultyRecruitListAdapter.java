package com.bxkj.enterprise.ui.fragment.facultyrecruit;

import android.content.Context;
import androidx.annotation.NonNull;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.BR;
import com.bxkj.enterprise.data.FacultyRecruitItemData;
import com.bxkj.enterprise.ui.activity.news.NewsActivity;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.facultyrecruit
 * @Description:
 * @TODO: TODO
 * @date 2019/6/15
 */
public class FacultyRecruitListAdapter extends SuperAdapter<FacultyRecruitItemData> {
    public FacultyRecruitListAdapter(Context context, int layoutId) {
        super(context, layoutId);
    }

    @Override
    protected void convert(@NonNull SuperViewHolder holder, int viewType, FacultyRecruitItemData facultyRecruitItemData, int position) {
        holder.bind(BR.facultyRecruitItem, facultyRecruitItemData);
        holder.itemView.setOnClickListener(v -> mContext.startActivity(NewsActivity.newIntent(mContext, facultyRecruitItemData.getId())));
    }
}
