package com.bxkj.enterprise.ui.fragment.ordermanagement

import com.bxkj.common.di.module.ApplicationModule
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.enterprise.api.BusinessApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject
import javax.inject.Named

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.ordermanagement
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/10/12
 * @version V1.0
 */
class OrderManagementPresenter @Inject constructor(
  @Named(ApplicationModule.USER_ID) userId: Int,
  api: BusinessApi
) : OrderManagementContract.Presenter() {

  var mEnterpriseApi = api
  var mUserId = userId

  override fun activateOrder(orderId: Int, position: Int) {
    mView.showLoading()
    mEnterpriseApi.activateOrder(mUserId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          mView.activateOrderSuccess(position)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mView.hiddenLoading()
          mView.onError(respondThrowable.errMsg)
        }

        override fun onComplete() {
          mView.hiddenLoading()
        }
      })
  }

  override fun deleteOrderById(orderId: Int, position: Int) {
    mView.showLoading()
    mEnterpriseApi.deleteOrder(mUserId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          mView.deleteOrderSuccess(position)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mView.hiddenLoading()
          mView.onError(respondThrowable.errMsg)
        }

        override fun onComplete() {
          mView.hiddenLoading()
        }
      })
  }
}