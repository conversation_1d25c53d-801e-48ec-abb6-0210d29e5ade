package com.bxkj.enterprise.ui.activity.collection

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.therouter.router.Route
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.BActivityFavoritesBinding
import com.bxkj.enterprise.ui.fragment.collection.FavoritesResumeListFragment
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * Description: 我的收藏
 * Author:Sanjin
 * Date:2024/3/21
 **/
@Route(path = FavoritesResumeNavigation.PATH)
class FavoritesActivity : BaseDBActivity<BActivityFavoritesBinding, BaseViewModel>() {

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_favorites

  override fun initPage(savedInstanceState: Bundle?) {
    setupContent()
  }

  private fun setupContent() {
    viewBinding.vpCollectionContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 2

      override fun createFragment(position: Int): Fragment {
        return if (position == 0) {
          FavoritesResumeListFragment.newInstance(FavoritesResumeListFragment.PAGE_TYPE_FAVORITES)
        } else {
          FavoritesResumeListFragment.newInstance(FavoritesResumeListFragment.PAGE_TYPE_DOWNLOAD)
        }
      }
    }

    viewBinding.indicatorType.navigator = CommonNavigator(this).apply {
      isAdjustMode = true
      adapter = MagicIndicatorAdapter(getResArray(R.array.collection_tags)).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpCollectionContent.currentItem = index
          }
        })
      }
    }

    viewBinding.vpCollectionContent.attachIndicator(viewBinding.indicatorType)
  }
}