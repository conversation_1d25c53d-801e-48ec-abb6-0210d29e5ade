package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.JobTemplateItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.jobtemplate
 * @Description: JobTemplate
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface GetJobTemplateContract {
    interface View extends BaseView {
        void getJobTemplateListSuccess(List<JobTemplateItemData> jobTemplateItemDataList);

        void noJobTemplate();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getJobTemplateList(int userId);
    }
}
