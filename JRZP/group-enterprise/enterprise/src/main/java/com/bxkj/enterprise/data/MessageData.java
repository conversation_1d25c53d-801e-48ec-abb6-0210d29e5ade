package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.BR;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data.db
 * @Description:
 * @TODO: TODO
 * @date 2018/11/23
 */
public class MessageData extends BaseObservable {

    private int id;

    private String typeName = ECommonApiConstants.NO_TEXT;

    private int state;

    private int otherid;

    private int resid;

    private int uid;

    private int cuid;

    private String content = ECommonApiConstants.NO_TEXT;

    private int look;

    private String date = ECommonApiConstants.NO_TEXT;

    private CompanyBean company;

    private int index;

    private int nolookCount;

    private int type;

    private String relName = ECommonApiConstants.NO_TEXT;

    private String cUserName = ECommonApiConstants.NO_TEXT;

    private String cUserPhoto = ECommonApiConstants.NO_TEXT;

    private JInterviewBean jInterview;

    private int relId;

    private ApplicantData userData;

    @Bindable
    public boolean isViewed() {
        return look > 0;
    }

    public void updateViewState(boolean viewed) {
        setLook(viewed ? 1 : 0);
        notifyPropertyChanged(BR.viewed);
    }

    public static class ApplicantData {

        private int id;

        private String nickName;

        private int sex;

        private String photo;

        private int age;

        private String quaName;

        private String cityName;

        private String wtName;

        private String otherName;

        private int otherID;

        private String desiredJob;

        private String selfIntro;

        private int applyState;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public int getSex() {
            return sex;
        }

        public void setSex(int sex) {
            this.sex = sex;
        }

        public String getPhoto() {
            return photo;
        }

        public void setPhoto(String photo) {
            this.photo = photo;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getQuaName() {
            return quaName;
        }

        public void setQuaName(String quaName) {
            this.quaName = quaName;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public String getWtName() {
            return wtName;
        }

        public void setWtName(String wtName) {
            this.wtName = wtName;
        }

        public String getOtherName() {
            return otherName;
        }

        public void setOtherName(String otherName) {
            this.otherName = otherName;
        }

        public int getOtherID() {
            return otherID;
        }

        public void setOtherID(int otherID) {
            this.otherID = otherID;
        }

        public String getDesiredJob() {
            return desiredJob;
        }

        public void setDesiredJob(String desiredJob) {
            this.desiredJob = desiredJob;
        }

        public String getSelfIntro() {
            return selfIntro;
        }

        public void setSelfIntro(String selfIntro) {
            this.selfIntro = selfIntro;
        }

        public int getApplyState() {
            return applyState;
        }

        public void setApplyState(int applyState) {
            this.applyState = applyState;
        }
    }

    public ApplicantData getUserData() {
        return userData;
    }

    public void setUserData(ApplicantData userData) {
        this.userData = userData;
    }

    public ApplicantResumeData getTransformResumeInfo() {
        if (userData == null) {
            return null;
        } else {
            ApplicantResumeData transformResume = new ApplicantResumeData();
            transformResume.setDesiredJob(userData.desiredJob);
            transformResume.setSelfIntro(userData.selfIntro);
            transformResume.setId(userData.otherID);
            transformResume.setWtName(userData.getWtName());
            transformResume.setAge(userData.getAge());
            transformResume.setEdate1(date.replaceAll("-", "/"));
            transformResume.setApplyState(userData.applyState);
            ResumeBaseInfoData userInfo = new ResumeBaseInfoData();
            userInfo.setCityJZName(userData.cityName);
            userInfo.setTx(userData.photo);
            userInfo.setSex(userData.sex);
            userInfo.setName(userData.nickName);
            userInfo.setQuaName(userData.getQuaName());
            transformResume.setUbInfo(userInfo);
            return transformResume;
        }
    }

    public int getRelId() {
        return relId;
    }

    public void setRelId(int relId) {
        this.relId = relId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public static MessageData fromPushData(int companyId, String companyName) {
        return new MessageData(companyId, CompanyBean.fromName(companyName));
    }

    public MessageData(int cuid, CompanyBean company) {
        this.cuid = cuid;
        this.company = company;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public int getOtherid() {
        return otherid;
    }

    public void setOtherid(int otherid) {
        this.otherid = otherid;
    }

    public int getResid() {
        return resid;
    }

    public void setResid(int resid) {
        this.resid = resid;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getCuid() {
        return cuid;
    }

    public void setCuid(int cuid) {
        this.cuid = cuid;
    }

    public String getContent() {
        if (type == 40) {
            return relName;
        }
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getLook() {
        return look;
    }

    public void setLook(int look) {
        this.look = look;
    }

    public String getDate() {
        return date;
    }

    public String getDateDiffText() {
        return TimeUtils.getTwoTimeDiff(date, "yyyy-MM-dd HH:mm");
    }

    public void setDate(String date) {
        this.date = date;
    }

    public CompanyBean getCompany() {
        return company;
    }

    public String getCompanyName() {
        return company == null || CheckUtils.isNullOrEmpty(company.name) ? "该公司已注销" : company.name;
    }

    public void setCompany(CompanyBean company) {
        this.company = company;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    @Bindable
    public int getNolookCount() {
        return nolookCount;
    }

    public void setNolookCount(int nolookCount) {
        this.nolookCount = nolookCount;
        notifyPropertyChanged(BR.nolookCount);
    }

    public void read() {
        setNolookCount(0);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getRelName() {
        return relName;
    }

    public void setRelName(String relName) {
        this.relName = relName;
    }

    public String getMsgType() {
        return "[" + typeName + "]";
    }

    public String getAuthor() {
        if (type == 40) {
            return cUserName;
        } else {
            return company.name;
        }
    }

    public String getAuthorAvatar() {
        if (type == 40) {
            return cUserPhoto;
        } else {
            return company.domain + company.logo;
        }
    }

    public JInterviewBean getInterview() {
        return jInterview;
    }

    public void setInterview(JInterviewBean jInterview) {
        this.jInterview = jInterview;
    }

    public static class JInterviewBean {

        /**
         * id : 0
         * cuid : 0
         * uid : 0
         * relid : 0
         * resid : 0
         * data : null
         * viewDate : 2018-11-19 02:53
         * viewAddress : 杭州滨江区浦沿街道伟业路1号
         * lxr : 刘先生
         * phone : 15868480780
         * isSms : 0
         * remark : 经过我公司HR的初步筛选，认为你与我们的职位要求很匹配，现诚邀你来我公司面谈。请准时出席，如时间有变
         * state : 2
         */

        private int id;

        private int cuid;

        private int uid;

        private int relid;

        private int resid;

        private Object data;

        private String viewDate;

        private String viewAddress;

        private String lxr;

        private String phone;

        private int isSms;

        private String remark;

        private int state;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getCuid() {
            return cuid;
        }

        public void setCuid(int cuid) {
            this.cuid = cuid;
        }

        public int getUid() {
            return uid;
        }

        public void setUid(int uid) {
            this.uid = uid;
        }

        public int getRelid() {
            return relid;
        }

        public void setRelid(int relid) {
            this.relid = relid;
        }

        public int getResid() {
            return resid;
        }

        public void setResid(int resid) {
            this.resid = resid;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public String getViewDate() {
            return viewDate;
        }

        public void setViewDate(String viewDate) {
            this.viewDate = viewDate;
        }

        public String getViewAddress() {
            return viewAddress.replaceAll("\n", "");
        }

        public void setViewAddress(String viewAddress) {
            this.viewAddress = viewAddress;
        }

        public String getLxr() {
            return lxr;
        }

        public void setLxr(String lxr) {
            this.lxr = lxr;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public int getIsSms() {
            return isSms;
        }

        public void setIsSms(int isSms) {
            this.isSms = isSms;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }
    }

    public static class CompanyBean implements Parcelable {

        private String name;

        private String logo;

        private String domain;

        public static CompanyBean fromName(String companyName) {
            return new CompanyBean(companyName);
        }

        public CompanyBean(String name) {
            this.name = name;
        }

        protected CompanyBean(Parcel in) {
            name = in.readString();
            logo = in.readString();
            domain = in.readString();
        }

        public static final Creator<CompanyBean> CREATOR = new Creator<CompanyBean>() {
            @Override
            public CompanyBean createFromParcel(Parcel in) {
                return new CompanyBean(in);
            }

            @Override
            public CompanyBean[] newArray(int size) {
                return new CompanyBean[size];
            }
        };

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getIntegratedLogo() {
            return domain + "/" + logo;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeString(name);
            parcel.writeString(logo);
            parcel.writeString(domain);
        }
    }
}
