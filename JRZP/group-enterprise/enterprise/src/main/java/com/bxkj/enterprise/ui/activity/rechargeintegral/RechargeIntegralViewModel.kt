package com.bxkj.enterprise.ui.activity.rechargeintegral

import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.enterprise.data.RechargeIntegralItemData
import com.bxkj.enterprise.data.source.AccountInfoRepo
import com.bxkj.enterprise.data.source.RechargeRepo
import com.bxkj.jrzp.user.data.AccountVipData
import javax.inject.Inject

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.rechargeintegral
 * @Description:
 * <AUTHOR>
 * @date 2019/8/12
 * @version V1.0
 */
class RechargeIntegralViewModel @Inject constructor(
  private val mRechargeRepo: RechargeRepo,
  private val mAccountInfoRepo: AccountInfoRepo
) : BaseViewModel() {

  val selectedItem = MutableLiveData<RechargeIntegralItemData>()
  val createOrderEvent = LiveEvent<Int>()
  val listViewModel = RefreshListViewModel()
  val integralBalance = ObservableInt()

  init {
    listViewModel.refreshLayoutViewModel.enableRefresh(false)
    listViewModel.refreshLayoutViewModel.enableLoadMore(false)
  }

  fun start() {
    mAccountInfoRepo.run {
      getAccountVipInfo(getSelfUserID(), object :
        ResultDataCallBack<com.bxkj.jrzp.user.data.AccountVipData> {
        override fun onSuccess(data: AccountVipData?) {
          data?.let {
            integralBalance.set(data.integral)
          }
        }

        override fun onError(respondThrowable: RespondThrowable) {
          showToast(respondThrowable.errMsg)
        }
      })
    }

    mRechargeRepo.getRechargeIntegralList(object :
        ResultListCallBack<List<RechargeIntegralItemData>> {
      override fun onSuccess(data: List<RechargeIntegralItemData>?) {
        data?.let {
          setSelectedIntegralItem(it[0])
          listViewModel.addAll(data)
        }
      }

      override fun onNoMoreData() {
        listViewModel.loadError()
      }

      override fun onError(respondThrowable: RespondThrowable) {
        listViewModel.loadError()
      }
    })
  }

  fun setSelectedIntegralItem(rechargeIntegralItem: RechargeIntegralItemData) {
    selectedItem.value = rechargeIntegralItem
  }

  fun createIntegralOrder() {
    selectedItem.value?.let {
      showLoading()
      mRechargeRepo.createRechargeOrder(getSelfUserID(), RechargeRepo.RECHARGE_FOR_INTEGRAL, it.price
        , object :
          ResultDataCallBack<String> {
          override fun onSuccess(data: String?) {
            hideLoading()
            createOrderEvent.value = data?.toInt()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            hideLoading()
            showToast(respondThrowable.errMsg)
          }
        })
    }
  }

  override fun onCleared() {
    super.onCleared()
    mRechargeRepo.clear()
    mAccountInfoRepo.clear()
  }
}