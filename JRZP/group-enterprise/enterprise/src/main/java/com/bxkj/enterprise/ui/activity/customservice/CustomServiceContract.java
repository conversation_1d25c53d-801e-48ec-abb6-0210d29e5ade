package com.bxkj.enterprise.ui.activity.customservice;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.customservice
 * @Description:
 * @TODO: TODO
 * @date 2019/6/3
 */
public class CustomServiceContract {
    interface View extends BaseView {
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
    }
}
