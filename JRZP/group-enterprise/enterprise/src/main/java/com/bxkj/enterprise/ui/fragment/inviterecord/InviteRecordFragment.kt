package com.bxkj.enterprise.ui.fragment.inviterecord

import android.os.Bundle
import com.bxkj.ecommon.base.EBaseDBFragment
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseFragmentInviteRecordBinding

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.fragment.inviterecord
 * @Description: 邀请好友记录
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
class InviteRecordFragment : EBaseDBFragment<EnterpriseFragmentInviteRecordBinding, InviteRecordViewModel>() {

    companion object {
        const val EXTRA_PAGE_TYPE = "page_type"
        const val TYPE_COMPLETED = 0
        const val TYPE_UNDONE = 1
        fun newInstance(pageIndex: Int): InviteRecordFragment {
            val bundle = Bundle()
            bundle.putInt(EXTRA_PAGE_TYPE, pageIndex)
            val fragment = InviteRecordFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun getViewModelClass(): Class<InviteRecordViewModel> = InviteRecordViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_fragment_invite_record

    override fun initPage() {
        dataBinding.viewModel = viewModel
        setupInviteRecordListAdapter()
        viewModel.start(arguments)
    }

    private fun setupInviteRecordListAdapter() {
        val inviteRecordListAdapter=InviteRecordListAdapter(parentActivity,R.layout.enterprise_recycler_invite_record_item)
        val recyclerInviteList=dataBinding.root.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recycler_content)
        recyclerInviteList.layoutManager= androidx.recyclerview.widget.LinearLayoutManager(context)
        viewModel.listViewModel.setAdapter(inviteRecordListAdapter)
    }
}