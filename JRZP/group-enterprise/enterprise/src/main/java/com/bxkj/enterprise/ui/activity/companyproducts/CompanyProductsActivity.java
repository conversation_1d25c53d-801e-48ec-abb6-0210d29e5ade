package com.bxkj.enterprise.ui.activity.companyproducts;

import android.content.Context;
import android.content.Intent;

import android.view.View;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.therouter.router.Route;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ProductItemData;
import com.bxkj.enterprise.ui.activity.companyproductsedit.CompanyProductsEditActivity;

import java.util.List;

import javax.inject.Inject;

/**
 * @date 2018/8/6
 */
@Route(path = CompanyProductNavigation.PATH)
public class CompanyProductsActivity extends BaseDaggerActivity
  implements CompanyProductsContract.View {

  private static final int TO_EDIT_PRODUCT_CODE = 1;
  @Inject
  CompanyProductsPresenter mCompanyProductsPresenter;

  private RecyclerView recyclerContent;

  private CompanyProductsAdapter mCompanyProductsAdapter;
  private PageStatusLayout mEmptyLayout;

  public static void start(Context context) {
    Intent starter = new Intent(context, CompanyProductsActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_company_products;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mCompanyProductsPresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.mine_company_products))
      .setRightText(getString(R.string.company_products_add))
      .setRightOptionClickListener(
        view -> startActivityForResult(CompanyProductsEditActivity.newIntent(this, null),
          TO_EDIT_PRODUCT_CODE));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mEmptyLayout = PageStatusLayout.wrap(recyclerContent);
    mCompanyProductsAdapter =
      new CompanyProductsAdapter(this, null, R.layout.enterprise_recycler_products_item);
    recyclerContent.setLayoutManager(new LinearLayoutManager(this));
    recyclerContent.addItemDecoration(new RecycleViewDivider(this, LinearLayoutManager.VERTICAL
      , DensityUtils.dp2px(this, 8), getMColor(R.color.common_f4f4f4), true));
    recyclerContent.setAdapter(mCompanyProductsAdapter);
    mCompanyProductsAdapter.setOnItemClickListener((view, position) -> {
      ProductItemData productItemData = mCompanyProductsAdapter.getData().get(position);
      if (view.getId() == R.id.tv_delete) {
        mCompanyProductsPresenter.deleteProduct(getMUserID(), productItemData.getId());
      } else {
        startActivityForResult(CompanyProductsEditActivity.newIntent(this, productItemData),
          TO_EDIT_PRODUCT_CODE);
      }
    });
    reloadData();
  }

  @Override
  public void getProductsListSuccess(List<ProductItemData> productItemDataList) {
    mEmptyLayout.hidden();
    mCompanyProductsAdapter.reset(productItemDataList);
  }

  @Override
  public void noProducts() {
    mEmptyLayout.show(PageStatusConfigFactory.newEmptyConfig());
  }

  @Override
  public void deleteSuccess() {
    reloadData();
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_EDIT_PRODUCT_CODE && resultCode == RESULT_OK) {
      reloadData();
    }
  }

  private void reloadData() {
    mCompanyProductsPresenter.getProductsList(getMUserID());
  }

  private void bindView(View bindSource) {
    recyclerContent = bindSource.findViewById(R.id.recycler_products);
  }
}
