package com.bxkj.enterprise.ui.activity.invoicedetails;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.InvoiceData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.invoicedetails
 * @Description: InvoiceDetails
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface InvoiceDetailsContract {
    interface View extends BaseView {
        void getInvoiceDetailsSuccess(InvoiceData invoiceData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getInvoiceDetails(int invoiceId);
    }
}
