package com.bxkj.enterprise.ui.fragment.conversationmsg

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/25
 * @version: V1.0
 */
class EnterpriseMsgNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/message"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}