package com.bxkj.enterprise.ui.activity.resumedetails.adapter;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeWorkExpData;

import java.util.List;

/**
 * @version V1.0
 * @Package com.bxkj.personal.adapter
 * @Description:
 * @TODO: TODO
 * @date 2018/5/9
 */

public class WorkExpAdapter extends SuperAdapter<ResumeWorkExpData> {
  public WorkExpAdapter(Context context, List<ResumeWorkExpData> list, int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, ResumeWorkExpData workExpItemData,
      int position) {
    holder.setText(R.id.tv_work_time,
        workExpItemData.getDate1().replaceAll("-", ".") + "—" + workExpItemData.getDate2()
            .replaceAll("-", "."));
    holder.setText(R.id.tv_company_and_position,
        workExpItemData.getWorkJobDesc());
    holder.setText(R.id.tv_job_responsibility, workExpItemData.getDes());
  }
}
