package com.bxkj.enterprise.data.db;

import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.db
 * @Description:
 * @TODO: TODO
 * @date 2018/11/5
 */
@Entity(tableName = "search_resume_record_table", indices = {@Index(value = "content")})
public class SearchResumeRecord {

    @PrimaryKey
    @NotNull
    private String content;

    public SearchResumeRecord(@NotNull String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
