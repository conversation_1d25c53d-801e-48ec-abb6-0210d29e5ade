package com.bxkj.enterprise.data

import android.os.Parcelable
import androidx.recyclerview.widget.DiffUtil
import kotlinx.parcelize.Parcelize

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/20
 * @version: V1.0
 */
@Parcelize
data class SearchCompanyResult(
    var id: Int,
    var comName: String
) : Parcelable {

    class DiffCallback : DiffUtil.ItemCallback<SearchCompanyResult>() {

        override fun areItemsTheSame(
            oldItem: SearchCompanyResult,
            newItem: SearchCompanyResult
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: SearchCompanyResult,
            newItem: SearchCompanyResult
        ): Boolean {
            return oldItem.comName == newItem.comName
        }
    }
}