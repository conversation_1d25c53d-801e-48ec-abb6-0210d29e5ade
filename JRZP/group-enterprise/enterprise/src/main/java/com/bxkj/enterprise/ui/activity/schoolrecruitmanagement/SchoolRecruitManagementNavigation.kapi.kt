package com.bxkj.enterprise.ui.activity.schoolrecruitmanagement

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2022/4/14
 */
class SchoolRecruitManagementNavigation {

    companion object {
        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/schoolrecruitmanagement"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }

}