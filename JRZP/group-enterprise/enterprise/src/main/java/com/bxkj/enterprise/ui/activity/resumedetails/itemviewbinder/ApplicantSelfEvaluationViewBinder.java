package com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 自我评价视图绑定
 * @TODO: TODO
 * @date 2018/5/9
 */

public class ApplicantSelfEvaluationViewBinder implements ItemViewBinder<String> {
    @Override
    public void onBindViewHolder(SuperViewHolder holder, String item, int position) {
        holder.setText(R.id.tv_self_evaluation, CheckUtils.isNullOrEmpty(item) ? "暂未完善" : item);
    }

    @Override
    public int getLayoutId() {
        return R.layout.enterprise_recycler_resume_self_evaluation;
    }
}
