package com.bxkj.enterprise.ui.activity.positionrefresh;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.PositionAutoRefreshData;

import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.positionrefresh
 * @Description: PositionRefresh
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface PositionRefreshContract {
    interface View extends BaseView {
        void getRefreshIntegralSuccess(int integral);

        void getPositionRefreshInfoSuccess(PositionAutoRefreshData positionAutoRefreshData);

        void refreshPositionSuccess();

        void refreshPositionFailed(String errorMsg);

        void getRefreshIntegralCountSuccess(int payment, int integral);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getIntegralForRefreshPosition(int userId);

        abstract void getPositionRefreshInfo(int userId, int positionId);

        abstract void refreshPosition(int userId, String positionIds, int payment, int refreshWay, List<Calendar> dates, List<String> times);

        abstract void getIntegralCountForRefresh(int userId, int positionId, int payment, List<Calendar> dates, List<String> times);
    }
}
