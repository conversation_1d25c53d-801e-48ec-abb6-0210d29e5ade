package com.bxkj.enterprise.ui.activity.invoicedetails;

import com.bxkj.common.di.module.ApplicationModule;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.InvoiceData;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.invoicedetails
 * @Description: InvoiceDetails
 * @TODO: TODO
 * @date 2018/3/27
 */

public class InvoiceDetailsPresenter extends InvoiceDetailsContract.Presenter {

    private static final String TAG = InvoiceDetailsPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;
    private int mUserId;

    @Inject
    public InvoiceDetailsPresenter(@Named(ApplicationModule.USER_ID) int userId, BusinessApi businessApi) {
        mBusinessApi = businessApi;
        mUserId = userId;
    }

    @Override
    void getInvoiceDetails(int invoiceId) {
        mView.showLoading();
        mBusinessApi.getInvoiceDetails(mUserId, invoiceId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getInvoiceDetailsSuccess((InvoiceData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        mView.hiddenLoading();
                    }
                });
    }
}
