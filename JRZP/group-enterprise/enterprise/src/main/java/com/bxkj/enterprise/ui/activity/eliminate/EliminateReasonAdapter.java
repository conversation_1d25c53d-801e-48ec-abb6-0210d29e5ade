package com.bxkj.enterprise.ui.activity.eliminate;

import android.content.Context;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.eliminate
 * @Description:
 * @TODO: TODO
 * @date 2018/8/16
 */
public class EliminateReasonAdapter extends SuperAdapter<String> {
    private int mSelectedPosition;

    public EliminateReasonAdapter(Context context, List<String> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, String s, int position) {
        ImageView ivSelector = holder.findViewById(R.id.iv_selector);
        holder.setText(R.id.tv_item, s);
        ivSelector.setSelected(mSelectedPosition == position);
        holder.itemView.setOnClickListener(view -> {
            if (mSelectedPosition != position) {
                mSelectedPosition = position;
                notifyDataSetChanged();
                if (SuperItemClickListener != null) {
                    SuperItemClickListener.onClick(view, position);
                }
            }
        });
    }

    public int getSelectedPosition() {
        return mSelectedPosition;
    }
}
