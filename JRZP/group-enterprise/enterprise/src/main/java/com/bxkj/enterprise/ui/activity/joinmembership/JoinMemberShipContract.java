package com.bxkj.enterprise.ui.activity.joinmembership;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.MemberServicesData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.joinmembership
 * @Description: JoinMemberShip
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface JoinMemberShipContract {
    interface View extends BaseView {
        void getMemberServicesListSuccess(List<MemberServicesData> memberServicesDataList);

        void createOrderSuccess(String orderId,int price);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getMemberServicesList();

        abstract void createVipOrder(int vipOrderType, int vipOrderLevel, int price);
    }
}
