package com.bxkj.enterprise.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.enterprise.api.BusinessApi
import com.bxkj.enterprise.data.InviteRecordItemData
import com.bxkj.enterprise.data.source.datasource.InviteFriendsDataSource
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.data.source
 * @Description:
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
class InviteFriendsRepo @Inject constructor(private val mBusinessApi: BusinessApi) : BaseRepo(), InviteFriendsDataSource {

    override fun getInviteRecordList(userId: Int, callBack: ResultListCallBack<List<InviteRecordItemData>>) {
     mBusinessApi.getInviteRecordList(userId)
             .compose(RxHelper.applyThreadSwitch())
             .subscribe(object :CustomObserver(){
                 override fun onSuccess(baseResponse: BaseResponse<*>) {
                     callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
                 }

                 override fun onSubscribe(d: Disposable) {
                     mCompositeDisposable.add(d)
                 }

                 override fun onError(respondThrowable: RespondThrowable) {
                     if (respondThrowable.errCode==30002){
                         callBack.onNoMoreData()
                     }else{
                         callBack.onError(respondThrowable)
                     }
                 }
             })
    }

    override fun getInviteUndoneList(userId: Int, callBack: ResultListCallBack<List<InviteRecordItemData>>) {
        mBusinessApi.getInviteUndoneList(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object :CustomObserver(){
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode==30002){
                            callBack.onNoMoreData()
                        }else{
                            callBack.onError(respondThrowable)
                        }
                    }
                })
    }

    override fun getShareTargetUrl(userId: Int,callback: ResultDataCallBack<String>) {
        mBusinessApi.getShareTargetUrl(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(object : CustomObserver(){
                    override fun onSuccess(baseResponse: BaseResponse<*>) {
                        callback.onSuccess(baseResponse.msg)
                    }

                    override fun onSubscribe(d: Disposable) {
                        mCompositeDisposable.add(d)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        callback.onError(respondThrowable)
                    }
                })
    }
}