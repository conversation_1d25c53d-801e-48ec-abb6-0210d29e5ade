package com.bxkj.enterprise.ui.activity.recruitmentdata

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.kotlin.getMonday
import com.bxkj.common.util.kotlin.getSunday
import com.bxkj.common.util.kotlin.toFormatString
import com.bxkj.common.util.kotlin.toLocalDate
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.RecruitmentDataItemBean
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.jrzp.user.repository.OpenUserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/9
 **/
class RecruitmentDataViewModel @Inject constructor(
  private val myJobRepo: MyJobRepo,
  private val _openUserRepo: OpenUserRepository
) : BaseViewModel() {

  val onlineJobList = MutableLiveData<List<PositionItemBean>>()
  val recruitmentDataList = MutableLiveData<List<RecruitmentDataItemBean>>()
  val showChart = MutableLiveData<Boolean>().apply { value = false }
  val showUpgradeMember = MutableLiveData<Boolean>().apply { value = false }

  val dateFilterText = MutableLiveData<String>().apply {
    value = TimeUtils.getToDayDate("yyyy.MM.dd")
  }

  val showDayFilterCommand = MutableLiveData<VMEvent<String>>()
  val showWeekFilterCommand = MutableLiveData<VMEvent<String>>()
  val showMonthFilterCommand = MutableLiveData<VMEvent<String>>()

  val recruitmentMonthData = MutableLiveData<List<Map<String, Int>>>()

  private var currentDataType = TYPE_DAY

  private var filterDate: String = TimeUtils.getToDayDate("yyyy.MM.dd")
  private var filterWeekEndDate = ""

  private var filterMonthDataCategory = 1

  fun start() {
    checkHasOnlineJob()
    refreshRecruitmentData()
    viewModelScope.launch {
      _openUserRepo.getUserVipLevel(getSelfUserID())
        .handleResult({
          it?.let {
            showUpgradeMember.value = it.isFreeMember()
          } ?: let {
            showUpgradeMember.value = true
          }
        }, {
          showUpgradeMember.value = true
        })
    }
  }

  fun switchFilterDate(date: String) {
    filterDate = date
    dateFilterText.value = date
    refreshRecruitmentData()
  }

  fun switchFilterWeekDate(monday: String, sunday: String) {
    filterDate = monday
    filterWeekEndDate = sunday
    dateFilterText.value = "$monday-$sunday"
    refreshRecruitmentData()
  }

  fun switchFilterMonthDate(month: String) {
    filterDate = month
    dateFilterText.value = month
    refreshRecruitmentData()
  }

  fun switchMonthDataCategory(category: Int) {
    filterMonthDataCategory = category
    refreshRecruitmentData()
  }

  fun switchRecruitmentDataType(type: Int) {
    currentDataType = type
    when (type) {
      TYPE_DAY -> {
        switchFilterDate(TimeUtils.getToDayDate("yyyy.MM.dd"))
        showChart.value = false
      }

      TYPE_WEEK -> {
        val today = TimeUtils.getToDayDate("yyyy.MM.dd").toLocalDate("yyyy.MM.dd")
        switchFilterWeekDate(
          today.getMonday().toFormatString("yyyy.MM.dd"),
          today.getSunday().toFormatString("yyyy.MM.dd")
        )
        showChart.value = false
      }

      TYPE_MONTH -> {
        switchFilterMonthDate(TimeUtils.getToDayDate("yyyy.MM"))
        showChart.value = true
      }
    }
  }

  fun showDateFilterDialog() {
    when (currentDataType) {
      TYPE_DAY -> {
        showDayFilterCommand.value = VMEvent(filterDate)
      }

      TYPE_WEEK -> {
        showWeekFilterCommand.value = VMEvent(filterDate)
      }

      TYPE_MONTH -> {
        showMonthFilterCommand.value = VMEvent(filterDate)
      }
    }
  }

  private fun refreshRecruitmentData() {
    when (currentDataType) {
      TYPE_DAY -> {
        getRecruitmentData(TYPE_DAY, filterDate)
      }

      TYPE_WEEK -> {
        getRecruitmentData(TYPE_WEEK, filterDate, filterWeekEndDate)
      }

      TYPE_MONTH -> {
        getMonthRecruitmentDate()
      }
    }
  }

  private fun getRecruitmentData(type: Int, date: String, endDate: String = "") {
    viewModelScope.launch {
      showLoading()
      myJobRepo.getRecruitmentData(type, date, endDate)
        .handleResult({
          it?.let {
            recruitmentDataList.value = it
          }
        }, {
          recruitmentDataList.value = emptyList()
        }, {
          hideLoading()
        })
    }
  }

  private fun getMonthRecruitmentDate() {
    showLoading()
    viewModelScope.launch {
      myJobRepo.getRecruitmentMonthData(filterMonthDataCategory, filterDate)
        .handleResult({
          it?.let {
            recruitmentMonthData.value = it
          }
        }, {
          if (!it.isNoDataError) {
            showToast(it.errMsg)
          }
        }, {
          hideLoading()
        })
    }
  }

  private fun checkHasOnlineJob() {
    viewModelScope.launch {
      myJobRepo.getOnlineJobList(getSelfUserID())
        .handleResult({
          it?.let {
            onlineJobList.value = it
          }
        }, {
          onlineJobList.value = emptyList()
          if (it.isNoDataError) {
          } else {
            showToast(it.errMsg)
          }
        })
    }
  }

  companion object {
    const val TYPE_DAY = 1
    const val TYPE_WEEK = 2
    const val TYPE_MONTH = 3
  }
}