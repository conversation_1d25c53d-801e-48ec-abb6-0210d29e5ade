package com.bxkj.enterprise.ui.activity.selectarea;

import android.content.Context;
import android.widget.TextView;
import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.enterprise.R;
import com.hjq.toast.Toaster;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.selectarea
 * @Description:
 * @TODO: TODO
 * @date 2018/4/15
 */

public class AreaListAdapter extends SuperAdapter<AreaOptionsData> {

    private Map<Integer, Boolean> mSelectedMap;

    public AreaListAdapter(Context context, List<AreaOptionsData> list, int layoutResId) {
        super(context, layoutResId, list);
        mSelectedMap = new HashMap<>();
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, AreaOptionsData areaOptionsData, int position) {
        TextView tvAreaItem = holder.findViewById(R.id.tv_area_item);
        tvAreaItem.setText(areaOptionsData.getName());

        if (mSelectedMap.get(position) != null && mSelectedMap.get(position)) {
            tvAreaItem.setSelected(true);
        } else {
            tvAreaItem.setSelected(false);
        }

        holder.itemView.setOnClickListener(view -> {
            if (mSelectedMap.size() >= 3 && !mSelectedMap.containsKey(position)) {
                Toaster.show(mContext.getString(R.string.common_select_up_to_three, 3));
                return;
            }

            if (mSelectedMap.get(position) == null || !mSelectedMap.get(position)) {
                mSelectedMap.put(position, true);
            } else {
                mSelectedMap.remove(position);
            }
            notifyDataSetChanged();

            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(holder.itemView, position);
            }
        });
    }
}
