package com.bxkj.enterprise.ui.activity.invitefriends

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.enterprise.data.source.InviteFriendsRepo
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.invitefriends
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/8/3
 * @version V1.0
 */
class InviteFriendsViewModel @Inject constructor(application: Application,private val mInviteFriendsRepo: InviteFriendsRepo) : BaseViewModel() {

    val targetUrl=MutableLiveData<String>()

    fun start(){
        showLoading()
        mInviteFriendsRepo.getShareTargetUrl(getSelfUserID(),object :
          ResultDataCallBack<String> {
            override fun onSuccess(data: String?) {
                hideLoading()
                targetUrl.value=data
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    override fun onCleared() {
        super.onCleared()
        mInviteFriendsRepo.clear()
    }
}