package com.bxkj.enterprise.data;

import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/15
 * @version: V1.0
 */
public class ApplicantResumeData {

    private int id;

    //期望职位
    private String desiredJob;

    //是否置顶
    private int top;

    //自我评价
    private String selfIntro;

    private int videoID;

    private String videoPic;

    private String wtName;

    private String edate1;

    private int applyState;

    private int age;

    private int state;

    private String applyTime;

    private String detailsName;

    private ResumeBaseInfoData ubInfo;

    public static class NoPublishJobStatus {

        public static NoPublishJobStatus getInstance() {
            return new NoPublishJobStatus();
        }
    }

    public String getTimeDiff() {
        return TimeUtils.formatTimeDiff(edate1, "yyyy/MM/dd HH:mm");
    }

    public boolean hasTimeDiff(){
        return !CheckUtils.isNullOrEmpty(edate1);
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDesiredJob() {
        return desiredJob;
    }

    public void setDesiredJob(String desiredJob) {
        this.desiredJob = desiredJob;
    }

    public int getTop() {
        return top;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public String getSelfIntro() {
        return selfIntro;
    }

    public void setSelfIntro(String selfIntro) {
        this.selfIntro = selfIntro;
    }

    public int getVideoID() {
        return videoID;
    }

    public void setVideoID(int videoID) {
        this.videoID = videoID;
    }

    public String getVideoPic() {
        return videoPic;
    }

    public void setVideoPic(String videoPic) {
        this.videoPic = videoPic;
    }

    public String getWtName() {
        return wtName;
    }

    public void setWtName(String wtName) {
        this.wtName = wtName;
    }

    public String getEdate1() {
        return edate1;
    }

    public void setEdate1(String edate1) {
        this.edate1 = edate1;
    }

    public int getApplyState() {
        return applyState;
    }

    public void setApplyState(int applyState) {
        this.applyState = applyState;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getStateText() {
        String stateText = "";
        switch (state) {
            case 0:
                stateText = "待处理";
                break;
            case 1:
                stateText = "待沟通";
                break;
            case 2:
                stateText = "已淘汰";
                break;
            case 3:
                stateText = "已邀请";
                break;
            case 4:
                stateText = "已拒绝";
                break;
            case 5:
                stateText = "已接受";
                break;
            case 6:
                stateText = "面试通过";
                break;
            case 7:
                stateText = "已发offer";
                break;
            case 8:
                stateText = "已拒offer";
                break;
            case 9:
                stateText = "已接受offer";
                break;
            case 10:
                stateText = "已入职";
                break;
            default:
                break;
        }
        return stateText;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getSampleTime() {
        if (CheckUtils.isNullOrEmpty(applyTime)) {
            return "";
        }
        return applyTime.split("\\s")[0];
    }

    public String getApplyTimeDiff() {
        return TimeUtils.getTwoTimeDiff(applyTime, "yyyy-MM-dd");
    }

    public String getDetailsName() {
        return detailsName;
    }

    public void setDetailsName(String detailsName) {
        this.detailsName = detailsName;
    }

    public ResumeBaseInfoData getUbInfo() {
        return ubInfo;
    }

    public void setUbInfo(ResumeBaseInfoData ubInfo) {
        this.ubInfo = ubInfo;
    }

    public String getDesc() {
        if (ubInfo == null) {
            return ECommonApiConstants.NO_TEXT;
        }
        StringBuilder descBuilder = new StringBuilder();
        descBuilder.append(ubInfo.getSexText()).append("/");
        if (age != 0) {
            descBuilder.append(age).append("岁 | ");
        } else {
            descBuilder.append(TimeUtils.getAge(ubInfo.getBirthday())).append("岁").append(" | ");
        }
        descBuilder.append(ubInfo.getQuaName()).append(" | ");
        if (!CheckUtils.isNullOrEmpty(wtName)) {
            descBuilder.append(wtName).append(" | ");
        }
        if (!CheckUtils.isNullOrEmpty(ubInfo.getCityJZName())) {
            descBuilder.append(ubInfo.getCityJZName());
        }
        return descBuilder.substring(0, descBuilder.length() - 1);
    }

    public String[] getExpectJobs() {
        if (CheckUtils.isNullOrEmpty(desiredJob)) {
            return null;
        }
        return desiredJob.split("，");
    }
}
