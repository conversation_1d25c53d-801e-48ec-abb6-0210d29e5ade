package com.bxkj.enterprise.ui.activity.welfare

import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.adapter.superadapter.SuperListAdapter
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.data.OtherWelfareItemData

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.welfare
 * @Description:
 * <AUTHOR>
 * @date 2019/9/23
 * @version V1.0
 */
class OtherWelfareListAdapter constructor(layoutId: Int,private val viewModel:WelfareViewModel) : SuperListAdapter<OtherWelfareItemData>(layoutId, OtherWelfareDiffCallBack()) {
    override fun bind(holder: SuperViewHolder, item: OtherWelfareItemData, position: Int) {
        holder.bind(BR.data, item)
        holder.itemView.setOnClickListener {
            viewModel.otherWelfareClicked(item)
        }
    }
}

class OtherWelfareDiffCallBack : DiffUtil.ItemCallback<OtherWelfareItemData>() {
    override fun areItemsTheSame(oldItem: OtherWelfareItemData, newItem: OtherWelfareItemData): Boolean {
       return oldItem.id==newItem.id
    }

    override fun areContentsTheSame(oldItem: OtherWelfareItemData, newItem: OtherWelfareItemData): Boolean {
        return oldItem==newItem
    }
}
