package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.contract
 * @Description: ResumeList
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface ResumeListContract {
  interface View extends BaseHasListView {
    void getResumeListSuccess(List<ResumeItemData> resumeItemDataList, boolean noMore);
  }

  abstract class Presenter extends BaseMvpPresenter<View> {
    public abstract void getResumeList(int userId,
        SearchResumeParameters searchResumeParameters, int pageIndex, int pageSize);
  }
}
