package com.bxkj.enterprise.ui.activity.companystyle;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.PermissionUtils;
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.ZPFileUtils;
import com.bxkj.common.util.imageloader.GlideEngine;
import com.bxkj.common.util.imageloader.SandboxFileEngine;
import com.bxkj.ecommon.util.BitmapUtils;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewGridDivider;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.CompanyStyleItemData;
import com.hjq.permissions.Permission;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.therouter.router.Route;

import java.util.List;

import javax.inject.Inject;
import org.jetbrains.annotations.NotNull;

/**
 * @Description: 公司风采
 * @date 2018/8/8
 */
@Route(path = CompanyStyleNavigation.PATH)
public class CompanyStyleActivity extends BaseDaggerActivity implements CompanyStyleContract.View {

  private static final int TO_SELECT_PICTURE_CODE = 1;
  private static final int TO_UPDATE_PICTURE_CODE = 2;

  @Inject
  CompanyStylePresenter mCompanyStylePresenter;

  private RecyclerView recyclerPictures;

  private CompanyPictureAdapter mCompanyPictureAdapter;
  private int mUpdateStyleId;

  public static void start(Context context) {
    Intent starter = new Intent(context, CompanyStyleActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_company_style;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mCompanyStylePresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    String intentTitle = getIntent().getStringExtra(CompanyStyleNavigation.EXTRA_PAGE_TITLE);
    titleBarManager.setTitle(
        intentTitle == null ? getString(R.string.mine_company_style) : intentTitle)
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> finish());
    String intentTips = getIntent().getStringExtra(CompanyStyleNavigation.EXTRA_UPLOAD_TIPS);
    if (intentTips != null) {
      ((TextView) findViewById(R.id.tv_tips)).setText(intentTips);
    }
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mCompanyPictureAdapter =
      new CompanyPictureAdapter(this, null, R.layout.enterprise_recycler_picture_item);
    recyclerPictures.setLayoutManager(new GridLayoutManager(this, 3));
    recyclerPictures.addItemDecoration(new RecycleViewGridDivider(DensityUtils
      .dp2px(this, 20), ContextCompat.getColor(this, R.color.common_white), false));
    recyclerPictures.setAdapter(mCompanyPictureAdapter);
    mCompanyPictureAdapter.setOnItemClickListener((view, position) -> {
      int styleId = mCompanyPictureAdapter.getData().get(position).getId();
      if (view.getId() == R.id.iv_delete) {
        mCompanyStylePresenter.deleteCompanyStyle(getMUserID(), styleId);
      } else {
        if (position == mCompanyPictureAdapter.getData().size() - 1) {
          openGallery(TO_SELECT_PICTURE_CODE);
        } else {
          mUpdateStyleId = styleId;
          openGallery(TO_UPDATE_PICTURE_CODE);
        }
      }
    });
    reloadData();
  }

  private void openGallery(int requestCode) {
    PermissionUtils.requestPermission(this
      , getString(R.string.permission_tips_title)
      , getString(R.string.permission_select_img_tips)
      , new OnRequestResultListener() {
        @Override
        public void onRequestSuccess() {
          PictureSelector.create(CompanyStyleActivity.this)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.Companion.getInstance())
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .setImageSpanCount(4)
            .forResult(requestCode);
        }

        @Override
        public void onRequestFailed(@NotNull List<@NotNull String> permissions, boolean never) {
          showToast(getString(R.string.cancel));
        }
      }, Permission.WRITE_EXTERNAL_STORAGE, Permission.READ_EXTERNAL_STORAGE);
  }

  @Override
  public void getCompanyStyleSuccess(List<CompanyStyleItemData> companyStyleItemDataList) {
    companyStyleItemDataList.add(new CompanyStyleItemData());
    mCompanyPictureAdapter.reset(companyStyleItemDataList);
  }

  @Override
  public void addCompanyStyleSuccess() {
    reloadData();
  }

  @Override
  public void updateCompanyStyleSuccess() {
    reloadData();
  }

  @Override
  public void deleteCompanyStyleSuccess() {
    reloadData();
  }

  @Override
  public void hasIntegralReward(int integral) {
    reloadData();
  }

  private void reloadData() {
    mCompanyStylePresenter.getCompanyStyle(getMUserID());
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK && data != null) {
      List<LocalMedia> localMedia = PictureSelector.obtainSelectorList(data);
      String realImgPath = ZPFileUtils.getPictureSelectorPath(localMedia.get(0));
      String imgBase64 = BitmapUtils.bitmapToString(realImgPath, 0.1f);
      if (requestCode == TO_SELECT_PICTURE_CODE) {
        mCompanyStylePresenter.addCompanyStyle(getMUserID()
          , imgBase64);
      } else {
        mCompanyStylePresenter.updateCompanyStyle(getMUserID(), mUpdateStyleId, imgBase64);
      }
    }
  }

  private void bindView(View bindSource) {
    recyclerPictures = bindSource.findViewById(R.id.recycler_pictures);
  }
}
