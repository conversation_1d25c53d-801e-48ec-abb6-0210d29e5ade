package com.bxkj.enterprise.ui.activity.positionrefresh;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.PositionAutoRefreshData;

import java.util.Calendar;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.positionrefresh
 * @Description: PositionRefresh
 * @TODO: TODO
 * @date 2018/3/27
 */

public class PositionRefreshPresenter extends PositionRefreshContract.Presenter {

  private static final String TAG = PositionRefreshPresenter.class.getSimpleName();
  private BusinessApi mBusinessApi;

  @Inject
  public PositionRefreshPresenter(BusinessApi businessApi) {
    mBusinessApi = businessApi;
  }

  @Override
  void getIntegralForRefreshPosition(int userId) {
    mBusinessApi.getIntegralForRefreshPosition(userId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.getRefreshIntegralSuccess(Integer.parseInt(baseResponse.getMsg()));
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  void getPositionRefreshInfo(int userId, int positionId) {
    mBusinessApi.getPositionRefreshInfo(userId, positionId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.getPositionRefreshInfoSuccess((PositionAutoRefreshData) baseResponse.getData());
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            if (!respondThrowable.getErrMsg().equals("未查到数据")) {
              mView.onError(respondThrowable.getErrMsg());
            }
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  void refreshPosition(int userId, String positionIds, int payment, int refreshWay,
      List<Calendar> dates, List<String> times) {
    String stringDates = null;
    String stringTimes = null;
    if (refreshWay == 2) {
      if (CheckUtils.isNullOrEmpty(dates)) {
        mView.onError("持续时间不可为空");
        return;
      } else if (CheckUtils.isNullOrEmpty(times)) {
        mView.onError("单日时间不可为空");
        return;
      }
      StringBuffer appendDates = new StringBuffer();
      for (Calendar date : dates) {
        appendDates.append(TimeUtils.calendarToString("yyyy-MM-dd", date)).append("|");
      }
      stringDates = appendDates.substring(0, appendDates.lastIndexOf("|"));
      StringBuffer appendTimes = new StringBuffer();
      for (String time : times) {
        appendTimes.append(time).append("|");
      }
      stringTimes = appendTimes.substring(0, appendTimes.lastIndexOf("|"));
    }
    showLoading();
    mBusinessApi.refreshPosition(userId, positionIds, payment, refreshWay, stringDates,
        stringTimes)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            hiddenLoading();
            mView.refreshPositionSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            hiddenLoading();
            if (respondThrowable.getErrCode() == 30002 || respondThrowable.getErrCode() == 30004) {
              mView.refreshPositionFailed(respondThrowable.getErrMsg());
            } else {
              mView.onError(respondThrowable.getErrMsg());
            }
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  void getIntegralCountForRefresh(int userId, int positionId, int payment, List<Calendar> dates,
      List<String> times) {
    String stringDates;
    String stringTimes;
    if (CheckUtils.isNullOrEmpty(dates)) {
      mView.getRefreshIntegralCountSuccess(payment, 0);
      return;
    } else if (CheckUtils.isNullOrEmpty(times)) {
      mView.getRefreshIntegralCountSuccess(payment, 0);
      return;
    }
    StringBuilder appendDates = new StringBuilder();
    for (Calendar date : dates) {
      appendDates.append(TimeUtils.calendarToString("yyyy-MM-dd", date)).append("|");
    }
    stringDates = appendDates.substring(0, appendDates.lastIndexOf("|"));
    StringBuilder appendTimes = new StringBuilder();
    for (String time : times) {
      appendTimes.append(time).append("|");
    }
    stringTimes = appendTimes.substring(0, appendTimes.lastIndexOf("|"));
    mBusinessApi.getIntegralCountForRefresh(userId, positionId, payment, stringDates, stringTimes)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.getRefreshIntegralCountSuccess(payment,
                !CheckUtils.isNullOrEmpty(baseResponse.getMsg()) ? Integer.parseInt(baseResponse.getMsg())
                    : 0);
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }
}
