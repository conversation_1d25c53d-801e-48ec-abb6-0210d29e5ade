package com.bxkj.enterprise.ui.fragment.schooljobfairchild;

import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.ecommon.base.EBaseDBFragment;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.databinding.EnterpriseFragmentSchoolJobFairChildBinding;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.schooljobfairchild
 * @Description: 校园招聘会子fragment
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolJobFairChildFragment extends
    EBaseDBFragment<EnterpriseFragmentSchoolJobFairChildBinding, SchoolJobFairChildViewModel> {

    public static final String EXTRA_SCHOOL_ID = "SCHOOL_ID";
    public static final String EXTRA_JOB_FAIR_TYPE = "JOB_FAIR_TYPE";

    public static SchoolJobFairChildFragment newInstance(int schoolId, int jobFairType) {
        Bundle args = new Bundle();
        args.putInt(EXTRA_SCHOOL_ID, schoolId);
        args.putInt(EXTRA_JOB_FAIR_TYPE, jobFairType);
        SchoolJobFairChildFragment fragment = new SchoolJobFairChildFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected Class<SchoolJobFairChildViewModel> getViewModelClass() {
        return SchoolJobFairChildViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.enterprise_fragment_school_job_fair_child;
    }

    @Override
    protected void initPage() {
        getDataBinding().setViewModel(getViewModel());
        getViewModel().setParams(getArguments());
        setupJobFairListAdapter();
        getViewModel().getListViewModel().refresh();
    }

    private void setupJobFairListAdapter() {
        if (getArguments() == null) return;
        SchoolJobFairListAdapter schoolJobFairListAdapter = new SchoolJobFairListAdapter(getParentActivity(), R.layout.enterprise_recycler_school_job_fair_item, getArguments().getInt(EXTRA_JOB_FAIR_TYPE));
        RecyclerView recyclerJobFair = getDataBinding().getRoot().findViewById(R.id.recycler_content);
        recyclerJobFair.setLayoutManager(new LinearLayoutManager(getParentActivity()));
        recyclerJobFair.addItemDecoration(new RecycleViewDivider(getParentActivity(), LinearLayoutManager.HORIZONTAL, DensityUtils
            .dp2px(getParentActivity(), 1), getMColor(R.color.common_f4f4f4), true));
        getViewModel().getListViewModel().setAdapter(schoolJobFairListAdapter);
    }
}
