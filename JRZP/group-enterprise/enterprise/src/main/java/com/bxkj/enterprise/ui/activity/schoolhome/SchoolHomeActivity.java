package com.bxkj.enterprise.ui.activity.schoolhome;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bxkj.common.base.mvvm.BaseDBActivity;
import com.bxkj.ecommon.adapter.CommonTabLayoutAdapter;
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.databinding.EnterpriseActivitySchoolHomeBinding;
import com.bxkj.enterprise.ui.fragment.facultyrecruit.FacultyRecruitFragment;
import com.bxkj.enterprise.ui.fragment.schooldetails.SchoolDetailsFragment;
import com.bxkj.enterprise.ui.fragment.schooljobfair.SchoolJobFairFragment;

import net.lucode.hackware.magicindicator.ViewPagerHelper;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolhome
 * @Description: 学校主页
 * @TODO: TODO
 * @date 2019/6/14
 */
public class SchoolHomeActivity extends
        BaseDBActivity<EnterpriseActivitySchoolHomeBinding, SchoolHomeViewModel> {

    private static final String EXTRA_SCHOOL_ID = "SCHOOL_ID";

    public static Intent newIntent(Context context, int schoolId) {
        Intent intent = new Intent(context, SchoolHomeActivity.class);
        intent.putExtra(EXTRA_SCHOOL_ID, schoolId);
        return intent;
    }

    @Override
    protected Class<SchoolHomeViewModel> getViewModelClass() {
        return SchoolHomeViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.enterprise_activity_school_home;
    }

    @Override
    protected void initPage(@Nullable Bundle savedInstanceState) {
        getViewBinding().setViewModel(getViewModel());
        getStatusBarManager().titleBar(getViewBinding().llTitleBar).init();
        setupCollapsingToolBarMinHeight();
        subscribeSchoolDetailsChange();
        getViewModel().getSchoolDetailsInfo(getIntent().getIntExtra(EXTRA_SCHOOL_ID, 0));
    }

    private void subscribeSchoolDetailsChange() {
        getViewModel().getSchoolDetailsData().observe(this, schoolDetailsData -> {
            if (schoolDetailsData != null) {
                setupContentViewPager(schoolDetailsData.getInfo());
                getViewBinding().titleBar.setTitle(schoolDetailsData.getName());
            }
        });
    }

    private void setupContentViewPager(String schoolDetails) {
        List<Fragment> childFragments = new ArrayList<>();
        childFragments.add(SchoolDetailsFragment.newInstance(schoolDetails));
        childFragments.add(
                SchoolJobFairFragment.newInstance(getIntent().getIntExtra(EXTRA_SCHOOL_ID, 0)));
        childFragments.add(
                FacultyRecruitFragment.newInstance(getIntent().getIntExtra(EXTRA_SCHOOL_ID, 0)));
        CommonPagerAdapter schoolHomePagerAdapter =
                new CommonPagerAdapter(getSupportFragmentManager(), childFragments,
                        getResources().getStringArray(R.array.school_home_titles));
        getViewBinding().vpContent.setAdapter(schoolHomePagerAdapter);
        getViewBinding().vpContent.setOffscreenPageLimit(3);
        final CommonNavigator commonNavigator = new CommonNavigator(this);
        commonNavigator.setAdjustMode(true);
        CommonTabLayoutAdapter commonTabLayoutAdapter =
                new CommonTabLayoutAdapter(getViewBinding().vpContent);
        commonTabLayoutAdapter.setTextSize(16);
        commonNavigator.setAdapter(commonTabLayoutAdapter);
        getViewBinding().indicator.setNavigator(commonNavigator);
        ViewPagerHelper.bind(getViewBinding().indicator, getViewBinding().vpContent);
    }

    private void setupCollapsingToolBarMinHeight() {
        getViewBinding().llTitleBar.getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        getViewBinding().llTitleBar.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        if (getViewBinding().collapsingToolBar.getMinimumHeight()
                                < getStatusBarManager().getBarParams().titleBarView.getHeight()) {
                            getViewBinding().collapsingToolBar.setMinimumHeight(
                                    getStatusBarManager().getBarParams().titleBarView.getHeight());
                        }
                        handleAppbarLayoutOffset();
                    }
                });
    }

    private void handleAppbarLayoutOffset() {
        int appLayoutFinalOffset = getViewBinding().collapsingToolBar.getHeight()
                - getStatusBarManager().getBarParams().titleBarView.getHeight();
        getViewBinding().appbarLayout.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            float alpha = Math.abs(absOffset) / (float) appLayoutFinalOffset;
            getViewBinding().titleBar.setTitleVisibility(alpha > 0.9 ? View.VISIBLE : View.GONE);
            getViewBinding().llTitleBar.getBackground().mutate().setAlpha((int) (255 * alpha));
        });
    }
}
