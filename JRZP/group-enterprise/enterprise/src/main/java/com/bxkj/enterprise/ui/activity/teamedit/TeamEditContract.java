package com.bxkj.enterprise.ui.activity.teamedit;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.TeamItemData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.teamedit
 * @Description: TeamEdit
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface TeamEditContract {
    interface View extends BaseView {
        void addTeamMemberSuccess();

        void integralReward(int integral);

        void updateTeamMemberSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void addTeamMember(int userId, TeamItemData teamItemData);

        abstract void updateTeamMember(int userId, TeamItemData teamItemData);
    }
}
