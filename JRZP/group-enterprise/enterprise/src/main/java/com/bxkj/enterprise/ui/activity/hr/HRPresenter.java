package com.bxkj.enterprise.ui.activity.hr;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.HrItemData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.hr
 * @Description: Hr
 * @TODO: TODO
 * @date 2018/3/27
 */

public class HRPresenter extends HRContract.Presenter {

  private static final String TAG = HRPresenter.class.getSimpleName();
  private BusinessApi mBusinessApi;

  @Inject
  public HRPresenter(BusinessApi businessApi) {
    mBusinessApi = businessApi;
  }

  @Override
  void getHrList(int userId) {
    mBusinessApi.getHrList(userId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.getHrListSuccess((List<HrItemData>) baseResponse.getDataList());
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            if (respondThrowable.getErrCode() == 30002) {
              mView.noHr();
            } else {
              mView.onError(respondThrowable.getErrMsg());
            }
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  void deleteHr(int userId, int hrId, int position) {
    mView.showLoading();
    mBusinessApi.deleteHr(userId, hrId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.hiddenLoading();
            mView.deleteHrSuccess(position);
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.hiddenLoading();
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }
}
