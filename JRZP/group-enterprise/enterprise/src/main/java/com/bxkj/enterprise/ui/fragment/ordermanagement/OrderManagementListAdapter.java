package com.bxkj.enterprise.ui.fragment.ordermanagement;

import android.content.Context;

import androidx.core.content.ContextCompat;

import android.view.View;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.OrderItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.ordermanagement
 * @Description:
 * @TODO: TODO
 * @date 2018/10/12
 */
public class OrderManagementListAdapter extends SuperAdapter<OrderItemData> {

  public OrderManagementListAdapter(Context context, List<OrderItemData> list, int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, OrderItemData orderItemData,
      int position) {
    holder.setText(R.id.tv_order_name, orderItemData.getContent());
    holder.setText(R.id.tv_order_price,
        String.format(mContext.getString(R.string.price_format), orderItemData.getPrice()));
    holder.setText(R.id.tv_order_type, String.format(mContext.getString(R.string.order_type_format),
        orderItemData.getTypeName()));
    holder.setText(R.id.tv_order_time, orderItemData.getTime());
    TextView tvOptionOne = holder.findViewById(R.id.tv_order_option_one);
    TextView tvOptionTwo = holder.findViewById(R.id.tv_order_option_two);

    String optionTwoText = "";
    if (orderItemData.getIspay() == 1) {
      if (orderItemData.getIseffect() == 0) {
        tvOptionOne.setVisibility(View.VISIBLE);
        holder.findViewById(R.id.tv_order_effected).setVisibility(View.GONE);
        tvOptionOne.setText(mContext.getString(R.string.order_effect_now));
        tvOptionOne.setTextColor(ContextCompat.getColor(mContext, R.color.common_49C280));
        tvOptionOne.setBackgroundResource(R.drawable.frame_ff865d_radius_4);
      } else {
        tvOptionOne.setVisibility(View.GONE);
        holder.findViewById(R.id.tv_order_effected).setVisibility(View.VISIBLE);
      }
      switch (orderItemData.isCanInvoice()) { //发票状态
        case 0:
          tvOptionTwo.setVisibility(View.GONE);
          break;
        case 1: //申请发票
          optionTwoText = mContext.getString(R.string.order_apply_for_invoice);
          break;
        case 2: //重新申请
          //                    optionTwoText = getString(R.string.order_invoice_details);
          optionTwoText = mContext.getString(R.string.order_reapply_for_invoice);
          break;
        case 3: //发票详情
          optionTwoText = mContext.getString(R.string.order_invoice_details);
          break;
        default:
          break;
      }
    } else {
      tvOptionOne.setText(mContext.getString(R.string.common_delete));
      optionTwoText = mContext.getString(R.string.order_pay_immediately);
    }
    tvOptionTwo.setText(optionTwoText);

    setOnChildClickListener(position, tvOptionOne, tvOptionTwo);
  }
}
