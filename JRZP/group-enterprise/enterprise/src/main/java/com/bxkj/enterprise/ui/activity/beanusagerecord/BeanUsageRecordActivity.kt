package com.bxkj.enterprise.ui.activity.beanusagerecord

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.BeanUsageRecordBean
import com.bxkj.enterprise.databinding.BActivityBeanUsageRecordBinding

/**
 * Description: 招聘豆使用记录
 * Author:Sanjin
 * Date:2024/2/20
 **/
@Route(path = BeanUsageRecordNavigation.PATH)
class BeanUsageRecordActivity :
  BaseDBActivity<BActivityBeanUsageRecordBinding, BeanUsageRecordViewModel>() {

  override fun getViewModelClass(): Class<BeanUsageRecordViewModel> =
    BeanUsageRecordViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_bean_usage_record

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupBeanUsageRecordAdapter()

    viewModel.start()
  }

  private fun setupBeanUsageRecordAdapter() {
    viewBinding.listBeanUsageRecord.recyclerContent.apply {
      layoutManager = LinearLayoutManager(this@BeanUsageRecordActivity)
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_4))
          .drawHeader(true)
          .drawFoot(true)
          .build()
      )
    }

    val adapter =
      SimpleDBListAdapter<BeanUsageRecordBean>(this, R.layout.b_recycler_bean_usage_record_item)
    viewModel.beanUsageRecordListViewModel.setAdapter(adapter)
  }

  companion object {

    fun newIntent(context: Context): Intent {
      return Intent(context, BeanUsageRecordActivity::class.java)
    }
  }
}