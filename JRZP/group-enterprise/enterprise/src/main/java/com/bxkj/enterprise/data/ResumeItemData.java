package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.common.util.TimeUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2018/7/23
 */
public class ResumeItemData implements Parcelable {

  /**
   * id : 53667 ubInfo : {"name":"安婷","sex":1,"tx":"","birthday":"1988/1/4
   * 0:00:00","cityJZ":19408,"quaId":7,"cityJZName":"塔城","quaName":"本科"} relName : null
   */

  private int id;
  private int uid;
  private int state;
  private int jaid;
  private String edate1;
  private UbInfoBean ubInfo;
  private String detailsName;
  private String detailsName2;
  private String relName;
  private String applyTime;
  private String wtName;
  private int willMoney;
  private int top;
  private String videoPic;
  private List<ResumeItemData> resumeItemDataList;

  public String getApplicantAboutInfo() {
    final StringBuilder aboutStringBuilder = new StringBuilder();
    if (ubInfo != null) {
      if (!CheckUtils.isNullOrEmpty(ubInfo.cityJZName)) {
        aboutStringBuilder.append(ubInfo.cityJZName).append(" | ");
      }
    }
    if (!CheckUtils.isNullOrEmpty(ubInfo.getBirthday())) {
      aboutStringBuilder.append(TimeUtils.getAge(ubInfo.getBirthday())).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(ubInfo.getQuaName())) {
      aboutStringBuilder.append(ubInfo.getQuaName()).append(" | ");
    }
    aboutStringBuilder.append(wtName);
    return aboutStringBuilder.toString();
  }

  public String getFormatMoney() {
    if (willMoney == 0) {
      return "面议";
    }
    return willMoney + "元";
  }

  public Boolean hasVideo() {
    return !CheckUtils.isNullOrEmpty(videoPic);
  }

  public int getWillMoney() {
    return willMoney;
  }

  public void setWillMoney(int willMoney) {
    this.willMoney = willMoney;
  }

  public String getVideoPic() {
    return videoPic;
  }

  public void setVideoPic(String videoPic) {
    this.videoPic = videoPic;
  }

  protected ResumeItemData(Parcel in) {
    id = in.readInt();
    uid = in.readInt();
    state = in.readInt();
    jaid = in.readInt();
    edate1 = in.readString();
    ubInfo = in.readParcelable(UbInfoBean.class.getClassLoader());
    detailsName = in.readString();
    detailsName2 = in.readString();
    relName = in.readString();
    applyTime = in.readString();
    wtName = in.readString();
    top = in.readInt();
    willMoney = in.readInt();
    videoPic = in.readString();
    resumeItemDataList = in.createTypedArrayList(ResumeItemData.CREATOR);
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(id);
    dest.writeInt(uid);
    dest.writeInt(state);
    dest.writeInt(jaid);
    dest.writeString(edate1);
    dest.writeParcelable(ubInfo, flags);
    dest.writeString(detailsName);
    dest.writeString(detailsName2);
    dest.writeString(relName);
    dest.writeString(applyTime);
    dest.writeString(wtName);
    dest.writeInt(top);
    dest.writeInt(willMoney);
    dest.writeString(videoPic);
    dest.writeTypedList(resumeItemDataList);
  }

  @Override
  public int describeContents() {
    return 0;
  }

  public static final Creator<ResumeItemData> CREATOR = new Creator<ResumeItemData>() {
    @Override
    public ResumeItemData createFromParcel(Parcel in) {
      return new ResumeItemData(in);
    }

    @Override
    public ResumeItemData[] newArray(int size) {
      return new ResumeItemData[size];
    }
  };

  public List<ResumeItemData> getResumeItemDataList() {
    return resumeItemDataList;
  }

  public void setResumeItemDataList(List<ResumeItemData> resumeItemDataList) {
    this.resumeItemDataList = resumeItemDataList;
  }

  public ResumeItemData() {
  }

  public ResumeItemData(List<ResumeItemData> resumeItemData) {
    resumeItemDataList = resumeItemData;
   }

  public String getWtName() {
    return wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public String getApplyTime() {
    return applyTime;
  }

  public void setApplyTime(String applyTime) {
    this.applyTime = applyTime;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getJaid() {
    return jaid;
  }

  public void setJaid(int jaid) {
    this.jaid = jaid;
  }

  public String getStateText() {
    String stateText = "";
    switch (state) {
      case 0:
        stateText = "待处理";
        break;
      case 1:
        stateText = "待沟通";
        break;
      case 2:
        stateText = "已淘汰";
        break;
      case 3:
        stateText = "已邀请";
        break;
      case 4:
        stateText = "已拒绝";
        break;
      case 5:
        stateText = "已接受";
        break;
      case 6:
        stateText = "面试通过";
        break;
      case 7:
        stateText = "已发offer";
        break;
      case 8:
        stateText = "已拒offer";
        break;
      case 9:
        stateText = "已接受offer";
        break;
      case 10:
        stateText = "已入职";
        break;
      default:
        break;
    }
    return stateText;
  }

  public String getDetailsName() {
    return detailsName;
  }

  public void setDetailsName(String detailsName) {
    this.detailsName = detailsName;
  }

  public String getDetailsName2() {
    return CheckUtils.isNullOrEmpty(detailsName2) ? "无" : detailsName2;
  }

  public void setDetailsName2(String detailsName2) {
    this.detailsName2 = detailsName2;
  }

  public String getEdate1() {
    return "";
  }

  public void setEdate1(String edate1) {
    this.edate1 = edate1;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public UbInfoBean getUbInfo() {
    return ubInfo;
  }

  public void setUbInfo(UbInfoBean ubInfo) {
    this.ubInfo = ubInfo;
  }

  public String getRelName() {
    return relName;
  }

  public void setRelName(String relName) {
    this.relName = relName;
  }

  public int getTop() {
    return top;
  }

  public void setTop(int top) {
    this.top = top;
  }

  public static class UbInfoBean implements Parcelable {

    /**
     * name : 安婷 sex : 1 tx : birthday : 1988/1/4 0:00:00 cityJZ : 19408 quaId : 7 cityJZName : 塔城
     * quaName : 本科
     */

    private String name;
    private int sex;
    private String tx;
    private String birthday;
    private int cityJZ;
    private int quaId;
    private String cityJZName;
    private String quaName;
    private String domain;
    private int uid;

    public int getUid() {
      return uid;
    }

    public void setUid(int uid) {
      this.uid = uid;
    }

    public boolean isMan() {
      return sex == 0;
    }

    protected UbInfoBean(Parcel in) {
      name = in.readString();
      sex = in.readInt();
      tx = in.readString();
      birthday = in.readString();
      cityJZ = in.readInt();
      quaId = in.readInt();
      cityJZName = in.readString();
      quaName = in.readString();
      domain = in.readString();
      uid = in.readInt();
    }

    public static final Creator<UbInfoBean> CREATOR = new Creator<UbInfoBean>() {
      @Override
      public UbInfoBean createFromParcel(Parcel in) {
        return new UbInfoBean(in);
      }

      @Override
      public UbInfoBean[] newArray(int size) {
        return new UbInfoBean[size];
      }
    };

    public String getDomain() {
      return domain;
    }

    public void setDomain(String domain) {
      this.domain = domain;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public int getSex() {
      return sex;
    }

    public void setSex(int sex) {
      this.sex = sex;
    }

    public String getTx() {
      return tx;
    }

    public String getRealAvatar() {
      if (CheckUtils.isNullOrEmpty(tx)) {
        return "";
      } else {
        return tx.contains("http") ? tx : domain + tx;
      }
    }

    public void setTx(String tx) {
      this.tx = tx;
    }

    public String getBirthday() {
      return birthday;
    }

    public void setBirthday(String birthday) {
      this.birthday = birthday;
    }

    public int getCityJZ() {
      return cityJZ;
    }

    public void setCityJZ(int cityJZ) {
      this.cityJZ = cityJZ;
    }

    public int getQuaId() {
      return quaId;
    }

    public void setQuaId(int quaId) {
      this.quaId = quaId;
    }

    public String getCityJZName() {
      return cityJZName;
    }

    public void setCityJZName(String cityJZName) {
      this.cityJZName = cityJZName;
    }

    public String getQuaName() {
      return quaName;
    }

    public void setQuaName(String quaName) {
      this.quaName = quaName;
    }

    @Override
    public int describeContents() {
      return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
      parcel.writeString(name);
      parcel.writeInt(sex);
      parcel.writeString(tx);
      parcel.writeString(birthday);
      parcel.writeInt(cityJZ);
      parcel.writeInt(quaId);
      parcel.writeString(cityJZName);
      parcel.writeString(quaName);
      parcel.writeString(domain);
      parcel.writeInt(uid);
    }
  }

  public String getAbout() {
    String about = "";
    about += MTextUtils.parseSex(ubInfo.getSex()) + " | ";
    about += TimeUtils.getAge(ubInfo.getBirthday()) + " | ";
    if (!CheckUtils.isNullOrEmpty(ubInfo.getCityJZName())) {
      about += ubInfo.getCityJZName() + " | ";
    }
    about += wtName;
    return about;
  }
}
