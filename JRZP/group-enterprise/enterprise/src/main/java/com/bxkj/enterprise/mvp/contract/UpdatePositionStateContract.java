package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.contract
 * @Description: UpdatePositionState
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface UpdatePositionStateContract {
    interface View extends BaseView {
        void updatePositionStateSuccess(int updatedState, int updatedPosition);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void updatePositionState(int userId, int positionId, int position, int state);
    }
}
