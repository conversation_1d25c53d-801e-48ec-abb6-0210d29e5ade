package com.bxkj.enterprise.data;

import com.bxkj.common.data.AreaOptionsData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 发票信息
 * @TODO: TODO
 * @date 2018/10/15
 */
public class InvoiceData {
    private int id;
    private int type;
    private String invoiceName;
    private String invoiceNo;
    private String regAddress;
    private String regPhone;
    private String bankName;
    private String bankAccount;
    private String name;
    private int province;
    private String provinceName;
    private int city;
    private String cityName;
    private int county;
    private String countyName;
    private String address;
    private String postcode;
    private String mobile;
    private int status;
    private String addDate;
    private String shDate;
    private String kpDate;
    private String yjDate;
    private String qsDate;
    private String express;
    private String expressNo;
    private int price;

    public InvoiceData() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getRegAddress() {
        return regAddress;
    }

    public void setRegAddress(String regAddress) {
        this.regAddress = regAddress;
    }

    public String getRegPhone() {
        return regPhone;
    }

    public void setRegPhone(String regPhone) {
        this.regPhone = regPhone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getProvince() {
        return province;
    }

    public void setProvince(int province) {
        this.province = province;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public int getCity() {
        return city;
    }

    public void setCity(int city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public int getCounty() {
        return county;
    }

    public void setCounty(int county) {
        this.county = county;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getAddDate() {
        return addDate;
    }

    public void setAddDate(String addDate) {
        this.addDate = addDate;
    }

    public String getShDate() {
        return shDate;
    }

    public void setShDate(String shDate) {
        this.shDate = shDate;
    }

    public String getKpDate() {
        return kpDate;
    }

    public void setKpDate(String kpDate) {
        this.kpDate = kpDate;
    }

    public String getYjDate() {
        return yjDate;
    }

    public void setYjDate(String yjDate) {
        this.yjDate = yjDate;
    }

    public String getQsDate() {
        return qsDate;
    }

    public void setQsDate(String qsDate) {
        this.qsDate = qsDate;
    }

    public String getExpress() {
        return express;
    }

    public void setExpress(String express) {
        this.express = express;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public void setProvinceData(AreaOptionsData areaOptionsData) {
        this.province = areaOptionsData.getId();
        this.provinceName = areaOptionsData.getName();
    }

    public void setCityData(AreaOptionsData areaOptionsData) {
        this.city = areaOptionsData.getId();
        this.cityName = areaOptionsData.getName();
    }

    public void setCountyData(AreaOptionsData areaOptionsData) {
        this.county = areaOptionsData.getId();
        this.countyName = areaOptionsData.getName();
    }

    public AreaOptionsData getProvinceData() {
        return new AreaOptionsData(province, provinceName);
    }

    public AreaOptionsData getCityData() {
        return new AreaOptionsData(city, cityName);
    }

    public AreaOptionsData getCountryData() {
        return new AreaOptionsData(county, countyName);
    }

    public String getStatusText() {
        String invoiceStatus = "";
        switch (status) {
            case 0:
                invoiceStatus = "待审核";
                break;
            case 1:
                invoiceStatus = "审核失败";
                break;
            case 2:
                invoiceStatus = "审核成功，待开票";
                break;
            case 3:
                invoiceStatus = "已开票，待邮寄";
                break;
            case 4:
                invoiceStatus = "已寄出，待签收";
                break;
            case 5:
                invoiceStatus = "已签收，完成";
                break;
        }
        return invoiceStatus;
    }

    public String getTypeText() {
        if (type == 0) {
            return "增值税普通发票";
        } else {
            return "增值税专用发票";
        }
    }

    public String getDetailsAddress() {
        return provinceName + cityName + countyName + address;
    }
}
