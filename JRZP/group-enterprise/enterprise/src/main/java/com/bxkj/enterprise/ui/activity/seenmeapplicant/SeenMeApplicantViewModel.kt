package com.bxkj.enterprise.ui.activity.seenmeapplicant

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.enterprise.api.EnterpriseApiConstants
import com.bxkj.enterprise.data.MessageData
import com.bxkj.enterprise.data.source.MessageRepo
import com.bxkj.enterprise.data.source.MyJobRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/6/4
 * @version: V1.0
 */
class SeenMeApplicantViewModel @Inject constructor(
    private val mNoticeRepository: MessageRepo,
    private val mMyJobRepo: MyJobRepo
) : BaseViewModel() {

    val seenMeApplicantListViewModel = RefreshListViewModel()
    val showNoJobTipsDialog = LiveEvent<Void>()
    val toInviteSendResume =
        LiveEvent<MessageData>()

    init {
        setupSeenMeApplicantListViewModel()
    }

    private fun setupSeenMeApplicantListViewModel() {
        seenMeApplicantListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mNoticeRepository.getMessageList(
                    getSelfUserID(),
                    EnterpriseApiConstants.MSG_TYPE_E_VIEW_ME,
                    currentPage,
                    ECommonApiConstants.DEFAULT_PAGE_SIZE
                ).handleResult({
                    seenMeApplicantListViewModel.autoAddAll(it)
                }, {
                    if (it.isNoDataError) {
                        seenMeApplicantListViewModel.noMoreData()
                    } else {
                        seenMeApplicantListViewModel.loadError()
                    }
                })
            }
        }
    }

    fun start() {
        seenMeApplicantListViewModel.refresh()
    }

    fun setupMsgRead(item: MessageData) {
        viewModelScope.launch {
            mNoticeRepository.setMsgRead(item.id)
        }
    }

    fun inviteSendResumePreCheck(item: MessageData) {
        viewModelScope.launch {
            showLoading()
            mMyJobRepo.getOnlineJobList(getSelfUserID())
                .handleResult({
                    if (CheckUtils.isNullOrEmpty(it)) {
                        showNoJobTipsDialog.call()
                    } else {
                        toInviteSendResume.value = item
                    }
                }, {
                    if (it.isNoDataError) {
                        showNoJobTipsDialog.call()
                    } else {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
        }
    }
}