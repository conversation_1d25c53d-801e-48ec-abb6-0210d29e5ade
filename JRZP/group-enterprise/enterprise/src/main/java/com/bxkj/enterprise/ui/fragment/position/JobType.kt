package com.bxkj.enterprise.ui.fragment.position

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author: Yang<PERSON><PERSON>
 * @date: 2020/12/9
 * @version: V1.0
 */
class JobType {

    companion object {

        const val ALL_TYPE = -1
        const val NORMAL_TYPE = 0
        const val SCHOOL_TYPE = 2
        const val PART_TIME = 3
    }

    @IntDef(ALL_TYPE, NORMAL_TYPE, SCHOOL_TYPE, PART_TIME)
    @Retention(SOURCE)
    @Target(VALUE_PARAMETER)
    annotation class Type {}
}