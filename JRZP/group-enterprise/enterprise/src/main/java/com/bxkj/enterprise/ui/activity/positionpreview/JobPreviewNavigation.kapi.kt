package com.bxkj.enterprise.ui.activity.positionpreview

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/30
 * @version: V1.0
 */
open class JobPreviewNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/jobpreview"

    const val EXTRA_POSITION_ID = "position_id"
    const val EXTRA_POSITION_STATE = "position_state"

    const val STATE_RUNNING = 1

    const val STATE_END = 2

    fun navigate(jobID: Int, jobState: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_POSITION_ID, jobID)
        .withInt(EXTRA_POSITION_STATE, jobState)
    }

  }
}