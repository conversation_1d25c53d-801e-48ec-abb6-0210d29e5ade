package com.bxkj.enterprise.data

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.enterprise.BR
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 *
 * @author: sanjin
 * @date: 2022/11/2
 */
@Parcelize
data class CompanyAddressData(
    var id: Int? = 0,
    var cuid: Int? = 0,
    var provinceId: Int? = 0,
    @get:Bindable
    var provinceName: String? = "",
    var cityId: Int? = 0,
    @get:Bindable
    var cityName: String? = "",
    var countyId: Int? = 0,
    @get:Bindable
    var countyName: String? = "",
    var townId: Int? = 0,
    var townName: String? = "",
    @get:Bindable
    var address: String? = "",
    var lng: String? = "",
    var lat: String? = "",
    var isDefault: Int? = 0,
) : BaseObservable(), Parcelable {

    @Bindable
    fun getProvinceCityAreaText(): String {
        val stringBuilder = StringBuilder()
        if (!provinceName.isNullOrEmpty()) {
            stringBuilder.append(provinceName)
        }
        if (!cityName.isNullOrEmpty() && cityName != provinceName) {
            stringBuilder.append("-").append(cityName)
        }
        if (!countyName.isNullOrEmpty()) {
            stringBuilder.append("-").append(countyName)
        }
        return stringBuilder.toString()
    }

    fun getCityAreaAddressText(): String {
        val stringBuilder = StringBuilder()
        if (!cityName.isNullOrEmpty() && cityName != provinceName) {
            stringBuilder.append(cityName)
        }
        if (!countyName.isNullOrEmpty()) {
            stringBuilder.append(countyName)
        }
        if (!address.isNullOrEmpty()) {
            stringBuilder.append(address)
        }
        return stringBuilder.toString()
    }

    fun updateProvinceCity(
        provinceId: Int?,
        provinceName: String?,
        cityId: Int?,
        cityName: String?,
    ) {
        this.provinceId = provinceId
        this.provinceName = provinceName
        this.cityId = cityId
        this.cityName = cityName
        notifyPropertyChanged(BR.cityName)
    }

    fun updateArea(
        areaId: Int,
        areaName: String
    ) {
        this.countyId = areaId
        this.countyName = areaName
        notifyPropertyChanged(BR.countyName)
    }

    fun hasLnglat(): Boolean {
        return !(lng.isNullOrEmpty() && lat.isNullOrEmpty())
    }

    fun resetAreaStreet() {
        countyId = 0
        countyName = ""
        townId = 0
        townName = ""
        notifyPropertyChanged(BR.countyName)
    }

    class DiffCallback : DiffUtil.ItemCallback<CompanyAddressData>() {

        override fun areItemsTheSame(
            oldItem: CompanyAddressData,
            newItem: CompanyAddressData
        ): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(
            oldItem: CompanyAddressData,
            newItem: CompanyAddressData
        ): Boolean {
            return oldItem.provinceName == newItem.provinceName && oldItem.cityName == newItem.cityName && oldItem.townName == newItem.townName && oldItem.address == newItem.address
        }
    }
}