package com.bxkj.enterprise.ui.fragment.talentpool

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup
import com.bxkj.common.widget.filterpopup.FilterUtils
import com.bxkj.common.widget.filterpopup.FilterView
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseFragmentTalentPoolBinding
import com.bxkj.enterprise.ui.fragment.resumelist.ResumeListFragmentV3
import com.bxkj.enterprise.weight.filterareapopup.FilterAreaView
import com.bxkj.enterprise.weight.filterareapopup.FilterAreaView.OnAreaItemClickListener
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity

@Route(path = TalentPoolFragmentNavigation.PATH)
class TalentPoolFragment :
    BaseDBFragment<EnterpriseFragmentTalentPoolBinding, TalentPoolViewModel>(), OnClickListener {

    private var _filterDropDownPopup: DropDownPopup? = null
    private var _eduFilterView: FilterView? = null
    private var _expFilterView: FilterView? = null
    private var _genderFilterView: FilterView? = null
    private var _dateFilterView: FilterView? = null
    private var _resumeListFragment: ResumeListFragmentV3? = null

    private var _areaFilterDropDownPopup: DropDownPopup? = null
    private var _filterAreaView: FilterAreaView? = null
    private var _noCityTipsDialog: ActionDialog? = null

    private val launcher = registerForActivityResult(
        StartActivityForResult()
    ) { result: ActivityResult ->
        if (result.resultCode == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
            _noCityTipsDialog?.dismiss()
        }
    }

    override fun getViewModelClass(): Class<TalentPoolViewModel> = TalentPoolViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_fragment_talent_pool

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        _resumeListFragment = ResumeListFragmentV3.newInstance()

        childFragmentManager.beginTransaction()
            .add(R.id.fl_container, _resumeListFragment!!)
            .commit()

        setupDropDownMenu()
        subscribeViewModelEvent()
        subscribeUserSelectedCityChangeEvent()

        viewBinding.etSearchKeyword.setOnCustomIconClickListener {
            viewModel.resetSearch()
        }

        viewBinding.etSearchKeyword.setOnEditorActionListener { textView, i, keyEvent ->
            if (i == EditorInfo.IME_ACTION_SEARCH) {
                viewModel.startSearch(textView.text.toString())
            }
            return@setOnEditorActionListener true
        }
    }

    private fun subscribeUserSelectedCityChangeEvent() {
        addDisposable(RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
            if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
                _filterAreaView?.setCurrentCity(UserUtils.getUserSelectedCityName())
                viewModel.changeFilterCity(UserUtils.getUserSelectedCityId())
            }
        })
    }

    override fun initImmersionBar() {
        statusBarManager.titleBar(viewBinding.titleBar).init()
    }

    override fun onClick(v: View?) {
        v?.let {
            SystemUtil.hideSoftKeyboard(requireActivity())
            when (it.id) {
                R.id.tv_city -> {
                    if (UserUtils.getUserSelectedCityId() == 0) {
                        _noCityTipsDialog = ActionDialog.Builder()
                            .setTitle("提示")
                            .setContent("选择期望城市后可切换区域")
                            .setOnConfirmClickListener { dialog: ActionDialog? ->
                                launcher.launch(
                                    CityPickerActivity.newIntent(requireActivity())
                                )
                            }
                            .setCancelable(false).build()
                        _noCityTipsDialog?.show(childFragmentManager)
                    } else {
                        _areaFilterDropDownPopup?.showItemAsDropDown(0)
                    }
                }

                R.id.tv_area -> {
                    _filterDropDownPopup?.showItemAsDropDown(0)
                }

                R.id.tv_salary -> {
                    _filterDropDownPopup?.showItemAsDropDown(1)
                }

                R.id.tv_work_exp -> {
                    _filterDropDownPopup?.showItemAsDropDown(2)
                }

                R.id.tv_education -> {
                    _filterDropDownPopup?.showItemAsDropDown(3)
                }

                else -> {}
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.areaOptions.observe(this) {
            _filterAreaView?.areaData = it
        }
        viewModel.eduOptions.observe(this) {
            _eduFilterView?.setData(listOf(FilterOptionsGroup(FilterOptionData.EDU, it)))
        }
        viewModel.expOptions.observe(this) {
            _expFilterView?.setData(listOf(FilterOptionsGroup(FilterOptionData.WORKING_EXP, it)))
        }
        viewModel.dateOptions.observe(this) {
            _dateFilterView?.setData(listOf(FilterOptionsGroup(FilterOptionData.PUBLISH_DATE, it)))
        }
        viewModel.resumeFilterParamsChangeEvent.observe(this, EventObserver {
            _resumeListFragment?.filterParamsChange(it)
        })
    }

    private fun setupDropDownMenu() {
        _areaFilterDropDownPopup = DropDownPopup(requireActivity(), viewBinding.llSearchBar).apply {
            addContentViews(getFilterAreaView())
        }

        _filterDropDownPopup = DropDownPopup(requireActivity(), viewBinding.clFilterBar).apply {
            setOnItemExpandStatusChangeListener { index, opened ->
                viewBinding.clFilterBar.getChildAt(index).isSelected = opened
            }
            addContentViews(getEduFilterView(), getExpFilterView(), getGenderFilterView(), getDateFilterView())
        }
    }

    private fun getFilterAreaView(): FilterAreaView {
        _filterAreaView = FilterAreaView(requireContext()).apply {
            setCurrentCity(UserUtils.getUserSelectedCityName())
            setOnAreaItemClickListener(object : OnAreaItemClickListener {
                override fun onAreaItemClicked(position: Int) {
                    areaData[position].let {
                        viewModel.changeFilterArea(it.id)
                    }
                    _areaFilterDropDownPopup?.close()
                }

                override fun onChangeCityClicked() {
                    startActivity(CityPickerActivity.newIntent(requireActivity()))
                }
            })
        }
        return _filterAreaView!!
    }

    private fun getEduFilterView(): FilterView {
        _eduFilterView = FilterView.Builder(requireContext())
            .setHeight(dip(300f))
            .setItemClickedDismiss(true)
            .setBottomBarVisible(View.GONE)
            .setOnFilterConfirmListener {
                _filterDropDownPopup?.dismiss()
                viewModel.changeFilterEduOption(it[FilterOptionData.EDU].getOrDefault())
            }
            .build()
        return _eduFilterView!!
    }

    private fun getExpFilterView(): FilterView {
        _expFilterView = FilterView.Builder(requireContext())
            .setHeight(dip(300f))
            .setItemClickedDismiss(true)
            .setBottomBarVisible(View.GONE)
            .setOnFilterConfirmListener {
                _filterDropDownPopup?.dismiss()
                viewModel.changeFilterExpOption(it[FilterOptionData.WORKING_EXP].getOrDefault())
            }
            .build()
        return _expFilterView!!
    }

    private fun getGenderFilterView(): FilterView {
        _genderFilterView = FilterView.Builder(requireContext())
            .setHeight(dip(300f))
            .setItemClickedDismiss(true)
            .setBottomBarVisible(View.GONE)
            .setOnFilterConfirmListener {
                _filterDropDownPopup?.dismiss()
                viewModel.changeFilterGenderOption(it[FilterOptionData.GENDER].getOrDefault())
            }
            .build().apply {
                setData(
                    listOf(
                        FilterOptionsGroup(
                            FilterOptionData.GENDER,
                            FilterUtils.parseFilterOptions("不限", "男", "女")
                        )
                    )
                )
            }
        return _genderFilterView!!
    }

    private fun getDateFilterView(): FilterView {
        _dateFilterView = FilterView.Builder(requireContext())
            .setHeight(dip(300f))
            .setItemClickedDismiss(true)
            .setBottomBarVisible(View.GONE)
            .setOnFilterConfirmListener {
                _filterDropDownPopup?.dismiss()
                viewModel.changeFilterDateOption(it[FilterOptionData.PUBLISH_DATE].getOrDefault())
            }
            .build()
        return _dateFilterView!!
    }
}