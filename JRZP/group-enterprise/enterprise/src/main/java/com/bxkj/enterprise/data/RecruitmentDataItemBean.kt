package com.bxkj.enterprise.data

import androidx.recyclerview.widget.DiffUtil
import com.google.gson.annotations.SerializedName

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/10
 **/
data class RecruitmentDataItemBean(
  @SerializedName("title1")
  var title: String,
  @SerializedName("value")
  var count: Int,
  @SerializedName("title2")
  var tag: String,
  @SerializedName("value1")
  var diffCount: Int
) {

  class DiffCallback : DiffUtil.ItemCallback<RecruitmentDataItemBean>() {
    override fun areItemsTheSame(
      oldItem: RecruitmentDataItemBean,
      newItem: RecruitmentDataItemBean
    ): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(
      oldItem: RecruitmentDataItemBean,
      newItem: RecruitmentDataItemBean
    ): Boolean {
      return oldItem.title == newItem.title
        && oldItem.count == newItem.count
        && oldItem.tag == newItem.tag
        && oldItem.diffCount == newItem.diffCount
    }
  }
}