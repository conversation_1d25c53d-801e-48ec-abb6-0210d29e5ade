package com.bxkj.enterprise.ui.activity.news;

import android.app.Application;
import androidx.lifecycle.MutableLiveData;
import androidx.annotation.NonNull;

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.data.NewsData;
import com.bxkj.enterprise.data.source.NewsRepo;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.news
 * @Description:
 * @TODO: TODO
 * @date 2019/6/17
 */
public class NewsViewModel extends BaseViewModel {

    @Inject
    NewsRepo mNewsRepo;

    private final MutableLiveData<NewsData> mNewsData = new MutableLiveData<>();

    @Inject
    public NewsViewModel(@NotNull Application application) {
        super();
    }

    public void getNewsDetailsById(int newsId) {
        mNewsRepo.getNewsDetails(newsId, new ResultDataCallBack<NewsData>() {
            @Override
            public void onSuccess(NewsData data) {
                mNewsData.setValue(data);
            }

            @Override
            public void onError(@NonNull RespondThrowable respondThrowable) {
                showToast(respondThrowable.getErrMsg());
            }
        });
    }

    public MutableLiveData<NewsData> getNewsData() {
        return mNewsData;
    }
}
