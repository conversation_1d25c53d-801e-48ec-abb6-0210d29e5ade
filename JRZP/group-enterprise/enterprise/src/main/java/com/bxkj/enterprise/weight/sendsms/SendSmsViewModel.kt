package com.bxkj.enterprise.weight.sendsms

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.SmsBalanceBean
import com.bxkj.enterprise.data.SmsPackageBean
import com.bxkj.enterprise.data.SmsPackageOrderBean
import com.bxkj.enterprise.data.source.AccountInfoRepo
import com.bxkj.enterprise.data.source.MessageRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class SendSmsViewModel @Inject constructor(
    private val _accountInfoRepo: AccountInfoRepo,
    private val _messageRepo: MessageRepo
) : BaseViewModel() {

    val smsBalance = MutableLiveData<SmsBalanceBean>()

    val smsPackageList = MutableLiveData<List<SmsPackageBean>>()

    val selectSmsPackage = MutableLiveData<SmsPackageBean>().apply { value = SmsPackageBean() }

    val toPaymentOrderCommand = MutableLiveData<VMEvent<SmsPackageOrderBean>>()

    val dismissDialogCommand = MutableLiveData<VMEvent<Unit>>()

    private var friendUserId: Int = 0

    fun start(friendUserId: Int) {
        this.friendUserId = friendUserId
        viewModelScope.launch {
            showLoading()
            _accountInfoRepo.getSmsBalance()
                .handleResult({
                    it?.let {
                        smsBalance.value = it
                        if (it.smsCount == 0) {
                            getSmsPackageList()
                        }
                    }
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    private fun getSmsPackageList() {
        viewModelScope.launch {
            showLoading()
            _accountInfoRepo.getSmsPackageList()
                .handleResult({
                    smsPackageList.value = it
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun setSelectSmsPackage(item: SmsPackageBean) {
        selectSmsPackage.value = item
    }

    fun confirm() {
        smsBalance.value?.let {
            if (it.hasBalance()) {
                viewModelScope.launch {
                    showLoading()
                    _messageRepo.sendHelloSms(friendUserId, it.content)
                        .handleResult({
                            showToast("发送成功")
                            dismissDialogCommand.value = VMEvent(Unit)
                        }, {
                            showToast(it.errMsg)
                        }, {
                            hideLoading()
                        })
                }
            } else {
                selectSmsPackage.value?.let { packageBean ->
                    viewModelScope.launch {
                        showLoading()
                        _accountInfoRepo.addSmsPackageOrder(packageBean.id)
                            .handleResult({
                                it?.let {
                                    toPaymentOrderCommand.value = VMEvent(it)
                                }
                            }, {
                                showToast(it.errMsg)
                            }, {
                                hideLoading()
                            })
                    }
                }
            }
        }
    }
}