package com.bxkj.enterprise.data.source.datasource;

import com.bxkj.common.base.mvvm.callback.ResultCallBack;
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.enterprise.data.AuthenticatedData;
import com.bxkj.enterprise.data.CompanyInfoDataV2;
import com.bxkj.enterprise.data.CustomServiceData;                  
import com.bxkj.enterprise.data.UserCenterData;
import com.bxkj.jrzp.user.data.AccountVipData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source.datasource
 * @Description:
 * @TODO: TODO
 * @date 2019/6/15
 */
public interface AccountInfoDataSource {

    interface CheckCompanyInfoFillCallBack {
        void onInfoFill();

        void onInfoNoFill();
    }

    void getCompanyInfo(int userId, ResultDataCallBack<CompanyInfoDataV2> callBack);

    void checkCompanyInfoFill(int userId, CheckCompanyInfoFillCallBack callBack);

    void checkCertification(int userId, ResultDataCallBack<AuthenticatedData> callBack);

    void getAccountVipInfo(int userId, ResultDataCallBack<AccountVipData> callBack);

    void checkResumeInfoCompleted(int userId, ResultCallBack callBack);

    void getUserCenterInfo(int userId, ResultDataCallBack<UserCenterData> callBack);

    void getServicesInfo(ResultDataCallBack<CustomServiceData> callBack);
}
