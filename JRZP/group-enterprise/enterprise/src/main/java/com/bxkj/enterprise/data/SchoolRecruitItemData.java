package com.bxkj.enterprise.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2019/6/13
 */
public class SchoolRecruitItemData {

    /**
     * id : 326124
     * title : 2019年杭州市大学生暑期就业见习招聘活动经贸专场暨2019年浙江经贸职业技术学院实习招聘会邀请函
     * pic : http://img.jrzp.com/images_server/gaoxiao/img/201201128021.jpg
     * ksdate : 2019-06-04
     * jsdate : 2019-06-04
     * dateName : 2019-06-04 09:00:00
     * dateNameFlag : 1
     * baomingKey : 1
     */

    private int id;
    private String title;
    private String pic;
    private String ksdate;
    private String jsdate;
    private String dateName;
    private int dateNameFlag;
    private int baomingKey;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getKsdate() {
        return ksdate;
    }

    public void setKsdate(String ksdate) {
        this.ksdate = ksdate;
    }

    public String getJsdate() {
        return jsdate;
    }

    public void setJsdate(String jsdate) {
        this.jsdate = jsdate;
    }

    public String getDateName() {
        return dateName;
    }

    public void setDateName(String dateName) {
        this.dateName = dateName;
    }

    public int getDateNameFlag() {
        return dateNameFlag;
    }

    public void setDateNameFlag(int dateNameFlag) {
        this.dateNameFlag = dateNameFlag;
    }

    public int getBaomingKey() {
        return baomingKey;
    }

    public void setBaomingKey(int baomingKey) {
        this.baomingKey = baomingKey;
    }
}
