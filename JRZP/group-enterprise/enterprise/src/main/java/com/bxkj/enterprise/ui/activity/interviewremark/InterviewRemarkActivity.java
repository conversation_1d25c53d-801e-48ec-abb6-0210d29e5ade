package com.bxkj.enterprise.ui.activity.interviewremark;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.EditText;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.base.BaseActivity;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.enterprise.R;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.interviewremark
 * @Description: 发送邀请备注
 * @TODO: TODO
 * @date 2018/9/3
 */
public class InterviewRemarkActivity extends BaseActivity {

  public static final String OTHER_REMARK = "other_remark";
  public static final String REMARK_TIPS = "remark_tips";

  private RecyclerView recyclerRemark;
  private EditText etOtherRemark;

  InterviewRemarkAdapter mInterviewRemarkAdapter;
  private List<String> mRemarkTips;
  private String mOtherRemark;

  public static Intent newIntent(Context context, List<String> remarkTips, String otherReamrk) {
    Intent starter = new Intent(context, InterviewRemarkActivity.class);
    starter.putStringArrayListExtra(REMARK_TIPS, (ArrayList<String>) remarkTips);
    starter.putExtra(OTHER_REMARK, otherReamrk);
    return starter;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_interview_remark;
  }

  @Override
  protected void initIntent(Intent intent) {
    mRemarkTips = intent.getStringArrayListExtra(REMARK_TIPS);
    mOtherRemark = intent.getStringExtra(OTHER_REMARK);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getResources().getString(R.string.invitation_remark))
      .setRightText(getResources().getString(R.string.common_save))
      .setRightOptionClickListener(view -> backAndResult());
  }

  private void backAndResult() {
    Intent intent = new Intent();
    intent.putStringArrayListExtra(REMARK_TIPS,
      (ArrayList<String>) mInterviewRemarkAdapter.getSelectedItems());
    intent.putExtra(OTHER_REMARK, etOtherRemark.getText().toString());
    setResult(RESULT_OK, intent);
    finish();
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mInterviewRemarkAdapter = new InterviewRemarkAdapter(this,
      Arrays.asList(getResources().getStringArray(R.array.invitation_interview_tips)),
      R.layout.enterprise_recycler_invitation_remark_item);
    mInterviewRemarkAdapter.setOnItemClickListener(
      (view, position) -> SystemUtil.hideSoftKeyboard(this));
    recyclerRemark.setLayoutManager(new LinearLayoutManager(this));
    recyclerRemark.setAdapter(mInterviewRemarkAdapter);
    if (!CheckUtils.isNullOrEmpty(mRemarkTips)) {
      mInterviewRemarkAdapter.setSelectItems(mRemarkTips);
    }
    if (!CheckUtils.isNullOrEmpty(mOtherRemark)) {
      etOtherRemark.setText(mOtherRemark);
    }
  }

  private void bindView(View bindSource) {
    recyclerRemark = bindSource.findViewById(R.id.recycler_remarks);
    etOtherRemark = bindSource.findViewById(R.id.et_other_remark);
  }
}
