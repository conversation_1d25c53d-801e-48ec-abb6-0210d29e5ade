package com.bxkj.enterprise.ui.activity.communicatedpeople

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/3/8
 **/
class CommunicatedPeopleNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/communicated_people"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}