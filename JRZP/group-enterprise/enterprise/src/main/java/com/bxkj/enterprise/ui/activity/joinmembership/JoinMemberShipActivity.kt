package com.bxkj.enterprise.ui.activity.joinmembership

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.BaseDaggerActivity
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.TitleBarManager
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.MemberServicesData
import com.bxkj.enterprise.mvp.contract.AccountVipContract
import com.bxkj.enterprise.mvp.presenter.AccountVipPresenter
import com.bxkj.enterprise.ui.activity.paymentorder.PaymentOrderActivity
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2018/10/15
 * @version V1.0
 */
@Route(path = UpgradeVipNavigation.PATH)
class JoinMemberShipActivity : BaseDaggerActivity(), JoinMemberShipContract.View,
  AccountVipContract.View {

  @Inject
  lateinit var mJoinMemberShipPresenter: JoinMemberShipPresenter

  @Inject
  lateinit var mAccountVipPresenter: AccountVipPresenter

  private lateinit var mMemberServicesListAdapter: MemberServicesListAdapter

  override fun getLayoutId(): Int = R.layout.enterprise_activity_join_membership

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>?): MutableList<BasePresenter<BaseView>> {
    presenters!!.add(CheckUtils.cast(mJoinMemberShipPresenter))
    presenters.add(CheckUtils.cast(mAccountVipPresenter))
    return presenters
  }

  override fun initTitleBar(titleBarManager: TitleBarManager?) {
    titleBarManager!!.setTitle(getString(R.string.join_membership))
  }

  override fun initPage() {
    mAccountVipPresenter.getAccountVipInfo(mUserID)
  }

  override fun getAccountVipInfoSuccess(accountVipData: com.bxkj.jrzp.user.data.AccountVipData?) {
    val recyclerMember = findViewById<RecyclerView>(R.id.recycler_member)
    mMemberServicesListAdapter = MemberServicesListAdapter(
      this,
      null,
      R.layout.enterprise_recycler_member_item,
      accountVipData!!.level
    )
    recyclerMember.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
    recyclerMember.adapter = mMemberServicesListAdapter
    recyclerMember.isNestedScrollingEnabled = false
    mMemberServicesListAdapter.setOnItemClickListener(object : SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        val memberServicesData = mMemberServicesListAdapter.data[position]
        mJoinMemberShipPresenter.createVipOrder(
          if (accountVipData.level == memberServicesData.Level) 2 else 1,
          memberServicesData.Level,
          memberServicesData.Price
        )
      }
    })
    mJoinMemberShipPresenter.getMemberServicesList()
  }

  override fun getMemberServicesListSuccess(memberServicesDataList: MutableList<MemberServicesData>?) {
    memberServicesDataList!!.reverse()
    mMemberServicesListAdapter.addAll(memberServicesDataList)
  }

  override fun createOrderSuccess(orderId: String?, price: Int) {
    startActivity(PaymentOrderActivity.newIntent(this, orderId!!.toInt(), price.toString()))
  }
}