package com.bxkj.enterprise.data.source.datasource

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.enterprise.data.AppVersionData

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source.datasource
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/8/7
 * @version V1.0
 */
interface AppDataSource {

    interface SystemStatusCallBack {
        fun onSystemMaintenance(url: String)
        fun onNormal()
    }

    fun checkSystemStatus(callBack: SystemStatusCallBack)

    fun checkAgreementVersion(callback: ResultDataCallBack<AppVersionData>)
}