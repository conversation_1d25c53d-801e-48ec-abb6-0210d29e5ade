package com.bxkj.enterprise.weight.sendsms

import android.view.ViewGroup.LayoutParams
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.SmsPackageBean
import com.bxkj.enterprise.databinding.EnterpriseDialogSendSmsBinding
import com.bxkj.enterprise.ui.activity.paymentweb.PaymentWebNavigation

/**
 * Description:发送短信Dialog
 * Author:45457
 **/
class SendSmsDialog(private val friendUserId: Int, private val sendSuccess: (() -> Unit)? = null) :
  BaseDBDialogFragment<EnterpriseDialogSendSmsBinding, SendSmsViewModel>() {

  override fun getViewModelClass(): Class<SendSmsViewModel> = SendSmsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_dialog_send_sms

  override fun enableBottomSheet(): Boolean = true

  override fun initPage() {
    viewBinding.viewModel = viewModel
    setFullHeight(LayoutParams.WRAP_CONTENT)

    viewBinding.ivClose.setOnClickListener {
      dismiss()
    }

    setupPricePackageList()

    subscribeViewModelEvent()

    viewModel.start(friendUserId)
  }

  private fun setupPricePackageList() {
    viewBinding.recyclerSmsPackage.apply {
      layoutManager = GridLayoutManager(requireContext(), 3)
      addItemDecoration(
        GridItemDecoration(
          ContextCompat.getDrawable(
            requireContext(),
            R.drawable.divider_12
          )
        )
      )
      adapter = object : SimpleDiffListAdapter<SmsPackageBean>(
        R.layout.enterprise_recycler_sms_package_item,
        SmsPackageBean.DiffCallback()
      ) {
        private var selectPosition = 0

        override fun bind(holder: SuperViewHolder, item: SmsPackageBean, position: Int) {
          super.bind(holder, item, position)

          val itemSelected = position == selectPosition
          holder.itemView.isSelected = itemSelected

          if (position == selectPosition) {
            viewModel.setSelectSmsPackage(item)
          }

          holder.itemView.setOnClickListener {
            if (selectPosition != position) {
              selectPosition = position
              notifyDataSetChanged()
            }
          }
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.toPaymentOrderCommand.observe(this, EventObserver {
      PaymentWebNavigation.create("${CommonApiConstants.PAYMENT_URL}AppPay/confirmPay.aspx?ids=${it.orderid}&paras=${it.uids}")
        .start()
      dismiss()
    })

    viewModel.dismissDialogCommand.observe(this, EventObserver {
      sendSuccess?.invoke()
      dismiss()
    })
  }
}