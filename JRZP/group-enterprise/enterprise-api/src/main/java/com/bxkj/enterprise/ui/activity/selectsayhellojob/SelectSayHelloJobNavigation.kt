package com.bxkj.enterprise.ui.activity.selectsayhellojob

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2021/11/1
 */
class SelectSayHelloJobNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/selectsayhellojob"

    const val EXTRA_APPLICANT_USER_ID = "APPLICANT_USER_ID"
    const val EXTRA_RESUME_ID = "RESUME_ID"

    fun create(applicantUserId: Int, resumeId: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_APPLICANT_USER_ID, applicantUserId)
        .withInt(EXTRA_RESUME_ID, resumeId)
    }
  }
}