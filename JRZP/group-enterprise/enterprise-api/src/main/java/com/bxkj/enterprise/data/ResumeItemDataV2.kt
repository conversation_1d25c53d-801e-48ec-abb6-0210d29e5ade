package com.bxkj.enterprise.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.util.TimeUtils
import com.bxkj.share.BR

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/25
 * @version: V1.0
 */
data class ResumeItemDataV2(
  var id: Int,
  var detailsName2: String? = "",
  var top: Int,
  var wtName: String? = "",
  var quaName: String? = "",
  var name: String? = "",
  var tx: String? = "",
  var sex: Int,
  var birthday: String? = "",
  var cityJZName: String? = "",
  var videoPic: String? = "",
  var willMoney: Int,
  var edate1: String? = "",
  var jobExp: LastWorkExp? = null,
  var edu: Education? = null,
  var ubInfo: ResumeBaseInfoData? = null,
  @get:Bindable
  var IsResume: Int,
  @get:Bindable
  var IsHuihua: Int,
  @get:Bindable
  var Isapply: Int,
  var ncID: Int = 0,
  var state: Int,
  var jaid: Int,
  var tdlyName: String,
  var applyTime: String,
) : BaseObservable() {

  fun hasOrigin(): Boolean {
    return tdlyName.isNotEmpty()
  }

  @Bindable
  fun isHasChat(): Boolean {
    return ncID != 0
  }

  fun setupHasChat() {
    ncID = 1
    notifyPropertyChanged(BR.hasChat)
  }

  data class LastWorkExp(
    var date1: String? = "",
    var coname: String? = "",
    var job: String? = "",
  )

  data class Education(
    var date1: String? = "",
    var school: String = "",
    var proName1: String = "",
  )

  fun hasJobExp(): Boolean {
    return jobExp != null
  }

  fun hasEdu(): Boolean {
    return edu != null
  }

  fun getFormatMoney(): String {
    if (willMoney == 0) {
      return "面议"
    }
    return "${willMoney}元"
  }

  fun getApplicantAboutInfo(): String {
    val aboutStringBuilder = StringBuilder()
    ubInfo?.let {
      if (!it.cityJZName.isNullOrEmpty()) {
        aboutStringBuilder.append(it.cityJZName).append(" | ")
      }

      if (!it.birthday.isNullOrEmpty()) {
        aboutStringBuilder.append(TimeUtils.getAge(it.birthday)).append(" | ")
      }
      if (!it.quaName.isNullOrEmpty()) {
        aboutStringBuilder.append(it.quaName).append(" | ")
      }
    }
    aboutStringBuilder.append(wtName)
    return aboutStringBuilder.toString()
  }

  fun getAboutV2(): String {
    val aboutBuilder = StringBuilder();
    if (!ubInfo?.cityJZName.isNullOrEmpty()) {
      aboutBuilder.append(ubInfo?.cityJZName).append(" | ")
    }
    if (!ubInfo?.birthday.isNullOrEmpty()) {
      aboutBuilder.append("${TimeUtils.getAge(ubInfo?.birthday)}岁").append(" | ")
    }
    if (getSexName().isNotEmpty()) {
      aboutBuilder.append(getSexName()).append(" | ")
    }
    if (!wtName.isNullOrEmpty()) {
      aboutBuilder.append(wtName).append(" | ")
    }
    if (!ubInfo?.quaName.isNullOrEmpty()) {
      aboutBuilder.append(ubInfo?.quaName)
    }
    return aboutBuilder.toString()
  }

  fun getConvertExpectJob(): Array<String>? {
    return if (detailsName2.isNullOrEmpty()) {
      arrayOf("无")
    } else {
      detailsName2?.split(",", "，")?.toTypedArray()
    }
  }

  private fun getSexName(): String = if (ubInfo?.sex == 0) "男" else "女"

  fun hasVideo(): Boolean {
    return !videoPic.isNullOrEmpty()
  }

  fun hasTime(): Boolean {
    return !edate1.isNullOrEmpty()
  }

  fun getIsMan(): Boolean {
    return sex == 0
  }

  fun getLastRefreshTimeDiff(): String {
    return TimeUtils.formatTimeDiff(edate1, "yyyy/MM/dd HH:mm:ss")
  }

  fun updateCollectStatus(status: Int) {
    IsResume = status
    notifyPropertyChanged(BR.isResume)
  }

  fun updateSayHelloStatus(status: Int) {
    IsHuihua = status
    notifyPropertyChanged(BR.isHuihua)
  }

  fun updateDownloadStatus(status: Int) {
    Isapply = status
    notifyPropertyChanged(BR.isapply)
  }

  fun getStateText(): String {
    var stateText = ""
    when (state) {
      0 -> stateText = "待处理"
      1 -> stateText = "待沟通"
      2 -> stateText = "已淘汰"
      3 -> stateText = "已邀请"
      4 -> stateText = "已拒绝"
      5 -> stateText = "已接受"
      6 -> stateText = "面试通过"
      7 -> stateText = "已发offer"
      8 -> stateText = "已拒offer"
      9 -> stateText = "已接受offer"
      10 -> stateText = "已入职"
      else -> {}
    }
    return stateText
  }

  class DiffCallback : DiffUtil.ItemCallback<ResumeItemDataV2>() {

    override fun areItemsTheSame(
      oldItem: ResumeItemDataV2,
      newItem: ResumeItemDataV2
    ): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(
      oldItem: ResumeItemDataV2,
      newItem: ResumeItemDataV2
    ): Boolean {
      return oldItem.id == newItem.id
    }
  }
}