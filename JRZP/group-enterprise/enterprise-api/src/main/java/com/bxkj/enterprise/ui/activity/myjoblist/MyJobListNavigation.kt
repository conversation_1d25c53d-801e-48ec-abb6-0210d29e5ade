package com.bxkj.enterprise.ui.activity.myjoblist

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

class MyJobListNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/myjoblist"

        fun navigate(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}