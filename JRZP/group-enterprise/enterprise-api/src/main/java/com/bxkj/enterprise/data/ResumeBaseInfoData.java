package com.bxkj.enterprise.data;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;
import com.bxkj.enterprise.api.R;
import com.bxkj.video.data.VideoData;
import java.util.List;

/**
 * @date 2018/5/7
 */

public class ResumeBaseInfoData {

  private int id;
  private int uid;
  private String name;
  private int sex;
  private String tx;
  private String domain;
  private String birthday;
  private String graduationTime;
  private String provinceHKName;
  private String cityHKName;
  private String provinceJZName;
  private String cityJZName;
  private String quaName;
  private String salary;
  private String position;
  private String phone;
  private String workExp;
  private String editTime;

  public String getEditTime() {
    return editTime;
  }

  public void setEditTime(String editTime) {
    this.editTime = editTime;
  }

  private List<VideoData> attachVideos;

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public String getWorkExp() {
    return workExp;
  }

  public void setWorkExp(String workExp) {
    this.workExp = workExp;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getSalary() {
    return salary;
  }

  public void setSalary(String salary) {
    this.salary = salary;
  }

  public String getFormatSalary() {
    return (salary == null || salary.equals("0.00")) ? "面议" : salary;
  }

  public String getPosition() {
    return position;
  }

  public void setPosition(String position) {
    this.position = position;
  }

  public String getQuaName() {
    return quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public String getProvinceHKName() {
    return provinceHKName;
  }

  public void setProvinceHKName(String provinceHKName) {
    this.provinceHKName = provinceHKName;
  }

  public String getProvinceJZName() {
    return provinceJZName;
  }

  public void setProvinceJZName(String provinceJZName) {
    this.provinceJZName = provinceJZName;
  }

  public String getCityJZName() {
    return cityJZName;
  }

  public void setCityJZName(String cityJZName) {
    this.cityJZName = cityJZName;
  }

  public String getDomain() {
    return domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public int getSex() {
    return sex;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public String getTx() {
    return tx;
  }

  public String getFixAvatarUrl() {
    return CheckUtils.fixImgUrl(tx);
  }

  public Object getRealAvatar() {
    if (CheckUtils.isNullOrEmpty(tx)) {
      return sex == 0 ? R.drawable.ic_default_avatar_man : R.drawable.ic_default_avatar_woman;
    } else {
      return tx.contains("http") ? tx : domain + tx;
    }
  }

  public void setTx(String tx) {
    this.tx = tx;
  }

  public String getBirthday() {
    return birthday;
  }

  public void setBirthday(String birthday) {
    this.birthday = birthday;
  }

  public String getGraduationTime() {
    return graduationTime;
  }

  public void setGraduationTime(String graduationTime) {
    this.graduationTime = graduationTime;
  }

  public String getCityHKName() {
    return cityHKName;
  }

  public void setCityHKName(String cityHKName) {
    this.cityHKName = cityHKName;
  }

  public List<VideoData> getAttachVideos() {
    return attachVideos;
  }

  public void setAttachVideos(List<VideoData> attachVideos) {
    this.attachVideos = attachVideos;
  }

  public String getSexText() {
    if (sex == 0) {
      return "男";
    } else {
      return "女";
    }
  }

  /**
   * 性别为男
   */
  public boolean isMan() {
    return sex == 0;
  }

  public String getHKAddress() {
    if (CheckUtils.isNullOrEmpty(provinceHKName) && CheckUtils.isNullOrEmpty(cityHKName)) {
      return "未完善";
    } else {
      return provinceHKName + "-" + cityHKName;
    }
  }

  public String getPersonalBasicInfoText() {
    final StringBuilder basicInfoBuilder = new StringBuilder();
    basicInfoBuilder.append(getSexText());
    basicInfoBuilder.append(" ").append(TimeUtils.getAge(getBirthday())).append("岁");
    return basicInfoBuilder.toString();
  }

  public String getPersonalOtherInfoText() {
    final StringBuilder basicInfoBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(getCityJZName())) {
      basicInfoBuilder.append(getCityJZName());
    }
    basicInfoBuilder.append(" | ").append(getQuaName());
    if (!CheckUtils.isNullOrEmpty(getGraduationTime())) {
      basicInfoBuilder.append(" | ").append("毕业于").append(getGraduationTime());
    }
    return basicInfoBuilder.toString();
  }
}
