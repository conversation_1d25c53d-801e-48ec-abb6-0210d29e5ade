<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="avatar"
            type="String" />
    </data>

    <com.google.android.material.imageview.ShapeableImageView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="@dimen/conversation_msg_avatar_size"
        android:layout_height="@dimen/conversation_msg_avatar_size"
        app:shapeAppearance="@style/roundedCornerImageStyle"
        bind:imgUrl="@{avatar}" />
</layout>