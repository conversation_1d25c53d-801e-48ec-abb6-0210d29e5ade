<?xml version="1.0" encoding="utf-8"?>
<layout>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:background="@drawable/common_bg_dialog">

    <TextView
      android:id="@+id/tv_dialog_title"
      style="@style/Text.18sp.333333.Bold"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/dp_16"
      android:lineSpacingExtra="@dimen/dp_3"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_dialog_content"
      style="@style/wrap_wrap"
      android:layout_width="0dp"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:gravity="center"
      android:lineSpacingExtra="@dimen/dp_3"
      android:textColor="@color/cl_333333"
      android:textSize="@dimen/common_sp_16"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_dialog_title"
      app:layout_goneMarginTop="@dimen/dp_16" />

    <View
      android:id="@+id/v_divider"
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_16"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_dialog_content" />

    <TextView
      android:id="@+id/tv_dialog_confirm"
      android:layout_width="0dp"
      android:layout_height="@dimen/dp_48"
      android:gravity="center"
      android:text="@string/common_confirm"
      android:textColor="@color/cl_ff7405"
      android:textSize="@dimen/common_sp_16"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/v_divider" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>