<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_dialog">

    <TextView
        android:id="@+id/tv_dialog_title"
        style="@style/Text.18sp.333333.Bold"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_10"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_dialog_content"
        style="@style/Text.16sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginTop="@dimen/common_dp_5"
        android:layout_marginEnd="@dimen/dp_20"
        android:lineSpacingExtra="@dimen/dp_3"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_title"
        app:layout_goneMarginTop="@dimen/dp_16" />

    <EditText
        android:id="@+id/et_content"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_40"
        android:layout_marginStart="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_40"
        android:background="@drawable/common_bg_dialog_edit"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_12"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_content" />

    <View
        android:id="@+id/v_h_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="16dp"
        android:background="@color/common_e8e8e8"
        app:layout_constraintTop_toBottomOf="@id/et_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_h_line">

        <TextView
            android:id="@+id/tv_dialog_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/common_cancel"
            android:textColor="@color/common_888888"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/v_v_line"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_h_line" />

        <View
            android:id="@+id/v_v_line"
            android:layout_width="1px"
            android:layout_height="48dp"
            android:background="#E8E8E8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_h_line" />

        <TextView
            android:id="@+id/tv_dialog_center_options"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/common_888888"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/v_v_line"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_h_line" />

        <View
            android:id="@+id/v_v_line1"
            android:layout_width="1px"
            android:layout_height="48dp"
            android:background="#E8E8E8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_h_line" />

        <TextView
            android:id="@+id/tv_dialog_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_action_dialog_confirm"
            android:gravity="center"
            android:text="@string/common_confirm"
            android:textColor="@color/common_white"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/v_v_line"
            app:layout_constraintTop_toBottomOf="@id/v_h_line" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>