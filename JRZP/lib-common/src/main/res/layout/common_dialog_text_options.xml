<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dialog_top_margin"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingEnd="@dimen/dp_12"
            android:paddingStart="@dimen/dp_18">

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text.DialogTitle"
                android:layout_width="0dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_big_close" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_desc"
            style="@style/Text.14sp.333333"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_8" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_options"
            style="@style/match_wrap"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_confirm"
            style="@style/Button.Basic"
            android:layout_marginBottom="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_18"
            android:text="@string/confirm" />

    </LinearLayout>
</layout>