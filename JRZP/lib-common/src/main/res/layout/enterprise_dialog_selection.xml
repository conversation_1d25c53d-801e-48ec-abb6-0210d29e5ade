<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="320dp"
  android:layout_height="wrap_content"
  android:background="@drawable/bg_ffffff_radius_4"
  android:paddingTop="@dimen/common_dp_5"
  android:paddingBottom="@dimen/dp_16">

  <TextView
    android:id="@+id/tv_title"
    style="@style/Text.16sp.333333.Bold"
    android:layout_marginStart="@dimen/dp_12"
    android:layout_marginEnd="@dimen/dp_12"
    android:paddingTop="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_10"
    android:visibility="gone"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/recycler_selection"
    style="@style/match_wrap"
    android:layout_marginBottom="@dimen/dp_20"
    app:layout_constrainedHeight="true"
    app:layout_constraintBottom_toTopOf="@id/tv_confirm"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintHeight_max="360dp"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tv_title" />

  <TextView
    android:id="@+id/tv_cancel"
    style="@style/Text.14sp.333333"
    android:layout_marginEnd="@dimen/dp_24"
    android:text="@string/common_cancel"
    app:layout_constraintBaseline_toBaselineOf="@id/tv_confirm"
    app:layout_constraintEnd_toStartOf="@id/tv_confirm" />

  <TextView
    android:id="@+id/tv_confirm"
    style="@style/Text.14sp.333333"
    android:layout_marginEnd="@dimen/dp_16"
    android:text="@string/common_confirm"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>