<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/title_bar"
  style="@style/Layout.TitleBar"
  android:background="@drawable/common_bg_title_bar">

  <ImageView
    android:id="@+id/iv_left"
    style="@style/wrap_wrap"
    android:src="@drawable/common_ic_back"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_title"
    style="@style/Text.18sp.333333"
    android:layout_width="@dimen/dp_0"
    android:layout_marginStart="@dimen/common_dp_35"
    android:layout_marginEnd="@dimen/common_dp_35"
    android:ellipsize="marquee"
    android:gravity="center"
    android:singleLine="true"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <TextView
    android:id="@+id/tv_right"
    style="@style/Text.15sp.333333"
    android:layout_marginEnd="@dimen/dp_12"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <ImageView
    android:id="@+id/iv_right_option_three"
    style="@style/wrap_wrap"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>