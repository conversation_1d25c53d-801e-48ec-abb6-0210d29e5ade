<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="listViewModel"
            type="com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel" />
    </data>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_content"
        style="@style/match_match"
        android:layout_weight="1"
        app:enableLoadMore="@{listViewModel.refreshLayoutViewModel.enableLoadMore}"
        app:enableRefresh="@{listViewModel.refreshLayoutViewModel.enableRefresh}"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
        app:loadFinish="@{listViewModel.refreshLayoutViewModel.loadFinish}"
        app:noMoreData="@{listViewModel.refreshLayoutViewModel.noMoreData}"
        app:onRefreshAndLoadMoreListener="@{listViewModel.refreshLayoutViewModel}"
        app:srlEnableLoadMoreWhenContentNotFull="false">

        <com.bxkj.common.widget.pagestatuslayout.PageStatusLayout
            android:id="@+id/psl_content"
            style="@style/match_match"
            app:hidden="@{listViewModel.hiddenPageStatusLayout}"
            app:show="@{listViewModel.pageStatusConfig}">


            <com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuRecyclerView
                android:id="@+id/recycler_content"
                style="@style/match_match"
                android:adapter="@{listViewModel.adapter}"
                android:overScrollMode="never"
                android:scrollbarStyle="insideOverlay" />


        </com.bxkj.common.widget.pagestatuslayout.PageStatusLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

</layout>
