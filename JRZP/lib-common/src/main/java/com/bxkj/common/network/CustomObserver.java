package com.bxkj.common.network;

import androidx.annotation.NonNull;

import com.bxkj.common.network.exception.ExceptionFactory;
import com.bxkj.common.network.exception.RespondThrowable;

import io.reactivex.Observer;

/**
 * @Package com.bxkj.baselib.network
 * @Description: 自定义异常处理Observer
 * @date 2018/3/19
 */

public abstract class CustomObserver implements Observer<BaseResponse> {

    @Override
    public void onNext(BaseResponse tBaseResponse) {
        if (tBaseResponse.getData() != null || tBaseResponse.getStatus() == 10001) {
            onSuccess(tBaseResponse);
        } else {
            onError(new RespondThrowable(null, tBaseResponse.getStatus(), tBaseResponse.getMsg()));
        }
    }

    @Override
    public void onError(Throwable e) {
        onError(ExceptionFactory.handleException(e));
    }

    @Override
    public void onComplete() {

    }

    protected abstract void onSuccess(@NonNull BaseResponse baseResponse);

    protected abstract void onError(@NonNull RespondThrowable respondThrowable);

}
