package com.bxkj.common.widget

import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.bxkj.common.R
import com.bxkj.common.databinding.CommonTitleBarBinding
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.kotlin.dip

/**
 * @Description: 通用标题栏
 * @date 2019/4/4
 */
class CommonTitleBar @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

  var ivRight: ImageView? = null
    private set
  private var imgRightTwo: ImageView? = null

  private val rootBinding: CommonTitleBarBinding =
    CommonTitleBarBinding.inflate(LayoutInflater.from(context), this, true)

  private var rightShowView: View? = null

  init {

    if (background == null) {
      setBackgroundResource(R.drawable.common_bg_title_bar)
    }
    ivRight = findViewById(R.id.iv_right_option_three)
    imgRightTwo = findViewById(R.id.iv_option_two)

    rootBinding.root.layoutParams = rootBinding.root.layoutParams.apply {
      height = dip(44)
    }

    initAttrs(context, attrs, defStyleAttr)
  }

  private fun initAttrs(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
    val typedArray =
      context.obtainStyledAttributes(attrs, R.styleable.CommonTitleBar, defStyleAttr, 0)

    val textTitle = typedArray.getString(R.styleable.CommonTitleBar_title)
    setTitle(textTitle)

    val showBackIcon = typedArray.getBoolean(R.styleable.CommonTitleBar_showBackIcon, true)
    if (showBackIcon && context is Activity) {
      setLeftImage(R.drawable.common_ic_back)
      rootBinding.ivLeft.setOnClickListener { v: View? -> context.finish() }
    }
    val leftText = typedArray.getString(R.styleable.CommonTitleBar_left_text)
    if (!CheckUtils.isNullOrEmpty(leftText)) {
      rootBinding.ivLeft.visibility = GONE
      rootBinding.tvLeft.text = leftText
      if (context is Activity) {
        rootBinding.tvLeft.setOnClickListener { v: View? -> context.finish() }
      }
    }
    val drawableLeft = typedArray.getResourceId(R.styleable.CommonTitleBar_left_img, 0)
    if (drawableLeft != 0) {
      setLeftImage(drawableLeft)
    }
    val drawableRight = typedArray.getResourceId(R.styleable.CommonTitleBar_right_img, 0)
    if (drawableRight != 0) {
      setRightImage(drawableRight)
    }
    val drawableRightTwo = typedArray.getResourceId(R.styleable.CommonTitleBar_right_img_two, 0)
    if (drawableRightTwo != 0) {
      setRightImageTwo(drawableRightTwo)
    }
    val rightText = typedArray.getString(R.styleable.CommonTitleBar_right_text)
    if (!CheckUtils.isNullOrEmpty(rightText)) {
      setRightText(rightText)
    }
    val showRightTextBg =
      typedArray.getBoolean(R.styleable.CommonTitleBar_show_right_text_bg, true)

    if (!showRightTextBg) {
      rootBinding.tvRight.apply {
        setPadding(0, 0, 0, 0)
        setTextColor(
          ContextCompat.getColor(getContext(), R.color.cl_333333_to_b5b5b5_enabler)
        )
      }
      setRightTextBg(R.drawable.bg_00000000)
    }
    val showRight = typedArray.getBoolean(R.styleable.CommonTitleBar_show_right, true)
    setShowRight(showRight)
    typedArray.recycle()
  }

  fun setShowRight(showRight: Boolean) {
    if (rightShowView == null) return
    if (showRight) {
      rightShowView!!.visibility = VISIBLE
    } else {
      rightShowView!!.visibility = GONE
    }
  }

  fun setShowLeftOption(show: Boolean) {
    rootBinding.ivLeft.visibility = if (show) VISIBLE else GONE
  }

  /**
   * 设置标题
   */
  fun setTitle(title: String?) {
    rootBinding.ctbTitle.text = title
  }

  fun setTitleColor(@ColorInt color: Int) {
    rootBinding.ctbTitle.setTextColor(color)
  }

  /**
   * 设置左侧图片
   */
  fun setLeftImage(@DrawableRes leftImageResId: Int): CommonTitleBar {
    rootBinding.ivLeft.setImageResource(leftImageResId)
    return this
  }

  /**
   * 设置右侧图片
   */
  fun setRightImage(@DrawableRes rightImage: Int) {
    setRightImage(ContextCompat.getDrawable(context, rightImage))
  }

  /**
   * 设置右侧第二张图片
   *
   * @param img
   */
  fun setRightImageTwo(@DrawableRes img: Int) {
    setRightDrawableTwo(ContextCompat.getDrawable(context, img))
  }

  /**
   * 设置右侧图片
   */
  fun setRightImage(rightImage: Drawable?) {
    ivRight?.apply {
      setVisibility(VISIBLE)
      setImageDrawable(rightImage)
    }
    rightShowView = this.ivRight
  }

  fun setRightDrawableTwo(rightImage: Drawable?) {
    imgRightTwo!!.visibility = VISIBLE
    imgRightTwo!!.setImageDrawable(rightImage)
  }

  fun setRightText(@StringRes stringId: Int) {
    setRightText(context.getString(stringId))
  }

  private fun setRightTextBg(rightTextBg: Int) {
    rootBinding.tvRight.setBackgroundResource(rightTextBg)
  }

  fun setRightTextBg(drawable: Drawable?) {
    rootBinding.tvRight.background = drawable
  }

  /**
   * 设置右侧文字
   */
  fun setRightText(text: String?) {
    rootBinding.tvRight.visibility = VISIBLE
    rootBinding.tvRight.text = text
    rightShowView = rootBinding.tvRight
  }

  fun setRightEnabled(enabled: Boolean) {
    rootBinding.tvRight.isEnabled = enabled
  }

  /**
   * 设置右侧文字颜色
   */
  fun setRightTextColor(@ColorInt color: Int): CommonTitleBar {
    rootBinding.tvRight.setTextColor(color)
    return this
  }

  /**
   * 设置左边点击事件
   */
  fun setLeftOptionClickListener(onClickListener: OnClickListener?): CommonTitleBar {
    rootBinding.ivLeft.setOnClickListener(onClickListener)
    return this
  }

  fun setOptionEnable(enable: Boolean) {
    rootBinding.tvRight.isEnabled = enable
  }

  /**
   * 设置右边点击事件
   */
  fun setRightOptionClickListener(onClickListener: OnClickListener?) {
    if (ivRight!!.visibility == VISIBLE) {
      ivRight!!.setOnClickListener(onClickListener)
    } else {
      rootBinding.tvRight.setOnClickListener(onClickListener)
    }
  }

  fun setRightOptionTwoClickListener(onClickListener: OnClickListener?) {
    if (imgRightTwo != null) {
      imgRightTwo!!.setOnClickListener(onClickListener)
    }
  }

  fun setTitleVisibility(visibility: Int): CommonTitleBar {
    rootBinding.ctbTitle.visibility = visibility
    return this
  }
}