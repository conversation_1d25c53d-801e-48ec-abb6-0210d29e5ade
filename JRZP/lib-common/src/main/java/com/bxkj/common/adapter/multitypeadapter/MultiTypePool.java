package com.bxkj.common.adapter.multitypeadapter;

import androidx.annotation.NonNull;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import static com.bxkj.common.util.Preconditions.checkNotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.jdzj.utils.superadapter
 * @Description:
 * @TODO: TODO
 * @date 2018/1/29
 */

public class MultiTypePool implements TypePool {

  private final List<Class<?>> classes;
  private final List<ItemViewBinder<?>> binders;
  private final List<Linker<?>> linkers;

  public MultiTypePool() {
    this.classes = new ArrayList<>();
    this.binders = new ArrayList<>();
    this.linkers = new ArrayList<>();
  }

  @Override
  public <T> void register(@NotNull Class<? extends T> clazz,
      @NotNull ItemViewBinder<T> itemViewBinder, @NotNull Linker<T> linker) {
    checkNotNull(clazz);
    checkNotNull(itemViewBinder);
    checkNotNull(linker);
    classes.add(clazz);
    binders.add(itemViewBinder);
    linkers.add(linker);
  }

  @Override
  public boolean unregister(@NonNull Class<?> clazz) {
    checkNotNull(clazz);
    boolean removed = false;
    while (true) {
      int index = classes.indexOf(clazz);
      if (index != -1) {
        classes.remove(index);
        binders.remove(index);
        removed = true;
      } else {
        break;
      }
    }
    return removed;
  }

  @Override
  public int size() {
    return classes.size();
  }

  @Override
  public int firstIndexOf(@NotNull Class<?> clazz) {
    checkNotNull(clazz);
    int index = classes.indexOf(clazz);
    if (index != -1) {
      return index;
    }
    for (int i = 0; i < classes.size(); i++) {
      if (classes.get(i).isAssignableFrom(clazz)) {
        return i;
      }
    }
    return -1;
  }

  @NotNull
  @Override
  public Class<?> getClass(int index) {
    return classes.get(index);
  }

  @NotNull
  @Override
  public ItemViewBinder<?> getItemViewBinder(int index) {
    if (index < binders.size()) {
      return binders.get(index);
    }
    return binders.get(0);
  }

  @NonNull
  @Override
  public Linker<?> getLinker(int index) {
    return linkers.get(index);
  }
}
