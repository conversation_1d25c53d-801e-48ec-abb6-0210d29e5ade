package com.bxkj.common.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.R
import com.bxkj.common.BR
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.UserUtils

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/4
 * @version: V1.0
 */
data class ContactBodyBean(
    var id: Int,
    var userID: Int,
    var cUserID: Int,
    var isTop: Int,
    var isCTop: Int,
    var date: String = "",
    var editDate: String = "",
    var relID: Int,
    var relName: String = "",
    var resID: Int,
    var resName: String = "",
    var cUserName: String = "",
    var cUserSex: Int,
    var cUserPhoto: String = "",
    var comID: Int,
    var comName: String = "",
    var jobCount: Int,
    var lastCActiveTime: String = "",
    var userName: String = "",
    var userSex: Int,
    var userPhoto: String = "",
    var lastActiveTime: String = "",
    @Bindable
    var newContent: String = "",
    var jaType: Int,
    var jaState: Int,
    var jaSubSate: Int,
    @Bindable
    var unReadCount: Int,
    var newIslook: Int,
    var SubTypeName: String?
) : BaseObservable() {

    fun getLastMsgTimeDiff(): String {
        return TimeUtils.formatTimeDiff(editDate, "yyyy/MM/dd HH:mm:ss")
    }

    /**
     * 全部消息已读
     */
    fun allMsgRead() {
        unReadCount = 0
        notifyPropertyChanged(BR.unReadCount)
    }

    fun updateLastMsg(msg: String) {
        newContent = msg
        notifyPropertyChanged(BR.newContent)
    }

    /**
     * 用户投递
     */
    private fun isUserSend(): Boolean {
        return jaType == 1
    }

    /**
     * 邀请投递
     */
    private fun isInviteSend(): Boolean {
        return jaType == 0
    }

    /**
     * 用户已接收邀请
     */
    private fun isAcceptInvite(): Boolean {
        return jaState == 2
    }

    fun getResumeSendStateImg(): Int {
        if ((isInviteSend() && isAcceptInvite()) || isUserSend()) {
            return if (jaSubSate >= 3) {  //已邀请
                R.drawable.ic_conversation_msg_yiyao
            } else {  //已投递
                R.drawable.ic_conversation_msg_yitou
            }
        }
        return 0
    }

    fun isOnTop(): Boolean {
        if (UserUtils.isPersonalRole()) {
            return isTop == 1
        } else {
            return isCTop == 1
        }
    }

    fun top() {
        if (UserUtils.isPersonalRole()) {
            isTop = 1
        } else {
            isCTop = 1
        }
    }

    fun cancelTop() {
        if (UserUtils.isPersonalRole()) {
            isTop = 0
        } else {
            isCTop = 0
        }
    }

    fun getUserAvatar(): String {
        if (UserUtils.isPersonalRole()) {
            return cUserPhoto
        } else {
            return userPhoto
        }
    }

    fun getUserNickName(): String {
        if (UserUtils.isPersonalRole()) {
            return comName
        } else {
            return userName
        }
    }

    fun hasMsgReqState(): Boolean {
        return !SubTypeName.isNullOrEmpty();
    }

    class DiffCallback : DiffUtil.ItemCallback<ContactBodyBean>() {

        override fun areItemsTheSame(oldItem: ContactBodyBean, newItem: ContactBodyBean): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: ContactBodyBean, newItem: ContactBodyBean): Boolean {
            return oldItem.id == newItem.id
        }
    }
}