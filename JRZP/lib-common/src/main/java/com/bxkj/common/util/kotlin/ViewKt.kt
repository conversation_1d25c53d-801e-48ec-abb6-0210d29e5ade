package com.bxkj.common.util.kotlin

import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doAfterTextChanged
import androidx.core.widget.doOnTextChanged
import com.bxkj.common.R
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow

fun throttleClick(wait: Long = 200, block: ((View) -> Unit)): View.OnClickListener {
    return View.OnClickListener { v ->
        v.throttleAction(wait, block)
    }
}

fun View.throttleAction(wait: Long = 200, block: ((View) -> Unit)) {
    val current = System.currentTimeMillis()
    val lastClickTime = (this.getTag(R.id.qmui_click_timestamp) as? Int) ?: 0
    if (current - lastClickTime > wait) {
        block(this)
        this.setTag(R.id.qmui_click_timestamp, current)
    }
}

fun debounceClick(wait: Long = 200, block: ((View) -> Unit)): View.OnClickListener {
    return View.OnClickListener { v ->
        var action = (v.getTag(R.id.qmui_click_debounce_action) as? DebounceAction)
        if (action == null) {
            action = DebounceAction(v, block)
            v.setTag(R.id.qmui_click_debounce_action, action)
        } else {
            action.block = block
        }
        v.removeCallbacks(action)
        v.postDelayed(action, wait)
    }
}

class DebounceAction(val view: View, var block: ((View) -> Unit)) : Runnable {

    override fun run() {
        if (view.isAttachedToWindow) {
            block(view)
        }
    }
}

fun View.onClick(wait: Long = 200, block: ((View) -> Unit)) {
    setOnClickListener(throttleClick(wait, block))
}

fun View.onDebounceClick(wait: Long = 200, block: ((View) -> Unit)) {
    setOnClickListener(debounceClick(wait, block))
}

fun View.getCenterXY(): IntArray {
    val centerXY = IntArray(2)
    getLocationOnScreen(centerXY)
    centerXY[0] = centerXY[0] + this.width / 2
    centerXY[1] = centerXY[1] + this.height / 2
    return centerXY
}

fun TextView.textChangeFlow(): Flow<CharSequence> = callbackFlow {
    val watcher = doOnTextChanged { text, _, _, _ ->
        text?.let { trySend(it) }
    }
    addTextChangedListener(watcher)
    awaitClose { removeTextChangedListener(watcher) }
}

fun View.setHeight(height: Int) {
    this.apply {
        layoutParams = layoutParams.apply {
            this.height = height
        }
    }
}

