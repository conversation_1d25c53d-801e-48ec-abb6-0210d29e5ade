package com.bxkj.common.network;

import com.bxkj.common.network.exception.ExceptionCode.Code;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib.network
 * @Description: 请求返回基类
 * @TODO: TODO
 * @date 2018/3/19
 */

public class BaseResponse<T> {

  //请求状态
  private int status;
  //提示消息
  private String msg;
  //返回数据
  private T data;
  //返回列表数据
  @SerializedName(value = "dataList", alternate = { "datalist" })
  private T dataList;
  private String count;
  //赠送积分
  private int integral;
  //第一次发布职位赠送积分
  private int integral1;
  //发布职位每日任务赠送积分
  private int integral2;

  public BaseResponse() {
  }

  public BaseResponse(int status, String msg) {
    this.status = status;
    this.msg = msg;
  }

  public String getCount() {
    return count;
  }

  public void setCount(String count) {
    this.count = count;
  }

  @Code
  public int getStatus() {
    return status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public T getData() {
    return data;
  }

  public void setData(T data) {
    this.data = data;
  }

  public T getDataList() {
    return dataList;
  }

  public void setDataList(T dataList) {
    this.dataList = dataList;
  }

  public int getIntegral() {
    return integral;
  }

  public void setIntegral(int integral) {
    this.integral = integral;
  }

  public int getIntegral1() {
    return integral1;
  }

  public void setIntegral1(int integral1) {
    this.integral1 = integral1;
  }

  public int getIntegral2() {
    return integral2;
  }

  public void setIntegral2(int integral2) {
    this.integral2 = integral2;
  }

  public boolean requestSuccess() {
    return status == 10001;
  }
}
