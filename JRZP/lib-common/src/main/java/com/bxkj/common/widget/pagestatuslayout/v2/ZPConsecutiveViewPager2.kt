package com.bxkj.common.widget.pagestatuslayout.v2

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.R
import com.donkingliang.consecutivescroller.ConsecutiveViewPager2

class ZPConsecutiveViewPager2 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : ConsecutiveViewPager2(context, attrs, defStyle) {
    
    override fun findScrolledItemView(view: View?): View? {
        if (mRecyclerView.adapter is FragmentStateAdapter
            && view is FrameLayout
        ) {
            val targetView = view.findViewWithTag<RecyclerView>(resources.getString(R.string.common_scroll_child_tag))
            if (targetView != null) {
                return targetView
            }
            if (view.childCount > 0) {
                val childRootView = view.getChildAt(0)
                if (childRootView is ViewGroup) {
                    return childRootView.getChildAt(0)
                }
            }
        }
        return view
    }
}