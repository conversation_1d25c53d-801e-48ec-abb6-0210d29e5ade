package com.bxkj.common.data

import androidx.recyclerview.widget.DiffUtil.ItemCallback

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/3/18
 * @version: V1.0
 */
data class ReportReasonItemData(
  val name: String,
  val content: String
) {

  class DiffCallBack : ItemCallback<ReportReasonItemData>() {
    override fun areItemsTheSame(
      oldItem: ReportReasonItemData,
      newItem: ReportReasonItemData
    ): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(
      oldItem: ReportReasonItemData,
      newItem: ReportReasonItemData
    ): Boolean {
      return oldItem.name == newItem.name
    }
  }
}
