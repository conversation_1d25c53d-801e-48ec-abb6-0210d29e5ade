package com.bxkj.common.util;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.TextView;

import com.bxkj.common.api.CommonApiConstants;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;

/**
 * @description 判空帮助类
 * @date 2018/3/30
 */

public class CheckUtils {

  /**
   * 判断list中是否有元素
   */
  public static boolean isNullOrEmpty(List list) {
    if (list == null || list.isEmpty()) {
      return true;
    }
    return false;
  }

  /**
   * 判断字符串是否有内容
   */
  public static boolean isNullOrEmpty(CharSequence charSequence) {
    if (charSequence == null
        || charSequence.toString().trim().isEmpty()
        || charSequence.equals("null")) {
      return true;
    }
    return false;
  }

  public static boolean isNullOrEmpty(CharSequence... items) {
    for (CharSequence item : items) {
      if (CheckUtils.isNullOrEmpty(item)) {
        return true;
      }
    }
    return false;
  }

  public static boolean equalsStr(Object a, Object b) {
    return Objects.equals(a, b);
  }

  /**
   * 判断多个参数是否为空
   */
  public static boolean hasEmptyOfItem(String... items) {
    if (items.length == 0) {
      return true;
    }
    for (String item : items) {
      if (CheckUtils.isNullOrEmpty(item) || item.equals("0")) {
        return true;
      }
    }
    return false;
  }

  /**
   * 判断多个参数是否为空
   */
  public static boolean hasEmptyOfItem(int... items) {
    for (int item : items) {
      if (item == 0) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查null返回int
   */
  public static int checkNullReturnInt(Object object, int defaultInt) {
    if (object == null) {
      return defaultInt;
    }
    return (int) object;
  }

  public static String checkNullReturnString(Object check, String result) {
    if (check == null) {
      return result;
    }
    return (String) check;
  }

  /**
   * 转义sqlite敏感字符
   */
  public static String sqliteEscape(String keyWord) {
    keyWord = keyWord.replace("/", "//");
    keyWord = keyWord.replace("'", "''");
    keyWord = keyWord.replace("[", "/[");
    keyWord = keyWord.replace("]", "/]");
    keyWord = keyWord.replace("%", "/%");
    keyWord = keyWord.replace("&", "/&");
    keyWord = keyWord.replace("_", "/_");
    keyWord = keyWord.replace("(", "/(");
    keyWord = keyWord.replace(")", "/)");
    return keyWord;
  }

  /**
   * 消除类型转换警告，在Observable中处理了异常
   */
  @SuppressWarnings("unchecked")
  public static <T> T cast(Object a) {
    return (T) a;
  }

  /**
   * utf-8编码
   */
  public static String encode(String text) {
    String result;
    try {
      result = URLEncoder.encode(text, "UTF-8");
    } catch (UnsupportedEncodingException e) {
      e.printStackTrace();
      result = "";
    }
    return result;
  }

  public static void checkEditTextIsFill(TextView tv, TextView... editTexts) {
    for (TextView editText : editTexts) {
      editText.addTextChangedListener(new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

        }

        @Override
        public void afterTextChanged(Editable editable) {
          for (TextView text : editTexts) {
            if (!CheckUtils.isNullOrEmpty(text.getText().toString())) {
              continue;
            } else {
              tv.setEnabled(false);
              return;
            }
          }
          tv.setEnabled(true);
        }
      });
    }
  }

  public static String fixImgUrl(String url) {
    if (isNullOrEmpty(url)) {
      return "";
    } else {
      if (url.startsWith("http")) {
        return url.startsWith("https") ? url.replace("https", "http") : url;
      } else {
        return CommonApiConstants.BASE_JRZP_IMG_URL + url;
      }
    }
  }
}
