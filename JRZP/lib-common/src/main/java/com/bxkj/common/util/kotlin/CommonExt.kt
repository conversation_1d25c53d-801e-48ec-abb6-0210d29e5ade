package com.bxkj.common.util.kotlin

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.fragment.app.FragmentActivity
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.ImageItem
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.luck.picture.lib.basic.PictureSelectionModel
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.entity.LocalMedia
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import net.lucode.hackware.magicindicator.MagicIndicator

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/6/30
 * @version: V1.0
 */

fun Map<String, Any>.paramsEncrypt(): EncryptReqParams {
  return EncryptReqParams.encryptMap(this)
}

fun Any.objectEncrypt(): EncryptReqParams {
  return EncryptReqParams.encryptObject(this)
}

fun List<ImageItem>.convertLocalMedia(): List<LocalMedia> {
  val mediaList = ArrayList<LocalMedia>()
  this.forEach { item ->
    mediaList.add(LocalMedia().apply {
      path = item.getPhotoUrl()
    })
  }
  return mediaList
}

fun Intent.getSelectedFirstMediaPath(): String {
  val localMedia: LocalMedia? = PictureSelector.obtainSelectorList(this)[0]
  return localMedia?.getRealMediaPath().getOrDefault()
}

fun LocalMedia?.getRealMediaPath(): String {
  return this?.availablePath.getOrDefault()
}

/**
 * 修复url缺失
 */
fun String.fixImgUrl(): String {
  return if (this.startsWith("http")) {
    if (this.startsWith("https")) this.replace("https", "http") else this
  } else {
    CommonApiConstants.BASE_JRZP_IMG_URL + this
  }
}

/**
 * 转换url为http
 */
fun String.convertUrlToHttp(): String {
  return this.replaceFirst("https", "http")
}

fun String?.toIntOrDefault(default: Int = 0): Int {
  return if (this.isNullOrBlank()) {
    default
  } else {
    this.toInt()
  }
}

fun String.fixJDZJImgUrl(): String {
  return if (this.contains("http")) {
    this.convertUrlToHttp()
  } else {
    "http://img.jdzj.com/$this"
  }
}

/**
 * 解析性别文字
 */
fun Int.parseSexText(): String {
  return if (this == 0) "男" else "女"
}

fun CoroutineScope.startCountDown(
  total: Int,
  onTick: ((Int) -> Unit)? = null,
  onFinish: (() -> Unit)? = null,
  duration: Long = 1000
) {
  flow {
    for (i in total downTo 0) {
      emit(i)
      delay(duration)
    }
  }.flowOn(Dispatchers.Default)
    .onCompletion { onFinish?.invoke() }
    .onEach { onTick?.invoke(it) }
    .flowOn(Dispatchers.Main)
    .launchIn(this)
}

fun CoroutineScope.debounce(
  delayMs: Long = 500L,
  f: suspend CoroutineScope.() -> Unit
) {
  var job: Job? = null
  job?.cancel()
  job = this.launch {
    delay(delayMs)
    job = null
    f()
  }
}

fun ViewPager2.attachIndicator(indicator: MagicIndicator) {
  this.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
    override fun onPageScrollStateChanged(state: Int) {
      super.onPageScrollStateChanged(state)
      indicator.onPageScrollStateChanged(state)
    }

    override fun onPageScrolled(
      position: Int,
      positionOffset: Float,
      positionOffsetPixels: Int
    ) {
      super.onPageScrolled(position, positionOffset, positionOffsetPixels)
      indicator.onPageScrolled(position, positionOffset, positionOffsetPixels)
    }

    override fun onPageSelected(position: Int) {
      super.onPageSelected(position)
      indicator.onPageSelected(position)
    }
  })
}

fun CombinedLoadStates.handleState(
  notLoading: (() -> Unit)? = null,
  noData: (() -> Unit)? = null,
  error: ((RespondThrowable) -> Unit)? = null
) {
  if (this.refresh is LoadState.Error) {
    val convertError = (this.refresh as LoadState.Error).error
    if (convertError is RespondThrowable) {
      if (convertError.isNoDataError) {
        noData?.invoke()
      } else {
        error?.invoke(convertError)
      }
    }
  } else if (this.refresh is LoadState.NotLoading) {
    notLoading?.invoke()
  }
}

fun Context.loadBitmap(picUrl: String, loadedDo: (bitmap: Bitmap?) -> Unit) {
  Glide.with(this.applicationContext)
    .asBitmap()
    .centerCrop()
    .override(150, 150)
    .load(picUrl)
    .into(object : SimpleTarget<Bitmap>() {
      override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
        loadedDo.invoke(resource)
      }

      override fun onLoadFailed(errorDrawable: Drawable?) {
        loadedDo.invoke(null)
      }
    })
}

fun PictureSelectionModel.applyDefaultConfig(): PictureSelectionModel {
  return apply {
    setImageEngine(GlideEngine.getInstance())
    setSandboxFileEngine(SandboxFileEngine.getInstance())
    setCompressEngine(ImageCompressEngine.getInstance())
    setImageSpanCount(4)
  }
}


