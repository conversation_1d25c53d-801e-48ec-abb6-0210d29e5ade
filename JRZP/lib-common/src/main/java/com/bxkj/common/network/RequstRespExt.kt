package com.bxkj.common.network

import com.bxkj.common.network.exception.RespondThrowable

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/3/11
 * @version: V1.0
 */
fun <T> BaseResponse<T>.convertResp(): ReqResponse<T> {
  return if (this.status == 10001) {
    when {
      data != null -> ReqResponse.Success(this.data)
      dataList != null -> ReqResponse.Success(this.dataList)
      else -> ReqResponse.Success(null)
    }
  } else {
    ReqResponse.Failure(RespondThrowable(null, status, msg))
  }
}

fun <T : BaseResponse<*>> T.convertOriginResp(): ReqResponse<T> {
  return if (this.status == 10001) {
    ReqResponse.Success(this)
//    when {
//      data != null -> RequestResp.Success(this)
//      dataList != null -> RequestResp.Success(this)
//      else -> RequestResp.Success(this)
//    }
  } else {
    ReqResponse.Failure(RespondThrowable(null, status, msg))
  }
}

inline fun <T> ReqResponse<T>.handleResult(
  success: (T?) -> Unit = {},
  error: (RespondThrowable) -> Unit = {},
  final: () -> Unit = {},
) {
  if (this is ReqResponse.Success) {
    success.invoke(this.data)
    final.invoke()
  } else if (this is ReqResponse.Failure) {
    error.invoke(this.error)
    final.invoke()
  }
}
