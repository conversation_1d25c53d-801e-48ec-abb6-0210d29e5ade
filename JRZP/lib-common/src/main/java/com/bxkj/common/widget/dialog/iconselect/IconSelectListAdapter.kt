package com.bxkj.common.widget.dialog.iconselect

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.R
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder

/**
 * @Description:
 * @author: Yang<PERSON><PERSON>
 * @date: 2020/12/22
 * @version: V1.0
 */
class IconSelectListAdapter constructor(context: Context, layoutID: Int) :
  SuperAdapter<IconSelectItem>(context, layoutID) {

  override fun convert(holder: SuperViewHolder, viewType: Int, item: IconSelectItem, position: Int) {
    holder.findViewById<ImageView>(R.id.iv_icon).setImageResource(item.icon)
    holder.findViewById<TextView>(R.id.tv_desc).text = item.desc
  }

}