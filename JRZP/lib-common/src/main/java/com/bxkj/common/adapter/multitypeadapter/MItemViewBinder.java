package com.bxkj.common.adapter.multitypeadapter;

import androidx.annotation.IdRes;

import android.view.ViewGroup;

import com.bxkj.common.adapter.superadapter.SuperViewHolder;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib.adapter.multitypeadapter
 * @Description:
 * @TODO: TODO
 * @date 2018/11/22
 */
public abstract class MItemViewBinder<T> implements ItemViewBinder<T> {

    private OnLayoutItemClickListener<T> OnLayoutItemClickListener;

    private int[] mNeedClickViews;

    @Override
    public void onBindViewHolder(SuperViewHolder holder, T item, int position) {
        ViewGroup viewGroup = (ViewGroup) holder.itemView;
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            viewGroup.getChildAt(i).setOnClickListener(view -> {
                if (OnLayoutItemClickListener != null) {
                    OnLayoutItemClickListener.onClicked(view, item, position);
                }
            });
        }
        viewGroup.setOnClickListener(view -> {
            if (OnLayoutItemClickListener != null) {
                OnLayoutItemClickListener.onClicked(view, item, position);
            }
        });
        if (mNeedClickViews!=null){
            for (int mNeedClickView : mNeedClickViews) {
                holder.findViewById(mNeedClickView).setOnClickListener(v -> {
                    if (OnLayoutItemClickListener != null) {
                        OnLayoutItemClickListener.onClicked(v, item, position);
                    }
                });
            }
        }
    }

    public void setOnLayoutItemClickListener(OnLayoutItemClickListener<T> onLayoutItemClickListener) {
        OnLayoutItemClickListener = onLayoutItemClickListener;
    }

    public void setNeedClickListenerViews(@IdRes int... viewIds) {
        mNeedClickViews = viewIds;
    }
}
