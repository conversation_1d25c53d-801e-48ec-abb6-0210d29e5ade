package com.bxkj.common.adapter.superadapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter

/**
 * @Project: ejrzp
 * @Package com.bxkj.baselib.adapter.superadapter
 * @Description:
 * <AUTHOR>
 * @date 2019/9/23
 * @version V1.0
 */
abstract class SuperListAdapter<T> constructor(
    private val layoutId: Int,
    diffUtil: DiffUtil.ItemCallback<T>
) : ListAdapter<T, SuperViewHolder>(diffUtil) {

    private var data: List<T>? = null
    private var mSuperItemClickListener: SuperItemClickListener? = null
    private var ids: IntArray? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SuperViewHolder {
        //兼容databinding
        val viewDataBinding = DataBindingUtil.inflate<ViewDataBinding>(
            LayoutInflater.from(parent.context),
            layoutId,
            parent,
            false
        )
        return if (viewDataBinding != null) {
            SuperViewHolder(viewDataBinding)
        } else {
            SuperViewHolder(
                LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: SuperViewHolder, position: Int) {
        ids?.let {
            for (id in it) {
                holder.findViewById<View>(id).setOnClickListener { v: View? ->
                    if (holder.absoluteAdapterPosition >= 0) {
                        v?.let {
                            mSuperItemClickListener?.onClick(v, holder.absoluteAdapterPosition)
                        }
                    }
                }
            }
        }
        holder.itemView.setOnClickListener {
            if (holder.absoluteAdapterPosition >= 0) {
                mSuperItemClickListener?.onClick(it, holder.absoluteAdapterPosition)
            }
        }
        bind(holder, getItem(position), holder.absoluteAdapterPosition)
    }

    abstract fun bind(holder: SuperViewHolder, item: T, position: Int)

    override fun submitList(list: List<T>?) {
        super.submitList(list)
        data = list
    }

    fun getData(): List<T>? {
        return data
    }

    fun getOnItemClickListener(): SuperItemClickListener? {
        return mSuperItemClickListener
    }

    fun setOnItemClickListener(superItemClickListener: SuperItemClickListener, vararg ids: Int) {
        this.mSuperItemClickListener = superItemClickListener
        this.ids = ids
    }
}