@file:JvmName("PickerUtilsKt")

package com.bxkj.common.util.kotlin

import android.graphics.Color
import com.bigkoo.pickerview.builder.OptionsPickerBuilder
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bxkj.common.data.PickerOptionsData

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/4/8
 * @version: V1.0
 */
fun TimePickerBuilder.applyCustomConfig(): TimePickerBuilder {
  return this.setSubCalSize(16)
    .setTitleBgColor(Color.parseColor("#FFFFFF"))
    .setCancelColor(Color.parseColor("#888888"))
    .setSubmitColor(Color.parseColor("#FF7647"))
    .setSubmitText("完成");
}

fun OptionsPickerBuilder.applyCustomConfig(): OptionsPickerBuilder {
  return this.setSubCalSize(16)
    .setTitleBgColor(Color.parseColor("#FFFFFF"))
    .setCancelColor(Color.parseColor("#888888"))
    .setSubmitColor(Color.parseColor("#FF7647"))
    .setSubmitText("完成");
}

@JvmOverloads
fun Array<String?>.convertWheelOptions(
  idOffset: Int = 0,
  insertPlaceHolder: Boolean = false,
  placeHolderId: Int? = null
): List<PickerOptionsData> {
  val finalList = ArrayList<PickerOptionsData>()
  if (insertPlaceHolder) {
    placeHolderId?.let {
      finalList.add(PickerOptionsData(placeHolderId, "不限"))
    } ?: let {
      finalList.add(PickerOptionsData(idOffset, "不限"))
    }
  }
  for (i in this.indices) {
    val realId = if (insertPlaceHolder) {
      i + idOffset + 1
    } else {
      i + idOffset
    }
    finalList.add(PickerOptionsData(realId, this[i]))
  }
  return finalList
}