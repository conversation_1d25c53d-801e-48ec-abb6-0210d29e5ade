package com.bxkj.common.util.rxbus;

import com.jakewharton.rxrelay2.BehaviorRelay;
import com.jakewharton.rxrelay2.PublishRelay;
import com.jakewharton.rxrelay2.Relay;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib.util
 * @Description:
 * @TODO: TODO
 * @date 2018/4/24
 */

public class RxBus {

  //普通事件
  private final Relay<Object> mBus;

  //粘性事件
  private final Relay<Object> mStickBus;

  private static class Holder {
    private static final RxBus BUS = new RxBus();
  }

  private RxBus() {
    // toSerialized method made bus thread safe
    mBus = PublishRelay.create().toSerialized();
    mStickBus = BehaviorRelay.create();
  }

  public static RxBus get() {
    return Holder.BUS;
  }

  public void post(Object obj) {
    mBus.accept(obj);
  }

  public void postStick(Object obj) {
    mStickBus.accept(obj);
    mBus.accept(obj);
  }

  public <T> Observable<T> toObservable(Class<T> tClass) {
    return mBus.ofType(tClass);
  }

  public <T> Observable<T> toStickObservable(Class<T> tClass) {
    return mStickBus.ofType(tClass);
  }

  public Observable<Object> toObservable() {
    return mBus.hide();
  }

  public boolean hasObservers() {
    return mBus.hasObservers() || mStickBus.hasObservers();
  }

  public static class Message {
    private int code;
    private Object msg;

    public Message() {
    }

    public Message(int code, Object msg) {
      this.code = code;
      this.msg = msg;
    }

    public static Message fromCode(int code) {
      return new Message(code, null);
    }

    public int getCode() {
      return code;
    }

    public void setCode(int code) {
      this.code = code;
    }

    public Object getMsg() {
      return msg;
    }

    public void setMsg(Object msg) {
      this.msg = msg;
    }
  }
}
