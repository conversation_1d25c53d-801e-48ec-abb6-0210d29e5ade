package com.bxkj.common.base;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.ColorInt;
import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import com.bxkj.common.R;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.ActivityManager;
import com.bxkj.common.util.CustomDensityManager;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.router.Router;
import com.bxkj.common.util.router.RouterNavigation;
import com.bxkj.common.widget.dialog.LoadingDialog;
import com.gyf.immersionbar.ImmersionBar;
import com.hjq.toast.Toaster;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib
 * @Description:Activity基类
 * @TODO: TODO
 * @date 2018/3/22
 */

public abstract class BaseActivity extends AppCompatActivity {

    private ImmersionBar mStatusBarManager;

    private TitleBarManager mTitleBarManager;

    private LoadingDialog mLoadingDialog;

    protected static final int LOGIN_REQUEST_CODE = 99;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getLayoutId() != 0) {
            setContentView(getLayoutId());
        }
        initGlobalConfiguration();
        initPresenter();
        initIntent(getIntent());
        setupTitleBarManager();
        initPage();
    }

    private void initGlobalConfiguration() {
        //屏幕适配换算
        CustomDensityManager.setCustomDensity(this, getApplication());
        //将activity压入栈中
        ActivityManager.getInstance().pushActivity(this);
        //初始化沉浸式状态栏
        mStatusBarManager = ImmersionBar.with(this);
        setupStatusBarColor();
    }

    private void setupStatusBarColor() {
        ConstraintLayout clTitleBar = findViewById(R.id.title_bar);
        if (clTitleBar != null) {
            mStatusBarManager.titleBar(clTitleBar).statusBarDarkFont(true, 0.4f).init();
        }
    }

    private void setupTitleBarManager() {
        View titleBar = findViewById(R.id.title_bar);
        if (titleBar != null) {
            mTitleBarManager = TitleBarManager.with(titleBar)
                    .setLeftOptionClickListener(view -> finish());
            initTitleBar(mTitleBarManager);
        }
    }

    @LayoutRes
    protected abstract int getLayoutId();

    /**
     * 处理Presenter
     */
    protected void initPresenter() {
    }

    protected void initTitleBar(TitleBarManager titleBarManager) {
    }

    /**
     * 初始化页面
     */
    protected abstract void initPage();

    /**
     * Intent
     */
    protected void initIntent(Intent intent) {
    }

    protected TitleBarManager getTitleBarManager() {
        return mTitleBarManager;
    }

    protected ImmersionBar getStatusBarManager() {
        return mStatusBarManager;
    }

    protected int getMUserID() {
        return UserUtils.getUserId();
    }

    public boolean checkToLogin() {
        if (getMUserID() == CommonApiConstants.NO_DATA) {
            toLogin();
            return false;
        }
        return true;
    }

    protected void toLogin() {
        Router.getInstance().to(RouterNavigation.LoginActivity.URL)
                .withBoolean(RouterNavigation.LoginActivity.EXTRA_BACK_AFTER_LOGIN, true)
                .startForResult(this, LOGIN_REQUEST_CODE);
    }

    public void showToast(String msg) {
        Toaster.show(msg);
    }

    public void showLoading() {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog.Builder(this).build();
        }
        if (mLoadingDialog.isShowing()) {
            return;
        }
        mLoadingDialog.show();
    }

    public void showLoading(String text) {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog.Builder(this).build();
        }
        mLoadingDialog.setText(text);
        mLoadingDialog.show();
    }

    public void hiddenLoading() {
      if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
        mLoadingDialog.dismiss();
      }
    }

    @ColorInt
    public int getMColor(int color) {
        return ContextCompat.getColor(this, color);
    }

    @Override
    public void finish() {
        SystemUtil.hideSoftKeyboard(this);
        super.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityManager.getInstance().popActivity(this);
    }
}
