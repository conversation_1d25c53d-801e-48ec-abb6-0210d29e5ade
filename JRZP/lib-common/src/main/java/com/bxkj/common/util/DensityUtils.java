package com.bxkj.common.util;

import android.content.Context;

import android.content.res.Resources;
import android.util.DisplayMetrics;
import androidx.annotation.Nullable;
import com.bxkj.common.constants.AppConstants;

/**
 * Created by Administrator on 2017-7-21.
 */

public class DensityUtils {

  /**
   * 根据手机的分辨率从 dip 的单位 转成为 px(像素)
   */
  public static int dp2px(Context context, float dpValue) {
    final float scale = context.getResources().getDisplayMetrics().density;
    return (int) (dpValue * scale + 0.5f);
  }

  /**
   * 由于使用屏幕适配，改变了屏幕密度，这里采用系统密度，避免转换异常
   *
   * @param context
   * @param dpValue
   * @return
   */
  public static int dp2RealPx(float dpValue) {
    final float scale = Resources.getSystem().getDisplayMetrics().density;
    return (int) (dpValue * scale + 0.5f);
  }

  /**
   * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
   */
  public static int px2dip(Context context, float pxValue) {
    final float scale = context.getResources().getDisplayMetrics().density;
    return (int) (pxValue / scale + 0.5f);
  }

  /**
   * 将px值转换为sp值，保证文字大小不变
   */
  public static int px2sp(Context context, float pxValue) {
    final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (pxValue / fontScale + 0.5f);
  }

  /**
   * 将sp值转换为px值，保证文字大小不变
   */
  public static int sp2px(Context context, float spValue) {
    final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (spValue * fontScale + 0.5f);
  }

  /**
   * 获取屏幕宽
   *
   * @return
   */
  public static int getScreenWidth(@Nullable Context context) {
    if (context == null) return 0;
    DisplayMetrics dm = context.getResources().getDisplayMetrics();
    return dm.widthPixels;
  }

  /**
   * 获取屏幕高度
   *
   * @return
   */
  public static int getScreenHeight(@Nullable Context context) {
    if (context == null) return 0;
    DisplayMetrics dm = context.getResources().getDisplayMetrics();
    return dm.heightPixels;
  }

  public static int getScreenDPHeight(Context context) {
    return px2dip(context, AppConstants.SCREEN_HEIGHT);
  }

}
