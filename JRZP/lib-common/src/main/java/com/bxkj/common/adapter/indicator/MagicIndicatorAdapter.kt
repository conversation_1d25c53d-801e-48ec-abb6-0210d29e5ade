package com.bxkj.common.adapter.indicator

import android.content.Context
import androidx.core.content.ContextCompat
import com.bxkj.common.R
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator

/**
 * <AUTHOR>
 * @date 2019/10/17
 * @version V1.0
 */
open class MagicIndicatorAdapter @JvmOverloads constructor(
  titleText: Array<String?>,
  private var tabItemConfig: TabItemConfig = TabItemConfig()
) : CommonNavigatorAdapter() {

  class TabItemConfig(
    var textSize: Float = 16f,
    var normalColor: Int = R.color.cl_333333,
    var selectedColor: Int = R.color.cl_ff7405,
    var scaleRatio: Float = 0f,
    var indicatorWidth: Float = 10f,
    var indicatorHeight: Float = 3f,
    var indicatorColor: Int = R.color.cl_ff7405,
    var indicatorMode: Int = LinePagerIndicator.MODE_EXACTLY,
    var itemPaddingLR: Int = 0,
    var pageIndicator: IPagerIndicator? = null,
  )

  private var onTabClickListener: OnTabClickListener? = null
  private var titles: Array<String?> = titleText
  private var mCurrentPosition = 0

  fun setTitles(titles: Array<String?>) {
    this.titles = titles
    notifyDataSetChanged()
  }

  override fun getTitleView(context: Context, index: Int): IPagerTitleView {
    val pageTitleView = ScalePagerTitleView(context, tabItemConfig.scaleRatio)
    if (tabItemConfig.itemPaddingLR != 0) {
      pageTitleView.setPadding(tabItemConfig.itemPaddingLR, 0, tabItemConfig.itemPaddingLR, 0)
    }
    pageTitleView.textSize = tabItemConfig.textSize
    pageTitleView.normalColor = ContextCompat.getColor(context, tabItemConfig.normalColor)
    pageTitleView.selectedColor =
      ContextCompat.getColor(context, tabItemConfig.selectedColor)
    pageTitleView.text = titles[index]
    pageTitleView.setOnClickListener { view ->
      onTabClickListener?.onTabClicked(view, index)
      mCurrentPosition = index
    }
    return pageTitleView
  }

  override fun getCount(): Int {
    return if (titles.isEmpty()) {
      0
    } else titles.size
  }

  override fun getIndicator(context: Context): IPagerIndicator {
    tabItemConfig.pageIndicator?.let {
      return it
    } ?: let {
      val indicator = LinePagerIndicator(context)
      indicator.mode = tabItemConfig.indicatorMode
      indicator.lineHeight = DensityUtils.dp2px(context, tabItemConfig.indicatorHeight).toFloat()
      indicator.lineWidth = DensityUtils.dp2px(context, tabItemConfig.indicatorWidth).toFloat()
      indicator.setColors(ContextCompat.getColor(context, tabItemConfig.indicatorColor))
      return indicator
    }
  }

  fun setOnTabClickListener(onTabClickListener: OnTabClickListener) {
    this.onTabClickListener = onTabClickListener
  }

  fun getOnTabClickListener(): OnTabClickListener? {
    return onTabClickListener
  }

  fun getTitles(): Array<String?> {
    return titles
  }

  fun getCurrent(): Int {
    return mCurrentPosition
  }

  protected fun setCurrent(current: Int) {
    mCurrentPosition = current
  }

  fun updateTitleByPosition(newTitle: String, position: Int): Boolean {
    if (!titles[position].equals(newTitle)) {
      titles[position] = newTitle
      notifyDataSetChanged()
      return true
    }
    return false
  }
}