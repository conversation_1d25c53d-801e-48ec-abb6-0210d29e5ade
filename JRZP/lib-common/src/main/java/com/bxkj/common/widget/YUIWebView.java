package com.bxkj.common.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.http.SslError;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.bxkj.common.BuildConfig;
import com.bxkj.common.constants.AppConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.HtmlUtils;
import com.bxkj.common.util.SystemUtil;
import com.google.gson.JsonObject;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget
 * @Description:
 * @TODO: TODO
 * @date 2019/1/10
 */
public class YUIWebView extends WebView {

  private static final String TAG = "MyWebView";

  private boolean needClearHistory = false;

  public YUIWebView(Context context) {
    this(context, null);
  }

  public YUIWebView(Context context, AttributeSet attributeSet) {
    super(context, attributeSet);
    init();
  }

  public YUIWebView(Context context, AttributeSet attributeSet, int i) {
    super(context, attributeSet, i);
    init();
  }

  public void loadRichText(String richText) {
    loadDataWithBaseURL(null, HtmlUtils.fixRichText(richText), "text/html", "utf-8", null);
  }

  public void setRichText(String richText) {
    if (CheckUtils.isNullOrEmpty(richText)) {
      return;
    }
    loadRichText(richText);
  }

  public void execJS(String jsCode, ValueCallback<String> callback) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      evaluateJavascript(jsCode, callback);
    } else {
      loadUrl(jsCode);
    }
  }

  private void init() {

    setWebContentsDebuggingEnabled(BuildConfig.DEBUG);

    removeJavascriptInterface("searchBoxJavaBridge_");
    removeJavascriptInterface("accessibility");
    removeJavascriptInterface("accessibilityTraversal");

    setupWebViewSetting();
    setupOnTouchListener();
  }

  private void setupOnTouchListener() {
    setOnTouchListener(new OnTouchListener() {
      private float startx;
      private float starty;
      private float offsetx;
      private float offsety;

      @Override
      public boolean onTouch(View v, MotionEvent event) {
        switch (event.getAction()) {
          case MotionEvent.ACTION_DOWN:
            v.getParent().requestDisallowInterceptTouchEvent(true);
            startx = event.getX();
            starty = event.getY();
            break;
          case MotionEvent.ACTION_MOVE:
            offsetx = Math.abs(event.getX() - startx);
            offsety = Math.abs(event.getY() - starty);
            if (offsetx > offsety) {
              v.getParent().requestDisallowInterceptTouchEvent(true);
            } else {
              v.getParent().requestDisallowInterceptTouchEvent(false);
            }
            break;
          default:
            break;
        }
        return false;
      }
    });
  }

  private boolean handleUrl(WebView view, String url) {
    if (url.startsWith("tel:")) {
      SystemUtil.callPhone(getContext(), url.substring(url.indexOf(":")));
      return true;
    } else {
      if (mOnUrlLoadingListener != null) {
        return mOnUrlLoadingListener.shouldOverrideUrlLoading(view, url);
      } else {
        return false;
      }
    }
  }

  public String getUserClientTag() {
    JsonObject tagJsonObj = new JsonObject();
    tagJsonObj.addProperty("ws_type", -20);
    tagJsonObj.addProperty("version", AppConstants.APP_VERSION_NAME);
    return tagJsonObj.toString();
  }

  @SuppressLint("SetJavaScriptEnabled")
  private void setupWebViewSetting() {
    WebSettings webSetting = getSettings();
    //开启js
    webSetting.setJavaScriptEnabled(true);
    //设置浏览器标识
    webSetting.setUserAgentString(
        webSetting.getUserAgentString() + ";JdzjClient" + "#" + getUserClientTag());
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      webSetting.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
    }
    webSetting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
    //显示缩放控件
    webSetting.setDisplayZoomControls(false);
    webSetting.setLoadWithOverviewMode(true);
    webSetting.setDefaultFontSize(14);
    //dom储存
    webSetting.setDomStorageEnabled(true);
    //缓存模式（浏览器缓存，在请求头加上了Last-Modified，Etag等）
    webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
    //地理位置
    webSetting.setGeolocationEnabled(true);

    webSetting.setAllowFileAccess(true);
    webSetting.setAllowFileAccessFromFileURLs(true);
    webSetting.setAllowUniversalAccessFromFileURLs(true);

    setWebViewClient(new WebViewClient() {

      @Override
      public void onPageFinished(WebView view, String url) {
        if (needClearHistory) {
          clearHistory();
          needClearHistory = false;
        }
      }

      @Override
      public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
        //允许加载不安全网址（自己的很多不安全）
        handler.proceed();
      }

      @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
      @Override
      public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
        return handleUrl(view, request.getUrl().toString());
      }

      @Override
      public boolean shouldOverrideUrlLoading(WebView view, String url) {
        return handleUrl(view, url);
      }

      @Override
      public void onLoadResource(WebView view, String url) {
        super.onLoadResource(view, url);
      }
    });
  }

  @Override
  public void loadUrl(String url) {
    Map<String, String> headerMap = new HashMap<>();
    headerMap.put("Referer", "https://jrzpapi2.jrzp.com");
    loadUrl(url, headerMap);
  }

  public void setSupportZoom(boolean support) {
    //支持缩放
    getSettings().setSupportZoom(support);
    getSettings().setBuiltInZoomControls(support);
  }

  public void markClearHistory() {
    needClearHistory = true;
  }

  @Override
  public void destroy() {
    stopLoading();
    removeAllViews();
    if (getParent() != null) {
      ((ViewGroup) getParent()).removeView(this);
    }
    setWebChromeClient(null);
    setWebViewClient(null);
    destroyDrawingCache();
    super.destroy();
  }

  private OnUrlLoadingListener mOnUrlLoadingListener;

  public void setOnUrlLoadingListener(OnUrlLoadingListener onUrlLoadingListener) {
    mOnUrlLoadingListener = onUrlLoadingListener;
  }

  public interface OnUrlLoadingListener {

    boolean shouldOverrideUrlLoading(@NonNull WebView view, @NonNull String url);
  }
}
