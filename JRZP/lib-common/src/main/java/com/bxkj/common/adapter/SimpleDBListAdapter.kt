package com.bxkj.common.adapter

import android.content.Context
import com.bxkj.common.BR
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder

/**
 * @date 2019/10/22
 * @version V1.0
 */
open class SimpleDBListAdapter<T> @JvmOverloads constructor(
  context: Context,
  layoutId: Int,
  private var brID: Int = BR.data
) : SuperAdapter<T>(context, layoutId) {
  private var onItemLongClickListener: OnItemLongClickListener<T>? = null
  private var needLongClickListenerIds: IntArray? = null

  override fun convert(holder: SuperViewHolder, viewType: Int, item: T, position: Int) {
    holder.bind(brID, item)

    onItemLongClickListener?.let { longClickListener ->
      holder.itemView.setOnLongClickListener {
        return@setOnLongClickListener longClickListener.onLongClicked(it, position, item)
      }
    }
  }

  fun setOnItemLongClickListener(
    onItemLongClickListener: OnItemLongClickListener<T>,
    vararg ids: Int
  ) {
    this.onItemLongClickListener = onItemLongClickListener
    this.needLongClickListenerIds = ids
  }
}