package com.bxkj.common.util

import android.content.ContentResolver
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.util.Base64
import androidx.core.content.FileProvider
import androidx.documentfile.provider.DocumentFile
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.kotlin.getOrDefault
import com.elvishew.xlog.XLog
import com.hjq.toast.Toaster
import com.luck.picture.lib.entity.LocalMedia
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

/**
 * <AUTHOR>
 * @version V1.0
 * @TODO: TODO
 * @date 2018/5/21
 */

/**
 * 获取文件MIME
 */

private val MIME_MapTable =
  arrayOf(
    arrayOf(".3gp", "video/3gpp"),
    arrayOf(".apk", "application/vnd.android.package-archive"),
    arrayOf(".asf", "video/x-ms-asf"),
    arrayOf(".avi", "video/x-msvideo"),
    arrayOf(".bin", "application/octet-stream"),
    arrayOf(".bmp", "image/bmp"),
    arrayOf(".c", "text/plain"),
    arrayOf(".class", "application/octet-stream"),
    arrayOf(".conf", "text/plain"),
    arrayOf(".cpp", "text/plain"),
    arrayOf(".doc", "application/msword"),
    arrayOf(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    arrayOf(".exe", "application/octet-stream"),
    arrayOf(".gif", "image/gif"),
    arrayOf(".gtar", "application/x-gtar"),
    arrayOf(".gz", "application/x-gzip"),
    arrayOf(".h", "text/plain"),
    arrayOf(".htm", "text/html"),
    arrayOf(".html", "text/html"),
    arrayOf(".jar", "application/java-archive"),
    arrayOf(".java", "text/plain"),
    arrayOf(".jpeg", "image/jpeg"),
    arrayOf(".jpg", "image/jpeg"),
    arrayOf(".js", "application/x-javascript"),
    arrayOf(".log", "text/plain"),
    arrayOf(".m3u", "audio/x-mpegurl"),
    arrayOf(".m4a", "audio/mp4a-latm"),
    arrayOf(".m4b", "audio/mp4a-latm"),
    arrayOf(".m4p", "audio/mp4a-latm"),
    arrayOf(".m4u", "video/vnd.mpegurl"),
    arrayOf(".m4v", "video/x-m4v"),
    arrayOf(".mov", "video/quicktime"),
    arrayOf(".mp2", "audio/x-mpeg"),
    arrayOf(".mp3", "audio/x-mpeg"),
    arrayOf(".mp4", "video/mp4"),
    arrayOf(".mpc", "application/vnd.mpohun.certificate"),
    arrayOf(".mpe", "video/mpeg"),
    arrayOf(".mpeg", "video/mpeg"),
    arrayOf(".mpg", "video/mpeg"),
    arrayOf(".mpg4", "video/mp4"),
    arrayOf(".mpga", "audio/mpeg"),
    arrayOf(".msg", "application/vnd.ms-outlook"),
    arrayOf(".ogg", "audio/ogg"),
    arrayOf(".pdf", "application/pdf"),
    arrayOf(".png", "image/png"),
    arrayOf(".pps", "application/vnd.ms-powerpoint"),
    arrayOf(".ppt", "application/vnd.ms-powerpoint"),
    arrayOf(".prop", "text/plain"),
    arrayOf(".rar", "application/x-rar-compressed"),
    arrayOf(".rc", "text/plain"),
    arrayOf(".rmvb", "audio/x-pn-realaudio"),
    arrayOf(".rtf", "application/rtf"),
    arrayOf(".sh", "text/plain"),
    arrayOf(".tar", "application/x-tar"),
    arrayOf(".tgz", "application/x-compressed"),
    arrayOf(".txt", "text/plain"),
    arrayOf(".wav", "audio/x-wav"),
    arrayOf(".wma", "audio/x-ms-wma"),
    arrayOf(".wmv", "audio/x-ms-wmv"),
    arrayOf(".wps", "application/vnd.ms-works"),
    arrayOf(".xml", "text/plain"),
    arrayOf(".z", "application/x-compress"),
    arrayOf(".zip", "application/zip"),
    arrayOf("", "*/*"),
  )

fun File.getMIMEType(): String {
  var type = "*/*"
  val fName = this.name
  // 获取后缀名前的分隔符"."在fName中的位置。
  val dotIndex = fName.lastIndexOf(".")
  if (dotIndex < 0) {
    return type
  }
  // 获取文件的后缀名
  val fileType = fName.substring(dotIndex, fName.length).lowercase()
  if ("" == fileType) {
    return type
  }
  // 在MIME和文件类型的匹配表中找到对应的MIME类型。
  for (i in MIME_MapTable.indices) {
    if (fileType == MIME_MapTable[i][0]) {
      type = MIME_MapTable[i][1]
    }
  }
  return type
}

fun String.bitmapToBase64(compressSize: Float): String =
  try {
    val options = BitmapFactory.Options()
    options.inJustDecodeBounds = true
    options.inPreferredConfig = Bitmap.Config.ARGB_4444
    BitmapFactory.decodeFile(this, options)
    val inSampleSize = 1

    options.inJustDecodeBounds = false // 计算好压缩比例后，这次可以去加载原图了
    options.inSampleSize = inSampleSize // 设置为刚才计算的压缩比例
    val bm = BitmapFactory.decodeFile(this, options)
    val baos = ByteArrayOutputStream()

    // 根据图片压缩后的大小循环压缩
    var size = 100
    bm.compress(Bitmap.CompressFormat.JPEG, size, baos)
    while (baos.toByteArray().size / 1024 > compressSize * 1024 && size > 10) {
      baos.reset()
      size -= 10
      bm.compress(Bitmap.CompressFormat.JPEG, size, baos)
    }
    val b = baos.toByteArray()
    baos.reset()
    bm.recycle()
    Base64.encodeToString(b, Base64.DEFAULT)
  } catch (e: Exception) {
    ""
  }

fun Context.getFileSavePath(pathType: Int): File {
  if (pathType == ZPFileUtils.DOWNLOAD) {
    return if (Environment.MEDIA_MOUNTED === Environment.getExternalStorageState()) {
      this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)!!
    } else {
      File(this.cacheDir.absolutePath + AppConstants.DOWNLOAD_PATH_V2)
    }
  }
  return File(this.cacheDir.absolutePath)
}

fun String.getFileExtensionSafely(): String =
  when {
    // 空字符串
    isEmpty() -> ""
    // 以点开头的隐藏文件
    startsWith(".") && indexOf(".", 1) == -1 -> ""
    // 没有扩展名
    indexOf(".") == -1 -> ""
    // 正常情况
    else -> substringAfterLast(".", "")
  }

object ZPFileUtils {
  const val DOWNLOAD = 1
  const val CACHE = 2

  private const val PREFIX_VIDEO = "video/"

  /**
   * 判断是否是视频文件
   */
  fun isVideoFile(mimeType: String): Boolean = !CheckUtils.isNullOrEmpty(mimeType) && mimeType.contains(PREFIX_VIDEO)

  // 把bitmap转换成String
  fun bitmapToString(
    filePath: String?,
    compressSize: Float,
  ): String =
    try {
      val options = BitmapFactory.Options()
      options.inJustDecodeBounds = true
      options.inPreferredConfig = Bitmap.Config.ARGB_4444
      BitmapFactory.decodeFile(filePath, options)
      val inSampleSize = 1

      options.inJustDecodeBounds = false // 计算好压缩比例后，这次可以去加载原图了
      options.inSampleSize = inSampleSize // 设置为刚才计算的压缩比例
      val bm = BitmapFactory.decodeFile(filePath, options)
      val baos = ByteArrayOutputStream()

      // 根据图片压缩后的大小循环压缩
      var size = 100
      bm.compress(Bitmap.CompressFormat.JPEG, size, baos)
      while (baos.toByteArray().size / 1024 > compressSize * 1024 && size > 10) {
        baos.reset()
        size -= 10
        bm.compress(Bitmap.CompressFormat.JPEG, size, baos)
      }
      val b = baos.toByteArray()
      baos.reset()
      bm.recycle()
      Base64.encodeToString(b, Base64.DEFAULT)
    } catch (e: Exception) {
      ""
    }

  /**
   * 获取weburl文件名
   */
  fun getFileNameForUrl(url: String): String = url.substring(url.lastIndexOf("/") + 1)

  /**
   * 根据全部路径获取文件
   */
  fun getPath(path: String?): File {
    val file = File(path)
    if (!file.exists()) {
      file.mkdirs()
    }
    return file
  }

  /**
   * 根据路径和文件名获取文件
   */
  @JvmStatic
  fun getFile(
    parentPath: String,
    fileName: String,
  ): File {
    val file = File(parentPath + fileName)
    if (!file.exists()) {
      file.parentFile.mkdirs()
      try {
        file.createNewFile()
      } catch (e: IOException) {
        e.printStackTrace()
      }
    }
    return file
  }

  /**
   * 7.0以上兼容用户使用了第三方的文件选择器
   */
  private fun getFilePathFromURI(
    context: Context,
    contentUri: Uri,
    isDocumentFile: Boolean = false,
  ): String? {
    val rootDataDir = context.filesDir
    val fileName =
      if (isDocumentFile) {
        getDocumentFileName(context, contentUri)
      } else {
        getFileName(context, contentUri)
      }
    XLog.d("rootDataDir:$rootDataDir--fileName:$fileName")
    val copyFile = File(rootDataDir.toString() + File.separator + fileName)
    copyFile(context, contentUri, copyFile)
    return copyFile.absolutePath
  }

  private fun getDocumentFileName(
    context: Context,
    uri: Uri,
  ): String? = DocumentFile.fromSingleUri(context, uri)?.name

  private fun getFileName(
    context: Context,
    uri: Uri,
  ): String? {
    var fileName = ""
    val path = uri.path
    path?.let {
      val cut = it.lastIndexOf(File.separator)
      if (cut != -1) {
        fileName = it.substring(cut + 1)
      }
      fileName.let { name ->
        if (!name.contains(".")) {
          val fileType = context.contentResolver.getType(uri)
          fileName = "$name." + fileType?.substringAfterLast("/")?.lowercase()
        }
      }
    }
    return fileName
  }

  private fun copyFile(
    context: Context,
    srcUri: Uri,
    dstFile: File?,
  ) {
    try {
      context.contentResolver.openInputStream(srcUri).use { inputStream ->
        FileOutputStream(dstFile).use { outputStream ->
          if (inputStream == null) return
          copyStream(inputStream, outputStream)
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun copyStream(
    input: InputStream?,
    output: OutputStream?,
  ): Int {
    val BUFFER_SIZE = 1024 * 2
    val buffer = ByteArray(BUFFER_SIZE)
    var count = 0
    var n: Int
    try {
      BufferedInputStream(input, BUFFER_SIZE).use { `in` ->
        BufferedOutputStream(output, BUFFER_SIZE).use { out ->
          while (`in`.read(buffer, 0, BUFFER_SIZE).also { n = it } != -1) {
            out.write(buffer, 0, n)
            count += n
          }
          out.flush()
        }
      }
    } catch (e: IOException) {
      e.printStackTrace()
    }
    return count
  }

  /**
   * 根据Uri获取文件绝对路径，解决Android4.4以上版本Uri转换
   *
   * <AUTHOR>
   * @date 2014-10-12
   */
  fun getFileAbsolutePath(
    context: Context,
    uri: Uri,
  ): String? {
    XLog.d(uri)
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT &&
      DocumentsContract.isDocumentUri(context, uri)
    ) {
      val docId = DocumentsContract.getDocumentId(uri)
      if (isExternalStorageDocument(uri)) {
        val split = docId.split(":").toTypedArray()
        val type = split[0]
        if ("primary".equals(type, ignoreCase = true)) {
          return Environment.getExternalStorageDirectory().toString() + "/" + split[1]
        }
      } else if (isDownloadsDocument(uri)) {
        if (docId.startsWith("raw:")) {
          return docId.replaceFirst("raw:".toRegex(), "")
        }
        if (docId.startsWith("msf:")) {
          return getFilePathFromURI(context, uri)
        }
        // 在不同的手机中下载路径可能有多种前缀
        val contentUriPrefixesToTry =
          arrayOf(
            "content://downloads/public_downloads",
            "content://downloads/my_downloads",
            "content://downloads/all_downloads",
          )
        for (contentUriPrefix in contentUriPrefixesToTry) {
          val contentUri =
            ContentUris.withAppendedId(
              Uri.parse(contentUriPrefix),
              docId.toLong(),
            )
          try {
            val path = getDataColumn(context, contentUri, null, null)
            if (path != null) {
              return path
            }
          } catch (ignored: Exception) {
          }
        }
      } else if (isMediaDocument(uri)) {
        val split = docId.split(":").toTypedArray()
        val type = split[0]
        var contentUri: Uri? = null
        XLog.d("fileType:$type\ndocId:$docId")
        when (type) {
          "image" -> {
            contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
          }

          "video" -> {
            contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
          }

          "audio" -> {
            contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
          }

          "document" -> {
            return getFilePathFromURI(context, uri, true)
//                        contentUri = MediaStore.Files.getContentUri("external")
          }
        }
        val selection = MediaStore.Images.Media._ID + "=?"
        val selectionArgs = arrayOf(split[1])
        return getDataColumn(context, contentUri, selection, selectionArgs)
      }
    } else if ("content".equals(uri.scheme, ignoreCase = true)) { // MediaStore (and general)
      // Return the remote address
      return if (isGooglePhotosUri(uri)) {
        uri.lastPathSegment
      } else {
        getDataColumn(context, uri, null, null)
      }
    } else if ("file".equals(uri.scheme, ignoreCase = true)) {
      return uri.path
    }
    return null
  }

  private fun getDataColumn(
    context: Context,
    uri: Uri?,
    selection: String?,
    selectionArgs: Array<String>?,
  ): String? {
    uri?.let {
      val column = MediaStore.MediaColumns.DATA
      val projection = arrayOf(column)
      try {
        XLog.d(
          "uri:$uri\nprojection:${projection.contentToString()}\nselection:$selection\nselectionArgs:${selectionArgs.contentToString()}",
        )
        context.contentResolver
          .query(uri, projection, selection, selectionArgs, null)
          .use { cursor ->
            XLog.d("cursorMoveFirst:${cursor?.moveToFirst()}--cursorCount:${cursor?.count}")
            // if (cursor?.count == 0) {
            //   getFilePathFromURI(context, uri, true)
            // }
            if (cursor?.moveToFirst() == true) {
              val index = cursor.getColumnIndexOrThrow(column)
              XLog.d("真实路径:${cursor.getString(index)}")
              return cursor.getString(index)
            }
          }
      } catch (e: Exception) {
        XLog.d(e.message)
        return getFilePathFromURI(context, uri)
      }
    }
    return null
  }

  /**
   * @param uri The Uri to check.
   * @return Whether the Uri authority is ExternalStorageProvider.
   */
  fun isExternalStorageDocument(uri: Uri): Boolean = "com.android.externalstorage.documents" == uri.authority

  /**
   * @param uri The Uri to check.
   * @return Whether the Uri authority is DownloadsProvider.
   */
  fun isDownloadsDocument(uri: Uri): Boolean = "com.android.providers.downloads.documents" == uri.authority

  /**
   * @param uri The Uri to check.
   * @return Whether the Uri authority is MediaProvider.
   */
  fun isMediaDocument(uri: Uri): Boolean = "com.android.providers.media.documents" == uri.authority

  /**
   * @param uri The Uri to check.
   * @return Whether the Uri authority is Google Photos.
   */
  fun isGooglePhotosUri(uri: Uri): Boolean = "com.google.android.apps.photos.content" == uri.authority

  /**
   * 从文件中获取Uri
   */
  fun getUriForFile(
    context: Context,
    file: File?,
  ): Uri =
    if (Build.VERSION.SDK_INT >= 24) {
      FileProvider.getUriForFile(
        context.applicationContext,
        "${context.packageName}.zpProvider",
        file!!,
      )
    } else {
      Uri.fromFile(file)
    }

  fun saveBitmap(bitmap: Bitmap?): String {
    if (bitmap == null) {
      return ""
    }
    val file =
      getFile(
        AppConstants.APP_IMG_CACHE_PATH,
        TimeUtils.getCurrentTime(TimeUtils.SECOND, "-") + ".jpg",
      )
    saveBitmap(bitmap, file)
    return file.path
  }

  private fun saveBitmap(
    bitmap: Bitmap,
    saveFile: File?,
  ): Boolean {
    try {
      FileOutputStream(saveFile).use { fos ->
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
        return true
      }
    } catch (e: FileNotFoundException) {
      e.printStackTrace()
      return false
    } catch (e: IOException) {
      e.printStackTrace()
      return false
    }
  }

  fun getVideoCover(videoPath: String?): Bitmap? {
    val mediaMetadataRetriever = MediaMetadataRetriever()
    mediaMetadataRetriever.setDataSource(videoPath)
    val videoCover = mediaMetadataRetriever.frameAtTime
    mediaMetadataRetriever.release()
    return videoCover
  }

  @JvmStatic
  fun getPictureSelectorPath(localMedia: LocalMedia): String = localMedia.availablePath

  @JvmStatic
  fun saveBitmapToGallery(
    context: Context,
    bitmap: Bitmap,
    fileName: String? = null,
  ): Boolean {
    val resolver = context.applicationContext.contentResolver

    val imageName: String =
      if (fileName.isNullOrBlank()) {
        String.format("%s.jpg", TimeUtils.getCurrentTime("yyyyMMdd-HHmmss"))
      } else {
        fileName
      }

    val imageDetails =
      ContentValues().apply {
        // 指定显示的名字
        put(MediaStore.Images.Media.DISPLAY_NAME, imageName)
        // 标记挂起文件，此时文件只能由文件所有者读取、写入，避免未写入完成时其他应用访问
        put(MediaStore.Images.Media.IS_PENDING, 1)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
          // AndroidQ以上指定文件相对路径
          put(
            MediaStore.Images.Media.RELATIVE_PATH,
            Environment.DIRECTORY_PICTURES + File.separator + "JRZP",
          )
        } else {
          // AndroidQ以下指定文件绝对路径
          // AndroidQ以上DATA设个属性被废弃了，避免意外访问无权限文件
          val dstPath: String =
            Environment
              .getExternalStorageDirectory()
              .toString() + File.separator.toString() +
              Environment.DIRECTORY_PICTURES +
              File.separator + "JRZP" + File.separator + fileName
          put(MediaStore.Images.Media.DATA, dstPath)
        }
      }

    val imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, imageDetails)

    imageUri?.let {
      resolver
        .openFileDescriptor(
          imageUri,
          "w",
          null,
        ).use { pfd ->
          pfd?.let {
            FileOutputStream(pfd.fileDescriptor).use { fos ->
              bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
              fos.flush()
            }
          }
        }
      imageDetails.clear()
      // 写入完成，停止挂起，避免其他应用无法访问
      imageDetails.put(MediaStore.Audio.Media.IS_PENDING, 0)
      resolver.update(imageUri, imageDetails, null, null)
      Toaster.show("已保存到手机")
      return true
    } ?: let {
      Toaster.show("保存失败")
      return false
    }
  }

  @JvmStatic
  fun deleteDir(dir: File?): Boolean {
    if (dir == null) return false

    if (!dir.exists()) return true

    dir.listFiles()?.forEach {
      if (it.isFile) {
        it.delete()
      } else if (it.isDirectory) {
        deleteDir(it)
      }
    }
    return dir.delete()
  }

  fun deleteFile(file: File?): Boolean {
    if (file == null) return false
    return if (file.exists()) {
      file.delete()
    } else {
      true
    }
  }

  /**
   * 获取APK文件版本
   */
  fun getApkFileVersion(
    context: Context,
    path: String,
  ): String {
    if (!File(path).exists()) {
      return ""
    }
    return try {
      val packageManager = context.packageManager
      val packageInfo =
        packageManager.getPackageArchiveInfo(path, PackageManager.GET_ACTIVITIES)
      packageInfo?.versionName.getOrDefault()
    } catch (e: Exception) {
      ""
    }
  }

  fun saveVideoToGallery(
    context: Context,
    videoPath: String,
  ): Boolean {
    val sourceFile = File(videoPath)
    if (!sourceFile.exists()) {
      return false
    }
    val fileName = "${System.currentTimeMillis()}.${videoPath.getFileExtensionSafely()}"
    return false
  }

  fun saveVideoToGalleryAndroidQ(
    context: Context,
    videoPath: String,
  ): Boolean {
    val fileName = "${System.currentTimeMillis()}.${videoPath.getFileExtensionSafely()}"
    val values = ContentValues()
    values.put(MediaStore.Video.Media.DISPLAY_NAME, fileName)
    values.put(MediaStore.Video.Media.MIME_TYPE, "video/mp4")
    values.put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM)

    // 设置文件状态为 pending，避免其他应用访问到没有完成写入的文件
    values.put(MediaStore.Video.Media.IS_PENDING, 1)

    val resolver = context.contentResolver
    val uri =
      resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, values) ?: return false

    try {
      // 读取源文件
      val sourceFile = File(videoPath)
      if (!sourceFile.exists()) {
        return false
      }

      resolver.openOutputStream(uri).use { os ->
        FileInputStream(sourceFile).use { fis ->

          // 设置缓冲区大小
          val buffer = ByteArray(1024 * 1024) // 1MB buffer
          var length: Int
          while ((fis.read(buffer).also { length = it }) > 0) {
            os!!.write(buffer, 0, length)
          }
          os!!.flush()
        }
      }
      // 更新文件状态为非 pending，让其他应用可以查看
      values.clear()
      values.put(MediaStore.Video.Media.IS_PENDING, 0)
      resolver.update(uri, values, null, null)

      // 获取视频信息并更新
      val retriever = MediaMetadataRetriever()
      retriever.setDataSource(sourceFile.absolutePath)

      val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
      val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
      val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)

      values.clear()
      if (duration != null) {
        values.put(MediaStore.Video.Media.DURATION, duration.toLong())
      }
      if (width != null && height != null) {
        values.put(MediaStore.Video.Media.WIDTH, width.toInt())
        values.put(MediaStore.Video.Media.HEIGHT, height.toInt())
      }

      if (values.size() > 0) {
        resolver.update(uri, values, null, null)
      }

      retriever.release()

      return true
    } catch (e: java.lang.Exception) {
      // 发生错误时删除文件
      resolver.delete(uri, null, null)
      e.printStackTrace()
      return false
    }
  }

  fun deleteFileByUri(
    contentResolver: ContentResolver,
    uri: Uri,
  ): Boolean =
    try {
      // 检查是否为媒体存储中的文件
      val rowsDeleted = contentResolver.delete(uri, null, null)
      rowsDeleted > 0 // 如果删除成功，返回 true
    } catch (e: Exception) {
      false
    }
}
