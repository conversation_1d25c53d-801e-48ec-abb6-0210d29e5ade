package com.bxkj.common.api;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib
 * @Description:
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CommonApiConstants {

  public static final String SOURCE_PARAMS_NAME = "siteid";

  public static final int SOURCE_ANDROID = -20;

  //账号注销提示
  public static final String ACCOUNT_CLEAR_TIPS = "账号已注销";

  public static final String BASE_URL = "https://jrzpapi2.jdzj.com/";

  public static final String PAYMENT_URL = "https://jrzpapi2.jdzj.com/";

  //分享视频前缀
  public static final String SHARE_VIDEO_URL_PREFIX =
      "https://m.jrzp.com/Shipin/ShipinView.aspx?spId=";

  //分享资讯前缀
  public static final String SHARE_NEWS_URL_PREFIX =
      "https://m.jrzp.com/InstitutionRecruit/InstitutionRecruitDetails.aspx?newsId=";

  //分享问答前缀
  public static final String SHARE_QUESTION_URL_PREFIX =
      "http://m.jrzp.com/Wenda/WendaView.aspx?wdid=";

  //    public static final String BASE_URL = "http://*************:8087";
  //服务协议url
  public static final String AGREEMENT_URL = "https://jrzpapi2.jdzj.com/page/agreement.html";

  //隐私政策
  public static final String PRIVACY_URL = "https://jrzpapi2.jdzj.com/page/secrecy.html";

  //直播协议
  public static final String LIVE_AGREEMENT_URL =
      "https://jrzpapi2.jdzj.com/page/agreement_zhibo.html";

  //图片上传前缀
  public static final String IMG_UPLOAD_PREFIX = "data:image/jpeg;base64,";

  //头像域名
  public static final String BASE_JRZP_IMG_URL = "http://img.jrzp.com/";

  //获取区域列表
  public static final String I_GET_AREA_LIST = "/Area/GetAreaList/";

  //获取职位分类list
  public static final String I_GET_JOB_CLASS_LIST = "/Job/GetJobList/";

  //获取用户选中的期望职位
  public static final String I_GET_USER_CHECKED_JOB = "/UserJob/GetUserJobList/";

  //检查是否已绑定手机号
  public static final String I_CHECK_IS_BIND_MOBILE = "/User/IsHasBindMobilePhone/";

  //默认分页大小
  public static final int DEFAULT_PAGE_SIZE = 15;

  //无数据默认值
  public static final int NO_DATA = -1;

  //无id默认值
  public static final int NO_ID = 0;

  //无内容默认值
  public static final String NO_TEXT = "";

  //获取职位一级分类
  public static final int GET_JOB_FIRST_CLASS_TYPE = 1;

  //获取职位二级分类
  public static final int GET_JOB_SECOND_CLASS_TYPE = 2;

  //省类别编号
  public static final int GET_PROVINCE_TYPE = 1;

  //市类别编号
  public static final int GET_CITY_TYPE = 2;

  //区类别编号
  public static final int GET_AREA_TYPE = 3;

  //街道类别编号
  public static final int GET_STREET_TYPE = 4;

  //企事业单位资讯id
  public static final int SUB_ENTERPRISE_ID = 1;

  //招聘会资讯id
  public static final int SUB_JOB_FAIR_ID = 2;

  //文件上传
  public static final String I_UPLOAD_FILE_URL = "http://img.jrzp.com/serviceInterface.ashx";

  //获取行业列表
  public static final String I_GET_INDUSTRY_LIST = "/Company/GetCompanyTrade/";

  //获取公司性质列表
  public static final String I_GET_COMPANY_NATURE_LIST = "/CompanyPorperty/GetComPorpertyList/";

  //根据位置名获取位置信息
  public static final String I_GET_ADDRESS_INFO_BY_NAME = "/Area/GetAreaByName/";

  /*****************Chat(聊天)*****************/
  //获取聊天对象信息
  public static final String I_GET_CONVERSATION_USER_INFO = "/Conversation/GetConUserInfoV2/";

  //检查是否存在会话
  public static final String I_CHECK_HAS_CONVERSATION = "/Conversation/MatchConInfo/";

  //会话id
  public static final String I_GET_CONVERSATION_CONTENT_LIST =
      "/Conversation/GetContentListByPage/";

  //获取会话列表
  public static final String I_GET_CONVERSATION_LIST = "/Conversation/GetConversationListByPageV2/";

  //发送消息
  public static final String I_SEND_CONVERSATION_MSG = "/Conversation/SendContentV2/";

  //删除会话
  public static final String I_DELETE_CONVERSATION = "/Conversation/DeleteConversation/";

  //举报
  public static final String I_REPORT_CONVERSATION = "/Report/Report/";

  //设置消息已读
  public static final String I_SETUP_MSG_HAS_READ = "/Conversation/SetAllRead/";

  //*********************分享*********************//
  public static final int SHARE_NEWS = 1;
  public static final int SHARE_QA = 2;
  public static final int SHARE_VIDEO = 3;
  public static final int SHARE_USER_HOME = 4;
  public static final int SHARE_STUDY = 5;
  public static final int SHARE_JOB = 6;

  @IntDef({
      SHARE_NEWS, SHARE_QA, SHARE_VIDEO, SHARE_USER_HOME, SHARE_JOB
  })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface ShareType {
  }

  public static final int SHARE_XSP_JOB = 1;
  public static final int SHARE_XSP_RESUME = 2;

  @IntDef({
      SHARE_XSP_JOB, SHARE_XSP_RESUME, SHARE_VIDEO
  })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface ShareXspType {
  }

  public static final int SHARE_SUB_TYPE_TEXT = 0;
  public static final int SHARE_SUB_TYPE_VIDEO = 1;

  @IntDef({
      SHARE_SUB_TYPE_TEXT, SHARE_SUB_TYPE_VIDEO
  })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface ShareSubType {
  }

  //获取分享信息
  public static final String I_GET_SHARE_INFO = "/Share/GetShareMessage/";
}
