package com.bxkj.common.data

import android.os.Parcelable
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.kotlin.fixImgUrl
import kotlinx.parcelize.Parcelize
import java.lang.StringBuilder

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/2
 * @version: V1.0
 */
@Parcelize
class ChatJobBean(
    var relName: String,
    var money: String,
    var companyName: String,
    var cityID: Int,
    var cityName: String,
    var quaID: Int,
    var quaName: String,
    var wtID: Int,
    var wtName: String,
    var description: String,
    var Photo: String?,
    var lxr: String
) : Parcelable {

    fun getJobAbout(): String {
        val stringBuilder = StringBuilder()
        if (!CheckUtils.isNullOrEmpty(cityName)) {
            stringBuilder.append(cityName).append(" | ")
        }
        if (!CheckUtils.isNullOrEmpty(quaName)) {
            stringBuilder.append(quaName).append(" | ")
        }
        if (!CheckUtils.isNullOrEmpty(wtName)) {
            stringBuilder.append(wtName).append(" | ")
        }
        return if (stringBuilder.isEmpty()) return CommonApiConstants.NO_TEXT else stringBuilder.substring(
            0,
            stringBuilder.lastIndexOf("|") - 1
        )
    }

    fun getFixHRPhoto(): String {
        return Photo?.fixImgUrl() ?: ""
    }

    fun getJobTags(): Array<String> {
        return arrayOf(cityName, quaName, wtName)
    }
}