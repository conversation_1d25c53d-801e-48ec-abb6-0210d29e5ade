package com.bxkj.common.widget.marquee;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ViewFlipper;

import androidx.annotation.NonNull;

import com.bxkj.common.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget
 * @Description: 滚动公告栏
 * @date 2018/4/27
 */

public class MarqueeView extends ViewFlipper {

  private static final int DEFAULT_INTERVAL = 3000;
  private static final int DEFAULT_ANIM_DURATION = 500;
  private boolean isSetAnimDuration = false;
  private Adapter mAdapter;

  public MarqueeView(Context context) {
    this(context, null);
  }

  public MarqueeView(Context context, AttributeSet attrs) {
    super(context, attrs);
    setFlipInterval(DEFAULT_INTERVAL);
  }

  public static abstract class Adapter<VH extends ViewHolder> {
    public abstract VH createViewHolder(@NonNull ViewGroup parent);

    public abstract void onBindViewHolder(@NonNull VH holder, int position);

    public abstract int getItemCount();
  }

  public void setAdapter(Adapter adapter) {
    removeAllViews();
    setAdapterInternal(adapter);
  }

  private void setAdapterInternal(Adapter adapterInternal) {
    mAdapter = adapterInternal;

    for (int i = 0; i < adapterInternal.getItemCount(); i++) {
      ViewHolder viewHolder = adapterInternal.createViewHolder(this);
      addView(viewHolder.itemView);
      adapterInternal.onBindViewHolder(viewHolder, i);
    }
  }

  public static abstract class ViewHolder {
    public final View itemView;

    protected ViewHolder(View itemView) {
      this.itemView = itemView;
    }
  }

  /**
   * 启动轮播
   */
  public void start() {
    if (mAdapter.getItemCount() <= 1) {
      stopFlipping();
      return;
    }
    resetAnimation();
    startFlipping();
  }

  private void resetAnimation() {
    clearAnimation();

    Animation animIn = AnimationUtils.loadAnimation(getContext(), R.anim.common_anim_marquee_in);
    if (isSetAnimDuration) animIn.setDuration(DEFAULT_ANIM_DURATION);
    setInAnimation(animIn);

    Animation animOut = AnimationUtils.loadAnimation(getContext(), R.anim.common_anim_marquee_out);
    if (isSetAnimDuration) animOut.setDuration(DEFAULT_ANIM_DURATION);
    setOutAnimation(animOut);
  }

  @Override
  protected void onDetachedFromWindow() {
    clearAnimation();
    mAdapter = null;
    super.onDetachedFromWindow();
  }
}
