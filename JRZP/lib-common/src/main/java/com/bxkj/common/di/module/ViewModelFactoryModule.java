package com.bxkj.common.di.module;

import androidx.lifecycle.ViewModelProvider;

import com.bxkj.common.base.mvvm.ViewModelFactory;

import dagger.Binds;
import dagger.Module;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.di.module
 * @Description:
 * @TODO: TODO
 * @date 2019/4/3
 */
@Module
public abstract class ViewModelFactoryModule {

    @Binds
    abstract ViewModelProvider.Factory bindViewModelFactory(ViewModelFactory viewModelFactory);
}
