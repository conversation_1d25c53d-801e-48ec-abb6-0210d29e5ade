package com.bxkj.common.widget.zpcalenderview

import android.content.Context
import android.view.Gravity
import android.view.ViewGroup
import android.view.ViewGroup.*
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.get
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.R
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.widget.zpcalenderview.CalenderGridAdapter.DayViewHolder
import java.time.LocalDate

/**
 * Description:
 * Author:45457
 **/
class CalenderGridAdapter constructor(
    private val _calenderHelper: CalenderHelper
) : RecyclerView.Adapter<DayViewHolder>() {

    private val days: ArrayList<Day> = ArrayList()

    fun setSelectedDate(date: LocalDate) {
        if (_calenderHelper.updateSelectedDate(date)) {
            notifyDataSetChanged()
        }
    }

    fun resetData(days: List<Day>, selectedDate: LocalDate? = null) {
        this.days.clear()
        this.days.addAll(days)
        selectedDate?.let {
            _calenderHelper.updateSelectedDate(it)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DayViewHolder {
        val itemView = LinearLayout(parent.context).apply {
            layoutParams =
                LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                    orientation = LinearLayout.VERTICAL
                    gravity = Gravity.CENTER
                    addView(getDateContentView(parent.context))
                    addView(getDateDescView(parent.context))
                }
        }
        return DayViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return days.size
    }

    override fun onBindViewHolder(holder: DayViewHolder, position: Int) {
        val day = days[position]
        holder.tvDate.text = day.getShowText()
        holder.tvDesc.text = day.getDescText()
        val selected = _calenderHelper.selectedDate == day.localDate
        holder.tvDate.apply {
            isEnabled = !ZPCalenderUtils.isBeforeToday(day.localDate)
            isSelected = selected
            setBackgroundResource(if (selected) R.drawable.bg_fe6600_round else 0)
        }
        holder.itemView.apply {
            isEnabled = !ZPCalenderUtils.isBeforeToday(day.localDate)
            setOnClickListener {
                _calenderHelper.onDateClickListener?.onDateClick(day.localDate)
                setSelectedDate(day.localDate)
            }
        }
    }

    private fun getDateContentView(context: Context): TextView {
        return TextView(context).apply {
            setTextColor(ContextCompat.getColorStateList(context, R.color.cl_text_calender_item))
            layoutParams = LayoutParams(dip(32), dip(32))
            textSize = 14f
            gravity = Gravity.CENTER
        }
    }

    private fun getDateDescView(context: Context): TextView {
        return TextView(context).apply {
            setTextColor(ContextCompat.getColor(context, R.color.cl_999999))
            layoutParams =
                MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                    topMargin = dip(2)
                    bottomMargin = dip(12)
                }
            textSize = 12f
            gravity = Gravity.CENTER
        }
    }

    class DayViewHolder constructor(view: LinearLayout) : RecyclerView.ViewHolder(view) {

        var tvDate: TextView = view[0] as TextView
        var tvDesc: TextView = view[1] as TextView
    }
}