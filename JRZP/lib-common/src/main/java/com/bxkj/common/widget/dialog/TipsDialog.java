package com.bxkj.common.widget.dialog;

import android.app.Dialog;
import android.content.DialogInterface;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.transition.Slide;

import com.bxkj.common.R;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description:
 * @TODO: TODO
 * @date 2018/5/7
 */

public class TipsDialog extends BaseDialogFragment {

  public static final String TAG = "TipsDialog";

  private TextView tvTitle;
  private TextView tvContent;
  private TextView tvConfirm;

  private OnConfirmClickListener mOnConfirmClickListener;
  private OnDismissListener mOnDismissListener;

  @Override
  protected int getRootViewId() {
    return R.layout.common_dialog_tips;
  }

  @Override
  protected void initView() {
    setCancelable(false);
    tvTitle = getRootView().findViewById(R.id.tv_dialog_title);
    tvContent = getRootView().findViewById(R.id.tv_dialog_content);
    tvConfirm = getRootView().findViewById(R.id.tv_dialog_confirm);

    tvConfirm.setOnClickListener(view -> {
      if (mOnConfirmClickListener != null) {
        mOnConfirmClickListener.onConfirmClicked(this);
      }
      dismiss();
    });
  }

  @Override
  public void onDismiss(DialogInterface dialog) {
    super.onDismiss(dialog);
    if (mOnDismissListener != null) {
      mOnDismissListener.onDismiss(dialog);
    }
  }

  private int titleIcon;
  private String title;
  private String content;
  private String confirmText;
  private int contentGravity;

  public TipsDialog setTitleIcon(@DrawableRes int iconId) {
    titleIcon = iconId;
    return this;
  }

  public TipsDialog setTitle(String title) {
    this.title = title;
    return this;
  }

  public TipsDialog setContentGravity(@Slide.GravityFlag int gravity) {
    this.contentGravity = gravity;
    return this;
  }

  public TipsDialog setContent(String content) {
    this.content = content;
    return this;
  }

  public TipsDialog setConfirmText(String confirmText) {
    this.confirmText = confirmText;
    return this;
  }

  public TipsDialog setOnConfirmClickListener(OnConfirmClickListener onConfirmClickListener) {
    mOnConfirmClickListener = onConfirmClickListener;
    return this;
  }

  public TipsDialog setOnOverrideDismissListener(OnDismissListener onDismissListener) {
    mOnDismissListener = onDismissListener;
    return this;
  }

  @Override
  public void onStart() {
    super.onStart();
    Dialog dialog = getDialog();
    if (dialog != null) {
      DisplayMetrics dm = new DisplayMetrics();
      getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
      dialog.getWindow()
          .setLayout((int) (dm.widthPixels * 0.75), ViewGroup.LayoutParams.WRAP_CONTENT);
    }
    if (!CheckUtils.isNullOrEmpty(title)) {
      tvTitle.setText(title);
    } else {
      tvTitle.setVisibility(View.GONE);
    }
    if (titleIcon != 0) {
      tvTitle.setCompoundDrawablePadding(DensityUtils.dp2px(getContext(), 8f));
      tvTitle.setCompoundDrawablesWithIntrinsicBounds(
          ContextCompat.getDrawable(getContext(), titleIcon), null, null, null);
    }

    if (contentGravity != 0) {
      tvContent.setGravity(contentGravity);
    }
    if (!CheckUtils.isNullOrEmpty(content)) {
      tvContent.setText(content);
    } else {
      tvContent.setVisibility(View.GONE);
    }
    if (!CheckUtils.isNullOrEmpty(confirmText)) {
      tvConfirm.setText(confirmText);
    }
  }

  public interface OnConfirmClickListener {
    void onConfirmClicked(DialogFragment dialogFragment);
  }

  public void show(FragmentManager fragmentManager) {
    //show(fragmentManager, TAG);
    this.show(fragmentManager,TAG);
  }

  @Override
  public void show(@NonNull FragmentManager manager, @Nullable String tag) {
    manager.beginTransaction().add(this, tag).commitAllowingStateLoss();
  }
}
