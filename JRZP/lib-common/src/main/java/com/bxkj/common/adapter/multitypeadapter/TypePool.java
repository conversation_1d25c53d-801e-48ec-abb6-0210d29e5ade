package com.bxkj.common.adapter.multitypeadapter;

import androidx.annotation.NonNull;

import org.jetbrains.annotations.NotNull;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.jdzj.utils.superadapter
 * @Description:
 * @TODO: TODO
 * @date 2018/1/29
 */

public interface TypePool {

    <T> void register(@NotNull Class<? extends T> clazz,
                      @NotNull ItemViewBinder<T> itemViewBinder,
                      @NotNull Linker<T> linker);

    boolean unregister(@NonNull Class<?> clazz);

    int size();

    int firstIndexOf(@NotNull Class<?> clazz);

    @NonNull
    Class<?> getClass(int index);

    @NonNull
    ItemViewBinder<?> getItemViewBinder(int index);

    @NonNull
    Linker<?> getLinker(int index);
}
