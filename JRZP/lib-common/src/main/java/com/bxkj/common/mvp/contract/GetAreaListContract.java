package com.bxkj.common.mvp.contract;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: GetAreaList
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface GetAreaListContract {
    interface View extends BaseView {
        void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getAreaList(int type, int parentId);
    }
}
