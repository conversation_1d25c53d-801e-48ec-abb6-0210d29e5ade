package com.bxkj.common.util.http;

import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/12
 * @version: V1.0
 */
public class HttpPlatform {
  private static final HttpPlatform PLATFORM = findPlatform();

  public static HttpPlatform get() {
    return PLATFORM;
  }

  private static HttpPlatform findPlatform() {
    try {
      Class.forName("android.os.Build");
      if (Build.VERSION.SDK_INT != 0) {
        return new Android();
      }
    } catch (ClassNotFoundException ignored) {
    }
    return new HttpPlatform();
  }

  public Executor defaultCallbackExecutor() {
    return Executors.newCachedThreadPool();
  }

  public void execute(Runnable runnable) {
    defaultCallbackExecutor().execute(runnable);
  }

  static class Android extends HttpPlatform {
    @Override
    public Executor defaultCallbackExecutor() {
      return new MainThreadExecutor();
    }

    static class MainThreadExecutor implements Executor {
      private final Handler handler = new Handler(Looper.getMainLooper());

      @Override
      public void execute(Runnable r) {
        handler.post(r);
      }
    }
  }
}
