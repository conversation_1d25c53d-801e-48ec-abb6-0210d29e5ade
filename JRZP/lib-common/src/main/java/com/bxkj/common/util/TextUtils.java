package com.bxkj.common.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.commonlib.util
 * @Description:
 * @date 2019/12/3
 */
public class TextUtils {

  /**
   * utf-8转16进制
   */
  public static String stringToHex(String str) {
    String hexString = "0123456789ABCDEF";
    byte[] bytes = str.getBytes();
    StringBuilder sb = new StringBuilder(bytes.length * 2);
    for (int i = 0; i < bytes.length; i++) {
      sb.append(hexString.charAt((bytes[i] & 0xf0) >> 4));// 作用同 n / 16
      sb.append(hexString.charAt((bytes[i] & 0x0f)));// 作用同 n
    }
    return sb.toString();
  }

  /**
   * hex转String
   */
  public static String hexToString(String rawNum) {
    StringBuilder stringBuffer = new StringBuilder();
    final String[] hexArray = new String[] {
        "0", "1", "2", "3", "4", "5",
        "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"
    };
    char[] arr = rawNum.toCharArray();
    for (int i = 0; i < arr.length; i += 2) {
      String str = rawNum.substring(i, i + 2);
      int octValue = 0;
      for (int j = 0; j < 2; j++) {
        String s = str.substring(j, j + 1);
        out:
        for (int k = 0; k < hexArray.length; k++) {
          if (hexArray[k].equals(s) && j == 0) {
            octValue += k * 16;
            break out;
          } else if (hexArray[k].equals(s) && j == 1) {
            octValue += k;
            break out;
          }
        }
      }
      char c = (char) octValue;
      stringBuffer.append(c);
    }
    return stringBuffer.toString();
  }

  public static String formatDistance(double distance) {
    if (distance >= 1000) {
      return new BigDecimal(distance / 1000).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()
          + "km";
    } else {
      return new BigDecimal(distance < 1 ? 1 : distance).intValue() + "m";
    }
  }

  /**
   * 修改url参数
   */
  public static String changeParamForKey(String url, String key, String value) {
    if (!CheckUtils.isNullOrEmpty(url) && !CheckUtils.isNullOrEmpty(key)) {
      url = url.replaceAll("(" + key + "=[^&]*)", key + "=" + value);
    }
    return url;
  }


}
