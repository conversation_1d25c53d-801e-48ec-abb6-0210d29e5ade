package com.bxkj.common.widget.dialog.optionsdialog

import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.ImageView.ScaleType.CENTER_INSIDE
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.bxkj.common.R
import com.bxkj.common.databinding.CommonDialogOptionsMenuBinding
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.widget.dialog.ZPDialogFragment

/**
 * Description: 底部选项Dialog
 * Author:45457
 **/
class IconOptionsDialog constructor(
    private var title: String = "",
    private var optionsItem: List<OptionsItem>? = null
) : ZPDialogFragment<CommonDialogOptionsMenuBinding>() {

    override fun enableBottomSheet(): Boolean = true

    override fun getLayoutId(): Int = R.layout.common_dialog_options_menu

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialog)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    private fun initView() {
        viewBinding.tvTitle.text = title
        viewBinding.ivClose.setOnClickListener { dismiss() }

        optionsItem?.let {
            viewBinding.llOptionsContainer.removeAllViews()
            it.forEach { item ->
                viewBinding.llOptionsContainer.addView(getItemView(item))
            }
        }
    }

    private fun getItemView(item: OptionsItem): View {
        return LinearLayout(context).apply {
            addView(ImageView(context).apply {
                layoutParams = MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                    bottomMargin = dip(12)
                }
                scaleType = CENTER_INSIDE
                setImageResource(item.icon)
            })
            addView(TextView(context).apply {
                textSize = 14f
                text = item.title
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
            })
            layoutParams = LinearLayout.LayoutParams(0, LayoutParams.WRAP_CONTENT).apply {
                weight = 1f
            }
            setPadding(0, dip(32), 0, dip(32))
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setOnClickListener {
                dismiss()
                item.onClickListener?.onClick(it)
            }
        }
    }
}