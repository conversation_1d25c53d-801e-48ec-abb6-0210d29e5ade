package com.bxkj.common.widget.filterpopup;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.bxkj.common.R;
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter;

import com.bxkj.common.widget.filterpopup.FilterItemRecyclerViewBinder.OnFilterOptionClickListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @date 2019/3/16
 */
public class FilterView extends LinearLayout {

  private RecyclerView recyclerFilterOptions;

  private MultiTypeAdapter mMultiTypeAdapter;

  private Map<Integer, Integer> positionHolderMap;

  private OnFilterConfirmListener OnFilterConfirmListener;

  private List mOptionsGroupList;

  private int mWidth;

  private int mHeight;

  private int mBottomBarVisible;

  private boolean isItemClickedDismiss;

  public static class Builder {

    private Context context;

    private int width = ViewGroup.LayoutParams.MATCH_PARENT;

    private int height = ViewGroup.LayoutParams.WRAP_CONTENT;

    private int bottomBarVisible = View.VISIBLE;

    private boolean isItemClickedDismiss = false;

    private OnFilterConfirmListener onFilterConfirmListener;

    public Builder(Context context) {
      this.context = context;
    }

    public Builder setHeight(int height) {
      this.height = height;
      return this;
    }

    public Builder setWidth(int width) {
      this.width = width;
      return this;
    }

    public Builder setBottomBarVisible(int visible) {
      this.bottomBarVisible = visible;
      return this;
    }

    public Builder setItemClickedDismiss(boolean isItemClickedDismiss) {
      this.isItemClickedDismiss = isItemClickedDismiss;
      return this;
    }

    public Builder setOnFilterConfirmListener(OnFilterConfirmListener onFilterConfirmListener) {
      this.onFilterConfirmListener = onFilterConfirmListener;
      return this;
    }

    public FilterView build() {
      return new FilterView(this);
    }
  }

  public FilterView(Builder builder) {
    this(builder.context, null);
    init(builder);
  }

  private FilterView(Context context, @Nullable AttributeSet attrs) {
    this(context, attrs, 0);
  }

  private FilterView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  private void init(Builder builder) {
    mWidth = builder.width;
    mHeight = builder.height;
    mBottomBarVisible = builder.bottomBarVisible;
    isItemClickedDismiss = builder.isItemClickedDismiss;
    OnFilterConfirmListener = builder.onFilterConfirmListener;

    inflate(getContext(), R.layout.common_layout_filter, this);

    findViewById(R.id.v_line).setVisibility(mBottomBarVisible);
    findViewById(R.id.ll_filter_bottom_bar).setVisibility(mBottomBarVisible);
    findViewById(R.id.ll_filter_content).getLayoutParams().height = mHeight;

    mOptionsGroupList = new ArrayList();
    recyclerFilterOptions = findViewById(R.id.recycler_filter_options);
    recyclerFilterOptions.setLayoutManager(new LinearLayoutManager(getContext()));
    mMultiTypeAdapter = new MultiTypeAdapter(getContext(), null);
    mMultiTypeAdapter.register(FilterGroupTitle.class, new FilterGroupTitleViewBinder());
    FilterItemRecyclerViewBinder filterItemRecyclerViewBinder =
      new FilterItemRecyclerViewBinder(getContext());
    mMultiTypeAdapter.register(FilterOptionsGroup.class, filterItemRecyclerViewBinder);
    recyclerFilterOptions.setAdapter(mMultiTypeAdapter);

    positionHolderMap = new HashMap<>();

    filterItemRecyclerViewBinder.setOnFilterOptionClickListener(new OnFilterOptionClickListener() {
      @Override
      public void onFilterOptionSelected(final int type, final int position) {
        positionHolderMap.put(type, position);
        if (isItemClickedDismiss) {
          if (OnFilterConfirmListener != null) {
            OnFilterConfirmListener.onFilterConfirm(positionHolderMap);
          }
        }
      }
    });

    //点击重置
    findViewById(R.id.tv_filter_reset).setOnClickListener(view -> {
      filterItemRecyclerViewBinder.resetPosition();
      mMultiTypeAdapter.notifyDataSetChanged();
      positionHolderMap.clear();
    });

    //点击确定
    findViewById(R.id.tv_filter_confirm).setOnClickListener(view -> {
      filterItemRecyclerViewBinder.confirmSelected();
      if (OnFilterConfirmListener != null) {
        OnFilterConfirmListener.onFilterConfirm(positionHolderMap);
      }
    });
  }

  /**
   * 添加分组标题
   */
  public void addGroupTitle(FilterGroupTitle filterGroupTitle) {
    mMultiTypeAdapter.add(filterGroupTitle);
  }

  /**
   * 添加分组items
   */
  public void addGroupItems(FilterOptionsGroup filterOptionsGroup) {
    mOptionsGroupList.add(filterOptionsGroup.filterOptionList);
    mMultiTypeAdapter.add(filterOptionsGroup);
  }

  public void setData(List data) {
    mOptionsGroupList.clear();
    for (int i = 0; i < data.size(); i++) {
      Object datum = data.get(i);
      if (datum instanceof FilterOptionsGroup) {
        mOptionsGroupList.add(((FilterOptionsGroup) datum).filterOptionList);
      }
    }
    mMultiTypeAdapter.setData(data);
    mMultiTypeAdapter.notifyDataSetChanged();
  }

  public List getOptionsGroupData(int position) {
    return (List) mOptionsGroupList.get(position);
  }

  public void setOnFilterConfirmListener(OnFilterConfirmListener onFilterConfirmListener) {
    OnFilterConfirmListener = onFilterConfirmListener;
  }

  public interface OnFilterConfirmListener {

    void onFilterConfirm(Map<Integer, Integer> positionHolderMap);
  }
}
