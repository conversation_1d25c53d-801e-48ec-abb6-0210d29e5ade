package com.bxkj.common.util.map;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description:
 * @TODO: TODO
 * @date 2018/4/26
 */

public class LngLat implements Parcelable {
    private double longitude;//经度
    private double lantitude;//维度

    public LngLat() {
    }

    public LngLat(double longitude, double lantitude) {
        this.longitude = longitude;
        this.lantitude = lantitude;
    }

    protected LngLat(Parcel in) {
        longitude = in.readDouble();
        lantitude = in.readDouble();
    }

    public static final Creator<LngLat> CREATOR = new Creator<LngLat>() {
        @Override
        public LngLat createFromParcel(Parcel in) {
            return new LngLat(in);
        }

        @Override
        public LngLat[] newArray(int size) {
            return new LngLat[size];
        }
    };

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLantitude() {
        return lantitude;
    }

    public void setLantitude(double lantitude) {
        this.lantitude = lantitude;
    }

    public String getString() {
        return longitude + "," + lantitude;
    }

    @Override
    public String toString() {
        return "LngLat{" +
                "longitude=" + longitude +
                ", lantitude=" + lantitude +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeDouble(longitude);
        dest.writeDouble(lantitude);
    }
}
