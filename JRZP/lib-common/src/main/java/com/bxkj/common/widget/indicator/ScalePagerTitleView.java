package com.bxkj.common.widget.indicator;

import android.content.Context;
import android.util.Log;

import android.view.Gravity;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView;

/**
 * @Description: 用于指示器的标题
 * @date 2019/2/25
 */
public class ScalePagerTitleView extends ColorTransitionPagerTitleView {

  private static final float FINAL_SCALE_SIZE = 0.1f;
  private final float scaleSize;


  public ScalePagerTitleView(Context context) {
    this(context, FINAL_SCALE_SIZE);
  }

  public ScalePagerTitleView(Context context, float scaleSize) {
    super(context);
    this.scaleSize = scaleSize;
  }

  @Override
  public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
    super.onLeave(index, totalCount, leavePercent, leftToRight);
    float scalePercent = (1 + scaleSize) - (scaleSize * leavePercent);
    setScaleX(scalePercent);
    setScaleY(scalePercent);
  }

  @Override
  public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {
    super.onEnter(index, totalCount, enterPercent, leftToRight);
    float scalePercent = 1 + (scaleSize * enterPercent);
    setScaleX(scalePercent);
    setScaleY(scalePercent);
  }

}
