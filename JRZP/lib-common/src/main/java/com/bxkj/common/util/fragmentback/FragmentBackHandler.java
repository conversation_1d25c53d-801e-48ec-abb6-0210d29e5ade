package com.bxkj.common.util.fragmentback;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.mafengs.commonlib.utils
 * @Description:
 * @TODO: TODO
 * @date 2019/4/19
 */
public class FragmentBackHandler {

    public static boolean handleFragmentBackPress(FragmentManager fragmentManager) {
        List<Fragment> fragments = fragmentManager.getFragments();
        for (int i = fragments.size() - 1; i >= 0; i--) {
            if (isFragmentBackHandled(fragments.get(i))) {
                return true;
            }
        }
        return false;
    }

    private static boolean isFragmentBackHandled(Fragment fragment) {
        return fragment != null && fragment.isVisible() && fragment instanceof FragmentBackPressHandler && ((FragmentBackPressHandler) fragment).onBackPressed();
    }
}
