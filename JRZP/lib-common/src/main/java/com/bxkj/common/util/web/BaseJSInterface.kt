package com.bxkj.common.util.web

import android.os.Handler
import android.os.Looper
import android.webkit.JavascriptInterface
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.UserUtils
import com.google.gson.JsonObject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
abstract class BaseJSInterface {

  private var mHandler = Handler(Looper.getMainLooper())

  @JavascriptInterface
  fun callHandler(method: String?) {
    callHandler(method, null, null)
  }

  @JavascriptInterface
  fun callHandler(method: String?, params: String?) {
    callHandler(method, params, null)
  }

  @JavascriptInterface
  fun callHandler(method: String?, params: String?, callback: Any?) {
    invokeMethodOnMainHandler {
      handleJsCall(method, params, callback)
    }
  }

  @JavascriptInterface
  fun getUserToken(): String {
    return if (UserUtils.logged()) {
      AESOperator.safeEncrypt(UserUtils.getUserId().toString());
    } else {
      ""
    }
  }

  abstract fun handleJsCall(method: String?, params: String?, callback: Any?)

  private fun invokeMethodOnMainHandler(method: () -> Unit) {
    mHandler.post {
      method.invoke()
    }
  }

  interface OnJSCallListener {
    fun onJSCall(method: String?, params: JsonObject?)
  }
}