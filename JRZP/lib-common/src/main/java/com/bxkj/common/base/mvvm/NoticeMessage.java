package com.bxkj.common.base.mvvm;

import androidx.lifecycle.LifecycleOwner;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.base.vm
 * @Description:
 * @TODO: TODO
 * @date 2018/12/26
 */
public class NoticeMessage extends LiveEvent<Object> {

    public void observe(LifecycleOwner owner, NoticeMessageObserver noticeMessageObserver) {
        super.observe(owner, text -> {
            if (text == null) {
                return;
            }
            noticeMessageObserver.onNoticeMessage(text);
        });
    }

    public interface NoticeMessageObserver {
        void onNoticeMessage(Object noticeMsgText);
    }
}
