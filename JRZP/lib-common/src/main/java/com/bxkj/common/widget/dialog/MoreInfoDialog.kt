package com.bxkj.common.widget.dialog

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.R
import com.bxkj.common.util.HtmlUtils
import com.google.android.material.bottomsheet.BottomSheetDialog

class MoreInfoDialog(
  context: Context,
  private val title: String,
  private val content: String,
) : BottomSheetDialog(context, R.style.BottomSheetDialog) {
  private var dismissAnimator: ValueAnimator? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.common_dialog_more_info)

    findViewById<TextView>(R.id.tv_title)?.text = title
    findViewById<TextView>(R.id.tv_content)?.text = HtmlUtils.fromHtml(content)
    findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
      dismiss()
    }

    // findViewById<View>(R.id.design_bottom_sheet)?.let {
    //   setupDismissAnimation(it)
    //   val behavior = BottomSheetBehavior.from(it)
    //   behavior.addBottomSheetCallback(
    //     object : BottomSheetBehavior.BottomSheetCallback() {
    //       private var lastSlideOffset = 1f
    //       private val DISMISS_THRESHOLD = 0.3f
    //
    //       override fun onSlide(
    //         bottomSheet: View,
    //         slideOffset: Float,
    //       ) {
    //         if (slideOffset < DISMISS_THRESHOLD && lastSlideOffset >= DISMISS_THRESHOLD) {
    //           dismissAnimator?.start()
    //         }
    //         lastSlideOffset = slideOffset
    //       }
    //
    //       override fun onStateChanged(
    //         bottomSheet: View,
    //         newState: Int,
    //       ) {
    //         if (newState == BottomSheetBehavior.STATE_HIDDEN) {
    //           cancel()
    //         }
    //       }
    //     },
    //   )
    // }
  }

  private fun setupDismissAnimation(bottomSheet: View) {
    dismissAnimator =
      ValueAnimator.ofFloat(1f, 0f).apply {
        setDuration(300)
        addUpdateListener { animation: ValueAnimator ->
          val value = animation.animatedValue as Float
          bottomSheet.translationY = bottomSheet.height * (1 - value)
        }
        addListener(
          object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
              dismiss()
            }
          },
        )
      }
  }

  override fun onStart() {
    super.onStart()
    window?.let { window ->
      window.setLayout(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
      )
      window.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let {
        it.setBackgroundColor(Color.TRANSPARENT)
        val layoutParams = it.layoutParams
        layoutParams.height = getLayoutHeight()
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
      }
    }
  }

  private fun getLayoutHeight(): Int {
    val peekHeight = context.resources.displayMetrics.heightPixels
    // 设置弹窗高度为屏幕高度的3/4
    return peekHeight - peekHeight / 3
  }
}
