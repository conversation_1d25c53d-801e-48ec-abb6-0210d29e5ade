package com.bxkj.common.util

import androidx.fragment.app.FragmentActivity
import com.bxkj.common.widget.dialog.ActionDialog
import com.elvishew.xlog.XLog
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions

/**
 * @Description: 权限工具类
 * @date 2019/2/14
 */
object PermissionUtils {
  private const val REQUEST_DIALOG_TAG = "PERMISSION_REQUEST_DIALOG"

  @JvmStatic fun requestPermission(
    activity: FragmentActivity?,
    tipsTitle: String?,
    tipsContent: String?,
    onRequestResultListener: OnRequestResultListener?,
    vararg permission: String?
  ) {
    if (activity == null || permission == null || permission.size == 0) {
      return
    }

    if (XXPermissions.isGranted(activity, *permission)) {
      onRequestResultListener?.onRequestSuccess()
    } else {
      ActionDialog.Builder()
        .setTitle(tipsTitle)
        .setCancelable(false)
        .setCancelText("拒绝")
        .setConfirmText("同意")
        .setContent(tipsContent)
        .setOnConfirmClickListener(ActionDialog.OnConfirmClickListener { dialog: ActionDialog? ->
          startRequestPermission(
            activity,
            onRequestResultListener,
            *permission
          )
        }
        )
        .setOnCancelClickListener(ActionDialog.OnCancelClickListener {
          onRequestResultListener?.onRequestFailed(mutableListOf(), false)
        }
        )
        .build()
        .show(activity.supportFragmentManager, REQUEST_DIALOG_TAG)
    }
  }

  private fun startRequestPermission(
    activity: FragmentActivity,
    onRequestResultListener: OnRequestResultListener?,
    vararg permission: String?
  ) {
    XXPermissions.with(activity)
      .permission(*permission)
      .request(object : OnPermissionCallback {
        override fun onGranted(permissions: MutableList<String?>, all: Boolean) {
          onRequestResultListener?.onRequestSuccess()
        }

        override fun onDenied(permissions: MutableList<String>, never: Boolean) {
          onRequestResultListener?.onRequestFailed(permissions, never)
        }
      })
  }

  interface OnRequestResultListener {
    fun onRequestSuccess()

    fun onRequestFailed(permissions: MutableList<String>, never: Boolean)
  }
}
