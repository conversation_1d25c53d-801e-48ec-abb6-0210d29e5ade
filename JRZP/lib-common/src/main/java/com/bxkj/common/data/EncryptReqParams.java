package com.bxkj.common.data;

import com.bxkj.common.util.AESOperator;
import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.Map;

/**
 * @Project: gzgk
 * @Description: 加密数据
 * @author:45457
 * @date: 2020/6/24
 * @version: V1.0
 */
public class EncryptReqParams {

  private String para;

  /**
   * 加密对象
   */
  public static EncryptReqParams encryptObject(Object object) {
    final GsonBuilder gsonBuilder = new GsonBuilder();
    gsonBuilder.disableHtmlEscaping();
    final Gson gson = gsonBuilder.create();
    final String strJson = gson.toJson(object);
    XLog.d("encryptParams: " + strJson);
    return encrypt(strJson);
  }

  /**
   * 加密map
   */
  public static EncryptReqParams encryptMap(Map<String, Object> map) {
    XLog.d("encryptParams: " + map.toString());
    return encrypt(new Gson().toJson(map));
  }

  private static EncryptReqParams encrypt(String paramsText) {
    return new EncryptReqParams(AESOperator.safeEncrypt(paramsText));
  }

  private EncryptReqParams(String encryptPara) {
    this.para = encryptPara;
  }

  @Override
  public String toString() {
    return "EncryptData{" +
        "para='" + para + '\'' +
        '}';
  }
}
