package com.bxkj.common.data

import android.os.Parcelable
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.CheckUtils
import kotlinx.parcelize.Parcelize

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/2
 * @version: V1.0
 */
@Parcelize
class ChatResumeBean(
  var name: String = "",
  var sex: Int,
  var photo: String,
  var birthday: String,
  var age: Int,
  //学历
  var quaID: Int,
  var quaName: String,
  var cityID: Int,
  var cityName: String,
  var wtID: Int,
  var wtName: String,
  var detailsName2: String,
  var money: String,
  var expID: Int,
  var expBeginTime: String,
  var expEndTime: String,
  var expCompanyName: String,
  var expJob: String,
  var expDescription: String,
  //教育背景
  var eduID: Int,
  var eduBeginTime: String,
  var eduEndTime: String,
  var eduSchool: String,
  var eduProName: String,
  var eduQuaName: String
) : Parcelable {
  fun getResumeAbout(): String {
    val stringBuilder = StringBuilder()
    stringBuilder.append(if (sex == 0) "男" else "女").append(" | ")
    stringBuilder.append("${age}岁").append(" | ")
    if (!CheckUtils.isNullOrEmpty(quaName)) {
      stringBuilder.append(quaName).append(" | ")
    }
    if (!CheckUtils.isNullOrEmpty(wtName)) {
      stringBuilder.append(wtName).append(" | ")
    }
    if (!CheckUtils.isNullOrEmpty(cityName)) {
      stringBuilder.append(cityName).append(" | ")
    }
    return if (stringBuilder.isEmpty()) return CommonApiConstants.NO_TEXT else stringBuilder.substring(
      0,
      stringBuilder.lastIndexOf("|") - 1
    )
  }

  fun getWorkTime(): String {
    return "${expBeginTime}至${expEndTime}"
  }

  fun getEduTime(): String {
    return "${eduBeginTime}至${eduEndTime}"
  }

  fun getEduAbout(): String {
    return "$eduProName | $eduQuaName"
  }
}