package com.bxkj.common.widget.dialog.selection;

import android.content.Context;

import com.bxkj.common.R;
import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.PickerOptionsData;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog.selection
 * @Description:
 * @TODO: TODO
 * @date 2018/8/7
 */
public class SelectionAdapter extends SuperAdapter<PickerOptionsData> {

  private List<PickerOptionsData> mSelectedItems;
  private boolean mSingleSelect;

  public SelectionAdapter(Context context, List<PickerOptionsData> list, int layoutResId,
      boolean singleSelect) {
    super(context, layoutResId, list);
    mSelectedItems = new ArrayList<>();
    mSingleSelect = singleSelect;
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, PickerOptionsData item, int position) {
    holder.setText(R.id.tv_item, item.getName());
    holder.findViewById(R.id.iv_checked).setSelected(isSelectedItem(item));
    holder.itemView.setOnClickListener(view -> {
      if (mSelectedItems.contains(item)) {
        mSelectedItems.remove(item);
      } else {
        if (mSingleSelect && mSelectedItems.size() >= 1) {
          mSelectedItems.clear();
        }
        mSelectedItems.add(item);
      }
      notifyDataSetChanged();
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(view, position);
      }
    });
  }

  private boolean isSelectedItem(PickerOptionsData item) {
    for (int i = 0; i < mSelectedItems.size(); i++) {
      if (mSelectedItems.get(i).getName().equals(item.getName())) {
        return true;
      }
    }
    return false;
  }

  public void setSelectedItems(List<PickerOptionsData> selectedItems) {
    if (selectedItems != null) {
      mSelectedItems.clear();
      mSelectedItems.addAll(selectedItems);
      notifyDataSetChanged();
    }
  }

  @Override
  public List<PickerOptionsData> getSelectedItems() {
    return mSelectedItems;
  }
}
