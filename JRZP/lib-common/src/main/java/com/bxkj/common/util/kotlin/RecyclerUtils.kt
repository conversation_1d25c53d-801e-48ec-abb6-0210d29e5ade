@file:JvmName("RecyclerUtils")

package com.bxkj.common.util.kotlin

import androidx.recyclerview.widget.*

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/4/20
 * @version: V1.0
 */

fun RecyclerView.toPosition(position: Int) {
    when (val layoutManager = this.layoutManager) {
        is LinearLayoutManager -> {
            layoutManager.scrollToPositionWithOffset(position, 0)
        }

        is GridLayoutManager -> {
            layoutManager.scrollToPositionWithOffset(position, 0)
        }

        is StaggeredGridLayoutManager -> {
            layoutManager.scrollToPositionWithOffset(position, 0)
        }
    }
}

fun RecyclerView.toLast() {
    this.toPosition(this.getAvailableCount())
}

fun RecyclerView.findLastCompletelyVisiblePosition(): Int {
    val layoutManager = this.layoutManager
    return if (layoutManager != null && layoutManager is LinearLayoutManager) {
        layoutManager.findLastCompletelyVisibleItemPosition()
    } else {
        0
    }
}

fun RecyclerView.getAvailableCount(): Int {
    return this.adapter?.let {
        it.itemCount - 1
    } ?: 0
}

fun RecyclerView.isAtListEnd():Boolean{
    return this.findLastCompletelyVisiblePosition() == this.getAvailableCount()
}

fun RecyclerView.closeDefaultAnim() {
    val simpleItemAnimator = this.itemAnimator as SimpleItemAnimator?
    simpleItemAnimator?.let {
        it.addDuration = 0
        it.changeDuration = 0
        it.moveDuration = 0
        it.removeDuration = 0
        it.supportsChangeAnimations = false
    }
}