package com.bxkj.common.widget.filterpopup;

import android.content.Context;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.R;
import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;

/**
 * @date 2018/4/3
 */

public class FilterItemRecyclerViewBinder implements ItemViewBinder<FilterOptionsGroup> {

    private Context mContext;

    private FilterRecyclerItemAdapter mFilterRecyclerItemAdapter;

    public FilterItemRecyclerViewBinder(Context context) {
        mContext = context;
    }

    private FilterOptionsGroup data;

    @Override
    public void onBindViewHolder(SuperViewHolder holder, FilterOptionsGroup item, int position) {
        data = item;
        RecyclerView recyclerFilterItem = holder.findViewById(R.id.recycler_filter_recycler_item);
        if (item.isFlow) {
            mFilterRecyclerItemAdapter = new FilterRecyclerItemAdapter(mContext,
                    R.layout.common_recycler_filter_options_wrap_item, item.filterOptionList);
            FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(mContext);
            layoutManager.setFlexWrap(FlexWrap.WRAP);
            recyclerFilterItem.setLayoutManager(layoutManager);
        } else {
            mFilterRecyclerItemAdapter = new FilterRecyclerItemAdapter(mContext,
                    R.layout.common_recycler_filter_options_item, item.filterOptionList, item.openMultiSelect);
            recyclerFilterItem.setLayoutManager(new GridLayoutManager(mContext, item.spanCount));
        }
        recyclerFilterItem.setAdapter(mFilterRecyclerItemAdapter);

        mFilterRecyclerItemAdapter.setOnItemClickListener((view, position1) -> {
            if (OnFilterOptionClickListener != null) {
                OnFilterOptionClickListener.onFilterOptionSelected(item.tag, position1);
            }
        });
    }

    @Override
    public int getLayoutId() {
        return R.layout.common_recycler_filter_recycler_item;
    }

    private OnFilterOptionClickListener OnFilterOptionClickListener;

    public void setOnFilterOptionClickListener(OnFilterOptionClickListener onFilterOptionClickListener) {
        OnFilterOptionClickListener = onFilterOptionClickListener;
    }

    public interface OnFilterOptionClickListener {

        void onFilterOptionSelected(int type, int position);
    }

    public void resetPosition() {
        if (mFilterRecyclerItemAdapter != null) {
            mFilterRecyclerItemAdapter.resetPosition();
        }
    }

    public void confirmSelected() {
        if (data != null && data.onMultiSelectListener != null && mFilterRecyclerItemAdapter != null) {
            data.onMultiSelectListener.onMultiSelect(mFilterRecyclerItemAdapter.getSelectedItems());
        }
    }
}
