package com.bxkj.common.util;
//
//import android.content.Context;
//import android.graphics.SurfaceTexture;
//import android.os.Handler;
//import android.os.HandlerThread;
//import android.util.Log;
//import android.view.Surface;
//
//import com.google.android.exoplayer2.C;
//import com.google.android.exoplayer2.DefaultLoadControl;
//import com.google.android.exoplayer2.DefaultRenderersFactory;
//import com.google.android.exoplayer2.ExoPlayer;
//import com.google.android.exoplayer2.LoadControl;
//import com.google.android.exoplayer2.MediaItem;
//import com.google.android.exoplayer2.PlaybackException;
//import com.google.android.exoplayer2.PlaybackParameters;
//import com.google.android.exoplayer2.Player;
//import com.google.android.exoplayer2.RenderersFactory;
//import com.google.android.exoplayer2.source.MediaSource;
//import com.google.android.exoplayer2.source.ProgressiveMediaSource;
//import com.google.android.exoplayer2.source.hls.HlsMediaSource;
//import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
//import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
//import com.google.android.exoplayer2.trackselection.ExoTrackSelection;
//import com.google.android.exoplayer2.trackselection.TrackSelector;
//import com.google.android.exoplayer2.upstream.DataSource;
//import com.google.android.exoplayer2.upstream.DefaultAllocator;
//import com.google.android.exoplayer2.upstream.DefaultDataSource;
//import com.google.android.exoplayer2.video.VideoSize;
//
//import cn.jzvd.JZMediaInterface;
//import cn.jzvd.Jzvd;
//
///**
// * Created by MinhDV on 5/3/18.
// */
//public class JZMediaExo extends JZMediaInterface implements Player.Listener {
//    private ExoPlayer simpleExoPlayer;
//    private Runnable callback;
//    private String TAG = "JZMediaExo";
//    private long previousSeek = 0;
//
//    public JZMediaExo(Jzvd jzvd) {
//        super(jzvd);
//    }
//
//
//    @Override
//    public void start() {
//        simpleExoPlayer.setPlayWhenReady(true);
//    }
//
//    @Override
//    public void prepare() {
//        Log.e(TAG, "prepare");
//        Context context = jzvd.getContext();
//
//        release();
//        mMediaHandlerThread = new HandlerThread("JZVD");
//        mMediaHandlerThread.start();
//        mMediaHandler = new Handler(context.getMainLooper());//主线程还是非主线程，就在这里
//        handler = new Handler();
//        mMediaHandler.post(() -> {
//
//            ExoTrackSelection.Factory videoTrackSelectionFactory = new AdaptiveTrackSelection.Factory();
//            TrackSelector trackSelector = new DefaultTrackSelector(context, videoTrackSelectionFactory);
//
//            LoadControl loadControl = new DefaultLoadControl.Builder()
//                    .setAllocator(new DefaultAllocator(true, C.DEFAULT_BUFFER_SEGMENT_SIZE))
//                    .setBufferDurationsMs(360000, 600000, 1000, 5000)
//                    .setTargetBufferBytes(C.LENGTH_UNSET)
//                    .setPrioritizeTimeOverSizeThresholds(false)
//                    .build();
//            // 2. Create the player
//
//            RenderersFactory renderersFactory = new DefaultRenderersFactory(context);
//            simpleExoPlayer =
//                    new ExoPlayer.Builder(context, renderersFactory).setTrackSelector(trackSelector)
//                            .setLoadControl(loadControl)
//                            .build();
//            // Produces DataSource instances through which media data is loaded.
//            DataSource.Factory dataSourceFactory = new DefaultDataSource.Factory(context);
//
//            String currUrl = jzvd.jzDataSource.getCurrentUrl().toString();
//            MediaSource videoSource;
//            if (currUrl.contains(".m3u8")) {
//                videoSource =
//                        new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(currUrl));
//            } else {
//                videoSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(currUrl));
//            }
//            Log.e(TAG, "URL Link = " + currUrl);
//
//            simpleExoPlayer.addListener(this);
//            boolean isLoop = jzvd.jzDataSource.looping;
//            if (isLoop) {
//                simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
//            } else {
//                simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_OFF);
//            }
//            simpleExoPlayer.setMediaSource(videoSource);
//            simpleExoPlayer.prepare();
//            simpleExoPlayer.setPlayWhenReady(true);
//            callback = new onBufferingUpdate();
//
//            if (jzvd.textureView != null) {
//                SurfaceTexture surfaceTexture = jzvd.textureView.getSurfaceTexture();
//                if (surfaceTexture != null) {
//                    simpleExoPlayer.setVideoSurface(new Surface(surfaceTexture));
//                }
//            }
//        });
//
//    }
//
//
////    private void createExoPlayer() {
////        Context context = jzvd.getContext();
////
////        mMediaHandler = new Handler();//主线程还是非主线程，就在这里
////        handler = new Handler();
////        ExoTrackSelection.Factory videoTrackSelectionFactory = new AdaptiveTrackSelection.Factory();
////        TrackSelector trackSelector = new DefaultTrackSelector(context, videoTrackSelectionFactory);
////
////        LoadControl loadControl = new DefaultLoadControl.Builder()
////                .setAllocator(new DefaultAllocator(true, C.DEFAULT_BUFFER_SEGMENT_SIZE))
////                .setBufferDurationsMs(360000, 600000, 1000, 5000)
////                .setTargetBufferBytes(C.LENGTH_UNSET)
////                .setPrioritizeTimeOverSizeThresholds(false)
////                .build();
////        // 2. Create the player
////
////        RenderersFactory renderersFactory = new DefaultRenderersFactory(context);
////        simpleExoPlayer =
////                new ExoPlayer.Builder(context, renderersFactory).setTrackSelector(trackSelector)
////                        .setLoadControl(loadControl)
////                        .build();
////
////        simpleExoPlayer.addListener(this);
////
////        Boolean isLoop = jzvd.jzDataSource.looping;
////        if (isLoop) {
////            simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
////        } else {
////            simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_OFF);
////        }
////
////        callback = new onBufferingUpdate();
////
////        if (jzvd.textureView.getSurfaceTexture() != null) {
////            simpleExoPlayer.setVideoSurface(new Surface(jzvd.textureView.getSurfaceTexture()));
////        }
////    }
////
////    @Override
////    public void prepare() {
////        Log.e(TAG, "prepare");
////
////        if (simpleExoPlayer == null) {
////            createExoPlayer();
////        }
////
////        String currUrl = jzvd.jzDataSource.getCurrentUrl().toString();
////        Log.e(TAG, "URL Link = " + currUrl);
////
//////        DataSource.Factory dataSourceFactory = new DefaultDataSource.Factory(context);
//////        MediaSource videoSource;
//////        if (currUrl.contains(".m3u8")) {
//////            videoSource =
//////                    new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(currUrl));
//////        } else {
//////            videoSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(currUrl));
//////        }
////        simpleExoPlayer.setMediaItem(MediaItem.fromUri(currUrl));
//////        simpleExoPlayer.setMediaSource(videoSource);
////        simpleExoPlayer.prepare();
////        simpleExoPlayer.setPlayWhenReady(true);
////
////    }
//
//    @Override
//    public void onVideoSizeChanged(VideoSize videoSize) {
//        Player.Listener.super.onVideoSizeChanged(videoSize);
//        handler.post(() -> jzvd.onVideoSizeChanged(videoSize.width, videoSize.height));
//    }
//
//    @Override
//    public void onRenderedFirstFrame() {
//        Log.e(TAG, "onRenderedFirstFrame");
//    }
//
//    @Override
//    public void pause() {
//        simpleExoPlayer.setPlayWhenReady(false);
//    }
//
//    @Override
//    public boolean isPlaying() {
//        return simpleExoPlayer.getPlayWhenReady();
//    }
//
//    @Override
//    public void seekTo(long time) {
//        if (time != previousSeek) {
//            simpleExoPlayer.seekTo(time);
//            previousSeek = time;
//            jzvd.seekToInAdvance = time;
//        }
//    }
//
//    @Override
//    public void release() {
//        Log.d(TAG, "release: ");
////        if (simpleExoPlayer != null) {
////            simpleExoPlayer.release();
////        }
////        JZMediaInterface.SAVED_SURFACE = null;
//
//        if (mMediaHandler != null && mMediaHandlerThread != null && simpleExoPlayer != null) {//不知道有没有妖孽
//            HandlerThread tmpHandlerThread = mMediaHandlerThread;
//            ExoPlayer tmpMediaPlayer = simpleExoPlayer;
//            JZMediaInterface.SAVED_SURFACE = null;
//
//            mMediaHandler.post(() -> {
//                tmpMediaPlayer.release();//release就不能放到主线程里，界面会卡顿
//                tmpHandlerThread.quit();
//            });
//            simpleExoPlayer = null;
//        }
//    }
//
//    @Override
//    public long getCurrentPosition() {
//        if (simpleExoPlayer != null) {
//            return simpleExoPlayer.getCurrentPosition();
//        } else {
//            return 0;
//        }
//    }
//
//    @Override
//    public long getDuration() {
//        if (simpleExoPlayer != null) {
//            return simpleExoPlayer.getDuration();
//        } else {
//            return 0;
//        }
//    }
//
//    @Override
//    public void setVolume(float leftVolume, float rightVolume) {
//        simpleExoPlayer.setVolume(leftVolume);
//        simpleExoPlayer.setVolume(rightVolume);
//    }
//
//    @Override
//    public void setSpeed(float speed) {
//        PlaybackParameters playbackParameters = new PlaybackParameters(speed, 1.0F);
//        simpleExoPlayer.setPlaybackParameters(playbackParameters);
//    }
//
//    @Override
//    public void onEvents(Player player, Player.Events events) {
//        Player.Listener.super.onEvents(player, events);
//        if (events.contains(Player.EVENT_TIMELINE_CHANGED)) {
////            JZMediaPlayer.instance().mainThreadHandler.post(() -> {
////                if (reason == 0) {
////
////                    JzvdMgr.getCurrentJzvd().onInfo(reason, timeline.getPeriodCount());
////                }
////            });
//        }
//    }
//
//    @Override
//    public void onPlayWhenReadyChanged(boolean playWhenReady, int reason) {
//        Player.Listener.super.onPlayWhenReadyChanged(playWhenReady, reason);
//        if (playWhenReady) {
//            jzvd.onStatePlaying();
//        }
//    }
//
//    @Override
//    public void onPlaybackStateChanged(int playbackState) {
//        Player.Listener.super.onPlaybackStateChanged(playbackState);
//        Log.e(TAG, "onPlayerStateChanged" + playbackState);
//        handler.post(() -> {
//            switch (playbackState) {
//                case Player.STATE_IDLE: {
//                }
//                break;
//                case Player.STATE_BUFFERING: {
//                    handler.post(callback);
//                }
//                break;
//                case Player.STATE_READY: {
//
//                }
//                break;
//                case Player.STATE_ENDED: {
//                    jzvd.onCompletion();
//                }
//                break;
//            }
//        });
//    }
//
//    @Override
//    public void onPlayerError(PlaybackException error) {
//        Player.Listener.super.onPlayerError(error);
//        Log.e(TAG, "onPlayerError" + error.toString());
//        handler.post(() -> jzvd.onError(1000, 1000));
//    }
//
//    @Override
//    public void onPositionDiscontinuity(Player.PositionInfo oldPosition, Player.PositionInfo newPosition, int reason) {
//        Player.Listener.super.onPositionDiscontinuity(oldPosition, newPosition, reason);
//        handler.post(() -> jzvd.onSeekComplete());
//    }
//
//    @Override
//    public void setSurface(Surface surface) {
//        simpleExoPlayer.setVideoSurface(surface);
//    }
//
//    @Override
//    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
//        if (SAVED_SURFACE == null) {
//            SAVED_SURFACE = surface;
//            prepare();
//        } else {
//            jzvd.textureView.setSurfaceTexture(SAVED_SURFACE);
//        }
//    }
//
//    @Override
//    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
//
//    }
//
//    @Override
//    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
//        return false;
//    }
//
//    @Override
//    public void onSurfaceTextureUpdated(SurfaceTexture surface) {
//
//    }
//
//    private class onBufferingUpdate implements Runnable {
//        @Override
//        public void run() {
//            if (simpleExoPlayer != null) {
//                final int percent = simpleExoPlayer.getBufferedPercentage();
//                handler.post(() -> jzvd.setBufferProgress(percent));
//                if (percent < 100) {
//                    handler.postDelayed(callback, 300);
//                } else {
//                    handler.removeCallbacks(callback);
//                }
//            }
//        }
//    }
//}

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import com.bxkj.common.R;
import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.DefaultRenderersFactory;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.LoadControl;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.RenderersFactory;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.Timeline;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.source.hls.HlsMediaSource;
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelection;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.trackselection.TrackSelector;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultAllocator;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;
import com.google.android.exoplayer2.video.VideoListener;

import cn.jzvd.JZMediaInterface;
import cn.jzvd.Jzvd;

/**
 * Created by MinhDV on 5/3/18.
 */
public class JZMediaExo extends JZMediaInterface implements Player.EventListener, VideoListener {
    private SimpleExoPlayer simpleExoPlayer;
    private Runnable callback;
    private String TAG = "JZMediaExo";
    private long previousSeek = 0;

    public JZMediaExo(Jzvd jzvd) {
        super(jzvd);
    }

    @Override
    public void start() {
        simpleExoPlayer.setPlayWhenReady(true);
    }

    @Override
    public void prepare() {
        Log.e(TAG, "prepare");
        Context context = jzvd.getContext();

        release();
        mMediaHandlerThread = new HandlerThread("JZVD");
        mMediaHandlerThread.start();
        mMediaHandler = new Handler(mMediaHandlerThread.getLooper());//主线程还是非主线程，就在这里
        handler = new Handler();
        //mMediaHandler.post(() -> {
        //BandwidthMeter bandwidthMeter = new DefaultBandwidthMeter.Builder(context).build();
        //new DefaultBandwidthMeter(); old
        TrackSelection.Factory videoTrackSelectionFactory = new AdaptiveTrackSelection.Factory();
        //new AdaptiveTrackSelection.Factory(bandwidthMeter);
        TrackSelector trackSelector = new DefaultTrackSelector(context, videoTrackSelectionFactory);
        //new DefaultTrackSelector(videoTrackSelectionFactory); old

        LoadControl loadControl = new DefaultLoadControl.Builder()
                .setAllocator(new DefaultAllocator(true, C.DEFAULT_BUFFER_SEGMENT_SIZE))
                .setBufferDurationsMs(360000, 600000, 1000, 5000)
                .setTargetBufferBytes(C.LENGTH_UNSET)
                .setPrioritizeTimeOverSizeThresholds(false)
                .createDefaultLoadControl();
        //new DefaultLoadControl(new DefaultAllocator(true, C.DEFAULT_BUFFER_SEGMENT_SIZE), old
        //    360000, 600000, 1000, 5000,
        //    C.LENGTH_UNSET,
        //    false);

        // 2. Create the player

        RenderersFactory renderersFactory = new DefaultRenderersFactory(context);
        simpleExoPlayer =
                new SimpleExoPlayer.Builder(context, renderersFactory).setTrackSelector(trackSelector)
                        .setLoadControl(loadControl)
                        .build();
        //ExoPlayerFactory.newSimpleInstance(context, renderersFactory, trackSelector, loadControl); old
        // Produces DataSource instances through which media data is loaded.
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(context,
                Util.getUserAgent(context, context.getResources().getString(R.string.app_name)));

        String currUrl = jzvd.jzDataSource.getCurrentUrl().toString();
        MediaSource videoSource;
        if (currUrl.contains(".m3u8")) {
            videoSource =
                    new HlsMediaSource.Factory(dataSourceFactory).createMediaSource(Uri.parse(currUrl));
            //new HlsMediaSource.Factory(dataSourceFactory) //old
            //.createMediaSource(Uri.parse(currUrl), handler, null);
        } else {
            videoSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(
                    Uri.parse(currUrl));
            //new ExtractorMediaSource.Factory(dataSourceFactory) //old
            //.createMediaSource(Uri.parse(currUrl));
        }
        simpleExoPlayer.addVideoListener(this);

        Log.e(TAG, "URL Link = " + currUrl);

        simpleExoPlayer.addListener(this);
        Boolean isLoop = jzvd.jzDataSource.looping;
        if (isLoop) {
            simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
        } else {
            simpleExoPlayer.setRepeatMode(Player.REPEAT_MODE_OFF);
        }
        simpleExoPlayer.prepare(videoSource);
        simpleExoPlayer.setPlayWhenReady(true);
        callback = new onBufferingUpdate();

        if (jzvd.textureView.getSurfaceTexture() != null) {
            simpleExoPlayer.setVideoSurface(new Surface(jzvd.textureView.getSurfaceTexture()));
        }
        //});
    }

    @Override
    public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees,
                                   float pixelWidthHeightRatio) {
        handler.post(() -> jzvd.onVideoSizeChanged(width, height));
    }

    @Override
    public void onRenderedFirstFrame() {
        Log.e(TAG, "onRenderedFirstFrame");
    }

    @Override
    public void pause() {
        simpleExoPlayer.setPlayWhenReady(false);
    }

    @Override
    public boolean isPlaying() {
        return simpleExoPlayer.getPlayWhenReady();
    }

    @Override
    public void seekTo(long time) {
        if (time != previousSeek) {
            simpleExoPlayer.seekTo(time);
            previousSeek = time;
            jzvd.seekToInAdvance = time;
        }
    }

    @Override
    public void release() {
        if (mMediaHandler != null && mMediaHandlerThread != null && simpleExoPlayer != null) {//不知道有没有妖孽
            HandlerThread tmpHandlerThread = mMediaHandlerThread;
            SimpleExoPlayer tmpMediaPlayer = simpleExoPlayer;
            JZMediaInterface.SAVED_SURFACE = null;

            mMediaHandler.post(() -> {
                tmpMediaPlayer.release();//release就不能放到主线程里，界面会卡顿
                tmpHandlerThread.quit();
            });
            simpleExoPlayer = null;
        }
    }

    @Override
    public long getCurrentPosition() {
        if (simpleExoPlayer != null) {
            return simpleExoPlayer.getCurrentPosition();
        } else {
            return 0;
        }
    }

    @Override
    public long getDuration() {
        if (simpleExoPlayer != null) {
            return simpleExoPlayer.getDuration();
        } else {
            return 0;
        }
    }

    @Override
    public void setVolume(float leftVolume, float rightVolume) {
        simpleExoPlayer.setVolume(leftVolume);
        simpleExoPlayer.setVolume(rightVolume);
    }

    @Override
    public void setSpeed(float speed) {
        PlaybackParameters playbackParameters = new PlaybackParameters(speed, 1.0F);
        simpleExoPlayer.setPlaybackParameters(playbackParameters);
    }

    @Override
    public void onTimelineChanged(final Timeline timeline, Object manifest, final int reason) {
        Log.e(TAG, "onTimelineChanged");
        //        JZMediaPlayer.instance().mainThreadHandler.post(() -> {
        //                if (reason == 0) {
        //
        //                    JzvdMgr.getCurrentJzvd().onInfo(reason, timeline.getPeriodCount());
        //                }
        //        });
    }

    @Override
    public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {

    }

    @Override
    public void onLoadingChanged(boolean isLoading) {
        Log.e(TAG, "onLoadingChanged");
    }

    @Override
    public void onPlayerStateChanged(final boolean playWhenReady, final int playbackState) {
        Log.e(TAG, "onPlayerStateChanged" + playbackState + "/ready=" + String.valueOf(playWhenReady));
        handler.post(() -> {
            switch (playbackState) {
                case Player.STATE_IDLE: {
                }
                break;
                case Player.STATE_BUFFERING: {
                    handler.post(callback);
                }
                break;
                case Player.STATE_READY: {
                    if (playWhenReady) {
                        jzvd.onStatePlaying();
                    } else {
                    }
                }
                break;
                case Player.STATE_ENDED: {
                    jzvd.onCompletion();
                }
                break;
            }
        });
    }

    @Override
    public void onRepeatModeChanged(int repeatMode) {

    }

    @Override
    public void onShuffleModeEnabledChanged(boolean shuffleModeEnabled) {

    }

    @Override
    public void onPlayerError(ExoPlaybackException error) {
        Log.e(TAG, "onPlayerError" + error.toString());
        handler.post(() -> jzvd.onError(1000, 1000));
    }

    @Override
    public void onPositionDiscontinuity(int reason) {

    }

    @Override
    public void onPlaybackParametersChanged(PlaybackParameters playbackParameters) {

    }

    @Override
    public void onSeekProcessed() {
        handler.post(() -> jzvd.onSeekComplete());
    }

    @Override
    public void setSurface(Surface surface) {
        simpleExoPlayer.setVideoSurface(surface);
    }

    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
        if (SAVED_SURFACE == null) {
            SAVED_SURFACE = surface;
            prepare();
        } else {
            jzvd.textureView.setSurfaceTexture(SAVED_SURFACE);
        }
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        return false;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {

    }

    private class onBufferingUpdate implements Runnable {
        @Override
        public void run() {
            if (simpleExoPlayer != null) {
                final int percent = simpleExoPlayer.getBufferedPercentage();
                handler.post(() -> jzvd.setBufferProgress(percent));
                if (percent < 100) {
                    handler.postDelayed(callback, 300);
                } else {
                    handler.removeCallbacks(callback);
                }
            }
        }
    }
}

