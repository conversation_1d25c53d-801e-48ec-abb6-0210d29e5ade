package com.bxkj.common.widget.pagestatuslayout;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Guideline;
import androidx.fragment.app.Fragment;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.R;
import com.bxkj.common.util.CheckUtils;
import com.donkingliang.consecutivescroller.IConsecutiveScroller;
import com.ethanhua.skeleton.ViewSkeletonScreen;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.shop.ui.news.newssub
 * @Description: 请求状态占位view
 * @TODO: TODO
 * @date 2018/3/11
 */

public class PageStatusLayout extends FrameLayout implements IConsecutiveScroller {

    private Context mContext;
    public static final int NORMAL_TYPE = 0, EMPTY = 1, LOADING = 2, ERROR = 3;
    private int mViewType = NORMAL_TYPE;

    private View mLoadingView;
    private View mErrorView;
    private View mContentView;

    private ImageView ivError;
    private TextView tvError, tvTitle, btnError, tvLoading;
    private Guideline guideCenter;

    public PageStatusLayout(Context context) {
        this(context, null);
    }

    public PageStatusLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PageStatusLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init();
    }

    public static PageStatusLayout wrap(Activity activity) {
        return wrap(((ViewGroup) activity.findViewById(android.R.id.content)).getChildAt(0));
    }

    public static PageStatusLayout wrap(Fragment fragment) {
        return wrap(fragment.getView());
    }

    public static PageStatusLayout wrap(View view) {
        if (view == null) {
            throw new NullPointerException("EmptyLayout content view can not be null");
        }

        ViewGroup parent = (ViewGroup) view.getParent();
        ViewGroup.LayoutParams lp = view.getLayoutParams();
        //获取目标的view在父容器中的下标
        int index = parent.indexOfChild(view);
        //先将其清除
        parent.removeView(view);

        //创建一个emptylayout
        PageStatusLayout layout = new PageStatusLayout(view.getContext());
        //添加目标view在父容器中的位置
        parent.addView(layout, index, lp);
        //然后在emptylayout中添加目标view，等于在中间加了一层
        layout.addView(view,
                new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        return layout;
    }

    private void init() {
        mLoadingView = LayoutInflater.from(mContext).inflate(R.layout.view_loading_view, null);
        mErrorView = LayoutInflater.from(mContext).inflate(R.layout.view_error_layout, null);

        LayoutParams lp =
                new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        mLoadingView.setLayoutParams(lp);
        mErrorView.setLayoutParams(lp);

        addView(mLoadingView);
        addView(mErrorView);

        mLoadingView.setVisibility(GONE);
        mErrorView.setVisibility(GONE);

        initView();
    }

    private void initView() {
        ivError = mErrorView.findViewById(R.id.iv_error_img);
        tvError = mErrorView.findViewById(R.id.tv_error_content);
        btnError = mErrorView.findViewById(R.id.tv_error_next);
        tvTitle = mErrorView.findViewById(R.id.tv_title);
        guideCenter = mErrorView.findViewById(R.id.gl_center_line);

        tvLoading = mLoadingView.findViewById(R.id.tv_loading);
    }

    public void show(PageStatusConfig pageStatusConfig) {
        if (pageStatusConfig == null) {
            mViewType = NORMAL_TYPE;
        } else if (pageStatusConfig instanceof LoadingConfig) {
            mViewType = LOADING;
            showLoading((LoadingConfig) pageStatusConfig);
        } else if (pageStatusConfig instanceof EmptyConfig) {
            mViewType = EMPTY;
            showEmpty((EmptyConfig) pageStatusConfig);
        } else {
            mViewType = ERROR;
            showError((ErrorConfig) pageStatusConfig);
        }
        setShowOrHide();
    }

    private void showLoading(LoadingConfig loadingConfig) {
        if (!CheckUtils.isNullOrEmpty(loadingConfig.getLoadingText())) {
            tvLoading.setVisibility(VISIBLE);
            tvLoading.setText(loadingConfig.getLoadingText());
        }
    }

    private void showEmpty(EmptyConfig emptyConfig) {
        guideCenter.setGuidelinePercent(emptyConfig.getGuidePercent());
        if (emptyConfig.getHeight() != 0) {
            LayoutParams lp =
                    new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, emptyConfig.getHeight());
            mErrorView.setLayoutParams(lp);
        }
        tvTitle.setVisibility(GONE);
        tvError.setTextSize(16);
        ivError.setImageResource(emptyConfig.getImg());
        if (emptyConfig.getTextId() != 0) {
            tvError.setText(emptyConfig.getTextId());
        } else {
            tvError.setText(emptyConfig.getText());
        }
        btnError.setVisibility(GONE);
    }

    private void showError(ErrorConfig errorConfig) {
        if (errorConfig.getHeight() != 0) {
            LayoutParams lp =
                    new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, errorConfig.getHeight());
            mErrorView.setLayoutParams(lp);
        }
        if (errorConfig.getImg() != 0) {
            ivError.setImageResource(errorConfig.getImg());
        } else {
            ivError.setVisibility(GONE);
        }
        if (errorConfig.getTitleId() != 0) {
            tvError.setTextSize(16);
            tvTitle.setVisibility(VISIBLE);
            tvTitle.setText(errorConfig.getTitleId());
        } else {
            tvError.setTextSize(16);
            tvTitle.setVisibility(GONE);
        }
        if (errorConfig.getTextId() != 0) {
            tvError.setText(errorConfig.getTextId());
        } else {
            tvError.setText(errorConfig.getText());
        }
        btnError.setVisibility(VISIBLE);
        if (errorConfig.getBtnTextId() != 0) {
            btnError.setText(errorConfig.getBtnTextId());
        } else {
            btnError.setText(errorConfig.getBtnText());
        }
        btnError.setOnClickListener(view -> {
            if (errorConfig.getOnButtonClickListener() != null) {
                errorConfig.getOnButtonClickListener().onBottomClick();
            }
        });
    }

    public void hidden() {
        setHidden(true);
    }

    public void setHidden(boolean hidden) {
        if (hidden && mViewType != NORMAL_TYPE) {
            mViewType = NORMAL_TYPE;
            setShowOrHide();
        }
    }

    private void setShowOrHide() {
        getContentView();
        mLoadingView.setVisibility(mViewType == LOADING ? VISIBLE : GONE);
        mErrorView.setVisibility(mViewType == ERROR || mViewType == EMPTY ? VISIBLE : GONE);
        mContentView.setVisibility(mViewType != NORMAL_TYPE ? GONE : VISIBLE);
    }

    public void updateLayout(View view) {
        if (view != null) {
            setLayoutParams(view.getLayoutParams());
        }
    }

    private void getContentView() {
        if (mContentView == null) {
            //顶层view为wrapview
            mContentView = getChildAt(getChildCount() - 1);
        }
    }

    @Override
    public View getCurrentScrollerView() {
        return mContentView;
    }

    @Override
    public List<View> getScrolledViews() {
        return Collections.singletonList(mContentView);
    }
}
