package com.bxkj.common.widget.pagestatuslayout;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.newemptylayout
 * @Description:
 * @TODO: TODO
 * @date 2018/10/24
 */
public class LoadingConfig extends PageStatusConfig {

  private String loadingText;

  String getLoadingText() {
    return loadingText;
  }

  public LoadingConfig setLoadingText(String loadingText) {
    this.loadingText = loadingText;
    return this;
  }
}
