package com.bxkj.common.util;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.router.RouterNavigation;
import com.bxkj.common.util.router.Router;

/**
 * @date 2019/5/30
 */
public class ActivityRouterUtils {

    public static void toAgreementActivity(){
        Router.getInstance().to(RouterNavigation.WebActivity.URL)
                .withString(RouterNavigation.WebActivity.EXTRA_PAGE_TITLE, "服务协议")
                .withString(RouterNavigation.WebActivity.EXTRA_URL, CommonApiConstants.AGREEMENT_URL)
                .start();
    }

    public static void toPrivacyActivity(){
        Router.getInstance().to(RouterNavigation.WebActivity.URL)
                .withString(RouterNavigation.WebActivity.EXTRA_URL, CommonApiConstants.PRIVACY_URL)
                .start();
    }
}
