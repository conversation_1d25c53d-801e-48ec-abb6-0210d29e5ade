package com.bxkj.common.widget.indexbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.bxkj.common.R;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ZPSideIndexBar extends View {

  private static final String[] DEFAULT_INDEX_ITEMS = {"A", "B", "C", "D", "E", "F", "G", "H",
      "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "W", "X", "Y", "Z"};

  private List<String> mIndexItems;
  private float mItemHeight; //每个index的高度
  private int mTextSize;      //sp
  private int mTextColor;
  private int mTextTouchedColor;
  private int mCurrentIndex = -1;

  private Paint mPaint;
  private Paint mTouchedPaint;

  private int mWidth;
  private int mHeight;
  private float mTopMargin;   //居中绘制，文字绘制起点和控件顶部的间隔

  private TextView mOverlayTextView;
  private OnIndexTouchedChangedListener mOnIndexChangedListener;

  public ZPSideIndexBar(Context context) {
    this(context, null);
  }

  public ZPSideIndexBar(Context context, @Nullable AttributeSet attrs) {
    this(context, attrs, 0);
  }

  public ZPSideIndexBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
    init(context, attrs);
  }

  private void init(Context context, AttributeSet attrs) {
    mIndexItems = new ArrayList<>();
    mIndexItems.addAll(Arrays.asList(DEFAULT_INDEX_ITEMS));

    TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ZPSideIndexBar);
    mTextSize = typedArray.getDimensionPixelOffset(R.styleable.ZPSideIndexBar_indexTextSize, 11);
    mTextColor = typedArray
        .getColor(R.styleable.ZPSideIndexBar_indexTextNormalColor, Color.parseColor("#fe6600"));
    mTextTouchedColor = typedArray
        .getColor(R.styleable.ZPSideIndexBar_indexTextSelectColor, Color.parseColor("#333333"));
    typedArray.recycle();

    mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    mPaint.setTextSize(mTextSize);
    mPaint.setColor(mTextColor);

    mTouchedPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    mTouchedPaint.setTextSize(mTextSize);
    mTouchedPaint.setColor(mTextTouchedColor);
  }

  @Override
  protected void onDraw(Canvas canvas) {
    super.onDraw(canvas);
    String index;
    for (int i = 0; i < mIndexItems.size(); i++) {
      index = mIndexItems.get(i);
      Paint.FontMetrics fm = mPaint.getFontMetrics();
      canvas.drawText(index,
          (mWidth - mPaint.measureText(index)) / 2,
          mItemHeight / 2 + (fm.bottom - fm.top) / 2 - fm.bottom + mItemHeight * i + mTopMargin,
          i == mCurrentIndex ? mTouchedPaint : mPaint);
    }
  }

  @Override
  protected void onSizeChanged(int w, int h, int oldw, int oldh) {
    super.onSizeChanged(w, h, oldw, oldh);
    mWidth = getWidth();
    mHeight = Math.max(h, oldh);
    mItemHeight = mHeight / mIndexItems.size();
    mTopMargin = (mHeight - mItemHeight * mIndexItems.size()) / 2;
  }

  @Override
  public boolean performClick() {
    return super.performClick();
  }

  @Override
  public boolean onTouchEvent(MotionEvent event) {
    performClick();
    switch (event.getAction()) {
      case MotionEvent.ACTION_DOWN:
      case MotionEvent.ACTION_MOVE:
        getParent().requestDisallowInterceptTouchEvent(true);
        float y = event.getY();
        int indexSize = mIndexItems.size();
        int touchedIndex = (int) (y / mItemHeight);
        if (touchedIndex < 0) {
          touchedIndex = 0;
        } else if (touchedIndex >= indexSize) {
          touchedIndex = indexSize - 1;
        }
        if (mOnIndexChangedListener != null && touchedIndex >= 0 && touchedIndex < indexSize) {
          if (touchedIndex != mCurrentIndex) {
            mCurrentIndex = touchedIndex;
            if (mOverlayTextView != null) {
              mOverlayTextView.setVisibility(VISIBLE);
              mOverlayTextView.setText(mIndexItems.get(touchedIndex));
            }
            mOnIndexChangedListener.onIndexChanged(mIndexItems.get(touchedIndex), touchedIndex);
            invalidate();
          }
        }
        break;
      case MotionEvent.ACTION_UP:
      case MotionEvent.ACTION_CANCEL:
        getParent().requestDisallowInterceptTouchEvent(false);
        mCurrentIndex = -1;
        if (mOverlayTextView != null) {
          mOverlayTextView.setVisibility(GONE);
        }
        invalidate();
        break;
    }
    return true;
  }

  public ZPSideIndexBar setOverlayTextView(TextView overlay) {
    this.mOverlayTextView = overlay;
    return this;
  }

  public ZPSideIndexBar setOnIndexChangedListener(OnIndexTouchedChangedListener listener) {
    this.mOnIndexChangedListener = listener;
    return this;
  }

  public interface OnIndexTouchedChangedListener {

    void onIndexChanged(String index, int position);
  }
}
