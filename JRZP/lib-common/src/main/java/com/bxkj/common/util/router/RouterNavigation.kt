package com.bxkj.common.util.router

import android.app.Activity

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.constants
 * @Description: 路由路径
 * @TODO: TODO
 * @date 2018/4/1
 */
object RouterNavigation {

  const val TO_ACTIVITY_TYPE = "toActivityType"

  //****************************Account**************************//
  const val ACCOUNT_URL = "/account"

  //登录
  object LoginActivity {

    const val LOGIN_FROM_CREATE_RESUME = "login_from_create_resume"
    const val EXTRA_BACK_AFTER_LOGIN = "BACK_AFTER_LOGIN"
    const val LOGIN_PHONE = "login_phone"
    const val URL = "$ACCOUNT_URL/login"
    const val EXTRA_IS_WECHAT_LOGIN_SUCCESS = "WECHAT_LOGIN_SUCCESS"
  }

  //选择期望职位
  object JobSelectActivity {

    const val EXPECT_JOBS = "checkedJobs"
    const val MAX_SELECT_COUNT = "maxSelectCount"
    const val CREATE = 0x01
    const val UPDATE = 0x02
    const val RESULT = 0x03
    const val RESULT_DATA = "resultData"
    const val RESULT_FIRST_CLASS_ID = "resultFirstClassId"
    const val RESULT_FIRST_CLASS_NAME = "resultSecondClassName"
    const val RESULT_UPDATE_SUCCESS = Activity.RESULT_FIRST_USER + 1
    const val URL = "$ACCOUNT_URL/jobselection"
  }

  //修改密码
  object UpdatePasswordActivity {

    const val URL = "$ACCOUNT_URL/updatepassword"
    const val RETRIEVE_PASSWORD_TYPE = 0x01
    const val UPDATE_PASSWORD_TYPE = 0x02
    const val MOBILE = "mobile"
  }

  //****************************个人**************************//
  private const val PERSONAL_URL = "/personal"

  object PersonalMainActivity {

    const val URL = "$PERSONAL_URL/main"
  }

  //用户基本信息
  object PersonalBasicInfoActivity {

    const val URL = "$PERSONAL_URL/personalbasicinfo"
    const val USER_ID = "userId"
    const val IS_NEED_BACK = "isNeedBack"
  }

  object CreateResumeStepTwoActivity {

    const val URL = "$PERSONAL_URL/createresumesteptwo"
  }

  object BindMobileActivity {

    const val URL = "$PERSONAL_URL/bindmobile"
    const val RESULT_BIND_SUCCESS = Activity.RESULT_FIRST_USER + 1
    const val RESULT_BIND_CANCEL = Activity.RESULT_FIRST_USER + 2
  }

  object UserBasicInfoActivity {

    const val URL = "$PERSONAL_URL/userbasicinfo"
  }

  object WebActivity {

    const val EXTRA_PAGE_TITLE = "PAGE_TITLE"
    const val EXTRA_URL = "URL"
    const val EXTRA_FULL_SCREEN = "FULL_SCREEN"
    const val URL = "$PERSONAL_URL/web"
  }
}