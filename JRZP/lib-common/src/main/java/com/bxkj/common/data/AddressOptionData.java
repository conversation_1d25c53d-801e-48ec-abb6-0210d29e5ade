package com.bxkj.common.data;

import androidx.annotation.NonNull;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;

import com.bxkj.common.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 地区item
 * @TODO: TODO
 * @date 2018/4/3
 */
public class AddressOptionData extends BaseObservable {

    /**
     * id : 1 pid : 0 type : 0 name : 上海
     */

    private int id;
    private int pid;
    private String pName;
    private int type;
    private String name;
    private boolean selected;

    @Bindable
    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
        notifyPropertyChanged(BR.selected);
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public AddressOptionData() {
    }

    public AddressOptionData(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public AddressOptionData(int id, int type, String name) {
        this.id = id;
        this.type = type;
        this.name = name;
    }

    public AddressOptionData(int id, int pid, String pName, int type, String name) {
        this.id = id;
        this.pid = pid;
        this.pName = pName;
        this.type = type;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public String getPName() {
        return pName;
    }

    public void setPName(String pName) {
        this.pName = pName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static class DiffCallBack extends DiffUtil.ItemCallback<AddressOptionData> {

        @Override
        public boolean areItemsTheSame(@NonNull AddressOptionData oldItem, @NonNull
                AddressOptionData newItem) {
            return oldItem.id == newItem.id;
        }

        @Override
        public boolean areContentsTheSame(@NonNull AddressOptionData oldItem, @NonNull
                AddressOptionData newItem) {
            return oldItem.name.equals(newItem.name);
        }
    }
}
