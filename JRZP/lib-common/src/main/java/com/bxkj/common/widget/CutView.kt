package com.bxkj.common.widget

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.bxkj.common.util.DensityUtils

/**
 * @Project: gzgk
 * @Package com.bxkj.commonlib.widget
 * @Description:
 * <AUTHOR>
 * @date 2020/2/20
 * @version V1.0
 */
class CutView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyle: Int = 0) : View(context, attrs, defStyle) {

    private val mPaint = Paint()
    private var mStrokeWidth: Float = DensityUtils.dp2px(getContext(), 2f).toFloat()

    init {
        mPaint.color = Color.parseColor("#FE6600")
        mPaint.isAntiAlias = true
        mPaint.style = Paint.Style.STROKE
        mPaint.strokeCap = Paint.Cap.ROUND
    }

    fun setBorderWidth(width: Float) {
        mStrokeWidth = width
        mPaint.strokeWidth = mStrokeWidth
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val rect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        rect.inset(mStrokeWidth / 2, mStrokeWidth / 2)
        canvas?.drawRoundRect(rect, 5f, 5f, mPaint)
    }

}
