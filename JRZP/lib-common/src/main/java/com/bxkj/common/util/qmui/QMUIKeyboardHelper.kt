/*
 * <PERSON><PERSON> is pleased to support the open source community by making QMUI_Android available.
 *
 * Copyright (C) 2017-2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the MIT License (the "License"); you may not use this file except in
 * compliance with the License. You may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is
 * distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.bxkj.common.util.qmui

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.Window
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.core.view.WindowInsetsCompat.Type
import com.bxkj.common.util.DensityUtils

/**
 * <AUTHOR>
 * @date 2016-11-07
 *
 *
 * https://github.com/yshrsmz/KeyboardVisibilityEvent/blob/master/keyboardvisibilityevent/src/main/java/net/yslibrary/android/keyboardvisibilityevent/KeyboardVisibilityEvent.java
 */
object QMUIKeyboardHelper {
  /**
   * 显示软键盘的延迟时间
   */
  const val SHOW_KEYBOARD_DELAY_TIME: Int = 200
  private const val TAG = "QMUIKeyboardHelper"
  const val KEYBOARD_VISIBLE_THRESHOLD_DP: Int = 100

  const val SELF = 0
  const val TOP = 1
  const val BOTTOM = 2

  fun showKeyboard(editText: EditText?, delay: Boolean) {
    showKeyboard(editText, if (delay) SHOW_KEYBOARD_DELAY_TIME else 0)
  }

  /**
   * 针对给定的editText显示软键盘（editText会先获得焦点）. 可以和[.hideKeyboard]
   * 搭配使用，进行键盘的显示隐藏控制。
   */
  fun showKeyboard(editText: EditText?, delay: Int) {
    editText?.let {
      if (!it.requestFocus()) {
        Log.w(TAG, "showSoftInput() can not get focus")
        return
      }
      val imm =
        it.context.applicationContext
          .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
      if (delay > 0) {
        it.postDelayed({
          imm.showSoftInput(it, InputMethodManager.SHOW_IMPLICIT)
        }, delay.toLong())
      } else {
        imm.showSoftInput(it, InputMethodManager.SHOW_IMPLICIT)
      }
    }
  }

  /**
   * 隐藏软键盘 可以和[.showKeyboard]搭配使用，进行键盘的显示隐藏控制。
   *
   * @param view 当前页面上任意一个可用的view
   */
  fun hideKeyboard(view: View?): Boolean {
    view?.let {
      if (VERSION.SDK_INT >= VERSION_CODES.R) {
        it.windowInsetsController?.hide(Type.ime())
        return true
      } else {
        val inputManager =
          it.context.applicationContext
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // 即使当前焦点不在editText，也是可以隐藏的。
        return inputManager.hideSoftInputFromWindow(
          it.windowToken,
          InputMethodManager.HIDE_NOT_ALWAYS
        )
      }
    }
    return false
  }

  /**
   * Set keyboard visibility change event listener.
   *
   * @param activity Activity
   * @param listener KeyboardVisibilityEventListener
   */
  @Suppress("deprecation") fun setVisibilityEventListener(
    activity: Activity?,
    listener: KeyboardVisibilityEventListener?
  ) {
    if (activity == null) {
      throw NullPointerException("Parameter:activity must not be null")
    }

    if (listener == null) {
      throw NullPointerException("Parameter:listener must not be null")
    }

    val activityRoot =
      (activity.findViewById<View>(Window.ID_ANDROID_CONTENT) as ViewGroup).getChildAt(0)

    val layoutListener: OnGlobalLayoutListener =
      object : OnGlobalLayoutListener {
        private val r = Rect()

        private val visibleThreshold = Math.round(
          DensityUtils.dp2px(activity, KEYBOARD_VISIBLE_THRESHOLD_DP.toFloat()).toFloat()
        )

        private var wasOpened = false

        override fun onGlobalLayout() {
          activityRoot.getWindowVisibleDisplayFrame(r)

          val heightDiff = activityRoot.rootView.height - r.height()

          val isOpen = heightDiff > visibleThreshold

          if (isOpen == wasOpened) {
            // keyboard state has not changed
            return
          }

          wasOpened = isOpen

          val removeListener = listener.onVisibilityChanged(isOpen, heightDiff)
          if (removeListener) {
            if (VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {
              activityRoot.viewTreeObserver
                .removeOnGlobalLayoutListener(this)
            } else {
              activityRoot.viewTreeObserver
                .removeGlobalOnLayoutListener(this)
            }
          }
        }
      }
    activityRoot.viewTreeObserver.addOnGlobalLayoutListener(layoutListener)
    activity.application
      .registerActivityLifecycleCallbacks(object : QMUIActivityLifecycleCallbacks(activity) {
        override fun onTargetActivityDestroyed() {
          if (VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {
            activityRoot.viewTreeObserver
              .removeOnGlobalLayoutListener(layoutListener)
          } else {
            activityRoot.viewTreeObserver
              .removeGlobalOnLayoutListener(layoutListener)
          }
        }
      })
  }

  /**
   * Determine if keyboard is visible
   *
   * @param activity Activity
   * @return Whether keyboard is visible or not
   */
  fun isKeyboardVisible(activity: Activity): Boolean {
    val r = Rect()

    val activityRoot =
      (activity.findViewById<View>(Window.ID_ANDROID_CONTENT) as ViewGroup).getChildAt(0)
    val visibleThreshold =
      Math.round(DensityUtils.dp2px(activity, KEYBOARD_VISIBLE_THRESHOLD_DP.toFloat()).toFloat())

    activityRoot.getWindowVisibleDisplayFrame(r)

    val heightDiff = activityRoot.rootView.height - r.height()

    return heightDiff > visibleThreshold
  }

  fun setClickEmptyListener(
    event: MotionEvent,
    excuseView: View?,
    direction: Int = SELF,
    listener: OnClickListener,
  ) {
    if (event.action == MotionEvent.ACTION_DOWN) {
      if (excuseView != null) {
        val rect = Rect()
        excuseView.getGlobalVisibleRect(rect)
        if (direction == TOP) {
          if (event.y.toInt() < rect.top) {
            listener.onClick(excuseView)
          }
        } else {
          if (!rect.contains((event.x.toInt()), (event.y.toInt()))) {
            listener.onClick(excuseView)
          }
        }
      }
    }
  }

  interface KeyboardVisibilityEventListener {
    /**
     * @return to remove global listener or not
     */
    fun onVisibilityChanged(isOpen: Boolean, heightDiff: Int): Boolean
  }
}
