package com.bxkj.common.adapter.indicator

import android.content.Context
import androidx.core.content.ContextCompat
import com.bxkj.common.R
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.widget.indicator.ScalePagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.fragment.myuniversity
 * @Description:
 * <AUTHOR>
 * @date 2019/11/26
 * @version V1.0
 */
open class ScaleIndicatorAdapter constructor(titles: Array<String?>) : MagicIndicatorAdapter(titles) {

    override fun getTitleView(context: Context, index: Int): IPagerTitleView {
        val pageTitleView =
            ScalePagerTitleView(context)
        pageTitleView.textSize = 17f
        pageTitleView.normalColor = ContextCompat.getColor(context, R.color.common_black)
        pageTitleView.selectedColor = ContextCompat.getColor(context, R.color.cl_ff7405)
        pageTitleView.text = getTitles()[index]
        pageTitleView.setOnClickListener { view ->
            getOnTabClickListener()?.onTabClicked(view, index)
        }
        return pageTitleView
    }

    override fun getIndicator(context: Context): IPagerIndicator {
        val indicator = LinePagerIndicator(context)
        indicator.mode = LinePagerIndicator.MODE_EXACTLY
        indicator.roundRadius = DensityUtils.dp2px(context, 1f).toFloat()
        indicator.lineHeight = DensityUtils.dp2px(context, 2f).toFloat()
        indicator.lineWidth = DensityUtils.dp2px(context, 20f).toFloat()
        indicator.setColors(ContextCompat.getColor(context, R.color.cl_ff7405))
        return indicator
    }
}