plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
}

android {

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()

    renderscriptTargetApi = 14
    renderscriptSupportModeEnabled = true
    multiDexEnabled = true

    ndk {
      abiFilters "armeabi"
    }
  }

  buildTypes {
    release {
      minifyEnabled = false
      proguardFiles.add(file('proguard-rules.pro'))
      signingConfig signingConfigs.debug
    }
  }
  signingConfigs {
    debug {
      storeFile file('debug.keystore')
      storePassword "android"
      keyAlias "androiddebugkey"
      keyPassword "android"
    }
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }
  packagingOptions {
    jniLibs {
      keepDebugSymbols +=
          ['*/armeabi/libYTCommon.so', '*/armeabi-v7a/libYTCommon.so', '*/x86/libYTCommon.so', '*/arm64-v8a/libYTCommon.so']
      pickFirsts += ['**/libc++_shared.so']
    }
  }

  namespace 'com.tencent.qcloud.ugckit'
}

dependencies {
  implementation fileTree(include: ['*.jar'], dir: 'libs')
  implementation fileTree(include: ['*.jar'], dir: 'src/main/jniLibs')
  implementation libs.appcompat
  implementation libs.recyclerview
  implementation 'com.google.code.gson:gson:2.8.6'
  implementation libs.glide
  implementation libs.multidex
  implementation libs.circleimageview
  implementation 'com.github.castorflex.verticalviewpager:library:19.0.1'
  implementation 'org.greenrobot:eventbus:3.0.0'
  implementation 'androidx.annotation:annotation:1.5.0'
  implementation libs.carshreport
  //  implementation 'com.tencent.bugly:nativecrashreport:3.9.2'
  implementation libs.okhttp
  implementation 'com.squareup.okio:okio:2.6.0'
  implementation libs.okhttp.interceptor.logging
  implementation('com.tencent.qcloud:cosxml:5.4.20') {
    exclude group: 'com.tencent.qcloud', module: 'mtaUtils' //关闭 mta 上报功能}
  }
  implementation project(':ugc:beauty')

  api 'com.tencent.imsdk:imsdk-plus:7.7.5294'

  //腾讯短视频sdk
  api 'com.tencent.liteav:LiteAVSDK_Professional:10.7.0.13053'
}