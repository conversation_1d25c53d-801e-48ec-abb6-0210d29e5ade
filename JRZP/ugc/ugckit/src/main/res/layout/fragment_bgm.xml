<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_below="@+id/bgm_rl_chose"
        android:layout_centerInParent="true"
        android:layout_marginEnd="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginStart="20dp"
        android:orientation="vertical"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="#515151"
                android:padding="5dp">


                <ImageView
                    android:id="@+id/motion_iv_delete"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/motion_iv_delete"
                    android:text="@string/fragment_bgm_cancel"
                    android:textColor="#dbdbdb"
                    android:textSize="12sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginBottom="5dp"
            android:layout_marginTop="5dp">


            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:src="@drawable/bgm_wave_voice" />

            <com.tencent.qcloud.ugckit.component.slider.RangeSlider
                android:id="@+id/bgm_range_slider"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:leftThumbDrawable="@drawable/ic_progress_left"
                app:leftThumbIndex="0"
                app:lineColor="@color/colorAccent"
                app:lineHeight="3dp"
                app:rightThumbDrawable="@drawable/ic_progress_right"
                app:thumbWidth="19dp"
                app:tickCount="100" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.tencent.qcloud.ugckit.component.seekbar.TCReversalSeekBar
                android:layout_width="0dp"
                android:layout_height="15dp"
                android:layout_marginEnd="12dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginStart="12dp"
                android:layout_weight="1"
                app:rs_backgroundColor="#BBBBBB"
                app:rs_pointerBackground="@drawable/icon_seek_bar_cursor"
                app:rs_progress="0.5"
                app:rs_progressColor="@color/colorAccent" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/bgm_rl_chose"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#0d0d0d"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/music_empty"
                android:visibility="gone" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:visibility="gone">

            <ProgressBar
                style="@style/Widget.AppCompat.ProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true" />
        </RelativeLayout>
    </RelativeLayout>

    <com.tencent.qcloud.ugckit.module.effect.bgm.view.TCEditMusicPannel
        android:id="@+id/tc_record_bgm_pannel"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true" />

</RelativeLayout>
