<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/white"
              android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="18sp"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/colorAccent"/>

    <TextView
        android:id="@+id/tv_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="16sp"/>

    <Button
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorAccent"
        android:padding="10dp"
        android:text="@string/dialog_ugc_tip_already_known"
        android:textColor="@color/white"
        android:textSize="14sp"
        />
</LinearLayout>