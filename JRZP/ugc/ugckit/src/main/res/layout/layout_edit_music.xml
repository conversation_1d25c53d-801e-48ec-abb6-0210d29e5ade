<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#000000"
    android:gravity="bottom">

    <RelativeLayout
        android:id="@+id/rl_bgm_operation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/layout_bottom"
        android:layout_alignParentRight="true">

        <ImageView
            android:id="@+id/iv_music_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentLeft="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="10dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ugc_record_music" />

        <TextView
            android:id="@+id/tx_music_name"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@+id/btn_bgm_replace"
            android:layout_toRightOf="@id/iv_music_icon"
            android:ellipsize="marquee"
            android:gravity="left|center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/white" />

        <ImageView
            android:id="@+id/btn_bgm_replace"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@+id/btn_bgm_delete"
            android:src="@drawable/ic_music_change_selector" />

        <ImageView
            android:id="@+id/btn_bgm_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_music_del_selector" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="10dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_mic_volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/rl_bgm_operation"
            android:layout_marginLeft="15dp"
            android:text="@string/mic_volume"
            android:textColor="@color/white" />

        <SeekBar
            android:id="@+id/seekbar_mic_volume"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/seekBar_voice_volume"
            android:layout_below="@id/tv_mic_volume"
            android:layout_marginTop="5dp"
            android:indeterminate="false"
            android:max="100"
            android:maxHeight="4dp"
            android:minHeight="4dp"
            android:progress="50"
            android:progressDrawable="@drawable/seekbar_progress_drawable"
            android:thumb="@drawable/play_seekbar_icon" />

        <TextView
            android:id="@+id/tv_bgm_volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/seekbar_mic_volume"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:text="@string/bgm_volume"
            android:textColor="@color/white" />

        <SeekBar
            android:id="@+id/seekbar_bgm_volume"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/seekBar_voice_volume"
            android:layout_below="@id/tv_bgm_volume"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@+id/textView_bgm_volume"
            android:indeterminate="false"
            android:max="100"
            android:maxHeight="4dp"
            android:minHeight="4dp"
            android:progress="50"
            android:progressDrawable="@drawable/seekbar_progress_drawable"
            android:thumb="@drawable/play_seekbar_icon" />

        <TextView
            android:id="@+id/tv_bgm_start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/seekbar_bgm_volume"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:textColor="@color/white" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_below="@id/tv_bgm_start_time"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:src="@drawable/bgm_wave_voice" />

            <com.tencent.qcloud.ugckit.component.slider.RangeSlider
                android:id="@+id/bgm_range_slider"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:leftThumbDrawable="@drawable/ic_music_left"
                app:leftThumbIndex="0"
                app:lineColor="#FF584C"
                app:lineHeight="3dp"
                app:rightThumbDrawable="@drawable/ic_music_right"
                app:thumbWidth="19dp"
                app:tickCount="100" />
        </RelativeLayout>
    </LinearLayout>

</RelativeLayout>