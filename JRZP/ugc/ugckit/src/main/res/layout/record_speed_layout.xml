<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">

    <RadioGroup
        android:id="@+id/rg_record_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginBottom="10dp"
        android:background="@drawable/drawable_gray_rect_bg"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_slowest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:text="@string/speed_very_slow"
            android:textColor="@drawable/record_speed_selector"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rb_slow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:text="@string/speed_slow"
            android:textColor="@drawable/record_speed_selector"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rb_normal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:text="@string/speed_normal"
            android:textColor="@drawable/record_speed_selector"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rb_fast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:text="@string/speed_fast"
            android:textColor="@drawable/record_speed_selector"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rb_fastest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@null"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:text="@string/speed_very_fast"
            android:textColor="@drawable/record_speed_selector"
            android:textSize="14sp" />
    </RadioGroup>
</LinearLayout>