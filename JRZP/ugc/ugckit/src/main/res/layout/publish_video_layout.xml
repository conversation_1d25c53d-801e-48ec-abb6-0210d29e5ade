<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/editer_bottom"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="50dp"
        android:layout_height="30dp"
        android:layout_gravity="right|center_vertical"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:src="@drawable/ic_close_selector" />

    <ProgressBar
        android:id="@+id/progressbar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:progressDrawable="@drawable/default_progress" />

    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/progressbar"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="10dp"
        android:textColor="@color/white"
        android:textSize="14dp" />
</RelativeLayout>