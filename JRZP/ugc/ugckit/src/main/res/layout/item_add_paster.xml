<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginRight="20dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/add_paster_image"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:background="@color/transparent" />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/add_paster_tint"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:background="@drawable/ic_effect5"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/add_paster_tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/add_paster_image"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="5dp"
        android:textColor="@color/white"
        android:textSize="10dp" />

</RelativeLayout>