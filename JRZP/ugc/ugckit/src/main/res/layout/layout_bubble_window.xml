<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ic_editer_bottom_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:paddingBottom="5dp"
        android:paddingTop="5dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/bubble_iv_bubble"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/bublle_style"
                android:textColor="@color/ic_text_selector"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/bubble_iv_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="44dp"
                android:text="@string/text_color"
                android:textColor="@color/ic_text_selector"
                android:textSize="16dp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_close_selector" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/colorGray5" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/bubble_rv_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

        <LinearLayout
            android:id="@+id/bubble_ll_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <com.tencent.qcloud.ugckit.component.circlebmp.TCCircleView
                android:id="@+id/bubble_cv_color"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp" />

            <com.tencent.qcloud.ugckit.component.seekbar.TCColorView
                android:id="@+id/bubble_color_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </RelativeLayout>
</LinearLayout>