<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:id="@+id/layout_progress">
    <ImageView
        android:id="@+id/progress_img"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:background="@drawable/loading_circle_progress"/>
    <TextView
        android:id="@+id/msg_tv"
        android:layout_below="@id/progress_img"
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:text=""
        android:textColor="@color/white"
        android:visibility="visible"/>

</LinearLayout>