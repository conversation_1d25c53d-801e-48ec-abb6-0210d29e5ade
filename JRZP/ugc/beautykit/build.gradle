plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
}

android {

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
  
  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  namespace 'com.tencent.liteav.support.beauty'
}

dependencies {
  implementation fileTree(include: ['*.jar'], dir: 'libs')
  implementation 'com.google.code.gson:gson:2.8.6'
  implementation libs.appcompat
  implementation libs.recyclerview

  implementation project(':ugc:ugckit')
}