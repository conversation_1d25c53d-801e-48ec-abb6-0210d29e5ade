package com.bxkj.personal.ui.activity.conversation

import androidx.core.os.bundleOf
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.enums.ChatRole.Companion.Role
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/7
 * @version: V1.0
 */
class GeekChatContentPageNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/converstaion"

    const val EXTRA_IDENTITY = "IDENTITY"
    const val EXTRA_CONVERSATION_USER_ID = "CHAT_USER_ID"

    //会话关联职位id
    const val EXTRA_ATTACH_JOB_ID = "ATTACH_JOB_ID"

    //会话关联简历id
    const val EXTRA_ATTACH_RESUME_ID = "ATTACH_RESUME_ID"

    //会话关联职位名称
    const val EXTRA_ATTACH_JOB_NAME = "ATTACH_JOB_OR_RESUME_NAME"

    //是否是打招呼
    const val EXTRA_IS_SAY_HELLO = "IS_SAY_HELLO"

    fun create(
      @Role role: Int,
      conversationUserId: Int,
      attachJobId: Int = CommonApiConstants.NO_ID,
      attachResumeId: Int = CommonApiConstants.NO_ID,
      attachJobName: String = CommonApiConstants.NO_TEXT,
      sayHello: Boolean = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH).apply {
        with(
          bundleOf(
            EXTRA_IDENTITY to role,
            EXTRA_CONVERSATION_USER_ID to conversationUserId,
            EXTRA_ATTACH_JOB_ID to attachJobId,
            EXTRA_ATTACH_RESUME_ID to attachResumeId,
            EXTRA_ATTACH_JOB_NAME to attachJobName,
            EXTRA_IS_SAY_HELLO to sayHello
          )
        )
      }
    }
  }

}