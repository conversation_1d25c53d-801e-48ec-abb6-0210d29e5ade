package com.bxkj.personal.ui.activity.invitationstodelivery

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class InviteToDeliveryNavigation {

    companion object {

        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/invitetodelivery"

        @JvmStatic
        fun navigate(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}