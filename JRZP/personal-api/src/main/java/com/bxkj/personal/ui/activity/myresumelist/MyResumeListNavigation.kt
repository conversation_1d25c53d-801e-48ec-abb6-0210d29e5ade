package com.bxkj.personal.ui.activity.myresumelist

import androidx.core.os.bundleOf
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
open class MyResumeListNavigation {
  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/myresumelist"

    const val EXTRA_PICKER_MODE = "PICKER_MODE"

    const val EXTRA_RESULT_SELECTED_RESUME = "RESULT_SELECTED_RESUME"

    fun navigate(pickerMode: Boolean): RouterNavigator {
      return Router.getInstance().to(PATH)
        .with(bundleOf(EXTRA_PICKER_MODE to pickerMode))
    }
  }
}