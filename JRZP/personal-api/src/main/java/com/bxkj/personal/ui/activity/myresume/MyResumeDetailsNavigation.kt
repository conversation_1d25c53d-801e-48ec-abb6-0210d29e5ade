package com.bxkj.personal.ui.activity.myresume

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant

/**
 *
 * @author: sanjin
 * @date: 2022/9/8
 */
class MyResumeDetailsNavigation {

    companion object {
        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/my_resume_details"

        fun create(action: Int=ResumeRouteConstant.ACTION_EDIT_AND_BACK): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(ResumeRouteConstant.EXTRA_EDIT_RESUME_ACTION, action)
        }
    }
}