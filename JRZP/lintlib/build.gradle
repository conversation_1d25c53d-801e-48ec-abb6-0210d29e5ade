plugins {
    id 'java-library'
    id 'kotlin'
    id 'com.android.lint'
}

java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}

dependencies {
    compileOnly "org.jetbrains.kotlin:kotlin-stdlib:1.8.10"
    compileOnly "com.android.tools.lint:lint-api:30.3.1"
    compileOnly "com.android.tools.lint:lint-checks:30.3.1"
}

jar {
    manifest {
        attributes("Lint-Registry-v2": "com.bxkj.lintlib.DangerIssueRegistry")
    }
}