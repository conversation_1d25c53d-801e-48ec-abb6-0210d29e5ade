package com.bxkj.jrzp.live.room.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.live.room.api.OpenLiveRoomApi
import com.bxkj.jrzp.live.room.data.LiveRoomData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/16
 * @version: V1.0
 */
class OpenLiveRoomRepository @Inject constructor(
  private val mOpenLiveRoomApi: OpenLiveRoomApi
) : BaseRepo() {

  /**
   * 获取直播房间列表
   */
  suspend fun getLiveRoomList(
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<LiveRoomData>> {
    return httpRequest {
      mOpenLiveRoomApi.getLiveRoomList(
        ZPRequestBody().apply {
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }
}