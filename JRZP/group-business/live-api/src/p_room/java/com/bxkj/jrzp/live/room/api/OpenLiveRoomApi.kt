package com.bxkj.jrzp.live.room.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.jrzp.live.room.data.LiveRoomData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/16
 * @version: V1.0
 */
interface OpenLiveRoomApi {

  @POST("/Zhibo/GetZhiboListByPage/")
  suspend fun getLiveRoomList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<LiveRoomData>>
}