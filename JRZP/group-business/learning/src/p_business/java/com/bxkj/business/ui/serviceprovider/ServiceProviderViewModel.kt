package com.bxkj.business.ui.serviceprovider

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/4/22
 */
class ServiceProviderViewModel @Inject constructor(
  private val learningRepository: LearningRepository
) : BaseViewModel() {

  private val _serviceProviderListViewModel = RefreshListViewModel()
  val serviceProviderListViewModel = _serviceProviderListViewModel

  private var searchKeyword: String = ""

  fun start(infoType: Int) {
    setupServiceProviderListViewModel(infoType)
    _serviceProviderListViewModel.refresh()
  }

  private fun setupServiceProviderListViewModel(infoType: Int) {
    _serviceProviderListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        learningRepository.getServiceProviderList(
          infoType,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          searchKeyword
        ).handleResult({
          _serviceProviderListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            _serviceProviderListViewModel.noMoreData()
          } else {
            _serviceProviderListViewModel.loadError()
          }
        })
      }
    }
  }

  fun startSearch(keyword: String) {
    searchKeyword = keyword
    _serviceProviderListViewModel.refresh(true)
  }

}