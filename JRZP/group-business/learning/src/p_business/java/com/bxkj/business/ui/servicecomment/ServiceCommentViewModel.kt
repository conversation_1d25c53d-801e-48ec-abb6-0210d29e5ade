package com.bxkj.business.ui.servicecomment

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/4/25
 */
class ServiceCommentViewModel @Inject constructor(
  private val learningRepository: LearningRepository
) : BaseViewModel() {

  private val _commentListViewModel = RefreshListViewModel()
  val commentListViewModel = _commentListViewModel

  fun start(encryptShopId: String, encryptProductId: String) {
    setupCommentListViewModel(encryptShopId, encryptProductId)
    _commentListViewModel.refresh()
  }

  private fun setupCommentListViewModel(encryptShopId: String, encryptProductId: String) {
    _commentListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        learningRepository.getServiceCommentList(
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          encryptShopId,
          encryptProductId
        ).handleResult({
          _commentListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            _commentListViewModel.noMoreData()
          } else {
            _commentListViewModel.loadError()
          }
        })
      }
    }
  }
}