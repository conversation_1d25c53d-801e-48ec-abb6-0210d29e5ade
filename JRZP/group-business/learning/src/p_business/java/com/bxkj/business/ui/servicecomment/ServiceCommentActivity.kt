package com.bxkj.business.ui.servicecomment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.learning.data.ServiceCommentData
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningActivityServiceCommentBinding
import com.willy.ratingbar.BaseRatingBar

/**
 * 服务评论
 * @author: YangXin
 * @date: 2021/4/25
 */
class ServiceCommentActivity :
    BaseDBActivity<LearningActivityServiceCommentBinding, ServiceCommentViewModel>() {

    companion object {
        const val EXTRA_ENCRYPT_SHOP_ID = "ENCRYPT_SHOP_ID"
        const val EXTRA_ENCRYPT_PRODUCT_ID = "ENCRYPT_PRODUCT_ID"

        fun newIntent(context: Context, encryptShopId: String, encryptProductId: String): Intent {
            return Intent(context, ServiceCommentActivity::class.java).apply {
                putExtra(EXTRA_ENCRYPT_SHOP_ID, encryptShopId)
                putExtra(EXTRA_ENCRYPT_PRODUCT_ID, encryptProductId)
            }
        }
    }

    override fun getViewModelClass(): Class<ServiceCommentViewModel> =
        ServiceCommentViewModel::class.java

    override fun getLayoutId(): Int = R.layout.learning_activity_service_comment

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupServiceCommentListAdapter()

        viewModel.start(
            intent.getStringExtra(EXTRA_ENCRYPT_SHOP_ID).getOrDefault(),
            intent.getStringExtra(EXTRA_ENCRYPT_PRODUCT_ID).getOrDefault()
        )
    }

    private fun setupServiceCommentListAdapter() {
        val serviceCommentListAdapter = object : SimpleDBListAdapter<ServiceCommentData>(
            this,
            R.layout.learning_recycler_service_comment_item
        ) {
            override fun convert(
                holder: SuperViewHolder,
                viewType: Int,
                item: ServiceCommentData,
                position: Int
            ) {
                super.convert(holder, viewType, item, position)
                holder.findViewById<BaseRatingBar>(R.id.rating_bar).rating = item.score.toFloat()
            }
        }
        viewBinding.includeServiceComment.recyclerContent.let {
            it.layoutManager = LinearLayoutManager(this)
            it.addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f4f4f4))
                    .margin(dip(12))
                    .build()
            )
        }
        viewModel.commentListViewModel.setAdapter(serviceCommentListAdapter)
    }
}