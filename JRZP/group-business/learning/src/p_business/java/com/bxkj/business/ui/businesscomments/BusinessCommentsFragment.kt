package com.bxkj.business.ui.businesscomments

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.learning.data.ServiceCommentData
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningFragmentBusinessCommentsBinding
import com.willy.ratingbar.BaseRatingBar

class BusinessCommentsFragment :
  BaseDBFragment<LearningFragmentBusinessCommentsBinding, BusinessCommentsViewModel>() {

  companion object {
    private const val EXTRA_BUSINESS_ID = "BUSINESS_ID"
    private const val EXTRA_ENCRYPT_BUSINESS_ID = "ENCRYPT_BUSINESS_ID"

    fun newInstance(businessId: Int, encryptBusinessId: String): Fragment {
      return BusinessCommentsFragment().apply {
        arguments = bundleOf(
          EXTRA_BUSINESS_ID to businessId,
          EXTRA_ENCRYPT_BUSINESS_ID to encryptBusinessId
        )
      }
    }
  }

  override fun getViewModelClass(): Class<BusinessCommentsViewModel> =
    BusinessCommentsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_fragment_business_comments

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupCommentListAdapter()

    viewModel.start(getIntentEncryptBusinessId())
  }

  private fun getIntentEncryptBusinessId(): String {
    return arguments?.getString(EXTRA_ENCRYPT_BUSINESS_ID).getOrDefault()
  }

  private fun getIntentBusinessId(): Int {
    return arguments?.getInt(EXTRA_BUSINESS_ID).getOrDefault()
  }

  private fun setupCommentListAdapter() {
    val serviceCommentListAdapter = object : SimpleDBListAdapter<ServiceCommentData>(
      parentActivity,
      R.layout.learning_recycler_service_comment_item
    ) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: ServiceCommentData,
        position: Int
      ) {
        super.convert(holder, viewType, item, position)
        holder.findViewById<BaseRatingBar>(R.id.rating_bar).rating = item.score.toFloat()
      }
    }
    viewBinding.includeBusinessComments.recyclerContent.let {
      it.layoutManager = LinearLayoutManager(parentActivity)
      it.addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_f4f4f4))
          .margin(dip(12))
          .build()
      )
    }
    viewModel.commentListViewModel.setAdapter(serviceCommentListAdapter)
  }
}