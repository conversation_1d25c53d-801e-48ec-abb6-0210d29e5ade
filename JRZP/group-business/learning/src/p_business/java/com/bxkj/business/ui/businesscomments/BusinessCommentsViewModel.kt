package com.bxkj.business.ui.businesscomments

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

class BusinessCommentsViewModel @Inject constructor(
  private val learningRepository: LearningRepository
) : BaseViewModel() {

  val commentListViewModel = RefreshListViewModel()

  val commentCount = MutableLiveData<String>()

  fun start(encryptBusinessId: String) {
    commentListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        learningRepository.getOriginServiceCommentList(
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          encryptBusinessId
        ).handleResult({
          it?.let {
            if (currentPage == 1) {
              commentCount.value = it.count
            }
            commentListViewModel.autoAddAll(it.data)
          }
        }, {
          if (it.isNoDataError) {
            if (currentPage == 1) {
              commentCount.value = "0"
            }
            commentListViewModel.noMoreData()
          } else {
            commentCount.value = "0"
            commentListViewModel.loadError()
          }
        })
      }
    }
    commentListViewModel.refresh()
  }
}