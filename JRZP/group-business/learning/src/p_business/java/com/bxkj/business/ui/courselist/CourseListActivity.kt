package com.bxkj.business.ui.courselist

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.learning.data.CourseData
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.bxkj.learning.R
import com.bxkj.learning.databinding.ActivityCoursesListBinding

/**
 * @Description:
 * @author:45457 技能提升列表
 * @date: 2020/7/24
 * @version: V1.0
 */
@Route(path = CourseListNavigation.PATH)
class CourseListActivity : BaseDBActivity<ActivityCoursesListBinding, CourseListViewModel>() {
  override fun getViewModelClass(): Class<CourseListViewModel> = CourseListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_courses_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupCoursesList()

    viewModel.start()
  }

  private fun setupCoursesList() {
    val coursesListAdapter =
      SimpleDBListAdapter<CourseData>(this, R.layout.recycler_courses_item)
        .apply {
          setOnItemClickListener(object : SuperItemClickListener {
            override fun onClick(v: View, position: Int) {
              afterLogin {
                LearningWebNavigation.navigate(
                  LearningWebNavigation.PAGE_TYPE_COURSE,
                  data[position].id,
                  localUserId
                ).start()
              }
            }
          })
        }
    val coursesList = viewBinding.includeCourseList.recyclerContent
    coursesList.layoutManager = LinearLayoutManager(this)
    viewModel.coursesListViewModel.setAdapter(coursesListAdapter)
  }

}