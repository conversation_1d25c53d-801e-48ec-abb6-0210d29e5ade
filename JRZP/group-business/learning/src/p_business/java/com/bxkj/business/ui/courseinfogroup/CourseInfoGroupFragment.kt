package com.bxkj.business.ui.courseinfogroup

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.os.bundleOf
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import com.bxkj.business.ui.courseinfogroup.CourseInfoType.Type
import com.bxkj.business.ui.educourse.EduCourseNavigation
import com.bxkj.business.ui.serviceprovider.ServiceProviderFragment
import com.bxkj.business.ui.skillcourse.SkillCourseFragment
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningFragmentCourseInfoGroupBinding
import com.bxkj.learning.ui.schoollist.InstitutionListFragment

/**
 *
 * @author: YangXin
 * @date: 2021/4/17
 */
class CourseInfoGroupFragment :
  BaseDBFragment<LearningFragmentCourseInfoGroupBinding, BaseViewModel>(),
  OnClickListener {

  companion object {

    //展示课程
    private const val SHOW_TYPE_FIRST_PAGE = 1

    //展示机构\学校
    private const val SHOW_TYPE_SECOND_PAGE = 2

    const val EXTRA_INFO_TYPE = "INFO_TYPE"

    fun newInstance(@Type infoType: Int): Fragment {
      return CourseInfoGroupFragment().apply {
        arguments = bundleOf(EXTRA_INFO_TYPE to infoType)
      }
    }
  }

  private var infoType = CourseInfoType.TYPE_SKILL
  private var showType = SHOW_TYPE_FIRST_PAGE
  private var currentShowPage: Fragment? = null

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_fragment_course_info_group

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.onClickListener = this

    setupSearchContentChangeListener()
  }

  override fun lazyLoadData() {
    arguments?.let {
      infoType = it.getInt(EXTRA_INFO_TYPE)
      updatePageState()
      updateShowFragment()
    }
  }

  private fun setupSearchContentChangeListener() {
    viewBinding.etSearchContent.addTextChangedListener {
      currentShowPage?.let { page ->
        if (page is LearningInfoChild) {
          page.startSearch(it.toString())
        }
      }
    }
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_switch_show_type) {
        switchShowType()
      }
    }
  }

  private fun switchShowType() {
    showType = if (showType == SHOW_TYPE_FIRST_PAGE) {
      SHOW_TYPE_SECOND_PAGE
    } else {
      SHOW_TYPE_FIRST_PAGE
    }
    updatePageState()
    updateShowFragment()
  }

  private fun updatePageState() {
    if (showType == SHOW_TYPE_FIRST_PAGE) {
      if (CourseInfoType.isCourse(infoType)) {
        viewBinding.tvSwitchShowType.text = "培训机构"
      } else {
        viewBinding.tvSwitchShowType.text = "找店铺"
      }
      viewBinding.tvSwitchShowType.setCompoundDrawablesWithIntrinsicBounds(
        0,
        R.drawable.learning_ic_institution_tag,
        0,
        0
      )
    } else {
      if (CourseInfoType.isCourse(infoType)) {
        viewBinding.tvSwitchShowType.text = "优质服务"
      } else {
        viewBinding.tvSwitchShowType.text = "找服务"
      }
      viewBinding.tvSwitchShowType.setCompoundDrawablesWithIntrinsicBounds(
        0,
        R.drawable.learning_ic_course_tag,
        0,
        0
      )
    }
    updateSearchHint()
  }

  private fun updateShowFragment() {
    updateSearchHint()
    currentShowPage = getShowFragment()
    childFragmentManager.beginTransaction().replace(R.id.fl_content, currentShowPage!!).commit()
  }

  private fun updateSearchHint() {
    when (infoType) {
      CourseInfoType.TYPE_SKILL -> {
        if (showType == SHOW_TYPE_FIRST_PAGE) {
          viewBinding.etSearchContent.hint = "搜索技能提升相关课程和关键词"
        } else {
          viewBinding.etSearchContent.hint = "搜索技能提升培训机构"
        }
      }
      CourseInfoType.TYPE_EDU -> {
        if (showType == SHOW_TYPE_FIRST_PAGE) {
          viewBinding.etSearchContent.hint = "搜索学历教育相关课程和关键词"
        } else {
          viewBinding.etSearchContent.hint = "搜索学历教育培训机构"
        }
      }
      CourseInfoType.TYPE_PRO_EMPLOYMENT -> {
        if (showType == SHOW_TYPE_FIRST_PAGE) {
          viewBinding.etSearchContent.hint = "搜索专业用工相关关键词"
        } else {
          viewBinding.etSearchContent.hint = "搜索专业用工相关关键词"
        }
      }
      CourseInfoType.TYPE_15 -> {
        viewBinding.etSearchContent.hint = "搜索推广相关关键词"
      }
      CourseInfoType.TYPE_3 -> {
        viewBinding.etSearchContent.hint = "搜索外协加工相关关键词"
      }
      CourseInfoType.TYPE_4 -> {
        viewBinding.etSearchContent.hint = "搜索知识产权相关关键词"
      }
      CourseInfoType.TYPE_5 -> {
        viewBinding.etSearchContent.hint = "搜索检测认证相关关键词"
      }
      CourseInfoType.TYPE_9 -> {
        viewBinding.etSearchContent.hint = "搜索机器换人相关关键词"
      }
      else -> {
        if (showType == SHOW_TYPE_FIRST_PAGE) {
          viewBinding.etSearchContent.hint = "搜索工业设计相关关键词"
        } else {
          viewBinding.etSearchContent.hint = "搜索工业设计相关关键词"
        }
      }
    }
  }

  private fun getShowFragment(): Fragment {
    return when (infoType) {
      CourseInfoType.TYPE_SKILL -> {
        getShowSkillFragment()
      }
      CourseInfoType.TYPE_EDU -> {
        getShowEduFragment()
      }
      CourseInfoType.TYPE_PRO_EMPLOYMENT -> {
        getShowProEmploymentFragment()
      }
      CourseInfoType.TYPE_INDUSTRIAL_DESIGN -> {
        getShowIndustrialDesignFragment()
      }
      else -> {
        getShowIndustrialDesignFragment()
      }
    }
  }

  private fun getShowIndustrialDesignFragment(): Fragment {
    return if (showType == SHOW_TYPE_FIRST_PAGE) {
      SkillCourseFragment.newInstance(infoType)
    } else {
      ServiceProviderFragment.newInstance(infoType)
    }
  }

  private fun getShowProEmploymentFragment(): Fragment {
    return if (showType == SHOW_TYPE_FIRST_PAGE) {
      SkillCourseFragment.newInstance(infoType)
    } else {
      ServiceProviderFragment.newInstance(infoType)
    }
  }

  private fun getShowSkillFragment(): Fragment {
    return if (showType == SHOW_TYPE_FIRST_PAGE) {
      SkillCourseFragment.newInstance(infoType)
    } else {
      InstitutionListFragment.newInstance()
    }
  }

  private fun getShowEduFragment(): Fragment {
    return if (showType == SHOW_TYPE_FIRST_PAGE) {
      EduCourseNavigation.create()
    } else {
      InstitutionListFragment.newInstance()
    }
  }

  override fun onDestroyView() {
    currentShowPage = null
    super.onDestroyView()
  }
}