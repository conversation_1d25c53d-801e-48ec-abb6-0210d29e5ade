<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.learning.data.IndustrialServicesData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_marginStart="@dimen/dp_12"
    android:layout_marginEnd="@dimen/dp_12"
    android:background="@drawable/bg_ffffff_radius_4"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_14"
    android:paddingBottom="@dimen/dp_10">

    <ImageView
      android:id="@+id/iv_img"
      android:layout_width="@dimen/dp_77"
      android:layout_height="@dimen/dp_77"
      android:background="@drawable/frame_f4f4f4_radius_2"
      android:padding="@dimen/dp_1"
      android:scaleType="centerInside"
      bind:imgUrl="@{data.fixProductImg}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.productName}"
      app:layout_constraintEnd_toStartOf="@id/tv_price"
      app:layout_constraintStart_toEndOf="@id/iv_img"
      app:layout_constraintTop_toTopOf="@id/iv_img" />

    <TextView
      android:id="@+id/tv_price"
      style="@style/Text.14sp.FE6600.Bold"
      android:text="@{@string/learning_pro_deployment_price_format(data.minPrice)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <ImageView
      android:id="@+id/tv_service_tag"
      style="@style/wrap_wrap"
      android:layout_marginTop="@dimen/dp_6"
      android:src="@drawable/learning_ic_business_service_tag"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp.999999"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{@string/learning_pro_deployment_desc_format(data.volume,data.commentCount)}"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_service_tag" />


  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>