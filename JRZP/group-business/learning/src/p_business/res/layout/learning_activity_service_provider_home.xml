<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.business.ui.serviceproviderhome.ServiceProviderHomeViewModel" />
  </data>

  <LinearLayout
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1"
      android:focusable="true"
      android:focusableInTouchMode="true">

      <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/abl_header"
        style="@style/match_wrap"
        android:orientation="vertical"
        app:elevation="@dimen/dp_0">

        <com.google.android.material.appbar.CollapsingToolbarLayout
          android:id="@+id/coll_top_layout"
          style="@style/match_wrap"
          app:layout_scrollFlags="scroll|exitUntilCollapsed">

          <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/match_wrap"
            android:background="@drawable/learning_img_service_provider_header">

            <ImageView
              android:id="@+id/iv_logo"
              android:layout_width="@dimen/dp_48"
              android:layout_height="@dimen/dp_48"
              android:layout_marginStart="@dimen/dp_14"
              android:background="@drawable/frame_f4f4f4_radius_4"
              android:padding="@dimen/dp_1"
              android:scaleType="centerInside"
              bind:imgUrl="@{viewModel.serviceProviderInfo.convertHttpLogo}"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent" />

            <TextView
              android:id="@+id/tv_name"
              style="@style/Text.16sp.FFFFFF"
              android:layout_width="@dimen/dp_0"
              android:layout_marginStart="@dimen/dp_14"
              android:layout_marginEnd="@dimen/dp_14"
              android:ellipsize="end"
              android:singleLine="true"
              android:text="@{viewModel.serviceProviderInfo.name}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toEndOf="@id/iv_logo"
              app:layout_constraintTop_toTopOf="@id/iv_logo" />

            <TextView
              android:id="@+id/tv_desc"
              style="@style/Text.12sp.999999"
              android:layout_width="@dimen/dp_0"
              android:layout_marginTop="@dimen/dp_4"
              android:layout_marginEnd="@dimen/dp_14"
              android:text="@{viewModel.serviceProviderInfo.homePageDesc}"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="@id/tv_name"
              app:layout_constraintTop_toBottomOf="@id/tv_name" />

            <LinearLayout
              android:id="@+id/ll_business_tag"
              android:layout_width="@dimen/dp_0"
              android:layout_height="wrap_content"
              android:layout_marginTop="@dimen/dp_8"
              android:orientation="horizontal"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="@id/tv_desc"
              app:layout_constraintTop_toBottomOf="@id/tv_desc">

              <ImageView
                style="@style/wrap_wrap"
                android:src="@drawable/learning_ic_industrial_tag" />

              <ImageView
                style="@style/wrap_wrap"
                android:layout_marginStart="@dimen/dp_4"
                android:src="@drawable/learning_ic_company_tag" />

            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_business_about"
              style="@style/match_wrap"
              android:layout_marginTop="@dimen/dp_16"
              android:layout_marginBottom="@dimen/dp_16"
              android:orientation="horizontal"
              app:layout_constraintBottom_toBottomOf="parent"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/ll_business_tag">

              <LinearLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                  style="@style/Text.14sp.FFFFFF.Bold"
                  android:text="@{viewModel.serviceProviderInfo.formatTurnOver}" />

                <TextView
                  style="@style/Text.10sp.888888"
                  android:layout_marginTop="@dimen/dp_4"
                  android:text="@string/learning_service_provider_turnover" />

              </LinearLayout>

              <LinearLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                  style="@style/Text.14sp.FFFFFF.Bold"
                  android:text="@{String.valueOf(viewModel.serviceProviderInfo.rating)}" />

                <TextView
                  style="@style/Text.10sp.888888"
                  android:layout_marginTop="@dimen/dp_4"
                  android:text="@string/learning_service_provider_rating" />

              </LinearLayout>

              <LinearLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                  style="@style/Text.14sp.FFFFFF.Bold"
                  android:text="@{@string/learning_service_provider_info_precent_foramt(viewModel.serviceProviderInfo.projectCompaletionRate)}" />

                <TextView
                  style="@style/Text.10sp.888888"
                  android:layout_marginTop="@dimen/dp_4"
                  android:text="@string/learning_service_provider_project_completion_rate" />

              </LinearLayout>

              <LinearLayout
                android:layout_width="@dimen/dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                  style="@style/Text.14sp.FFFFFF.Bold"
                  android:text="@{@string/learning_service_provider_info_precent_foramt(viewModel.serviceProviderInfo.praiseRate)}" />

                <TextView
                  style="@style/Text.10sp.888888"
                  android:layout_marginTop="@dimen/dp_4"
                  android:text="@string/learning_service_provider_praise" />

              </LinearLayout>

            </LinearLayout>

            <!--                    <TextView-->
            <!--                        style="@style/Text.10sp.888888"-->
            <!--                        android:layout_width="@dimen/dp_0"-->
            <!--                        android:layout_marginTop="@dimen/dp_16"-->
            <!--                        android:ellipsize="marquee"-->
            <!--                        android:paddingStart="@dimen/dp_14"-->
            <!--                        android:paddingTop="@dimen/dp_4"-->
            <!--                        android:paddingEnd="@dimen/dp_14"-->
            <!--                        android:paddingBottom="@dimen/dp_4"-->
            <!--                        android:singleLine="true"-->
            <!--                        android:text="擅长：玻璃管切割刀  隔热服套装  气动二联件  产品设计 模具设计制造 钣金加工"-->
            <!--                        app:layout_constraintEnd_toEndOf="parent"-->
            <!--                        app:layout_constraintStart_toStartOf="parent"-->
            <!--                        app:layout_constraintTop_toBottomOf="@id/ll_business_about" />-->

          </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <net.lucode.hackware.magicindicator.MagicIndicator
          android:id="@+id/indicator_info_type"
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_36"
          android:background="@drawable/bg_ffffff" />

        <View style="@style/Line.Horizontal.Light" />
      </com.google.android.material.appbar.AppBarLayout>

      <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_content"
        style="@style/match_wrap"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

      <LinearLayout
        android:id="@+id/ll_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:background="@drawable/bg_ffffff"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
          android:id="@+id/iv_back"
          android:layout_width="@dimen/common_dp_32"
          android:layout_height="@dimen/common_dp_32"
          android:scaleType="center"
          android:src="@drawable/ic_back_white" />

        <com.bxkj.common.widget.MyEditText
          style="@style/Text.14sp"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/common_dp_32"
          android:layout_marginStart="@dimen/dp_12"
          android:layout_marginEnd="@dimen/dp_24"
          android:layout_weight="1"
          android:background="@drawable/bg_f4f4f4_radius_2"
          android:drawableStart="@drawable/ic_search"
          android:drawablePadding="@dimen/dp_8"
          android:gravity="center_vertical"
          android:hint="@string/learning_service_provider_search_hint"
          android:paddingStart="@dimen/dp_8"
          android:paddingEnd="@dimen/dp_8"
          android:text="@={viewModel.searchKeyword}"
          android:textColorHint="@color/cl_999999" />

      </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <View style="@style/Line.Horizontal.Light" />

    <LinearLayout
      android:id="@+id/ll_options_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52"
      android:layout_gravity="bottom"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent">

      <TextView
        style="@style/Text.10sp.888888"
        android:layout_marginStart="@dimen/common_dp_28"
        android:drawableTop="@drawable/learning_ic_business_call_phone"
        android:drawablePadding="@dimen/dp_2"
        android:gravity="center"
        android:onClick="@{()->viewModel.callPhone()}"
        android:text="@string/learning_service_provider_call_phone" />

      <TextView
        style="@style/Text.16sp.FFFFFF"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/common_dp_28"
        android:layout_marginEnd="@dimen/common_dp_28"
        android:background="@drawable/bg_fe6600_round"
        android:gravity="center"
        android:onClick="@{()->viewModel.showContractWay()}"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:text="@string/learning_service_provider_buy_service" />

    </LinearLayout>
  </LinearLayout>

</layout>