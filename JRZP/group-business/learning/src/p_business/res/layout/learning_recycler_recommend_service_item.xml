<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.learning.data.IndustrialServicesData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingBottom="@dimen/dp_4">

    <ImageView
      android:id="@+id/iv_pic"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerInside"
      bind:imgUrl="@{data.fixProductImg}"
      app:layout_constraintDimensionRatio="3:2"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.12sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.productName}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_pic" />

    <TextView
      style="@style/Text.12sp.FF7647"
      android:layout_width="0dp"
      android:layout_marginTop="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{HtmlUtils.fromHtml(@string/learning_industrial_service_price_format(data.minPrice))}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>