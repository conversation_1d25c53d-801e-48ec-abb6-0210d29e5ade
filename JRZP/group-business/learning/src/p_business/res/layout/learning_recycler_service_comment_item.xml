<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.learning.data.ServiceCommentData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:padding="@dimen/dp_14">

    <ImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_36"
      android:layout_height="@dimen/dp_36"
      android:background="@drawable/frame_f4f4f4_round"
      android:padding="@dimen/dp_1"
      bind:imgIsCircle="@{true}"
      bind:imgUrl="@{data.fixUserAvatar}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.14sp.666666"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_6"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.userNickName}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="parent" />

    <com.willy.ratingbar.BaseRatingBar
      android:id="@+id/rating_bar"
      style="@style/wrap_wrap"
      android:layout_marginTop="@dimen/dp_4"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name"
      app:srb_drawableEmpty="@drawable/learning_ic_comment_star_empty"
      app:srb_drawableFilled="@drawable/learning_ic_comment_star_filled"
      app:srb_isIndicator="true"
      app:srb_starHeight="@dimen/dp_12"
      app:srb_starPadding="@dimen/dp_2"
      app:srb_starWidth="@dimen/dp_12" />

    <TextView
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_4"
      android:text="@{data.scoreName}"
      app:layout_constraintBottom_toBottomOf="@id/rating_bar"
      app:layout_constraintStart_toEndOf="@id/rating_bar"
      app:layout_constraintTop_toTopOf="@id/rating_bar" />

    <androidx.constraintlayout.widget.Barrier
      android:id="@+id/barrier_user_info_bottom"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      app:barrierDirection="bottom"
      app:constraint_referenced_ids="iv_avatar,rating_bar" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{data.content}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/barrier_user_info_bottom" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>