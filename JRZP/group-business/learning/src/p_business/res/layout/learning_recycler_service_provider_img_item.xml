<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.learning.data.ProductImgData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
      android:layout_width="0dp"
      android:layout_height="0dp"
      bind:imgRadius="@{@dimen/dp_4}"
      android:padding="@dimen/dp_1"
      android:background="@drawable/frame_f4f4f4_radius_4"
      bind:imgUrl="@{data.convertHttpImg}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>