<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.learning.data.InstitutionData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:padding="@dimen/dp_14">

    <ImageView
      android:id="@+id/iv_logo"
      android:layout_width="@dimen/common_dp_42"
      android:layout_height="@dimen/common_dp_42"
      android:background="@drawable/frame_f4f4f4_round"
      android:padding="@dimen/dp_1"
      bind:imgIsCircle="@{true}"
      bind:imgUrl="@{data.comLogo}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.15sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.name}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_2"
      android:text="@{HtmlUtils.fromHtml(@string/learning_institution_desc_format(data.kechengCount,data.buyCount,data.pingfen))}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_address"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{data.formatAddress}"
      app:layout_constraintBottom_toBottomOf="@id/tv_view_details"
      app:layout_constraintEnd_toStartOf="@id/tv_view_details"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_view_details" />

    <TextView
      android:id="@+id/tv_view_details"
      style="@style/Text.12sp.FF7647"
      android:layout_marginEnd="@dimen/dp_6"
      android:background="@drawable/bg_ffd9c0_round"
      android:gravity="center"
      android:paddingStart="@dimen/dp_8"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_8"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/learning_institution_view_details"
      app:layout_constraintBottom_toBottomOf="@id/tv_follow"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintTop_toTopOf="@id/tv_follow" />

    <TextView
      android:id="@+id/tv_follow"
      style="@style/Text.12sp"
      android:layout_width="55dp"
      android:layout_marginTop="@dimen/dp_6"
      android:background="@{data.followUser?@drawable/frame_eaeaea_round:@drawable/bg_10c198_round}"
      android:gravity="center"
      android:paddingTop="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{data.followUser?@string/news_details_followed:@string/news_details_follow}"
      android:textColor="@{data.followUser?@color/cl_999999:@color/common_white}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_desc" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>