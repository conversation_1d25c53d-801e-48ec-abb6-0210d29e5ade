package com.bxkj.learning.data

import androidx.recyclerview.widget.DiffUtil

/**
 *
 * @author: Yang<PERSON>in
 * @date: 2021/4/25
 */
data class ServiceAttributeData(
  val id: Int,
  val attribute: String,
  val price: Float,
  val unit: String
) {

  fun getFixAttribute(): String {
    if (attribute.isNullOrEmpty()) {
      return "标准"
    } else {
      return attribute
    }
  }

  class DiffCallback : DiffUtil.ItemCallback<ServiceAttributeData>() {
    override fun areItemsTheSame(
      oldItem: ServiceAttributeData,
      newItem: ServiceAttributeData
    ): Boolean {
      return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
      oldItem: ServiceAttributeData,
      newItem: ServiceAttributeData
    ): Boolean {
      return oldItem.attribute == newItem.attribute
    }

  }
}