package com.bxkj.learning.api

/**
 * @Project: gzgk
 * @Description: 视频接口常量
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
class LearningApiConstants {
  companion object {
    //获取课程列表
    const val I_GET_COURSES_LIST = "/Course/GetCourseListByPage/"

    //获取课程详情
    const val I_GET_COURSE_DETAILS = "/Course/GetCourseDetail/"

    //课程分享信息
    const val I_GET_COURSE_SHARE_INFO = "/Course/GetCourseFxInfo/"

    //学历提升分享信息
    const val I_GET_CONTINUING_EDUCATION_SHARE_INFO = "/School/GetCourseFxInfo/"

    //获取学历提升列表
    const val I_GET_SCHOOL_LIST = "/School/GetXueliCourseListByPage/"

    //获取优质课程分类
    const val I_GET_COURSE_TYPE_LIST = "/Course/GetkechengClassListByPage/"

    //获取机构列表
    const val I_GET_INSTITUTION_LIST = "/Jigou/GetJigouListByPage/"

    //获取用户课程列表
    const val I_GET_USER_COURSE_LIST = "/ShejiaoNews/GetQyZhuyeListByPage/"

    //获取工业服务列表
    const val I_GET_INDUSTRIAL_SERVICE_LIST = "/Zhuanyeyonggong/GetShangchengProductZuListByPage/"

    //获取工业服务类型列表
    const val I_GET_INDUSTRIAL_SERVICE_TYPE_LIST = "/Zhuanyeyonggong/GetTypeList/"

    //优质服务商列表
    const val I_GET_SERVICE_PROVIDER_LIST = "/Zhuanyeyonggong/GetShangchengYpListByPage/"

    //工业服务详情
    const val I_GET_INDUSTRIAL_SERVICE_DETAILS = "/Zhuanyeyonggong/GetproductZuSelect/"

    //推荐服务列表
    const val I_GET_RECOMMEND_SERVICE_LIST = "/Zhuanyeyonggong/GettjproductZuList/"

    //服务评价列表
    const val I_GET_SERVICE_COMMENT_LIST = "/Zhuanyeyonggong/GetShangchengScoreList/"

    //获取工业服务规格
    const val I_GET_INDUSTRIAL_SERVICE_ATTRIBUTE = "/Zhuanyeyonggong/GetProAttribute/"

    //创建工业服务订单
    const val I_CREATE_INDUSTRIAL_SERVICE_ORDER = "/Zhuanyeyonggong/AddOrder/"

    //获取工服商家详情
    const val I_GET_SERVICE_PROVIDER_DETAILS = "/Yp/GetGongfuYpInfo/"

    //获取企业服务分类
    const val I_GET_BUSINESS_SERVICE_TYPE_LIST = "/ShangchengProductType/GetCategory1List/"
  }
}