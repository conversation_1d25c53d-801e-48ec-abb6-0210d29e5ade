package com.bxkj.learning.data

import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.util.CheckUtils

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
data class CoursesShareInfoData(
  var name: String? = "",
  var pic: String? = "",
  var money: String? = "",
  var yongjinMoney: String? = "",
  var orderList: List<CommissionNoticeData>? = null
) : ShareInfoData() {

  fun hasCommission(): Boolean {
    return !CheckUtils.isNullOrEmpty(yongjinMoney)
  }
}