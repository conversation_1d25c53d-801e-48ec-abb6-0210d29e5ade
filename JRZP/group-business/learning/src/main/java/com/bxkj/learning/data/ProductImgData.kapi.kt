package com.bxkj.learning.data

import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.util.kotlin.convertUrlToHttp
import com.google.gson.annotations.SerializedName

/**
 *
 * @author: YangXin
 * @date: 2021/4/22
 */
data class ProductImgData(
  @SerializedName("pic", alternate = ["proimg"])
  val pic: String
) {
  fun getConvertHttpImg(): String {
    return pic.convertUrlToHttp()
  }

  class DiffCallback : DiffUtil.ItemCallback<ProductImgData>() {
    override fun areItemsTheSame(oldItem: ProductImgData, newItem: ProductImgData): Boolean {
      return oldItem.pic.equals(newItem.pic)
    }

    override fun areContentsTheSame(oldItem: ProductImgData, newItem: ProductImgData): Boolean {
      return oldItem.pic.equals(newItem.pic)
    }
  }
}