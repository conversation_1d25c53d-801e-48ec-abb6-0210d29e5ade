package com.bxkj.jrzp.live.audience.ui.commodity

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.ui.activity.positionpreview.JobPreviewNavigation
import com.bxkj.jrzp.live.BR
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.databinding.LiveDialogCommodityListBinding
import com.bxkj.jrzp.support.feature.ui.sendresume.SendResumeActionActivity
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation

/**
 * @Description: 直播商品
 * @author:45457
 * @date: 2020/10/20
 * @version: V1.0
 */
class LiveCommodityDialog :
    BaseDBDialogFragment<LiveDialogCommodityListBinding, LiveCommodityViewModel>() {

    companion object {

        private const val EXTRA_LIVE_ID = "LIVE_ID"
        private const val EXTRA_SHOW_SEND_RESUME = "SHOW_SEND_RESUME"

        fun newInstance(liveID: Int, showSendResume: Boolean = true): LiveCommodityDialog {
            return LiveCommodityDialog().apply {
                arguments = bundleOf(
                    EXTRA_LIVE_ID to liveID,
                    EXTRA_SHOW_SEND_RESUME to showSendResume
                )
            }
        }
    }

    private var mOnSendResumeClickListener: OnSendResumeClickListener? = null

    private val sendResumeLauncher = registerForActivityResult(StartActivityForResult()) {
        if (it.resultCode == Activity.RESULT_OK) {
            viewModel.syncSendState(it.data?.getIntExtra(SendResumeActionActivity.RESULT_JOB_ID, 0))
        }
    }

    override fun getViewModelClass(): Class<LiveCommodityViewModel> =
        LiveCommodityViewModel::class.java

    override fun getLayoutId(): Int = R.layout.live_dialog_commodity_list

    override fun initPage() {
        viewBinding.viewModel = viewModel

        setupLiveCommodityListAdapter()

        viewModel.start(arguments?.getInt(EXTRA_LIVE_ID) ?: 0)
    }

    override fun getTheme(): Int {
        return R.style.BaseDialogFragmentStyle_NoDim
    }

    override fun enableBottomSheet(): Boolean {
        return true
    }

    fun setOnSendResumeClickListener(onSendResumeClickListener: OnSendResumeClickListener): LiveCommodityDialog =
        apply {
            mOnSendResumeClickListener = onSendResumeClickListener
        }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let {
            it.setWindowAnimations(R.style.BottomPopupAnim)
            it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
                val layoutParams = layout.layoutParams
                layoutParams.height = getLayoutHeight()
            }
        }
    }

    private fun getLayoutHeight(): Int {
        val peekHeight = resources.displayMetrics.heightPixels
        //设置弹窗高度为屏幕高度的3/4
        return peekHeight - peekHeight / 3
    }

    private fun setupLiveCommodityListAdapter() {
        val commodityListAdapter = object : SimpleDBListAdapter<PositionItemBean>(
            parentActivity,
            R.layout.live_recycler_job_item,
            BR.data
        ) {
            override fun convert(
                holder: SuperViewHolder,
                viewType: Int,
                item: PositionItemBean,
                position: Int
            ) {
                super.convert(holder, viewType, item, position)
                holder.findViewById<TextView>(R.id.tv_send_resume).visibility =
                    if (arguments?.getBoolean(
                            EXTRA_SHOW_SEND_RESUME
                        ) == true
                    ) View.VISIBLE else View.GONE
                holder.findViewById<TextView>(R.id.tv_received_resume).visibility =
                    if (arguments?.getBoolean(
                            EXTRA_SHOW_SEND_RESUME
                        ) == true
                    ) View.GONE else View.VISIBLE
            }
        }.apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    val clickItem = data[position]
                    if (v.id == R.id.tv_send_resume) {
                        afterLogin {
                            sendResumeLauncher.launch(
                                SendResumeActionActivity.newIntent(
                                    requireContext(),
                                    clickItem.id,
                                    clickItem.uid
                                )
                            )
                        }
                    } else {
                        if (arguments?.getBoolean(EXTRA_SHOW_SEND_RESUME) == true) {
                            if (mOnSendResumeClickListener?.onClickResume(clickItem) == true) {
                                return
                            }
                            JobDetailsNavigation.navigate(clickItem.id, true).start()
                        } else {
                            JobPreviewNavigation.navigate(
                                clickItem.id,
                                JobPreviewNavigation.STATE_RUNNING
                            ).start()
                        }
                        dismiss()
                    }
                }
            }, R.id.tv_send_resume)
        }
        viewBinding.includeCommodityList.recyclerContent.layoutManager =
            LinearLayoutManager(parentActivity)
        viewModel.commodityListViewModel.setAdapter(commodityListAdapter)
    }

    interface OnSendResumeClickListener {

        fun onSendResume(position: PositionItemBean)

        fun onClickResume(position: PositionItemBean): Boolean
    }
}