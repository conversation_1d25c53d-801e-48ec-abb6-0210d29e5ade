package com.bxkj.jrzp.live.audience.ui.audience

import android.content.Context
import android.os.SystemClock
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.widget.GestureLayout
import kotlin.math.abs

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/29
 * @version: V1.0
 */
class LiveAudienceGestureController constructor(private val mContent: Context) {

  private var mGestureDetector: GestureDetector? = null

  private var mOnGestureListener: OnGestureListener? = null

  private var lastClickTime = 0L

  private val mFlipDistance = AppConstants.SCREEN_WIDTH / 5

  init {
    mGestureDetector =
      GestureDetector(mContent, object : GestureDetector.SimpleOnGestureListener() {

        override fun onDown(e: MotionEvent): Boolean {
          val systemTime = SystemClock.uptimeMillis()
          if (systemTime - lastClickTime < 400) {
            mOnGestureListener?.onContinuousTap()
          }
          lastClickTime = SystemClock.uptimeMillis()
          return true
        }

        override fun onScroll(
          e1: MotionEvent?,
          e2: MotionEvent,
          distanceX: Float,
          distanceY: Float
        ): Boolean {
          e1?.let {
            Log.d("GestureLayout:onScroll", "按下点：${e1.x}*${e1.y}---抬起点：${e2.x}*${e2.y}")
            if (e1.x - e2.x > 40 && abs(e1.x - e2.x) / abs(e1.y - e2.y) > 2) {
              return mOnGestureListener?.onGesture(GestureLayout.OP_LEFT_SCROLL) ?: super.onScroll(
                e1,
                e2,
                distanceX,
                distanceY
              )
            }
          }
          return false;
        }

        override fun onFling(
          e1: MotionEvent?,
          e2: MotionEvent,
          velocityX: Float,
          velocityY: Float
        ): Boolean {
          e1?.let {
            Log.d("GestureLayout:onScroll", "按下点：${e1.x}*${e1.y}---抬起点：${e2.x}*${e2.y}")
            if (e1.x - e2.x > mFlipDistance && abs(e1.x - e2.x) / abs(e1.y - e2.y) > 2) {
              return mOnGestureListener?.onGesture(GestureLayout.OP_LEFT_SCROLL) ?: super.onFling(
                e1,
                e2,
                velocityX,
                velocityY
              )
            }
          }
          return false;
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
          return mOnGestureListener?.onSingleTapConfirm() ?: super.onSingleTapConfirmed(e)
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
          return mOnGestureListener?.onDoubleTap() ?: super.onDoubleTap(e)
        }
      })
  }

  fun processEvent(event: MotionEvent): Boolean {
    return mGestureDetector!!.onTouchEvent(event)
  }

  fun setOnGestureListener(onGestureListener: OnGestureListener) {
    mOnGestureListener = onGestureListener
  }

  interface OnGestureListener {

    fun onGesture(gesture: Int): Boolean

    fun onDoubleTap(): Boolean

    fun onSingleTapConfirm(): Boolean

    fun onContinuousTap()
  }
}
