package com.bxkj.jrzp.live.audience.ui.commodity

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.jrzp.live.api.LiveRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/20
 * @version: V1.0
 */
class LiveCommodityViewModel @Inject constructor(
    private val mLiveRepository: LiveRepository
) : BaseViewModel() {

    val commodityListViewModel = RefreshListViewModel()

    private var mLiveID: Int = 0

    init {
        setupCommodityListViewModel()
    }

    private fun setupCommodityListViewModel() {
        commodityListViewModel.refreshLayoutViewModel.enableRefresh(false)
        commodityListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mLiveRepository.getLiveRelateJobList(
                    getSelfUserID(),
                    mLiveID,
                    currentPage,
                    CommonApiConstants.DEFAULT_PAGE_SIZE
                ).handleResult({
                    commodityListViewModel.autoAddAll(it)
                }, {
                    if (it.isNoDataError) {
                        commodityListViewModel.noMoreData()
                    } else {
                        commodityListViewModel.loadError()
                    }
                })
            }
        }
    }

    fun start(liveID: Int) {
        mLiveID = liveID
        commodityListViewModel.refresh()
    }

    fun syncSendState(jobId: Int?) {
        jobId?.let {
            commodityListViewModel.data?.filter { it is PositionItemBean }?.forEach {
                if ((it as PositionItemBean).id == jobId) {
                    it.markDelivered()
                }
            }
        }
    }
}