<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.live.notice.ui.list.LiveNoticeListViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/live_notice_list_page_title" />

    <include
      android:id="@+id/include_live_notice"
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.liveNoticeListViewModel}" />

    <TextView
      android:id="@+id/tv_add_live_notice"
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_30"
      android:onClick="@{onClickListener}"
      android:text="@string/live_notice_add" />
  </LinearLayout>
</layout>