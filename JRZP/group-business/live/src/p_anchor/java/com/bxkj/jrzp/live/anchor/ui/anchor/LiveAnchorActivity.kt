package com.bxkj.jrzp.live.anchor.ui.anchor

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.animation.doOnEnd
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.toLast
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup.Builder
import com.bxkj.jrzp.live.LiveIMConstants
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.R.array
import com.bxkj.jrzp.live.R.drawable
import com.bxkj.jrzp.live.R.string
import com.bxkj.jrzp.live.adapter.LiveChatMsgListAdapter
import com.bxkj.jrzp.live.anchor.ui.livestatistics.LiveStatisticsNavigation
import com.bxkj.jrzp.live.audience.ui.commodity.LiveCommodityDialog
import com.bxkj.jrzp.live.data.LiveChatMsg
import com.bxkj.jrzp.live.databinding.LiveActivityAnchorBinding
import com.bxkj.jrzp.live.dialog.userhome.LiveUserHomeDialog
import com.bxkj.jrzp.live.dialog.userhome.LiveUserHomeDialog.OnReportClickListener
import com.bxkj.jrzp.live.dialog.web.LiveWebViewDialog
import com.bxkj.jrzp.live.utils.TCLiveUtils
import com.bxkj.share.ui.StandardShareDialog
import com.elvishew.xlog.XLog
import com.robinhood.ticker.TickerUtils
import com.tencent.qcloud.ugckit.roomservice.IMLVBLiveRoomListener
import com.tencent.qcloud.ugckit.roomservice.IMLVBLiveRoomListener.*
import com.tencent.qcloud.ugckit.roomservice.MLVBLiveRoom
import com.tencent.qcloud.ugckit.roomservice.TXVideoViewManager
import com.tencent.qcloud.ugckit.roomservice.commondef.AnchorInfo
import com.tencent.qcloud.ugckit.roomservice.commondef.AudienceInfo
import com.tencent.qcloud.ugckit.roomservice.commondef.LoginInfo
import com.tencent.qcloud.ugckit.roomservice.commondef.MLVBCommonDef
import com.tencent.qcloud.ugckit.roomservice.commondef.MLVBCommonDef.CustomFieldOp.SET
import java.util.*

/**
 * @Description:  直播推流页面
 * @author:       45457
 * @date:         2020/9/29
 * @version:      V1.0
 */
@Route(path = LIVE_ANCHOR_PATH)
class LiveAnchorActivity : BaseDBActivity<LiveActivityAnchorBinding, LiveAnchorViewModel>(),
    IMLVBLiveRoomListener, OnClickListener {

    companion object {

        const val WHAT_UPDATE_LIVE_TIME = 1
    }

    private var mLiveRoom: MLVBLiveRoom? = null //直播房间

    private var mLiveChatMsgListAdapter: LiveChatMsgListAdapter? = null //聊天消息适配器

    private val mMainHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            msg.let {
                if (it.what == WHAT_UPDATE_LIVE_TIME) {
                    mLiveTime = it.obj as Long
                    if ((mLiveTime % 2) == 0L) {
                        liveHeartBeat()
                    }
                    if ((mLiveTime % 4) == 0L) {
                        addFakeAudience()
                    }
                    if ((mLiveTime % 300) == 0L) {
                        viewModel.checkNeedSendRoomTips()
                    }
                    onBroadcasterTimeUpdate(mLiveTime)
                }
            }
        }
    }

    private var mLiveTime = 0L  //直播时长

    // 定时的 Timer 去更新开播时间
    private var mBroadcastTimer: Timer? = null                  // 定时的 Timer
    private var mBroadcastTimerTask: BroadcastTimerTask? = null // 定时任务

    private var mUserOPAnimSet: AnimatorSet? = null //用户动画

    private val mPendingAnimUser = Stack<String>()   //用户动画队列

    //画面镜像，仅影响观众端
    private var _mirrorImage: Boolean = false

    override fun getViewModelClass(): Class<LiveAnchorViewModel> = LiveAnchorViewModel::class.java

    override fun getLayoutId(): Int = R.layout.live_activity_anchor

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        //设置滚动数字字符集
        viewBinding.anchorTvAudience.setCharacterLists(TickerUtils.provideNumberList())

        initLiveRoom()

        initLiveChatMsgList()

        subscribeViewModelEvent()

        viewModel.setupPageParams(intent.getIntExtra(EXTRA_LIVE_ID, 0))

        viewModel.start()
    }

    override fun onClick(v: View?) {
        if (v != null) {
            when (v.id) {
                R.id.anchor_cl_room_info -> {
//                    if (viewModel.getShowAudienceCount() > 0) {
//                        LiveAudienceListDialog.newInstance(
//                            viewModel.getLiveID(),
//                            viewModel.getShowAudienceCount()
//                        ).show(supportFragmentManager)
//                    }
                }

                R.id.anchor_tv_exit -> {
                    showStopAnchorTips()
                }

                R.id.anchor_ic_switch_camera -> {
                    mLiveRoom?.switchCamera()
                }

                R.id.anchor_ic_mirror -> {
                    _mirrorImage = _mirrorImage.not()
                    mLiveRoom?.setMirror(_mirrorImage)
                    TipsDialog()
                        .setContent("画面镜像仅影响观众端\n请跟观众确认效果")
                        .show(supportFragmentManager)
                }

                R.id.tv_push_url -> {
                    SystemUtil.copy(
                        this,
                        "直播地址",
                        viewBinding.tvPushUrl.text.toString(),
                        "复制成功，在推流软件中打开使用"
                    )
                }
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.roomDetails.observe(this, Observer {
            if (it.count == 0) {
                viewBinding.flAttachInfo.visibility = View.GONE
            } else {
                viewBinding.flAttachInfo.visibility = View.VISIBLE
                viewBinding.tvAttachInfo.text = it.count.toString()
                viewBinding.tvAttachInfo.post {
                    val ivAttachInfoLayoutParams: MarginLayoutParams =
                        viewBinding.ivAttachInfo.layoutParams as MarginLayoutParams
                    ivAttachInfoLayoutParams.marginEnd = (viewBinding.tvAttachInfo.width / 2)
                    viewBinding.ivAttachInfo.layoutParams = ivAttachInfoLayoutParams
                }
            }
        })

        viewModel.sendRoomTipsCommand.observe(this, Observer {
            addChatMsg(LiveChatMsg.getAnchorMsg(localUserId.toString(), it))
            mLiveRoom?.sendRoomCustomMsg(LiveIMConstants.IMCMD_ANCHOR_MSG, it, null)
        })

        viewModel.loginRoomServiceCommand.observe(this, Observer {
            loginRoomService(it)
        })

        viewModel.stopLiveCompletedEvent.observe(this, Observer {
            LiveStatisticsNavigation.navigate(
                mLiveTime,
                viewModel.getLikeCountValue(),
                viewModel.getCumAudienceCount(),
                viewModel.getNewFansCount(),
                viewModel.getReceivedResumeCount()
            ).start()
            finish()
        })

        //累计观看人数更新
        viewModel.cumulativeAudienceCount.observe(this, Observer {
            viewBinding.anchorTvAudience.text = it.toString()
            mLiveRoom?.setCustomInfo(
                SET,
                LiveIMConstants.ROOM_INFO_KEY_VIEW_COUNT,
                it.toString(),
                null
            )
        })

        viewModel.showCommodityCommand.observe(this, Observer {
            LiveCommodityDialog.newInstance(it, false).show(supportFragmentManager)
        })

        viewModel.showMeetingEnterpriseCommand.observe(this, Observer {
            LiveWebViewDialog.newInstance("https://m.jrzp.com/page/xiaoqihuicompany.aspx?hyid=6&ly=android")
                .show(supportFragmentManager)
        })

        viewModel.showShareDialogCommand.observe(this, Observer {
            StandardShareDialog.Builder()
                .setShareTitle(it.title)
                .setShareMomentTitle(it.title2)
                .setShareContent(it.content)
                .setSharePic(it.sharePic)
                .setShareUrl(
                    "${it.shareUrl}?id=${viewModel.getLiveID()}&paras=${
                        android.util.Base64.decode(mLiveRoom?.token, android.util.Base64.DEFAULT)
                    }"
                )
                .build().show(supportFragmentManager)
        })
    }

    override fun onResume() {
        super.onResume()
        if (viewBinding.flVideoGroup.childCount == 0) {
            addVideoView()
            mLiveRoom?.let {
                if (!it.isRunning) {
                    it.resume()
                }
            }
        }
    }

    /**
     * 添加视频播放view
     */
    private fun addVideoView() {
        val videoView = TXVideoViewManager.getVideoView(this)
        videoView?.let {
            videoView.parent?.let {
                (it as ViewGroup).removeView(videoView)
            }
            viewBinding.flVideoGroup.addView(
                videoView,
                LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            )
        }
    }

    /**
     * 初始化直播房间
     */
    private fun initLiveRoom() {
        addVideoView()
        mLiveRoom = MLVBLiveRoom.sharedInstance(this)
        mLiveRoom?.let {
            it.setListener(this)
            if (isOnlyCreateRoom()) {
//                it.stopLocalPreview()
            } else {
                it.startLocalPreview(getFrontCamera(), TXVideoViewManager.getVideoView(this))
            }
        }
    }

    private fun getFrontCamera(): Boolean {
        return intent.getBooleanExtra(EXTRA_FRONT_CAMERA, false)
    }

    /**
     * 登录房间服务
     */
    private fun loginRoomService(userIMSigID: String) {
        val loginInfo = LoginInfo().apply {
            sdkAppID = AppConstants.TENCENT_LIVE_APP_ID
            userID = localUserId.toString()
            userSig = userIMSigID
            userName = viewModel.roomDetails.value?.comName
            userAvatar = viewModel.roomDetails.value?.comLogo
        }

        mLiveRoom?.login(loginInfo, object : LoginCallback {
            override fun onSuccess() {
                XLog.d("onSuccess: 登录RoomService成功")
                createRoom()
            }

            override fun onError(errCode: Int, errInfo: String?) {
                viewModel.errorExit(getString(R.string.live_create_room_error_tips))
                XLog.d(
                    "onError: ${
                        String.format(
                            "登录RoomService失败, code=%s,error=%s",
                            errCode,
                            errInfo
                        )
                    }"
                )
            }
        })
    }

    private fun isOnlyCreateRoom(): Boolean {
        return intent?.getBooleanExtra(EXTRA_ONLY_CREATE_ROOM, false) ?: false
    }

    /**
     * 创建房间并推流
     */
    private fun createRoom() {
        mLiveRoom?.setCameraMuteImage(drawable.live_ic_pause_publish)
        val roomID = intent.getStringExtra(EXTRA_ROOM_ID)
        mLiveRoom?.createRoom(
            isOnlyCreateRoom(),
            roomID,
            getRoomInfo(),
            object : CreateRoomCallback {
                override fun onSuccess(RoomID: String?, pushUrl: String) {
                    XLog.d("onSuccess: ${String.format("创建直播间%s成功", "roomId")}")
                    createRoomSuccess()
                    if (isOnlyCreateRoom()) {
                        viewBinding.tvPushUrl.visibility = View.VISIBLE
                        viewBinding.tvPushUrl.text = pushUrl
                    }
                }

                override fun onError(errCode: Int, errInfo: String?) {
                    viewModel.errorExit(getString(R.string.live_create_room_error_tips))
                    XLog.d(
                        "onError: ${
                            String.format(
                                "创建直播间错误, code=%s,error=%s",
                                errCode,
                                errInfo
                            )
                        }"
                    )
                }
            })
    }

    private fun initLiveChatMsgList() {
        mLiveChatMsgListAdapter = LiveChatMsgListAdapter(this)
            .apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        val clickItem = data[position]
                        if (clickItem.msgType == LiveChatMsg.MSG_TYPE_CHAT) {
                            if (!clickItem.isSelf && !clickItem.isGuest()) {
                                LiveUserHomeDialog
                                    .newInstance(clickItem.senderUserID.toInt())
                                    .setOnReportClickListener(object : OnReportClickListener {
                                        override fun onReportClick(dialog: LiveUserHomeDialog) {
                                            showUserHomeOPMenu(clickItem, dialog)
                                        }
                                    })
                                    .show(supportFragmentManager)
                            }
                        }
                    }
                })
            }
        viewBinding.recyclerChatMsg.layoutManager = LinearLayoutManager(this)
        viewBinding.recyclerChatMsg.adapter = mLiveChatMsgListAdapter
    }

    /**
     * 直播间心跳
     */
    private fun liveHeartBeat() {
        //更新房间人数
        mLiveRoom?.sendRoomCustomMsg(
            LiveIMConstants.IMCMD_AUDIENCE_COUNT_CHANGE,
            viewModel.getCumAudienceCount().toString(),
            null
        )
        //更新点赞数量
        mLiveRoom?.sendRoomCustomMsg(
            LiveIMConstants.IMCMD_LIKE_COUNT_CHANGE,
            viewModel.getLikeCountValue().toString(),
            null
        )

        viewModel.handleAudienceCount()
    }

    /**
     * 添加直播人气
     */
    private fun addFakeAudience() {
        mLiveRoom?.sendRoomCustomMsg(
            LiveIMConstants.IMCMD_ENTER_LIVE,
            "",
            "http://img.jrzp.com/images_server/zph/20240103172437.png",
            "游客",
            null
        )
        audienceEnter("游客")
    }

    private fun showUserHomeOPMenu(
        clickItem: LiveChatMsg,
        dialog: LiveUserHomeDialog
    ) {
        Builder(this@LiveAnchorActivity)
            .setData(resources.getStringArray(array.live_anchor_audience_op_menu))
            .setOnItemClickListener { _, position ->
                when (position) {
                    0 -> {  //禁言
                        muteUser(clickItem, dialog)
                    }

                    1 -> {  //踢出
                        mLiveRoom?.sendRoomCustomMsg(
                            LiveIMConstants.IMCMD_KICK_OUT_MEMBER,
                            clickItem.senderUserID.toString(),
                            object : SendRoomCustomMsgCallback {
                                override fun onSuccess() {
                                    dialog.dismiss()
                                    showToast("已将该观众踢出直播间")
                                }

                                override fun onError(errCode: Int, errInfo: String?) {
                                    showToast("踢出失败,请重试")
                                }
                            }
                        )
                        mLiveRoom?.muteGroupMember(clickItem.senderUserID.toString(), null)
                    }
                }
            }.build().showBottom(dialog.view)
    }

    /**
     * 踢人
     */
    private fun muteUser(
        clickItem: LiveChatMsg,
        dialog: LiveUserHomeDialog
    ) {
        showLoading()
        mLiveRoom?.muteGroupMember(
            clickItem.senderUserID.toString(),
            object : MuteGroupMemberCallback {
                override fun onSuccess() {
                    showToast("禁言成功")
                    dialog.dismiss()
                    hiddenLoading()
                }

                override fun onError(errCode: Int, errInfo: String?) {
                    showToast(R.string.live_anchor_mute_failed)
                    hiddenLoading()
                }
            })
    }

    /**
     * 停止直播提示
     */
    private fun showStopAnchorTips() {
        ActionDialog.Builder()
            .setContent(getString(R.string.live_anchor_stop_tips))
            .setOnConfirmClickListener {
                exitRoom()
            }.build().show(supportFragmentManager)
    }

    /**
     * 退出房间并结束推流
     */
    private fun exitRoom(toStatistics: Boolean = true) {
        mLiveRoom?.exitRoom(object : ExitRoomCallback {
            override fun onSuccess() {
                viewModel.stopLive(toStatistics)
                XLog.d("onSuccess: 退出直播间")
            }

            override fun onError(errCode: Int, errInfo: String?) {
                showErrorAndQuit(getString(R.string.live_anchor_error_tips))
            }
        })
    }

    private fun getRoomInfo(): String {
        return ""
//    val roomInfo = intent.getParcelableExtra<RoomInfoData>(EXTRA_ROOM_INFO)
//    return try {
//    JSONObject()
//        .put("title", roomInfo.title)
//        .put("frontcover", roomInfo.frontCover)
//        .put("location", intent.getStringExtra(EXTRA_LIVE_ROOM_LOCATION))
//        .toString()
//    } catch (e: JSONException) {
//      ""
//    }
    }

    /**
     * 创建直播间成功
     */
    private fun createRoomSuccess() {
        startTimer()
        addChatMsg(
            LiveChatMsg.getSystemMsg(
                getString(R.string.live_anchor_system_tips)
            )
        )
        viewModel.checkNeedSendRoomTips()
    }

    /**
     *     /////////////////////////////////////////////////////////////////////////////////
     *     //
     *     //                      界面动画与时长统计
     *     //
     *     /////////////////////////////////////////////////////////////////////////////////
     */

    private fun initUserOPAnim() {
        val enterAnim =
            ObjectAnimator.ofFloat(
                viewBinding.tvUserOpMsg, "TranslationX",
                DensityUtils.getScreenWidth(this).toFloat(), 0f
            ).apply {
                duration = 500
                interpolator = FastOutSlowInInterpolator()
            }
        val hiddenAnim = ObjectAnimator.ofFloat(viewBinding.tvUserOpMsg, "alpha", 1f, 0f)
            .apply {
                duration = 500
                startDelay = 500
            }
        mUserOPAnimSet = AnimatorSet().apply {
            play(enterAnim).before(hiddenAnim)
            doOnEnd {
                if (mPendingAnimUser.isNotEmpty()) {
                    val user = mPendingAnimUser.pop()
                    prepareUserOPAnim(user)
                }
            }
        }
    }

    private fun prepareUserOPAnim(msg: String?) {
        msg?.let {
            mUserOPAnimSet?.let { animSet ->
                if (animSet.isRunning) {
                    try {
                        if (mPendingAnimUser.size > 100) {
                            mPendingAnimUser.removeAt(0)
                        }
                    } catch (e: Exception) {

                    }
                    mPendingAnimUser.add(msg)
                } else {
                    runUserOPAnim(msg)
                }
            } ?: let {
                initUserOPAnim()
                runUserOPAnim(msg)
            }
        }
    }

    /**
     * 运行用户操作动画
     */
    private fun runUserOPAnim(msg: String) {
        viewBinding.tvUserOpMsg.visibility = View.VISIBLE
        viewBinding.tvUserOpMsg.text = msg
        mUserOPAnimSet?.start()
    }

    /**
     * 开始直播计时
     */
    private fun startTimer() {
        //直播时间
        if (mBroadcastTimer == null) {
            mBroadcastTimer = Timer(true)
            mBroadcastTimerTask = BroadcastTimerTask(mMainHandler)
            mBroadcastTimer?.schedule(mBroadcastTimerTask, 1000, 1000)
        }
    }

    /**
     * 结束直播计时
     */
    private fun stopTimer() {
        //直播时间
        mBroadcastTimerTask?.cancel()
    }

    /**
     * 计时任务
     */
    private class BroadcastTimerTask(private val handler: Handler) : TimerTask() {

        var second: Long = 0
        override fun run() {
            ++second
            handler.sendMessage(Message.obtain().apply {
                this.what = WHAT_UPDATE_LIVE_TIME
                this.obj = second
            })
        }
    }

    /**
     * 直播时长更新
     */
    private fun onBroadcasterTimeUpdate(second: Long) {
        viewBinding.anchorTvBroadcastingTime.text = TCLiveUtils.formattedTime(second)
    }

    override fun onDebugLog(log: String?) {
    }

    /**
     * 接收自定义消息
     */
    override fun onRecvRoomCustomMsg(
        roomID: String?,
        userID: String?,
        userName: String?,
        userAvatar: String?,
        cmd: String?,
        message: String?
    ) {
        when (cmd) {
            LiveIMConstants.IMCMD_ENTER_LIVE -> { //观众加入
                audienceEnter(userName)
            }

            LiveIMConstants.IMCMD_EXIT_LIVE -> {  //观众退出
                viewModel.audienceExit()
            }

            LiveIMConstants.IMCMD_PRAISE -> { //点赞
                viewModel.addLike()
            }

            LiveIMConstants.IMCMD_SEND_RESUME -> {  //投递简历
                viewModel.addReceivedResume()
                prepareUserOPAnim(getString(R.string.live_anchor_user_send_resume_format, userName))
            }

            LiveIMConstants.IMCMD_FOLLOW_ANCHOR -> {  //关注主播
                viewModel.addNewFans()
                addChatMsg(
                    LiveChatMsg.getChatMsg(
                        userID.getOrDefault("0"),
                        userName,
                        getString(R.string.live_anchor_user_follow_msg)
                    )
                )
            }
        }
    }

    private fun audienceEnter(userName: String?) {
        viewModel.audienceEnter()
        prepareUserOPAnim(getString(string.live_anchor_user_enter_msg_format, userName))
    }

    override fun onAnchorEnter(anchorInfo: AnchorInfo?) {
    }

    override fun onAnchorExit(anchorInfo: AnchorInfo?) {
    }

    override fun onAudienceExit(audienceInfo: AudienceInfo?) {
    }

    override fun onQuitRoomPK(anchorInfo: AnchorInfo?) {
    }

    override fun onWarning(warningCode: Int, warningMsg: String?, extraInfo: Bundle?) {
    }

    override fun onRequestJoinAnchor(anchorInfo: AnchorInfo?, reason: String?) {
    }

    override fun onRecvRoomTextMsg(
        roomID: String?,
        userID: String?,
        userName: String?,
        userAvatar: String?,
        message: String?
    ) {
        addChatMsg(LiveChatMsg.getChatMsg(userID.getOrDefault("0"), userName, message))
    }

    private fun addChatMsg(chatMsg: LiveChatMsg) {
        viewBinding.recyclerChatMsg.toLast()
        mLiveChatMsgListAdapter?.add(chatMsg)
    }

    override fun onRequestRoomPK(anchorInfo: AnchorInfo?) {
    }

    override fun onKickoutJoinAnchor() {
    }

    override fun onAudienceEnter(audienceInfo: AudienceInfo?) {
    }

    override fun onError(errCode: Int, errMsg: String?, extraInfo: Bundle?) {
        exitRoom(false)
        if (errCode == MLVBCommonDef.LiveRoomErrorCode.ERROR_IM_FORCE_OFFLINE) { //IM被强制下线
            if (mLiveTime == 0L) {
                createRoom()  //一进来就被挤直接重新创建
            } else {
                showErrorAndQuit(getString(R.string.live_anchor_force_offline_tips))
            }
        } else {
            showErrorAndQuit(getString(R.string.live_audience_play_error_tips))
        }
    }

    override fun onRoomDestroy(roomID: String?) {
    }

    override fun onBackPressed() {
        showStopAnchorTips()
    }

    override fun onDestroy() {
        stopTimer()
        super.onDestroy()
    }
}