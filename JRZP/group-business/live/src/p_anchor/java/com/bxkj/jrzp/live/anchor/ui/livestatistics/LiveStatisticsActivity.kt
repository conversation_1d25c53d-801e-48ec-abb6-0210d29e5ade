package com.bxkj.jrzp.live.anchor.ui.livestatistics

import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.databinding.LiveActivityStatisticsBinding
import com.bxkj.jrzp.live.utils.TCLiveUtils

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/26
 * @version: V1.0
 */
@Route(path = LiveStatisticsNavigation.PATH)
class LiveStatisticsActivity :
    BaseDBActivity<LiveActivityStatisticsBinding, LiveStatisticsViewModel>() {

    override fun getViewModelClass(): Class<LiveStatisticsViewModel> =
        LiveStatisticsViewModel::class.java

    override fun getLayoutId(): Int = R.layout.live_activity_statistics

    override fun initPage(savedInstanceState: Bundle?) {

        viewBinding.tvReceivedResume.text =
            intent.getIntExtra(LiveStatisticsNavigation.EXTRA_RECEIVED_RESUME, 0).toString()

        viewBinding.tvLikeCount.text =
            intent.getIntExtra(LiveStatisticsNavigation.EXTRA_LIKE_COUNT, 0).toString()

        viewBinding.tvAudienceCount.text =
            intent.getIntExtra(LiveStatisticsNavigation.EXTRA_AUDIENCE_COUNT, 0).toString()

        viewBinding.tvNewFans.text =
            intent.getIntExtra(LiveStatisticsNavigation.EXTRA_NEW_FANS_COUNT, 0).toString()

        viewBinding.tvLiveTime.text =
            getString(
                R.string.live_statistics_time_format,
                TCLiveUtils.formattedTime(
                    intent.getLongExtra(
                        LiveStatisticsNavigation.EXTRA_LIVE_TIME,
                        0L
                    )
                )
            )

        viewBinding.tvConfirm.setOnClickListener { finish() }
    }

}