<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 取消界面转场动画使用-->
    <style name="Live_AnchorActivityTheme" parent="JrzpAppTheme">
        <item name="android:windowAnimationStyle">@style/Live_NoAnimStyle</item>
    </style>

    <style name="Live_NoAnimStyle">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@anim/common_in_form_left</item>
        <item name="android:activityCloseExitAnimation">@anim/common_out_to_right</item>
        <!--        <item name="android:taskOpenEnterAnimation">@null</item>-->
        <!--        <item name="android:taskOpenExitAnimation">@null</item>-->
        <!--        <item name="android:taskCloseEnterAnimation">@null</item>-->
        <!--        <item name="android:taskCloseExitAnimation">@null</item>-->
        <!--        <item name="android:taskToFrontEnterAnimation">@null</item>-->
        <!--        <item name="android:taskToFrontExitAnimation">@null</item>-->
        <!--        <item name="android:taskToBackEnterAnimation">@null</item>-->
        <!--        <item name="android:taskToBackExitAnimation">@null</item>-->
    </style>

</resources>