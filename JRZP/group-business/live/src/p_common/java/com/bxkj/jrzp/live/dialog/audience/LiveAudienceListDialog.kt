package com.bxkj.jrzp.live.dialog.audience

import android.content.Intent
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.ListFooterPlaceholder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.jrzp.live.BR
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.data.LiveAudienceInfo
import com.bxkj.jrzp.live.databinding.LiveDialogAudienceListBinding
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.tencent.qcloud.ugckit.roomservice.IMLVBLiveRoomListener.GetAudienceListCallback
import com.tencent.qcloud.ugckit.roomservice.MLVBLiveRoom
import com.tencent.qcloud.ugckit.roomservice.commondef.AudienceInfo
import java.util.ArrayList

/**
 * @Description: 直播观众列表
 * @author:45457
 * @date: 2020/11/5
 * @version: V1.0
 */
class LiveAudienceListDialog :
    BaseDBDialogFragment<LiveDialogAudienceListBinding, LiveAudienceListViewModel>() {

    companion object {

        private const val MAX_AUDIENCE_COUNT = 30

        const val TO_USER_HOME_PAGE_CODE = 1

        const val EXTRA_LIVE_ID = "LIVE_ID"
        const val EXTRA_AUDIENCE_COUNT = "AUDIENCE_COUNT"

        fun newInstance(liveID: Int, audienceCount: Int): LiveAudienceListDialog {
            return LiveAudienceListDialog().apply {
                arguments = bundleOf(
                    EXTRA_LIVE_ID to liveID,
                    EXTRA_AUDIENCE_COUNT to audienceCount.let {
                        if (it > MAX_AUDIENCE_COUNT) {
                            MAX_AUDIENCE_COUNT
                        } else {
                            it
                        }
                    }
                )
            }
        }
    }

    override fun getViewModelClass(): Class<LiveAudienceListViewModel> =
        LiveAudienceListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.live_dialog_audience_list

    override fun initPage() {
        viewBinding.viewModel = viewModel

        arguments?.let {
            viewModel.setupPageParams(it.getInt(EXTRA_LIVE_ID), it.getInt(EXTRA_AUDIENCE_COUNT))
        }

        setupLiveAudienceList()

        MLVBLiveRoom.sharedInstance(activity).getAudienceList(object : GetAudienceListCallback {
            override fun onSuccess(audienceInfoList: ArrayList<AudienceInfo>?) {
                audienceInfoList?.let {
                    viewModel.setupIMAudienceList(it)
                } ?: let {
                    viewModel.setupIMAudienceList(emptyList())
                }
            }

            override fun onError(errCode: Int, errInfo: String?) {
                viewModel.setupIMAudienceList(emptyList())
            }
        })
    }

    private fun setupLiveAudienceList() {
        val liveAudienceListAdapter = MultiTypeAdapter(activity)
            .apply {
                register(
                    LiveAudienceInfo::class.java,
                    DefaultViewBinder<LiveAudienceInfo>(R.layout.live_recycler_audience_list_item, BR.data)
                        .apply {
                            setOnItemClickListener(object :
                                DefaultViewBinder.OnItemClickListener<LiveAudienceInfo> {
                                override fun onItemClicked(v: View, position: Int, item: LiveAudienceInfo) {
                                    if (v.id == R.id.tv_follow) {
                                        afterLogin {
                                            viewModel.followUser(item)
                                        }
                                    } else {
                                        UserHomeNavigation.navigate(item.userID).startForResult(
                                            this@LiveAudienceListDialog,
                                            TO_USER_HOME_PAGE_CODE
                                        )
                                    }
                                }
                            }, R.id.tv_follow)
                        }
                )
                register(
                    ListFooterPlaceholder::class.java,
                    DefaultViewBinder<ListFooterPlaceholder>(
                        R.layout.live_recycler_audience_list_footer,
                        BR.data,
                        false
                    )
                )
            }

        viewBinding.includeAudienceList.recyclerContent.layoutManager = LinearLayoutManager(activity)
        viewModel.audienceListViewModel.setAdapter(liveAudienceListAdapter)
    }

    override fun getTheme(): Int {
        return R.style.BaseDialogFragmentStyle_NoDim
    }

    override fun enableBottomSheet(): Boolean {
        return true
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let {
            it.setWindowAnimations(R.style.BottomPopupAnim)
            it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
                val layoutParams = layout.layoutParams
                layoutParams.height = getLayoutHeight()
                layout.layoutParams = layoutParams
            }
        }
    }

    private fun getLayoutHeight(): Int {
        val peekHeight = resources.displayMetrics.heightPixels
        //设置弹窗高度为屏幕高度的3/4
        return peekHeight - peekHeight / 3
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        viewModel.handleActivityResult(requestCode, resultCode, data)
    }
}