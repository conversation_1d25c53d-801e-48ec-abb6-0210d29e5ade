<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.live.dialog.userhome.LiveUserHomeViewModel" />
  </data>

  <FrameLayout style="@style/match_wrap">

    <androidx.constraintlayout.widget.ConstraintLayout
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/live_user_home_content_layout_margin_top"
      android:background="@drawable/live_bg_user_home">

      <TextView
        android:id="@+id/tv_name"
        style="@style/Text.18sp.FFFFFF.Bold"
        android:layout_marginTop="@dimen/live_user_home_name_margin_top"
        android:text="@{viewModel.userInfo.name}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_desc"
        style="@style/Text.12sp.A3A3A3"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_16"
        android:maxLines="2"
        android:text="@{viewModel.userInfo.desc}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

      <LinearLayout
        android:id="@+id/ll_user_info_count"
        style="@style/match_wrap"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_12"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc">

        <LinearLayout
          android:id="@+id/ll_follow_count"
          style="@style/wrap_wrap"
          android:layout_weight="1"
          android:gravity="center"
          android:onClick="@{onClickListener}"
          android:orientation="vertical">

          <TextView
            style="@style/Text.18sp.FFFFFF.Bold"
            android:gravity="center"
            android:text="@{String.valueOf(viewModel.userInfo.followCount)}" />

          <TextView
            style="@style/Text.12sp.A3A3A3"
            android:text="@string/live_user_home_follow" />

        </LinearLayout>

        <LinearLayout
          android:id="@+id/ll_fans_count"
          style="@style/wrap_wrap"
          android:layout_weight="1"
          android:gravity="center"
          android:onClick="@{onClickListener}"
          android:orientation="vertical">

          <TextView
            style="@style/common_Text.16sp.FFFFFF.Bold"
            android:gravity="center"
            android:text="@{String.valueOf(viewModel.userInfo.fansCount)}" />

          <TextView
            style="@style/Text.12sp.A3A3A3"
            android:text="@string/live_user_home_fans" />

        </LinearLayout>

        <LinearLayout
          android:id="@+id/ll_notice_count"
          style="@style/wrap_wrap"
          android:layout_weight="1"
          android:gravity="center"
          android:onClick="@{onClickListener}"
          android:orientation="vertical">

          <TextView
            style="@style/common_Text.16sp.FFFFFF.Bold"
            android:gravity="center"
            android:text="@{String.valueOf(viewModel.userInfo.actionCount)}" />

          <TextView
            style="@style/Text.12sp.A3A3A3"
            android:text="@string/live_user_home_notice" />

        </LinearLayout>

      </LinearLayout>

      <LinearLayout
        style="@style/match_wrap"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:paddingStart="@dimen/dp_18"
        android:paddingEnd="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_user_info_count">

        <TextView
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/dp_40"
          android:layout_weight="1"
          android:background="@drawable/live_bg_follow_user"
          android:gravity="center"
          android:onClick="@{()->viewModel.followUser()}"
          android:text="@{viewModel.userInfo.isFollowed?@string/live_user_home_followed:@string/live_user_home_add_follow}"
          android:textColor="@color/live_cl_follow_text"
          bind:selected="@{viewModel.userInfo.isFollowed}" />

        <TextView
          style="@style/Text.16sp.FFFFFF"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/dp_40"
          android:layout_marginStart="@dimen/dp_6"
          android:layout_weight="1"
          android:background="@drawable/live_bg_view_home_page"
          android:gravity="center"
          android:onClick="@{()->viewModel.toUserHomePage()}"
          android:text="@string/live_user_home_enter_home" />

      </LinearLayout>

      <ImageView
        android:id="@+id/iv_report"
        style="@style/wrap_wrap"
        android:layout_marginTop="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_18"
        android:src="@drawable/live_ic_user_home_report"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
      style="@style/wrap_wrap"
      android:layout_gravity="center_horizontal">

      <de.hdodenhof.circleimageview.CircleImageView
        android:layout_width="@dimen/live_user_home_avatar_size"
        android:layout_height="@dimen/live_user_home_avatar_size"
        android:onClick="@{()->viewModel.toUserHomePage()}"
        app:civ_border_color="@color/common_black"
        app:civ_border_width="@dimen/dp_2"
        bind:imgUrl="@{viewModel.userInfo.photo}" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:src="@drawable/live_ic_auth_personal_tag"
        android:visibility="@{viewModel.userInfo.personalAuthSuccess()?View.VISIBLE:View.GONE}" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:src="@drawable/live_ic_auth_enterprise_tag"
        android:visibility="@{viewModel.userInfo.higherEnterpriseAuth()?View.VISIBLE:View.GONE}" />

    </FrameLayout>

  </FrameLayout>
</layout>