package com.bxkj.jrzp.live.room.ui.livenotice

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.bigkoo.pickerview.builder.TimePickerBuilder
import com.bigkoo.pickerview.listener.OnTimeSelectListener
import com.bigkoo.pickerview.view.TimePickerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.kotlin.applyCustomConfig
import com.bxkj.common.util.kotlin.format
import com.bxkj.common.util.kotlin.toCalender
import com.bxkj.common.util.qmui.QMUIKeyboardHelper
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.ui.activity.joinmembership.UpgradeVipNavigation
import com.bxkj.enterprise.ui.activity.selectrealtejob.SelectRelateJobNavigation
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.databinding.LiveActivityLiveNoticeBinding
import com.bxkj.jrzp.live.notice.ui.list.LiveNoticeListNavigation
import com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolNavigation
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route
import java.util.Calendar

/**
 * @Description: 添加直播预告
 * @author:45457
 * @date: 2020/10/26
 * @version: V1.0
 */
@Route(path = LiveNoticeNavigation.PATH)
class LiveNoticeActivity :
  BaseDBActivity<LiveActivityLiveNoticeBinding, LiveNoticeViewModel>(), OnClickListener {

  companion object {
    const val TO_SELECT_RELATE_JOB_CODE = 1
    const val TO_SELECT_RELATE_SCHOOL_CODE = 2
    const val TO_UPGRADE_VIP_CODE = 3

    private const val LIVE_TIME_PATTERN = "yyyy-MM-dd HH:mm"
  }

  private var mLiveStartTimePicker: TimePickerView? = null
  private var mLiveEndTimePicker: TimePickerView? = null

  override fun getViewModelClass(): Class<LiveNoticeViewModel> =
    LiveNoticeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.live_activity_live_notice

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewModel.start(intent.getIntExtra(LiveNoticeNavigation.EXTRA_LIVE_NOTICE_ID, 0))
  }

  private fun subscribeViewModelEvent() {
    viewModel.saveSuccessEvent.observe(this, Observer {
      LiveNoticeListNavigation.navigate().start()
      showToast(R.string.save_success)
      finish()
    })

    viewModel.upgradeVipTipsCommand.observe(this, Observer {
      ActionDialog.Builder()
        .setContent(getString(R.string.live_add_notice_no_vip_tips))
        .setConfirmText(getString(R.string.live_create_room_upgrade_vip))
        .setCancelable(false)
        .setCancelText(getString(R.string.live_add_notice_upgrade_vip_close))
        .setOnCancelClickListener {
          finish()
        }
        .setOnConfirmClickListener {
          UpgradeVipNavigation.navigate().startForResult(
            this,
            TO_UPGRADE_VIP_CODE
          )
        }.build().show(supportFragmentManager)
    })
  }

  override fun onClick(v: View?) {
    QMUIKeyboardHelper.hideKeyboard(v)
    if (v != null) {
      when (v.id) {
        R.id.rl_cover -> {
          toSelectLiveFrontCover()
        }

        R.id.tv_live_start_time -> {
          showStartTimePicker()
        }

        R.id.tv_live_end_time -> {
          showEndTimePicker()
        }

        R.id.tv_relate_job -> {
          SelectRelateJobNavigation.navigate(viewModel.getSelectedJobList())
            .startForResult(this, TO_SELECT_RELATE_JOB_CODE)
        }

        R.id.tv_relate_school -> {
          SelectRelateSchoolNavigation.navigate(viewModel.getSelectedSchoolList())
            .startForResult(this, TO_SELECT_RELATE_SCHOOL_CODE)
        }
      }
    }
  }

  private fun toSelectLiveFrontCover() {
    PermissionUtils.requestPermission(
      this,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_select_img_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector.create(this@LiveNoticeActivity)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .forResult(PictureConfig.CHOOSE_REQUEST)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(R.string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  private fun showStartTimePicker() {
    if (mLiveStartTimePicker == null) {
      mLiveStartTimePicker =
        getTimePicker(
          OnTimeSelectListener { date, _ ->
            viewModel.setupLiveStartTime(date.format(LIVE_TIME_PATTERN))
          },
          Calendar.getInstance()
        )
    }
    mLiveStartTimePicker?.show()
  }

  private fun showEndTimePicker() {
    var liveEndTimeStart = Calendar.getInstance()
    viewModel.getLiveStartTime()?.let {
      liveEndTimeStart = it.toCalender(LIVE_TIME_PATTERN)
    }
    mLiveEndTimePicker = getTimePicker(
      OnTimeSelectListener { date, _ ->
        viewModel.setupLiveEndTime(date.format(LIVE_TIME_PATTERN))
      },
      liveEndTimeStart
    )
    mLiveEndTimePicker?.show()
  }

  private fun getTimePicker(
    onTimeSelectListener: OnTimeSelectListener,
    startTime: Calendar
  ): TimePickerView? {
    return TimePickerBuilder(this, onTimeSelectListener).applyCustomConfig()
      .setType(booleanArrayOf(false, true, true, true, true, false))
      .setDate(startTime)
      .setDecorView(window.decorView.findViewById(android.R.id.content))
      .setRangDate(
        startTime, Calendar.getInstance()
          .apply {
            add(Calendar.YEAR, 1)
            set(Calendar.MONTH, 6)
          }).build()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}