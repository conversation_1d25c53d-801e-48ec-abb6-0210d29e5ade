package com.bxkj.jrzp.live.room.ui.livenotice

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.fixImgUrl
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.ui.activity.selectrealtejob.SelectRelateJobNavigation
import com.bxkj.jrzp.live.LiveConstants
import com.bxkj.jrzp.live.api.LiveRepository
import com.bxkj.jrzp.live.room.data.PendingLiveData
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.personal.data.CompanyItemData
import com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolNavigation
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/26
 * @version: V1.0
 */
class LiveNoticeViewModel @Inject constructor(
  private val mUploadRepository: UploadRepository,
  private val mLiveRepository: LiveRepository,
  private val mUserRepository: OpenUserRepository
) : BaseViewModel() {

  //关联职位
  val liveRelatePosition = MutableLiveData<String>()

  //关联学校
  val liveRelateSchool = MutableLiveData<String>()

  //直播预告
  val liveNotice = MutableLiveData<PendingLiveData>()

  //保存成功
  val saveSuccessEvent = LiveEvent<Void>()

  //升级vip
  val upgradeVipTipsCommand = LiveEvent<Void>()

  //待上传封面id
  private var mPendingAttachCoverID = 0

  //关联职位id
  private var mLiveRelateJobIDs: String = ""

  //关联学校id
  private var mLiveRelateSchoolIDs: String = ""

  //选中职位列表
  private var mSelectedJobList: List<PositionItemBean>? = null

  //选中学校列表
  private var mSelectedSchoolList: List<CompanyItemData>? = null

  private var mLiveNoticeID: Int = 0

  fun start(liveNoticeID: Int) {
    mLiveNoticeID = liveNoticeID
    if (liveNoticeID == 0) {
      setupDefaultNotice()
    } else {
      getLiveNoticeDetails(liveNoticeID)
    }
    checkUserVipLevel()
  }

  private fun getLiveNoticeDetails(liveNoticeID: Int) {
    viewModelScope.launch {
      showLoading()
      mLiveRepository.getLiveNoticeDetails(liveNoticeID)
        .handleResult({
          it?.let {
            liveNotice.value = it
            it.relData?.let { jobList ->
              setupSelectedJob(jobList)
            }
            it.schoolData?.let { schoolList ->
              setupSelectedSchool(schoolList)
            }
          } ?: setupDefaultNotice()
        }, {
          setupDefaultNotice()
        }, {
          hideLoading()
        })
    }
  }

  private fun setupDefaultNotice() {
    liveNotice.value = PendingLiveData()
  }

  fun setupLiveStartTime(time: String) {
    liveNotice.value?.setupStartTime(time)
  }

  fun setupLiveEndTime(time: String) {
    liveNotice.value?.setupEndTime(time)
  }

  fun getLiveStartTime(): String? {
    return liveNotice.value?.ksDate
  }

  fun getSelectedJobList(): List<PositionItemBean>? {
    return mSelectedJobList
  }

  fun getSelectedSchoolList(): List<CompanyItemData>? {
    return mSelectedSchoolList
  }

  fun submit() {
    liveNotice.value?.let {

      if (it.pic.isNullOrEmpty()) {
        showToast("未选择封面")
        return
      }
      if (it.title.isNullOrEmpty()) {
        showToast("未填写标题")
        return
      }
      if (it.ksDate.isNullOrEmpty()) {
        showToast("未选择开始时间")
        return
      }
      if (it.jsDate.isNullOrEmpty()) {
        showToast("未选择结束时间")
        return
      }
      if (mLiveRelateJobIDs.isEmpty()) {
        showToast("未选择关联职位")
        return
      }

      if (mLiveNoticeID == 0) {
        addLiveNotice(it)
      } else {
        updateLiveNotice(it)
      }
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    when (requestCode) {
      PictureConfig.CHOOSE_REQUEST -> {
        if (resultCode == Activity.RESULT_OK && data != null) {
          uploadLiveFrontCover(data.getSelectedFirstMediaPath())
        }
      }
      LiveNoticeActivity.TO_SELECT_RELATE_JOB_CODE -> {
        if (resultCode == Activity.RESULT_OK && data != null) {
          val selectedPosition =
            data.getParcelableArrayListExtra<PositionItemBean>(SelectRelateJobNavigation.EXTRA_SELECTED_ITEMS)

          if (!selectedPosition.isNullOrEmpty()) {
            setupSelectedJob(selectedPosition)
          }
        }
      }
      LiveNoticeActivity.TO_SELECT_RELATE_SCHOOL_CODE -> {
        if (resultCode == Activity.RESULT_OK && data != null) {
          val selectedSchool =
            data.getParcelableArrayListExtra<CompanyItemData>(SelectRelateSchoolNavigation.EXTRA_SELECTED_SCHOOL)

          if (!selectedSchool.isNullOrEmpty()) {
            setupSelectedSchool(selectedSchool)
          }
        }
      }
      LiveNoticeActivity.TO_UPGRADE_VIP_CODE -> {
        checkUserVipLevel()
      }
    }
  }

  private fun checkUserVipLevel() {
    viewModelScope.launch {
      showLoading()
      mUserRepository.getUserVipLevel(getSelfUserID())
        .handleResult({
          it?.let {
            if (it.isFreeMember()) {
              upgradeVipTipsCommand.call()
            }
          }
        }, {
          showToast(it.errMsg)
          finishPage()
        }, {
          hideLoading()
        })
    }
  }

  private fun addLiveNotice(notice: PendingLiveData) {
    viewModelScope.launch {
      showLoading()
      mLiveRepository.addLiveNotice(
        getSelfUserID(),
        LiveConstants.getLiveID(),
        notice,
        mPendingAttachCoverID,
        mLiveRelateJobIDs,
        mLiveRelateSchoolIDs
      ).handleResult({
        saveSuccessEvent.call()
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  private fun updateLiveNotice(notice: PendingLiveData) {
    viewModelScope.launch {
      showLoading()
      mLiveRepository.updateLiveNotice(
        getSelfUserID(),
        mLiveNoticeID,
        notice,
        mPendingAttachCoverID,
        mLiveRelateJobIDs,
        mLiveRelateSchoolIDs
      ).handleResult({
        saveSuccessEvent.call()
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  private fun setupSelectedJob(selectedPosition: List<PositionItemBean>) {
    mSelectedJobList = selectedPosition
    val jobIDsBuilder = StringBuilder()
    val jobNamesBuilder = StringBuilder()
    selectedPosition.forEach { item ->
      jobIDsBuilder.append(item.id).append(",")
      jobNamesBuilder.append(item.name).append(",")
    }
    mLiveRelateJobIDs = jobIDsBuilder.substring(0, jobIDsBuilder.length - 1)
    liveRelatePosition.value = jobNamesBuilder.substring(0, jobNamesBuilder.length - 1)
  }

  private fun setupSelectedSchool(selectedSchool: List<CompanyItemData>) {
    mSelectedSchoolList = selectedSchool
    val schoolIDsBuilder = StringBuilder()
    val schoolNamesBuilder = StringBuilder()
    selectedSchool.forEach { item ->
      schoolIDsBuilder.append(item.uid).append(",")
      schoolNamesBuilder.append(item.comName).append(",")
    }
    mLiveRelateSchoolIDs = schoolIDsBuilder.substring(0, schoolIDsBuilder.length - 1)
    liveRelateSchool.value = schoolNamesBuilder.substring(0, schoolNamesBuilder.length - 1)
  }

  private fun uploadLiveFrontCover(imgPath: String) {
    viewModelScope.launch {
      showLoading()
      mUploadRepository.uploadFileV3(
        imgPath,
        UploadFileRequestParams.getImageUploadParams(
          getSelfUserID(),
          UploadFileRequestParams.PATH_NAME_MOMENT
        )
      ).handleResult({
        it?.let {
          liveNotice.value?.setupCover(it.url.fixImgUrl())
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

}
