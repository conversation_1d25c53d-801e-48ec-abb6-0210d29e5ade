package com.bxkj.jrzp.live.room.ui.createroom

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.live.LiveConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/10
 * @version: V1.0
 */
class CreateLiveRoomNavigation {

  companion object {
    const val PATH = "${LiveConstants.DIRECTORY}/createroom"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}