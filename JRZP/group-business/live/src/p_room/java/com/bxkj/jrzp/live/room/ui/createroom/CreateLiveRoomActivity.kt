package com.bxkj.jrzp.live.room.ui.createroom

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.EditInfoDialog
import com.bxkj.common.widget.dialog.EditInfoDialog.OnSendClickListener
import com.bxkj.enterprise.ui.activity.joinmembership.UpgradeVipNavigation
import com.bxkj.enterprise.ui.activity.selectrealtejob.SelectRelateJobNavigation
import com.bxkj.jrzp.live.LiveConstants
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.anchor.ui.anchor.navigateToLiveAnchor
import com.bxkj.jrzp.live.databinding.LiveActivityCreateLiveRoomBinding
import com.bxkj.jrzp.live.room.data.LiveRoomTypeData
import com.bxkj.jrzp.live.room.data.PendingLiveData
import com.bxkj.jrzp.live.room.ui.createroom.LiveRoomTypeListAdapter.OnTypeClickListener
import com.bxkj.jrzp.live.room.ui.createroom.PendingLiveListDialog.OnLiveSelectedListener
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.tencent.liteav.support.beauty.model.ItemInfo
import com.tencent.liteav.support.beauty.model.TabInfo
import com.tencent.liteav.support.beauty.view.LiveBeautyPanel.OnBeautyListener
import com.tencent.qcloud.ugckit.roomservice.MLVBLiveRoom
import com.tencent.qcloud.ugckit.roomservice.TXVideoViewManager
import com.therouter.router.Route

/**
 * @Description: 创建直播间
 * @author:45457
 * @date: 2020/10/10
 * @version: V1.0
 */
@Route(path = CreateLiveRoomNavigation.PATH)
class CreateLiveRoomActivity :
  BaseDBActivity<LiveActivityCreateLiveRoomBinding, CreateLiveRoomViewModel>(), OnClickListener {

  companion object {

    const val TO_SELECT_RELATE_JOB_CODE = 1
    const val TO_UPGRADE_VIP_CODE = 2
  }

  private var mLiveRoom: MLVBLiveRoom? = null

  private var mFrontCamera: Boolean = true

  private var _mirrorImage: Boolean = true

  private var mLiveTypeListAdapter: LiveRoomTypeListAdapter? = null

  private var onlyCreateRoom: Boolean = false

  override fun getViewModelClass(): Class<CreateLiveRoomViewModel> =
    CreateLiveRoomViewModel::class.java

  override fun getLayoutId(): Int = R.layout.live_activity_create_live_room

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    if (UserUtils.TEST_LIVE_ANCHOR_USER_IDS.contains(UserUtils.getUserId().toString())) {
      viewBinding.cbLiveByOther.let {
        it.visibility = View.VISIBLE
        it.setOnCheckedChangeListener { buttonView, isChecked ->
          onlyCreateRoom = isChecked
        }
      }
    }


    setupLiveTypeList()

    checkLivePermission()

    subscribeViewModelEvent()

    viewModel.getStartLiveType()
  }

  private fun setupLiveTypeList() {
    mLiveTypeListAdapter = LiveRoomTypeListAdapter().apply {
      setOnTypeClickListener(object : OnTypeClickListener {
        override fun onTypeClick(type: LiveRoomTypeData) {
          viewModel.setupLiveType(type)
        }
      })
    }

    viewBinding.recyclerLiveType.layoutManager =
      LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
    viewBinding.recyclerLiveType.addItemDecoration(
      LineItemDecoration.Builder()
        .divider(ContextCompat.getDrawable(this, R.drawable.divider_24))
        .orientation(LinearLayoutManager.HORIZONTAL)
        .drawHeader(true)
        .drawFoot(true)
        .build()
    )
    viewBinding.recyclerLiveType.adapter = mLiveTypeListAdapter
  }

  private fun checkLivePermission() {
    PermissionUtils.requestPermission(
      this,
      getString(R.string.live_create_room_permission_tips_title),
      getString(R.string.live_create_room_permission_tips_content),
      object : OnRequestResultListener {
        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(R.string.live_create_room_permission_failed_tips))
          finish()
        }

        override fun onRequestSuccess() {
          viewModel.start()
          initLivePreview()
          setupBeautyControl()
        }
      },
      Permission.CAMERA, Permission.RECORD_AUDIO
    )
  }

  private fun setupBeautyControl() {
    val beautyControl = viewBinding.beautyPannel
    beautyControl.setBeautyManager(mLiveRoom?.beautyManager)
    beautyControl.setOnBeautyListener(object : OnBeautyListener {
      override fun onClick(
        tabInfo: TabInfo?,
        tabPosition: Int,
        itemInfo: ItemInfo?,
        itemPosition: Int
      ): Boolean {
        return false
      }

      override fun onTabChange(tabInfo: TabInfo?, position: Int) {
      }

      override fun onLevelChanged(
        tabInfo: TabInfo?,
        tabPosition: Int,
        itemInfo: ItemInfo?,
        itemPosition: Int,
        beautyLevel: Int
      ): Boolean {
        return false
      }

      override fun onClose(): Boolean {
        beautyControl.visibility = View.GONE
        return true
      }
    })
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.iv_cover -> {
          toSelectLiveFrontCover()
        }

        R.id.tv_title -> {
          showEditTitleDialog()
        }

        R.id.tv_relate_job -> {
          toSelectRelateJob()
        }

        R.id.tv_switch_camera -> {
          mFrontCamera = !mFrontCamera
          mLiveRoom?.switchCamera()
        }

        R.id.tv_beauty -> {
          viewBinding.beautyPannel.visibility = View.VISIBLE
        }

//                R.id.tv_mirror -> {
//                    _mirrorImage = _mirrorImage.not()
//                    mLiveRoom?.setMirror(_mirrorImage)
//                }

        R.id.tv_agreement -> {
          WebNavigation.navigate(CommonApiConstants.LIVE_AGREEMENT_URL).start()
        }

        R.id.ll_agreement, R.id.tv_privacy -> {
          viewBinding.tvPrivacy.isSelected = !viewBinding.tvPrivacy.isSelected
        }

        R.id.tv_start_live -> {
          if (viewBinding.tvPrivacy.isSelected) {
            viewModel.createRoomPreCheck(mLiveTypeListAdapter?.getSelectItem()?.id)
          } else {
            showToast(getString(R.string.live_create_room_no_agree_privacy_tips))
          }
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.showSelectPendingLiveCommand.observe(this, Observer {
      PendingLiveListDialog
        .newInstance(it)
        .setOnLiveSelectedListener(object : OnLiveSelectedListener {
          override fun onLiveSelected(pendingLive: PendingLiveData) {
            viewModel.startLiveByPendingLiveInfo(pendingLive)
          }

          override fun onStartNow() {
            viewModel.startLivePreCheck()
          }
        })
        .show(supportFragmentManager)
    })

    viewModel.toLiveAnchorCommand.observe(this) {
      navigateToLiveAnchor(mFrontCamera, it, LiveConstants.getLiveID(), onlyCreateRoom).start()
      finish()
    }

    viewModel.upgradeVipTipsCommand.observe(this, Observer {
      ActionDialog.Builder()
        .setContent(getString(R.string.live_create_room_no_vip_tips))
        .setConfirmText(getString(R.string.live_create_room_upgrade_vip))
        .setCancelText(getString(R.string.live_create_room_upgrade_vip_close))
        .setCancelable(false)
        .setOnCancelClickListener {
          finish()
        }
        .setOnConfirmClickListener {
          UpgradeVipNavigation.navigate().startForResult(this, TO_UPGRADE_VIP_CODE)
        }.build().show(supportFragmentManager)
    })
  }

  /**
   * 选择直播封面
   */
  private fun toSelectLiveFrontCover() {
    PermissionUtils.requestPermission(
      this,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_select_img_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector.create(this@CreateLiveRoomActivity)
            .openGallery(SelectMimeType.ofImage())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .setImageEngine(GlideEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .forResult(PictureConfig.CHOOSE_REQUEST)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(R.string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  private fun showEditTitleDialog() {
    EditInfoDialog(this).apply {
      hint = getString(R.string.live_create_room_title_hint)
      initText = viewBinding.tvTitle.text.toString().trim()
      inputMinLength = 0
      inputMaxLength = 16
      onConfirmClickListener = object : OnSendClickListener {
        override fun onSendClicked(commentDialog: EditInfoDialog, content: String) {
          commentDialog.dismiss()
          viewModel.stepRoomTitle(content)
        }
      }
    }.show()
  }

  /**
   * 选择关联职位
   */
  private fun toSelectRelateJob() {
    SelectRelateJobNavigation.navigate().startForResult(this, TO_SELECT_RELATE_JOB_CODE)
  }

  private fun initLivePreview() {
    addVideoView()

    mLiveRoom = MLVBLiveRoom.sharedInstance(this)

    mLiveRoom?.startLocalPreview(true, TXVideoViewManager.getVideoView(this))
  }

  /**
   * 添加视频播放view
   */
  private fun addVideoView() {
    val videoView = TXVideoViewManager.getVideoView(this)
    videoView?.let {
      videoView.parent?.let {
        (it as ViewGroup).removeView(videoView)
      }
      viewBinding.flVideoGroup.addView(
        videoView,
        LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
      )
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onBackPressed() {
    mLiveRoom?.stopLocalPreview()
    super.onBackPressed()
  }
}