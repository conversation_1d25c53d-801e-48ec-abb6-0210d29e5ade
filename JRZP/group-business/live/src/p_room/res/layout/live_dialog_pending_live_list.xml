<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="pendingLiveList"
      type="java.util.List" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:background="@drawable/bg_bottom_sheet"
    android:orientation="vertical">

    <TextView
      style="@style/Text.14sp.333333.Bold"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginBottom="@dimen/dp_10"
      android:text="@string/live_create_room_has_pending_tips_title" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_pending_live"
      bind:items="@{pendingLiveList}"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      android:id="@+id/tv_start_live"
      style="@style/Button.Basic.Round"
      android:layout_marginStart="@dimen/dp_30"
      android:layout_marginTop="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_30"
      android:layout_marginBottom="@dimen/dp_12"
      android:text="@string/live_create_room_start" />

  </LinearLayout>
</layout>