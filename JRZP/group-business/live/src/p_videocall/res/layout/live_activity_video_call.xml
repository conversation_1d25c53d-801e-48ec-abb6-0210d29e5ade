<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.live.videocall.ui.videocall.VideoCallViewModel" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/live_calling_bg_main_gradient">

    <androidx.constraintlayout.widget.Guideline
      android:id="@+id/gl_horizontal_top"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      app:layout_constraintGuide_begin="20dp" />

    <androidx.constraintlayout.widget.Group
      android:id="@+id/group_sponsor"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:visibility="gone"
      app:constraint_referenced_ids="iv_sponsor_avatar,tv_sponsor_user_name,tv_sponsor_video_tag,shade_sponsor" />

    <com.bxkj.jrzp.live.videocall.weight.videolayout.TRTCVideoLayoutManager
      android:id="@+id/trtc_layout_manager"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_bias="0.0"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <View
      android:id="@+id/shade_sponsor"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:background="@color/live_calling_color_shade" />

    <ImageView
      android:id="@+id/iv_sponsor_avatar"
      android:layout_width="80dp"
      android:layout_height="80dp"
      android:layout_marginEnd="10dp"
      android:layout_marginRight="10dp"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@+id/gl_horizontal_top" />

    <TextView
      android:id="@+id/tv_sponsor_user_name"
      style="@style/Text.18sp.FFFFFF.Bold"
      android:layout_marginEnd="10dp"
      app:layout_constraintBottom_toTopOf="@id/tv_sponsor_video_tag"
      app:layout_constraintEnd_toStartOf="@+id/iv_sponsor_avatar"
      app:layout_constraintTop_toTopOf="@+id/gl_horizontal_top"
      app:layout_constraintVertical_chainStyle="packed" />

    <TextView
      android:id="@+id/tv_sponsor_video_tag"
      style="@style/Text.16sp.FFFFFF"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginEnd="10dp"
      app:layout_constraintBottom_toBottomOf="@+id/iv_sponsor_avatar"
      app:layout_constraintEnd_toStartOf="@+id/iv_sponsor_avatar"
      app:layout_constraintTop_toBottomOf="@id/tv_sponsor_user_name" />


    <androidx.constraintlayout.widget.Group
      android:id="@+id/group_inviting"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:visibility="gone"
      app:constraint_referenced_ids="tv_inviting_tag,ll_img_container" />

    <TextView
      android:id="@+id/tv_inviting_tag"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/trtc_layout_manager" />

    <LinearLayout
      android:id="@+id/ll_img_container"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="10dp"
      android:gravity="center"
      android:orientation="horizontal"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_inviting_tag" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.14sp.FFFFFF"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginBottom="40dp"
      android:visibility="@{viewModel.timeCount>0?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toTopOf="@+id/ll_hangup"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      tools:text="@{@string/live_calling_called_time_format(viewModel.timeCount/60,viewModel.timeCount%60)}" />

    <LinearLayout
      android:id="@+id/ll_mute"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center_horizontal"
      android:onClick="@{onClickListener}"
      android:orientation="vertical"
      android:visibility="gone"
      app:layout_constraintBottom_toTopOf="@+id/gl_horizontal_bottom"
      app:layout_constraintEnd_toStartOf="@+id/ll_hangup"
      app:layout_constraintHorizontal_bias="0.5"
      app:layout_constraintStart_toStartOf="parent">

      <ImageView
        android:id="@+id/iv_mute"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/live_calling_bg_mute_mic" />
    </LinearLayout>

    <LinearLayout
      android:id="@+id/ll_hangup"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center_horizontal"
      android:orientation="vertical"
      android:visibility="gone"
      app:layout_constraintBottom_toTopOf="@+id/gl_horizontal_bottom"
      app:layout_constraintEnd_toStartOf="@+id/ll_handsfree"
      app:layout_constraintHorizontal_bias="0.5"
      app:layout_constraintStart_toEndOf="@+id/ll_mute">

      <ImageView
        android:id="@+id/iv_hangup"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@drawable/live_calling_bg_hangup" />
    </LinearLayout>

    <LinearLayout
      android:id="@+id/ll_handsfree"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center_horizontal"
      android:onClick="@{onClickListener}"
      android:orientation="vertical"
      android:visibility="gone"
      app:layout_constraintBottom_toTopOf="@+id/gl_horizontal_bottom"
      app:layout_constraintEnd_toStartOf="@+id/ll_dialing"
      app:layout_constraintHorizontal_bias="0.5"
      app:layout_constraintStart_toEndOf="@+id/ll_hangup">

      <ImageView
        android:id="@+id/iv_handsfree"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/live_calling_bg_handsfree" />

    </LinearLayout>

    <LinearLayout
      android:id="@+id/ll_dialing"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center_horizontal"
      android:orientation="vertical"
      android:visibility="gone"
      app:layout_constraintBottom_toTopOf="@+id/gl_horizontal_bottom"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_bias="0.5"
      app:layout_constraintStart_toEndOf="@+id/ll_handsfree">

      <ImageView
        android:id="@+id/iv_dialing"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@drawable/live_calling_bg_dialing" />

    </LinearLayout>

    <androidx.constraintlayout.widget.Guideline
      android:id="@+id/gl_horizontal_bottom"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      app:layout_constraintGuide_percent="0.98084813" />


  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>