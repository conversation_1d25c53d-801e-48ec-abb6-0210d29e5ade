package com.bxkj.jrzp.live.videocall.ui.videocall

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.api.OpenUserHomeRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *  视频聊天
 * @author: YangXin
 * @date: 2021/3/11
 */
class VideoCallViewModel @Inject constructor(
  private val mUserRepository: OpenUserHomeRepository
) : BaseViewModel() {

  val timeCount = MutableLiveData<Int>().apply { value = 0 }

  val videoCallTimeOut = LiveEvent<Void>()

  private var videoCallIdentity = VideoCallNavigation.TYPE_CALL

  fun setupIdentity(intentCallType: Int) {
    videoCallIdentity = intentCallType
  }

  fun getUserInfo(userId: Int, callback: (userInfo: VideoCallUserInfo) -> Unit) {
    viewModelScope.launch {
      mUserRepository.getUserHomePageInfo(
        userId,
        AuthenticationType.QUERY_HIGHER_AUTH,
        getSelfUserID()
      ).handleResult({
        it?.let {
          callback.invoke(VideoCallUserInfo(it.userID, it.name, it.photo))
        }
      })
    }
  }

  fun showTimeCount(maxTime: Long) {
    if (timeCount.value.getOrDefault() > 0) return
    viewModelScope.launch {
      while (true) {
        timeCount.value?.let {
          if (maxTime != 0L) {
            if (it >= maxTime) {
              videoCallTimeOut.call()
              return@launch
            }
            if (it.toLong() == (maxTime - (60 * 3))) {
              showToast("您的视频时长还剩3分钟，请注意时间。")
            }
          }
          timeCount.value = it + 1
        }
        delay(1000)
      }
    }
  }
}