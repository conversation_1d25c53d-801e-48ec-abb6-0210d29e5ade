package com.bxkj.video.message;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.video.data.VideoData;

import java.util.List;

/**
 * @Project: VideoRecruitment
 * @Description: 视频列表消息
 * @author:45457
 * @date: 2020/3/18
 * @version: V1.0
 */
public class VideoListMassage {

  private int currentPosition;
  private List<? extends VideoData> videoList;
  private int galleryTag = CommonApiConstants.NO_ID;

  public static VideoListMassage from(int currentPosition, List<? extends VideoData> videoList,
      int galleryTag) {
    return new VideoListMassage(currentPosition, videoList, galleryTag);
  }

  private VideoListMassage(int currentPosition, List<? extends VideoData> videoList,
      int galleryTag) {
    this.currentPosition = currentPosition;
    this.videoList = videoList;
    this.galleryTag = galleryTag;
  }

  public int getCurrentPosition() {
    return currentPosition;
  }

  public void setCurrentPosition(int currentPosition) {
    this.currentPosition = currentPosition;
  }

  public List<? extends VideoData> getVideoList() {
    return videoList;
  }

  public void setVideoList(List<VideoData> videoList) {
    this.videoList = videoList;
  }

  public int getGalleryTag() {
    return galleryTag;
  }

  public void setGalleryTag(int galleryTag) {
    this.galleryTag = galleryTag;
  }
}
