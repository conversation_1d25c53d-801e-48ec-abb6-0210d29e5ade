package com.bxkj.video.openapi

import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
interface OpenVideoApi {


  @POST(OpenVideoApiConstants.I_GET_USER_VIDEO_LIST)
  suspend fun getUserVideoList(@Body requestBody: ZPRequestBody): BaseResponse<List<OnlineVideoData>>

  @POST(OpenVideoApiConstants.I_PUBLISH_VIDEO)
  suspend fun publishVideo(@Body requestBody: ZPRequestBody): BaseResponse<String>

  @POST(OpenVideoApiConstants.I_GET_INFO_ATTACH_VIDEOS)
  suspend fun getInfoLinkVideos(@Body requestBody: ZPRequestBody): BaseResponse<List<VideoData>>

  @POST(OpenVideoApiConstants.I_ADD_VIDEO_ATTACH_INFO)
  suspend fun addVideoLinkInfo(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(OpenVideoApiConstants.I_REMOVE_VIDEO_ATTACH_INFO)
  suspend fun removeVideoLinkInfo(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

}