package com.bxkj.video

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
open class VideoType {
  companion object {
    //发布简历视频类型
    const val VIDEO_TYPE_RESUME = 1

    //发布招聘视频消息
    const val VIDEO_TYPE_RECRUIT = 2
  }

  @IntDef(VIDEO_TYPE_RESUME, VIDEO_TYPE_RECRUIT)
  @Retention(AnnotationRetention.SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Type
}