package com.bxkj.video

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */

class VideoLinkMethod {

  companion object {
    //发布简历或职位类型
    const val VIDEO_LINK_ONE_VIDEO_TO_MORE: Int = 1

    //发布视频类型O
    const val VIDEO_LINK_MORE_VIDEO_TO_ONE = 2
  }

  @IntDef(
    VIDEO_LINK_ONE_VIDEO_TO_MORE,
    VIDEO_LINK_MORE_VIDEO_TO_ONE
  )
  @Retention(AnnotationRetention.SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Method
}