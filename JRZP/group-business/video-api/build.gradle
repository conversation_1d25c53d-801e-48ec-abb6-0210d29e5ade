plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
}

android {

  namespace "com.bxkj.video.api"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()

    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  buildFeatures {
    dataBinding = true
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }
}

dependencies {
  implementation fileTree(dir: "libs", include: ["*.jar"])

  implementation project(":lib-common")
  api project(":group-support:upload")
}