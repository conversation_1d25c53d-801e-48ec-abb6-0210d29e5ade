package com.bxkj.business.ui.educourse

import androidx.fragment.app.Fragment
import com.bxkj.learning.LearningConstants
import com.bxkj.common.util.router.Router

class EduCourseNavigation {

  companion object {

    const val PATH = "${LearningConstants.LEARNING_DIRECTORY}/educourse"

    const val EXTRA_INFO_TYPE = "INFO_TYPE"
    const val EXTRA_QUERY_USER_ID = "QUERY_USER_ID"

    const val TYPE_NORMAL = 1
    const val TYPE_USER_RELEASE = 2

    fun create(infoType: Int = TYPE_NORMAL, queryUserId: Int = 0): Fragment {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_INFO_TYPE, infoType)
        .withInt(EXTRA_QUERY_USER_ID, queryUserId)
        .createFragment() as Fragment
    }
  }
}