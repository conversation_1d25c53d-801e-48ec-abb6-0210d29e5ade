package com.bxkj.business.ui.courseinfogroup

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 *
 * @author: YangXin
 * @date: 2021/4/17
 */
class CourseInfoType {

  companion object {
    const val TYPE_SKILL = 10
    const val TYPE_EDU = 11
    const val TYPE_PRO_EMPLOYMENT = 8
    const val TYPE_INDUSTRIAL_DESIGN = 2

    const val TYPE_3 = 3
    const val TYPE_4 = 4
    const val TYPE_5 = 5
    const val TYPE_9 = 9
    const val TYPE_15 = 15

    fun isCourse(type: Int): Boolean {
      return type == TYPE_SKILL || type == TYPE_EDU
    }
  }

  @IntDef(TYPE_SKILL, TYPE_EDU, TYPE_PRO_EMPLOYMENT, TYPE_INDUSTRIAL_DESIGN, TYPE_15)
  @Target(VALUE_PARAMETER)
  @Retention(SOURCE)
  annotation class Type

}