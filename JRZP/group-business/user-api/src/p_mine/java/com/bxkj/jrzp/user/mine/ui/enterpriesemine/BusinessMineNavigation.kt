package com.bxkj.jrzp.user.mine.ui.enterpriesemine

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class BusinessMineNavigation {

  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/enterprisemine"

    fun navigate(): RouterNavigator = Router.getInstance().to(PATH)
  }
}
