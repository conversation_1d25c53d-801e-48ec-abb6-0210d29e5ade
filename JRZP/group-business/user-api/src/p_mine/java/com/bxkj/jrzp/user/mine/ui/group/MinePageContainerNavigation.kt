package com.bxkj.jrzp.user.mine.ui.group

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class MinePageContainerNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/minegroup"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}