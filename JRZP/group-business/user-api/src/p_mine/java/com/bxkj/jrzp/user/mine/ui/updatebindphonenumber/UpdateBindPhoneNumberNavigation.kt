package com.bxkj.jrzp.user.mine.ui.updatebindphonenumber

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/12
 **/
class UpdateBindPhoneNumberNavigation {
  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/update_bind_phone_number"

    const val EXTRA_REQ_STEP = "extra_req_step"

    const val STEP_VERIFY_OLD_PHONE = 1

    const val STEP_VERIFY_NEW_PHONE = 2

    fun create(step: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_REQ_STEP, step)
    }
  }
}