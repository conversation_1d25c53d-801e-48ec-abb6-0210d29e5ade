package com.bxkj.jrzp.userhome.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TimeUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.jrzp.user.api.BR;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2020/1/14
 */
public class UserQuestionItemData extends BaseObservable {

  /**
   * id : 13
   * toWendaID : 12
   * toWendaTitle : 你有什么道理后悔没有早点知道？
   * toWendaCommentsCount : 10
   * type : 2
   * title : 回复:你有什么道理后悔没有早点知道？
   * content : <div class="RichContent-inner"><span class="RichText ztext
   * CopyrightRichText-richText" itemprop="text"><p>有些面具戴久了就永远脱不下来了。</p><p
   * class="ztext-empty-paragraph"><br></p><p>我以前打游戏认识一个公会的朋友。大家一起组队一起开黑，时间久了就有些熟了。
   * 有一次在QQ群聊到年龄，我才知道他们都22.23这样子了，这时候我才初三，十五六岁的小屁孩。 我怕我说我只有初三，他们会嘲笑我，会看低我，这样我们关系就不平等了。
   * 我说我今年也23了，毕业在找工作。</p><p class="ztext-empty-paragraph"><br></p><p>我和他们越来越熟，越来越交心，四人组实力也强，开黑战无不胜。
   * 我写作业的时候，他们会在群里吐工作上的苦水，互相安慰。每次我都躲得远远的，生怕他们拉我讨论。 我问我姐夫什么是五险一金，问他工作主要是做什么，一切只是为了能在他们面前有个说辞。</p><p
   * class="ztext-empty-paragraph"><br></p><p>后来我初三下，学业紧张，一周我也不见得能上次线，哪怕周末。
   * 我说我找了份工作，在城郊，住员工宿舍，里面没有电脑。周末如果忙，也不高兴回家。 四人组突然少了一人就欠了些味道，一次一个东莞的朋友埋怨，花b（我id花开头），我有时候真觉得你是个上学的小屁孩，平时被家里人管着不敢打游戏。
   * 我看着那句话心惊肉跳，很久都不敢说话。</p><p class="ztext-empty-paragraph"><br></p><p>后来中考完了，大家换了游戏，新游戏很烧钱，我明明时间最多，却玩得举步维艰。
   * 我对他们说，家里有困难，赚的钱都寄回家了。偏偏四人组里有两个都是富二代，听了这些二话不说邮了我最顶级的装备。 他们说，没关系，我们不在意你玩得怎么样，大家聚在一起图个乐子，人别散就行。</p><p
   * class="ztext-empty-paragraph"><br></p><p>到这个时候，不，其实更早许多的时候，我就真的很想告诉他们，我是骗你们的，我只是个初中生，我骗你们是怕你们不带我玩了…
   * 可，一直没说出口。 倘若我一开始知道会同他们走那么远，我一定一定不会骗他们。 东莞的后来两次到了上海，想请我吃饭，我推脱自己在外地，“我一来你就在外地”他说。
   * 我也很想见他啊，要知道我可是和他们一起拿过上个游戏全国线上赛八强的，谁不想见见战友，谁不想见见交心这么久的网友？</p><p class="ztext-empty-paragraph"><br></p><p>可我见不了，永远也见不了。时间拖得越久，我们感情越深，我越告诉自己：千万不能告诉他们。
   * 他们互发照片，我拿了朋友他哥的充数。他们在yy语音，我推脱自己麦有问题。</p><p class="ztext-empty-paragraph"><br></p><p>后来。</p><p
   * class="ztext-empty-paragraph"><br></p><p>东莞的要结婚了，他说自己想认真做事业，再不玩游戏了，他请我们三人去参加他婚礼，机票食宿报销。
   * 我当然，又在“外地”。那天我看着他们三个人在婚礼上的合照，看着他们在群里说“花b垃圾”，很想哭。</p><p>另一个义乌富二代也不玩游戏了，他和家里闹翻，出来玩了两年，后来决定得好好过日子，回去继承家业。
   * 我们就这样散了，开始在群里还有三三两两的交流，后来就寂静了。 有一天，东莞传了自己孩子的照片。 有一天，义乌发了自己西装革履，在一个灯具展览上演讲的照片。
   * 我没有说话，在心里祝福了一万遍，你们一定要过得好。</p><p class="ztext-empty-paragraph"><br></p><p>我怕我一说话，他们又骂，花b你这垃圾，准备什么时候来见我们？
   * 我到最后，终究还是没把一切说出口，我们在微信繁荣前散了，互相只保有qq，再之后，那个群再也没亮过。</p><p class="ztext-empty-paragraph"><br></p><p>我现在活到了当初他们的年纪，假如让现在的我回到那时候，给我重新介绍自己的机会……
   * 我翻着qq空间里七年前的游戏截图，无数次想过。</p><p class="ztext-empty-paragraph"><br></p><p>那时候，我是全区最强的刀，义乌是全区最强的狙，我总能放心冲到最前线乱砍，他总能帮我狙倒最麻烦的敌人，帮我牵制。
   * 后来东莞来了，他话多，刀也强，总爱和我分个胜负。义乌来劝，我们就忽然统一战线，先虐他这个刀最菜的。 我们打过99胜一负，我们全区无敌。 我们最后散了。 我没说出口。</p><p
   * class="ztext-empty-paragraph"><br></p><p>我有时候会想，他们应该是有所感觉的，破绽其实不少，是不是他们早就知道了，只是一直装不知道，帮我圆谎。
   * 这些我都已经不会知道了，可能永远不会知道了，我今年真的23岁了。</p><p class="ztext-empty-paragraph"><br></p><p>我真的23岁了......</p></span></div>
   * count : 0
   * Medialist : [{"type":4,"url":"http://img.jrzp.com/images_server/shejiao/2896rsiqov.jpg"},{"type":4,"url":"http://img.jrzp.com/images_server/shejiao/2896rsiqov.jpg"}]
   * commentsCount : 0
   * likesCount : 0
   * createTime : 2019.12.10 15:17:42
   */

  private int id;
  private int toWendaID;
  private String toWendaTitle;
  private int toWendaCommentsCount;
  private int type;
  private String title;
  private String content;
  private int count;
  private int commentsCount;
  private int likesCount;
  private String createTime;
  private List<MedialistBean> Medialist;
  private String userPhoto;
  private String userName;
  private boolean isLike;
  private int userID;

  public int getUserID() {
    return userID;
  }

  public void setUserID(int userID) {
    this.userID = userID;
  }

  @Bindable
  public boolean isLike() {
    return isLike;
  }

  public void setLike(boolean like) {
    isLike = like;
    notifyPropertyChanged(BR.like);
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getUserPhoto() {
    return userPhoto;
  }

  public void setUserPhoto(String userPhoto) {
    this.userPhoto = userPhoto;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getToWendaID() {
    return toWendaID;
  }

  public void setToWendaID(int toWendaID) {
    this.toWendaID = toWendaID;
  }

  public String getToWendaTitle() {
    return CheckUtils.isNullOrEmpty(toWendaTitle) ? CommonApiConstants.NO_TEXT : toWendaTitle;
  }

  public void setToWendaTitle(String toWendaTitle) {
    this.toWendaTitle = toWendaTitle;
  }

  public int getToWendaCommentsCount() {
    return toWendaCommentsCount;
  }

  public void setToWendaCommentsCount(int toWendaCommentsCount) {
    this.toWendaCommentsCount = toWendaCommentsCount;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public int getCommentsCount() {
    return commentsCount;
  }

  public void setCommentsCount(int commentsCount) {
    this.commentsCount = commentsCount;
  }

  @Bindable
  public int getLikesCount() {
    return likesCount;
  }

  public void setLikesCount(int likesCount) {
    this.likesCount = likesCount;
    notifyPropertyChanged(BR.likesCount);
  }

  public String getCreateTime() {
    return TimeUtils.getNewTimeDiff(createTime);
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public List<MedialistBean> getMedialist() {
    return Medialist;
  }

  public void setMedialist(List<MedialistBean> Medialist) {
    this.Medialist = Medialist;
  }

  public void addLike() {
    setLike(true);
    setLikesCount(likesCount + 1);
  }

  public void removeLike() {
    setLike(false);
    setLikesCount(likesCount - 1);
  }

  public boolean isQuestion() {
    return type == 1;
  }

  public boolean isAnswer() {
    return type == 2;
  }

  public static class MedialistBean {
    /**
     * type : 4
     * url : http://img.jrzp.com/images_server/shejiao/2896rsiqov.jpg
     */

    private int type;
    private String url;

    public int getType() {
      return type;
    }

    public void setType(int type) {
      this.type = type;
    }

    public String getUrl() {
      return url;
    }

    public void setUrl(String url) {
      this.url = url;
    }
  }

  public boolean isSelf() {
    return UserUtils.logged() && userID == UserUtils.getUserId();
  }
}
