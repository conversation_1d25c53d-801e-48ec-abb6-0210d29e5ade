package com.bxkj.jrzp.userhome.ui.homepage

import android.app.Activity
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.common.enums.AuthenticationType.Type
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData.Tab

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class UserHomeNavigation {

    companion object {

        const val PATH = "${UserConstants.DIRECTORY}/userhome"

        const val EXTRA_QUERY_USER_ID = "QUERY_USER_ID"

        const val EXTRA_QUERY_ENTERPRISE_ID = "QUERY_ENTERPRISE_ID"

        const val EXTRA_USER_AUTH_TYPE = "USER_AUTH_TYPE"

        const val EXTRA_TARGET_TAB = "TARGET_TAB_INDEX"

        const val EXTRA_FOLLOW_STATUS = "FOLLOW_STATUS"

        const val RESULT_FOLLOW_STATUS_CHANGE = Activity.RESULT_FIRST_USER + 1

        @JvmOverloads
        fun navigate(
            queryUserId: Int,
            @Type userAuthType: Int = AuthenticationType.QUERY_HIGHER_AUTH,
            queryEnterpriseId: Int = 0,
            @Tab targetTab: Int = UserInfoNavigationData.NAVIGATION_ALL
        ): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_QUERY_USER_ID, queryUserId)
                .withInt(EXTRA_USER_AUTH_TYPE, userAuthType)
                .withInt(EXTRA_QUERY_ENTERPRISE_ID, queryEnterpriseId)
                .withInt(EXTRA_TARGET_TAB, targetTab)
        }
    }
}