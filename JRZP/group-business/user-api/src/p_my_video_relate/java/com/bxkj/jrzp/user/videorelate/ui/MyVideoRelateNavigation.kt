package com.bxkj.jrzp.user.videorelate.ui

import android.app.Activity
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants
import com.bxkj.video.data.OnlineVideoData

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
class MyVideoRelateNavigation {

  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/videorelate"

    const val EXTRA_VIDEO_INFO = "VIDEO_INFO"

    const val EXTRA_USER_TYPE = "USER_TYPE"

    const val RESULT_VIDEO_DELETED = Activity.RESULT_FIRST_USER + 1

    fun navigate(
      onlineVideoData: OnlineVideoData? = null,
      userType: Int = AppConstants.USER_TYPE_PERSONAL
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withParcelable(EXTRA_VIDEO_INFO, onlineVideoData)
        .withInt(
          EXTRA_USER_TYPE, userType
        )
    }
  }
}