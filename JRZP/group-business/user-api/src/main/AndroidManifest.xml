<manifest package="com.bxkj.jrzp.user.api">
  <application android:allowBackup="false" xmlns:android="http://schemas.android.com/apk/res/android" tools:replace="android:allowBackup" xmlns:tools="http://schemas.android.com/tools">
    <activity android:name="com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.selectauthtype.SelectAuthTypeActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.enterpriseauth.EnterpriseAuthActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.personalauthentication.PersonalAuthenticationActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.jobproveauth.JobProveAuthActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.alipaylist.AlipayAccountListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.ui.addalipay.AddReceivingAccountActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.mine.ui.updatebindphonenumber.UpdateBindPhoneNumberActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.commission.ui.list.CommissionListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.switchidentity.ui.SwitchUserIdentityActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.withdraw.ui.alipay.WithdrawByAlipayActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.withdraw.ui.history.WithdrawHistoryActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.selectidentity.SelectIdentityActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.review.CourseReviewActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.details.CourseOrderDetailsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.videorelate.ui.VideoRelateActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:launchMode="singleTop" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.userhome.ui.homepage.UserHomePageActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.videomanagement.VideoManagementActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.friend.ui.findfriends.FindFriendsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.switchaccount.ui.switchaccount.SwitchAccountActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.doubleelections.DoubleElectionsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection.EditDoubleElectionActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.seminars.SeminarsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.editemployment.EditEmploymentActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.bindschoolattachinfo.BindSchoolAttachInfoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.employments.EmploymentsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanys.DoubleElectionCompanyActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanydetails.DoubleElectionCompanyDetailsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair.EnterpriseJobFairActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.edit.EditCampusTalkActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.civilserviceinfolist.CivilServiceInfoListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.schoolinfo.ui.contractinfo.SchoolContractInfoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo.EnterpriseVideoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.institutions.ui.institutionsinfo.InstitutionsInfoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.ordergroup.OrderGroupActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.servceorderdetails.ServiceOrderDetailsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.CampusTalkManagementActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
  </application>
</manifest>
