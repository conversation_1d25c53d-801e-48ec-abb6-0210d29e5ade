package com.bxkj.jrzp.user.ui.addalipay

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/3
 * @version: V1.0
 */
class AddAlipayAccountNavigation {
  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/addalipayaccount"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}