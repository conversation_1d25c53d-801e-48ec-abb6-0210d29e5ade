package com.bxkj.jrzp.user.ui.selectauthtype

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/14
 * @version: V1.0
 */
object SelectAuthTypeNavigation {

  const val PATH = "${UserConstants.DIRECTORY}/selectauthtype"

  fun navigate(): RouterNavigator {
    return Router.getInstance().to(PATH)
  }
}