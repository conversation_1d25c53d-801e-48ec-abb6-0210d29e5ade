<manifest package="com.bxkj.jrzp.user.api">
  <application android:allowBackup="false" xmlns:android="http://schemas.android.com/apk/res/android" tools:replace="android:allowBackup" xmlns:tools="http://schemas.android.com/tools">
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.review.CourseReviewActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.details.CourseOrderDetailsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.ordergroup.OrderGroupActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.orderrecord.ui.servceorderdetails.ServiceOrderDetailsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
  </application>
</manifest>
