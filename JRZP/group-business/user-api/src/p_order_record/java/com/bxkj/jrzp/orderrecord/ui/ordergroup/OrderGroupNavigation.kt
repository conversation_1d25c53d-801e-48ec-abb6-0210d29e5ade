package com.bxkj.jrzp.orderrecord.ui.ordergroup

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class OrderGroupNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/ordergroup"

    const val EXTRA_TARGET_INDEX = "TARGET_INDEX"

    const val RECRUIT_ORDER_INDEX = 0
    const val COURSE_ORDER_INDEX = 1
    const val GONGFU_ORDER_INDEX = 2

    fun navigate(targetIndex: Int = 0): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_TARGET_INDEX, targetIndex)
    }
  }
}