package com.bxkj.jrzp.user.switchidentity.ui

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/6
 * @version: V1.0
 */
class SwitchUserIdentityNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/switchidentity"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}