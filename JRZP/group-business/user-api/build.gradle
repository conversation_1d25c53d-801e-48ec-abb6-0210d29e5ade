plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
}
android {

  namespace "com.bxkj.jrzp.user.api"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()
  }

  buildFeatures {
    dataBinding = true
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }
}

dependencies {
  implementation project(":lib-common")

  includeApi(':group-business:video')
}
