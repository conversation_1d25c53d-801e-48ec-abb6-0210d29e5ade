package com.bxkj.videorecord.ui.videorecord

import android.content.ContentValues
import android.content.Context
import android.provider.MediaStore
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.camera.core.CameraSelector
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.FileOutputOptions
import androidx.camera.video.MediaStoreOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.Recording
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.camera.view.PreviewView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.util.Consumer
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import java.io.File

/**
 * @date 2024/11/25
 * <AUTHOR>
 */
class CameraXView @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyle: Int = 0
) : ConstraintLayout(context, attrs, defStyle) {

  private val previewView by lazy { PreviewView(context) }
  private val mainThreadExecutor by lazy { ContextCompat.getMainExecutor(context) }

  private lateinit var videoCapture: VideoCapture<Recorder>
  private var currentRecording: Recording? = null
  private var cameraSelector: CameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

  init {
    background = ContextCompat.getDrawable(context, android.R.color.black)
    addView(previewView)
    previewView.updateLayoutParams<LayoutParams> {
      leftToLeft = LayoutParams.PARENT_ID
      rightToRight = LayoutParams.PARENT_ID
      topToTop = LayoutParams.PARENT_ID
      bottomToBottom = LayoutParams.PARENT_ID
      height = 0
      width = 0
      dimensionRatio = "9:16"
    }
  }

  override fun onAttachedToWindow() {
    super.onAttachedToWindow()
    findViewTreeLifecycleOwner()?.lifecycleScope?.launch {
      bindCaptureVideoUsecase()
    }
  }

  fun takeVideo(file: File, videoRecordEventListener: Consumer<VideoRecordEvent>) {
    currentRecording =
      videoCapture.output.prepareRecording(context, FileOutputOptions.Builder(file).build())
        .apply { withAudioEnabled() }
        .start(
          mainThreadExecutor, videoRecordEventListener
        )

  }

  fun takeVideo(
    contentValues: ContentValues,
    videoRecordEventListener: Consumer<VideoRecordEvent>
  ) {
    currentRecording =
      videoCapture.output.prepareRecording(
        context,
        MediaStoreOutputOptions.Builder(
          context.contentResolver,
          MediaStore.Video.Media.EXTERNAL_CONTENT_URI
        ).setContentValues(contentValues).build()
      )
        .apply { withAudioEnabled() }
        .start(
          mainThreadExecutor, videoRecordEventListener
        )
  }

  fun stop() {
    val recording = currentRecording
    recording?.let {
      recording.stop()
      currentRecording = null
    }
  }

  fun isRecording(): Boolean {
    return currentRecording != null
  }

  fun switchCamera() {
    cameraSelector = if (cameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
      CameraSelector.DEFAULT_FRONT_CAMERA
    } else {
      CameraSelector.DEFAULT_BACK_CAMERA
    }
    bindCaptureVideoUsecase()
  }

  fun startPreview() {
    bindCaptureVideoUsecase()
  }

  private fun bindCaptureVideoUsecase() {
    val cameraProvider = ProcessCameraProvider.getInstance(context).get()

    val preview = Preview.Builder()
      .build().apply {
        setSurfaceProvider(previewView.surfaceProvider)
      }

    val recorder = Recorder.Builder()
      .setQualitySelector(QualitySelector.from(Quality.HD))
      .build()

    videoCapture = VideoCapture.withOutput(recorder)

    cameraProvider.unbindAll()
    findViewTreeLifecycleOwner()?.let {
      cameraProvider.bindToLifecycle(it, cameraSelector, videoCapture, preview)
    }
  }

}