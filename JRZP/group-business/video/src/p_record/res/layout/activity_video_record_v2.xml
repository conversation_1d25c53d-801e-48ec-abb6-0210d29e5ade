<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.bxkj.videorecord.ui.videorecord.CameraXView
      android:id="@+id/camerax_view"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <ImageView
        android:id="@+id/iv_back"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/ic_back_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.tencent.qcloud.ugckit.module.record.RecordProgressView
      android:id="@+id/record_progress_view"
      android:layout_width="match_parent"
      android:layout_height="4dp"
      android:layout_marginBottom="32dp"
      app:layout_constraintBottom_toTopOf="@id/cl_bottom_layout"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_bottom_layout"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_gravity="bottom|center"
      android:layout_marginBottom="32dp"
      android:orientation="horizontal"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent">

      <ImageView
        android:id="@+id/iv_switch_camera"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:onClick="@{onClickListener}"
        android:src="@drawable/selector_switch_camera"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/capture_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <com.bxkj.videorecord.ui.videorecord.CaptureButton
        android:id="@+id/capture_button"
        android:layout_width="62dp"
        android:layout_height="62dp"
        android:layout_gravity="bottom|center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:smallCircleRadius="4dp"
        app:smallCircleSize="42dp" />

      <ImageView
        android:id="@+id/iv_album"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:onClick="@{onClickListener}"
        android:src="@drawable/ic_album"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/capture_button"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>