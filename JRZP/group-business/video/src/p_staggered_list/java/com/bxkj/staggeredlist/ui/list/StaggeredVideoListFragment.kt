package com.bxkj.staggeredlist.ui.list

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getCenterXY
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceNavigation
import com.bxkj.jrzp.live.room.data.LiveRoomData
import com.bxkj.staggeredlist.VideoListType
import com.bxkj.video.BR
import com.bxkj.video.R
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.databinding.VideoFragmentStaggeredVideoListBinding
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.ui.galllery.VideoGalleryNavigation
import com.bxkj.video.ui.galllery.VideoGalleryNavigation.Companion.RevealCenter
import com.bxkj.video.ui.galllery.VideoGalleryType

/**
 * @Description: 瀑布流视频列表列表
 * @author:45457
 * @date: 2020/8/17
 * @version: V1.0
 */
@Route(path = StaggeredVideoListNavigation.PATH)
class StaggeredVideoListFragment :
    BaseDBFragment<VideoFragmentStaggeredVideoListBinding, StaggeredVideoListViewModel>(),
    StaggeredVideoListNavigation.Content {

    private var mStaggeredVideoListAdapter: MultiTypeAdapter? = null

    private val extraVideoType: Int by lazy {
        arguments?.getInt(StaggeredVideoListNavigation.EXTRA_VIDEO_TYPE)
            .getOrDefault(VideoListType.RECOMMEND)
    }

    override fun getViewModelClass(): Class<StaggeredVideoListViewModel> =
        StaggeredVideoListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.video_fragment_staggered_video_list

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupVideoListAdapter()
        subscribeRxBusMsg()

        viewModel.start(extraVideoType)
    }

    private fun subscribeRxBusMsg() {
        addDisposable(
            RxBus.get().toObservable(RxBus.Message::class.java)
                .subscribe {
                    if (it.code == RxMsgCode.ACTION_VIDEO_GALLERY_POSITION_CHANGE) {   //图片浏览position改变

                        val currentVideoId = it.msg as Int
                        mStaggeredVideoListAdapter?.let { adapter ->
                            adapter.data.find { item ->
                                if (item is VideoData) {
                                    item.id == currentVideoId
                                } else {
                                    false
                                }
                            }?.let { result ->
                                (result as VideoData).addViewCount()
                            }
                        }
                    } else if (it.code == RxMsgCode.VIDEO_GALLERY_LOAD_MORE) { //图片浏览加载更多
                        val loadMorePageTag = it.msg as String
                        if (loadMorePageTag == this.toString()) {
                            viewModel.loadMoreVideo()
                        }
                    } else if (it.code == RxMsgCode.ACTION_SELECT_CITY_CHANGE) { //手动选择的城市发生了变化
                        if (extraVideoType == VideoListType.ADDRESS) {
                            viewModel.refreshData()
                            viewBinding.includeVideoList.recyclerContent.scrollToPosition(0)
                        }
                    } else if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS) {
                        if (extraVideoType == VideoListType.FOLLOW) {
                            viewModel.refreshData()
                        }
                    }
                }
        )
    }

    private fun setupVideoListAdapter() {
        mStaggeredVideoListAdapter = MultiTypeAdapter(parentActivity)
            .apply {
                register(
                    OnlineVideoData::class.java,
                    DefaultViewBinder<OnlineVideoData>(
                        R.layout.video_recycler_staggered_video_list_item,
                        BR.data
                    ).apply {
                        setOnItemClickListener(object :
                            DefaultViewBinder.OnItemClickListener<OnlineVideoData> {
                            override fun onItemClicked(v: View, position: Int, item: OnlineVideoData) {
                                toVideoGallery(v, item)
                            }
                        })
                    }
                )
                register(
                    LiveRoomData::class.java,
                    DefaultViewBinder<LiveRoomData>(R.layout.video_recycler_live_room_item, BR.data).apply {
                        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<LiveRoomData> {
                            override fun onItemClicked(v: View, position: Int, item: LiveRoomData) {
//                                afterLogin {
                                LiveAudienceNavigation.navigate(item).start()
//                                }
                            }
                        })
                    }
                )
            }

        val videoList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        videoList.layoutManager = GridLayoutManager(parentActivity, 2)
        videoList.addItemDecoration(
            GridItemDecoration(ContextCompat.getDrawable(parentActivity, R.drawable.divider_4))
        )
        viewModel.videoListViewModel.setAdapter(mStaggeredVideoListAdapter)
    }

    private fun toVideoGallery(itemView: View, item: OnlineVideoData) {
        val itemIndex = viewModel.getVideoList().indexOf(item)
        if (viewModel.getVideoList().isEmpty() || (itemIndex == -1)) {
            return
        }
        VideoGalleryNavigation.navigate(
            VideoGalleryType.MULTIPLE_VIDEO,
            VideoListMassage.from(
                itemIndex,
                viewModel.getVideoList(),
                VideoGalleryType.MULTIPLE_VIDEO
            ),
            fromPageTag = this.toString(),
            revealCenter = RevealCenter(itemView.getCenterXY()[0], itemView.getCenterXY()[1])
        ).withTransition(-1, -1).start()
    }

    override fun onStartSearch(keyword: String) {
        viewModel.startSearch(keyword)
    }
}