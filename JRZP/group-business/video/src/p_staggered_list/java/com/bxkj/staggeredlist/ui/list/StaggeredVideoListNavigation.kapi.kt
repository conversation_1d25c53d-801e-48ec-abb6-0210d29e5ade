package com.bxkj.staggeredlist.ui.list

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.staggeredlist.VideoListType.Type
import com.bxkj.video.VideoConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/17
 * @version: V1.0
 */
class StaggeredVideoListNavigation {
  companion object {
    const val PATH = "${VideoConstants.VIDEO_DIRECTORY}/staggeredvideolist"

    const val EXTRA_VIDEO_TYPE = "VIDEO_TYPE"

    fun create(@Type videoType: Int): Fragment {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_VIDEO_TYPE, videoType)
        .createFragment() as Fragment
    }
  }

  interface Content {
    fun onStartSearch(keyword: String)
  }
}