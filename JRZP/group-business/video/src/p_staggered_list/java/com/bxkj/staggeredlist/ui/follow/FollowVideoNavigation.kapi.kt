package com.bxkj.staggeredlist.ui.follow

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.video.VideoConstants

/**
 *
 * @author: YangXin
 * @date: 2021/6/2
 */
class FollowVideoNavigation {
  companion object {
    const val PATH = "${VideoConstants.VIDEO_DIRECTORY}/followvideolist"

    fun create(): Fragment {
      return Router.getInstance().to(PATH)
        .createFragment() as Fragment
    }
  }
}