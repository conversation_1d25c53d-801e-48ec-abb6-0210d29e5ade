package di

import com.bxkj.addvideolinkinfo.ui.addvideolinkinfos.AddVideoLinkInfoViewModel
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.di.ViewModelKey
import com.bxkj.report.ui.videoreport.VideoReportViewModel
import com.bxkj.staggeredlist.ui.follow.FollowVideoViewModel
import com.bxkj.staggeredlist.ui.list.StaggeredVideoListViewModel
import com.bxkj.video.myvideolist.ui.MyVideoViewModel
import com.bxkj.video.ui.addinfolinkvideos.AddInfoLinkVideosViewModel
import com.bxkj.video.ui.galllery.VideoGalleryViewModel
import com.bxkj.video.ui.recruitmentplayer.PersonalRecruitmentPlayerViewModel
import com.bxkj.video.ui.recruitmentplayer.RecruitmentPlayerViewModel
import com.bxkj.video.ui.sendcontractinfo.SendContractInfoViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
@Module
abstract class VideoViewModelModule {

  @Binds
  @IntoMap
  @ViewModelKey(FollowVideoViewModel::class)
  abstract fun bindFollowVideoViewModel(followVideoViewModel: FollowVideoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(AddInfoLinkVideosViewModel::class)
  abstract fun bindAddInfoLinkVideosViewModel(addInfoLinkVideosViewModel: AddInfoLinkVideosViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(VideoGalleryViewModel::class)
  abstract fun bindVideoGalleryViewModel(videoGalleryViewModel: VideoGalleryViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(RecruitmentPlayerViewModel::class)
  abstract fun bindRecruitmentPlayerViewModel(recruitmentPlayerViewModel: RecruitmentPlayerViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(PersonalRecruitmentPlayerViewModel::class)
  abstract fun bindPersonalRecruitmentPlayerViewModel(personalRecruitmentPlayerViewModel: PersonalRecruitmentPlayerViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(VideoReportViewModel::class)
  abstract fun bindReportViewModel(videoReportViewModel: VideoReportViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(AddVideoLinkInfoViewModel::class)
  abstract fun bindAddVideoLinkInfoViewModel(addVideoLinkInfoViewModel: AddVideoLinkInfoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(StaggeredVideoListViewModel::class)
  abstract fun bindStaggeredVideoListViewModel(staggeredVideoListViewModel: StaggeredVideoListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SendContractInfoViewModel::class)
  abstract fun bindSendContractInfoViewModel(sendContractInfoViewModel: SendContractInfoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(MyVideoViewModel::class)
  abstract fun bindMyVideoViewModel(myVideoViewModel: MyVideoViewModel): BaseViewModel

}