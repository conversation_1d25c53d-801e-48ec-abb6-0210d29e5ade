package di

import com.bxkj.addvideolinkinfo.ui.addvideolinkinfos.AddVideoLinkInfoActivity
import com.bxkj.common.di.scope.PerActivity
import com.bxkj.common.di.scope.PerFragment
import com.bxkj.report.ui.videoreport.VideoReportActivity
import com.bxkj.staggeredlist.ui.follow.FollowVideoFragment
import com.bxkj.staggeredlist.ui.list.StaggeredVideoListFragment
import com.bxkj.video.myvideolist.ui.MyVideoFragment
import com.bxkj.video.ui.addinfolinkvideos.AddInfoLinkVideosActivity
import com.bxkj.video.ui.galllery.VideoGalleryActivity
import com.bxkj.video.ui.normalplayer.NormalPlayerFragment
import com.bxkj.video.ui.opencourseplayer.OpenCoursePlayerFragment
import com.bxkj.video.ui.personalrecruitmentplayer.PersonalRecruitmentPlayerFragment
import com.bxkj.video.ui.recruitmentplayer.RecruitmentPlayerFragment
import com.bxkj.video.ui.sendcontractinfo.VideoSendContractInfoDialog
import com.bxkj.videocut.ui.videocut.VideoCutActivity
import com.bxkj.videorecord.ui.videorecord.VideoRecordActivity
import com.bxkj.videorecord.ui.videorecord.VideoRecordActivityV2
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/20
 * @version: V1.0
 */
@Module
abstract class VideoUIModule {

  @ContributesAndroidInjector
  abstract fun videoRecordActivityV2():VideoRecordActivityV2

  @ContributesAndroidInjector
  abstract fun followVideoFragment(): FollowVideoFragment

  @PerActivity
  @ContributesAndroidInjector
  abstract fun addInfoLinkVideosActivity(): AddInfoLinkVideosActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun videoRecordActivity(): VideoRecordActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun videoCutActivity(): VideoCutActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun videoGalleryActivity(): VideoGalleryActivity

  @PerFragment
  @ContributesAndroidInjector
  abstract fun normalPlayerFragment(): NormalPlayerFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun recruitmentPlayerFragment(): RecruitmentPlayerFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun openCoursePlayerFragment(): OpenCoursePlayerFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun personalRecruitmentPlayerFragment(): PersonalRecruitmentPlayerFragment

  @PerActivity
  @ContributesAndroidInjector
  abstract fun videoReportActivity(): VideoReportActivity

  @PerActivity
  @ContributesAndroidInjector
  abstract fun addVideoLinkInfoActivity(): AddVideoLinkInfoActivity

  @PerFragment
  @ContributesAndroidInjector
  abstract fun staggeredVideoListFragment(): StaggeredVideoListFragment

  @PerFragment
  @ContributesAndroidInjector
  abstract fun videoSendContractInfoDialog(): VideoSendContractInfoDialog

  @PerFragment
  @ContributesAndroidInjector
  abstract fun myVideoFragment(): MyVideoFragment
}