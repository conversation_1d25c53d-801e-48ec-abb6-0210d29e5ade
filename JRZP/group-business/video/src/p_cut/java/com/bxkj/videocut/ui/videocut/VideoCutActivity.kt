package com.bxkj.videocut.ui.videocut

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.video.R
import com.bxkj.video.VideoConstants
import com.bxkj.video.databinding.VideoActivityVideoCutBinding
import com.tencent.qcloud.ugckit.UGCKit
import com.tencent.qcloud.ugckit.basic.JumpActivityMgr
import com.tencent.qcloud.ugckit.basic.UGCKitResult
import com.tencent.qcloud.ugckit.module.cut.IVideoCutKit
import com.tencent.qcloud.ugckit.utils.CoverUtil
import com.tencent.ugc.TXVideoInfoReader

/**
 * @Project: VideoRecruitment
 * @Description: 视频剪辑
 * @author:45457
 * @date: 2020/6/29
 * @version: V1.0
 */
@Route(path = VideoCutNavigation.PATH)
class VideoCutActivity : BaseDBActivity<VideoActivityVideoCutBinding, BaseViewModel>() {

    private val mOnCutListener = object : IVideoCutKit.OnCutListener {
        override fun onCutterCanceled() {
            val videoPath = getIntentVideoPath()
            val videoInfo =
                TXVideoInfoReader.getInstance(UGCKit.getAppContext())
                    .getVideoFileInfo(videoPath)
            val result = UGCKitResult()
            result.outputPath = videoPath
            result.width = videoInfo.width
            result.height = videoInfo.height
            //保存封面图片
            showLoading()
            CoverUtil.getInstance().setInputPath(videoPath)
            CoverUtil.getInstance().createThumbFile {
                hiddenLoading()
                result.coverPath = it
                returnVideoData(result)
            }
        }

        override fun onCutterCompleted(ugcKitResult: UGCKitResult?) {
            ugcKitResult?.let {
                if (ugcKitResult.errorCode == 0) {
                    val videoInfo =
                        TXVideoInfoReader.getInstance(UGCKit.getAppContext())
                            .getVideoFileInfo(ugcKitResult.outputPath)
                    it.width = videoInfo.width
                    it.height = videoInfo.height
                    returnVideoData(it)
                }
            }
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.video_activity_video_cut

    override fun initPage(savedInstanceState: Bundle?) {
        setupVideoCut()
    }

    private fun setupVideoCut() {
        JumpActivityMgr.getInstance().editFlagFromCut = false
        viewBinding.videoCutterLayout.titleBar.rightButton.text = getString(R.string.complete)
        viewBinding.videoCutterLayout.setMaxDuration(300)
        viewBinding.videoCutterLayout.setVideoPath(getIntentVideoPath())
        viewBinding.videoCutterLayout.setTargetBitrate(getIntentBitrate())
        viewBinding.videoCutterLayout.titleBar.setOnBackClickListener {
            finish()
        }
    }

    private fun getIntentBitrate(): Int {
        return intent.getIntExtra(
            VideoConstants.EXTRA_VIDEO_BITRATE,
            VideoConstants.VIDEO_DEFAULT_BITRATE
        )
    }

    private fun getIntentVideoPath() = intent.getStringExtra(VideoConstants.EXTRA_VIDEO_PATH)

    private fun returnVideoData(result: UGCKitResult) {
        val resultIntent = Intent()
        resultIntent.putExtra(VideoConstants.EXTRA_RESULT_VIDEO_INFO, result)
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }

    override fun onResume() {
        super.onResume()
        viewBinding.videoCutterLayout.setOnCutListener(mOnCutListener)
        viewBinding.videoCutterLayout.startPlay()
    }

    override fun onPause() {
        super.onPause()
        viewBinding.videoCutterLayout.stopPlay()
        viewBinding.videoCutterLayout.setOnCutListener(null)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewBinding.videoCutterLayout.release()
    }

}