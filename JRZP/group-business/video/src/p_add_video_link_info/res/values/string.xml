<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
  <!--=============AddVideoLinkInfoActivity(添加视频关联信息)============-->
  <string name="post_video_page_title">发布视频</string>
  <string name="post_video_link_resume">简历关联</string>
  <string name="post_video_link_job">职位关联</string>
  <string name="post_video_post">发布</string>
  <string name="post_video_locating">定位中…</string>
  <string name="post_video_location_failed">定位失败，点击重新定位</string>
  <string name="post_video_location_format">当前定位：%1$s</string>
  <string name="post_video_no_city_tips">请先获取当前定位</string>
  <string name="post_video_no_link_info_tips">请先选择关联信息</string>
  <string name="post_video_success">发布成功</string>
</resources>