package com.bxkj.video.ui.normalplayer

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import cn.jzvd.JZDataSource
import cn.jzvd.Jzvd
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.JZMediaExo
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.video.R
import com.bxkj.video.data.VideoData
import com.bxkj.video.databinding.FragmentNormalPlayerBinding

/**
 * @Project: VideoRecruitment
 * @Description: 普通视频播放
 * @author:45457
 * @date: 2020/6/16
 * @version: V1.0
 */
class NormalPlayerFragment : BaseDBFragment<FragmentNormalPlayerBinding, BaseViewModel>() {

  companion object {
    const val EXTRA_PLAY_VIDEO = "PLAY_VIDEO"

    fun newInstance(video: VideoData): NormalPlayerFragment {
      return NormalPlayerFragment().apply {
        arguments = bundleOf(EXTRA_PLAY_VIDEO to video)
      }
    }
  }

  override fun getViewModelClass(): Class<BaseViewModel> {
    return BaseViewModel::class.java
  }

  override fun getLayoutId(): Int = R.layout.fragment_normal_player

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    arguments?.let {
      val videoItemInfo =
        it.getParcelable<VideoData>(EXTRA_PLAY_VIDEO)
      videoItemInfo?.let {
        ImageLoader.loadImage(
          this,
          GlideLoadConfig.Builder().url(videoItemInfo.pic)
            .into(viewBinding.videoPlayer.posterImageView).build()
        )
        viewBinding.videoPlayer.setUp(
          JZDataSource(videoItemInfo.video, null),
          Jzvd.SCREEN_NORMAL,
          JZMediaExo::class.java
        )
      }
    }
  }

  override fun onResume() {
    super.onResume()
    viewBinding.videoPlayer.startVideoAfterPreloading()
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }
}