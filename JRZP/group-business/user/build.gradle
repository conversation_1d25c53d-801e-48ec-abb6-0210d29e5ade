plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
  id 'com.google.devtools.ksp'
}

android {

  namespace "com.bxkj.jrzp.user"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()

    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  buildFeatures {
    dataBinding = true
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }
}

dependencies {
  implementation fileTree(dir: "libs", include: ["*.jar","*.aar"])

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor

  implementation project(":lib-common")
  implementation project(':group-support:upload')
  implementation project(':group-support:share')
  implementation project(':group-support:feedback')
  implementation project(':group-support:scan')
  implementation project(':group-support:db')
  implementation project(':group-support:feature')

  implementation 'com.params.stepview:stepview:1.0.2'

  includeApi(':personal')
  includeApi(':accountmodule')
  includeApi(':group-enterprise:enterprise')

  includeApi(':group-business:learning')
  includeApi(':group-business:video')
  includeApi(':group-business:live')
}