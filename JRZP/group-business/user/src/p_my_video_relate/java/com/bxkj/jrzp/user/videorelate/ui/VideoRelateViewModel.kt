package com.bxkj.jrzp.user.videorelate.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.videorelate.data.VideoRelateResumeData.Resume
import com.bxkj.jrzp.user.videorelate.repository.VideoRelateRepository
import com.bxkj.video.VideoLinkMethod
import com.bxkj.video.VideoType
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.repository.OpenVideoRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
class VideoRelateViewModel @Inject constructor(
  private val mVideoRepository: OpenVideoRepository,
  private val mVideoRelateRepository: VideoRelateRepository
) : BaseViewModel() {

  val video = MutableLiveData<OnlineVideoData>()

  val toVideoPreviewCommand =
    LiveEvent<OnlineVideoData>()

  val isPersonalIdentity =
    MutableLiveData<Boolean>()

  val refreshRelateInfoCommand = LiveEvent<Int>()

  //个人版相关
  val relatedResumeList = MutableLiveData<ArrayList<Resume>>()
  private val _relatedResumeList = ArrayList<Resume>()
  val unrelatedResumeList = MutableLiveData<ArrayList<Resume>>()
  private val _unrelatedResumeList = ArrayList<Resume>()

  fun getVideoRelateResume() {
    video.value?.let {
      viewModelScope.launch {
        mVideoRelateRepository.getVideoRelateResume(getSelfUserID(), it.id)
          .handleResult({ result ->
            result?.let {
              _relatedResumeList.clear()
              _unrelatedResumeList.clear()
              _relatedResumeList.addAll(it.yiGuanlianList)
              _unrelatedResumeList.addAll(it.weiGuanlianList)
              refreshResumeList()
            }
          }, {
            if (it.isNoDataError) {
              _relatedResumeList.clear()
              _unrelatedResumeList.clear()
              refreshResumeList()
            } else {
              showToast(it.errMsg)
            }
          })
      }
    }
  }

  private fun refreshResumeList() {
    relatedResumeList.value = ArrayList(_relatedResumeList)
    unrelatedResumeList.value = ArrayList(_unrelatedResumeList)
  }

  fun start(videoInfo: OnlineVideoData, isPersonal: Boolean) {
    video.value = videoInfo
    isPersonalIdentity.value = isPersonal
  }

  fun cancelRelateResume(resume: Resume) {
    video.value?.let {
      viewModelScope.launch {
        showLoading()
        mVideoRepository.removeVideoLinkInfo(
          getSelfUserID(),
          VideoLinkMethod.VIDEO_LINK_ONE_VIDEO_TO_MORE,
          VideoType.VIDEO_TYPE_RESUME, it.id.toString(), resume.id.toString()
        ).handleResult({
          _relatedResumeList.remove(resume)
          _unrelatedResumeList.add(resume)
          refreshResumeList()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun addRelateResume(resume: Resume) {
    video.value?.let {
      viewModelScope.launch {
        showLoading()
        mVideoRepository.addVideoLinkInfo(
          getSelfUserID(),
          CommonApiConstants.NO_ID,
          VideoType.VIDEO_TYPE_RESUME,
          VideoLinkMethod.VIDEO_LINK_ONE_VIDEO_TO_MORE,
          it.id.toString(),
          resume.id.toString(),
          resume.name.toString()
        ).handleResult({
          _relatedResumeList.add(resume)
          _unrelatedResumeList.remove(resume)
          refreshResumeList()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun toVideoPreview() {
    video.value?.let {
      toVideoPreviewCommand.value = it
    }
  }

  fun refreshRelateInfoByStatus(relateStatus: Int) {
    refreshRelateInfoCommand.setValue(relateStatus, true)
  }
}