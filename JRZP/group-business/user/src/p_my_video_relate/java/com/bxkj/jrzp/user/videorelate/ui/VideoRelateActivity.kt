package com.bxkj.jrzp.user.videorelate.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.constants.RouterConstants
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityVideoRelateBinding
import com.bxkj.jrzp.user.videorelate.RelateStatus
import com.bxkj.jrzp.user.videorelate.data.VideoRelateResumeData.Resume
import com.bxkj.jrzp.user.videorelate.ui.relatejob.RelateJobsFragment
import com.bxkj.personal.ui.activity.createresumesteptwo.CreateResumeNavigation
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.ui.galllery.VideoGalleryNavigation
import com.bxkj.video.ui.galllery.VideoGalleryType
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * @Description: 视频管理
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
@Route(path = MyVideoRelateNavigation.PATH)
class VideoRelateActivity : BaseDBActivity<UserActivityVideoRelateBinding, VideoRelateViewModel>() {

    companion object {
        const val TO_VIDEO_PLAY_CODE = 1
    }

    override fun getViewModelClass(): Class<VideoRelateViewModel> = VideoRelateViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_activity_video_relate

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupTitleBarRightOptionClickListener()

        subscribeViewModelEvent()

        intent.getParcelableExtra<OnlineVideoData>(MyVideoRelateNavigation.EXTRA_VIDEO_INFO)?.let {
            viewModel.start(
                it,
                isPersonal()
            )
        }

        refreshRelateInfoList()
    }

    private fun setupRelateJobViewPager(videoId: Int) {
        val relateJobsPagerAdapter = CommonPagerAdapter(
            supportFragmentManager, arrayListOf(
                RelateJobsFragment.newInstance(RelateStatus.RELATED, videoId),
                RelateJobsFragment.newInstance(RelateStatus.DISRELATED, videoId)
            )
        )
        viewBinding.vpRelateJobs.adapter = relateJobsPagerAdapter

        val commonNavigator = CommonNavigator(this).apply {
            isAdjustMode = true
        }
        commonNavigator.adapter =
            MagicIndicatorAdapter(resources.getStringArray(R.array.user_video_relate)).apply {
                setOnTabClickListener(object : OnTabClickListener {
                    override fun onTabClicked(v: View, index: Int) {
                        viewBinding.vpRelateJobs.currentItem = index
                    }
                })
            }
        viewBinding.indicatorRelate.navigator = commonNavigator
        ViewPagerHelper.bind(viewBinding.indicatorRelate, viewBinding.vpRelateJobs)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        refreshRelateInfoList()
    }

    private fun refreshRelateInfoList() {
        if (isPersonal()) {
            viewModel.getVideoRelateResume()
        }
    }

    private fun setupTitleBarRightOptionClickListener() {
        viewBinding.titleBar.setRightText(
            if (isPersonal()) R.string.user_video_relate_create_resume else R.string.user_video_relate_post_job
        )

        viewBinding.titleBar.setRightOptionClickListener {
            if (isPersonal()) {
                CreateResumeNavigation.navigate(RouterConstants.CREATE_RESUME_FROM_VIDEO_RELATE)
                    .start()
            } else {
                PostJobNavigation.navigate(PostJobNavigation.TYPE_FULL_TIME, PostJobNavigation.NEXT_BACK).start()
            }
        }
    }

    private fun isPersonal(): Boolean {
        return intent.getIntExtra(
            MyVideoRelateNavigation.EXTRA_USER_TYPE,
            AppConstants.USER_TYPE_PERSONAL
        ) == AppConstants.USER_TYPE_PERSONAL
    }

    private fun subscribeViewModelEvent() {
        viewModel.toVideoPreviewCommand.observe(this, Observer {
            VideoGalleryNavigation.navigate(
                VideoGalleryType.USER_RELEASE_VIDEO, VideoListMassage.from(
                    0,
                    arrayListOf(it), VideoGalleryType.USER_RELEASE_VIDEO
                )
            ).startForResult(this, TO_VIDEO_PLAY_CODE)
        })

        viewModel.video.observe(this, Observer {
            if (isPersonal()) {
                setupResumeListAdapter()
            } else {
                setupRelateJobViewPager(it.id)
            }
        })
    }

    private fun setupResumeListAdapter() {
        val relateResumeListAdapter =
            object : SimpleDiffListAdapter<Resume>(
                R.layout.user_recycler_video_relate_resume_item,
                Resume.DiffCallback()
            ) {
                override fun bind(holder: SuperViewHolder, item: Resume, position: Int) {
                    super.bind(holder, item, position)
                    holder.findViewById<TextView>(R.id.tv_relate).apply {
                        isSelected = true
                        text = getString(R.string.user_video_cancel_relate)
                    }
                }
            }.apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.let {
                            viewModel.cancelRelateResume(it[position])
                        }
                    }
                })
            }
        viewBinding.recyclerRelatedResume.layoutManager = LinearLayoutManager(this)
        viewBinding.recyclerRelatedResume.addItemDecoration(
            LineItemDecoration(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.divider_f4f4f4
                ), LinearLayoutManager.VERTICAL
            )
        )
        viewBinding.recyclerRelatedResume.closeDefaultAnim()
        viewBinding.recyclerRelatedResume.adapter = relateResumeListAdapter

        val unrelateResumeListAdapter =
            object : SimpleDiffListAdapter<Resume>(
                R.layout.user_recycler_video_relate_resume_item,
                Resume.DiffCallback()
            ) {
                override fun bind(holder: SuperViewHolder, item: Resume, position: Int) {
                    super.bind(holder, item, position)
                    holder.findViewById<TextView>(R.id.tv_relate).apply {
                        isSelected = false
                        text = getString(R.string.user_video_add_relate)
                    }
                }
            }.apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.let {
                            viewModel.addRelateResume(it[position])
                        }
                    }
                })
            }
        viewBinding.recyclerUnrelatedResume.layoutManager = LinearLayoutManager(this)
        viewBinding.recyclerUnrelatedResume.addItemDecoration(
            LineItemDecoration(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.divider_f4f4f4
                ), LinearLayoutManager.VERTICAL
            )
        )
        viewBinding.recyclerUnrelatedResume.closeDefaultAnim()
        viewBinding.recyclerUnrelatedResume.adapter = unrelateResumeListAdapter
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == TO_VIDEO_PLAY_CODE && resultCode == VideoGalleryNavigation.RESULT_DELETE_SUCCESS) {
            setResult(MyVideoRelateNavigation.RESULT_VIDEO_DELETED)
            finish()
        }
    }

}