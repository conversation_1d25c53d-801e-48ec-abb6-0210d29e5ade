package com.bxkj.jrzp.user.withdraw.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.jrzp.user.withdraw.data.WithdrawHistoryItemData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/1
 * @version: V1.0
 */
interface WithdrawApi {

  @POST("/UserTixian/AddUserTixian/")
  suspend fun submitWithdraw(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/UserTixian/GetMyTixianListByPage/")
  suspend fun getWithdrawHistory(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<WithdrawHistoryItemData>>
}