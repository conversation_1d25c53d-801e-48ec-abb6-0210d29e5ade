package com.bxkj.jrzp.user.withdraw.ui.history

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.withdraw.repository.WithdrawRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/2
 * @version: V1.0
 */
class WithdrawHistoryViewModel @Inject constructor(private val mWithdrawRepository: WithdrawRepository) :
  BaseViewModel() {
  val withdrawHistoryListViewModel = RefreshListViewModel()

  init {
    setupWithdrawHistoryListViewModel()
  }

  private fun setupWithdrawHistoryListViewModel() {
    withdrawHistoryListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mWithdrawRepository.getWithdrawHistory(
          getSelfUserID(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          withdrawHistoryListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            withdrawHistoryListViewModel.noMoreData()
          } else {
            withdrawHistoryListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start() {
    withdrawHistoryListViewModel.refresh()
  }

}
