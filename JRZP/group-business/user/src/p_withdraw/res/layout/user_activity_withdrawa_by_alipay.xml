<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.withdraw.ui.alipay.WithdrawByAlipayViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/frame_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/user_withdraw_history"
      app:show_right_text_bg="false"
      app:title="@string/user_withdraw_by_alipay_page_title" />

    <TextView
      style="@style/Text.InputTips.FE6600"
      android:text="@string/user_withdraw_rule" />

    <LinearLayout
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff"
      android:orientation="vertical">

      <LinearLayout
        style="@style/Layout.InfoItem"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:visibility="@{viewModel.hasAccount?View.VISIBLE:View.GONE}">

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:text="@string/user_withdraw_by_alipay_account" />

        <TextView
          android:id="@+id/tv_alipay_account"
          style="@style/Text.InfoItem.Select"
          android:onClick="@{onClickListener}"
          android:text="@{@string/user_withdraw_by_alipay_account_format(viewModel.alipayName,viewModel.alipayAccount)}"
          android:textSize="@dimen/sp_14" />

      </LinearLayout>

      <View
        style="@style/Line.Horizontal"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:visibility="@{viewModel.hasAccount?View.VISIBLE:View.GONE}" />

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16"
        android:visibility="@{viewModel.hasAccount?View.GONE:View.VISIBLE}">

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:text="@string/user_withdraw_by_alipay_account" />

        <EditText
          style="@style/Text.14sp.333333"
          android:layout_width="match_parent"
          android:background="@null"
          android:hint="@string/user_withdraw_by_alipay_account_hint"
          android:paddingTop="@dimen/dp_12"
          android:paddingBottom="@dimen/dp_8"
          android:text="@={viewModel.alipayAccount}"
          android:textColorHint="@color/cl_999999" />

        <View style="@style/Line.Horizontal" />
      </LinearLayout>

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16"
        android:visibility="@{viewModel.hasAccount?View.GONE:View.VISIBLE}">

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:text="@string/user_withdraw_by_alipay_name" />

        <EditText
          style="@style/Text.14sp.333333"
          android:layout_width="match_parent"
          android:background="@null"
          android:hint="@string/user_withdraw_by_alipay_name_hint"
          android:paddingTop="@dimen/dp_12"
          android:paddingBottom="@dimen/dp_8"
          android:text="@={viewModel.alipayName}"
          android:textColorHint="@color/cl_999999" />

        <View style="@style/Line.Horizontal" />
      </LinearLayout>

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16">

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:text="@string/user_withdraw_money" />

        <LinearLayout
          style="@style/match_wrap"
          android:gravity="center_vertical"
          android:orientation="horizontal">

          <EditText
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_weight="1"
            android:background="@null"
            android:hint="@{viewModel.accountBalanceHint}"
            android:inputType="number"
            android:paddingTop="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_8"
            android:text="@={viewModel.withdrawAmount}"
            android:textColorHint="@color/cl_999999" />

          <TextView
            style="@style/Text.14sp.FE6600"
            android:onClick="@{()->viewModel.withdrawAll()}"
            android:text="@string/user_withdraw_all" />
        </LinearLayout>

        <View style="@style/Line.Horizontal" />
      </LinearLayout>

    </LinearLayout>

    <TextView
      style="@style/Button.Basic"
      android:layout_margin="@dimen/dp_30"
      android:enabled="@{!CheckUtils.isNullOrEmpty(viewModel.alipayName)&amp;&amp;!CheckUtils.isNullOrEmpty(viewModel.alipayAccount)&amp;&amp;!CheckUtils.isNullOrEmpty(viewModel.withdrawAmount)&amp;&amp;Float.valueOf(viewModel.withdrawAmount)>100}"
      android:onClick="@{()->viewModel.submitWithdraw()}"
      android:text="@string/user_withdraw_now" />

  </LinearLayout>
</layout>