package com.bxkj.jrzp.commission.ui.list

import android.content.Context
import com.bxkj.common.adapter.CommonDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.commission.data.CommissionItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
class CommissionListAdapter constructor(context: Context) : CommonDBListAdapter<CommissionItemData>(
  context,
  R.layout.user_recycler_commission_list_item
) {
  override fun onBindViewHolder(holder: SuperViewHolder, position: Int, payloads: MutableList<Any>) {
    super.onBindViewHolder(holder, position, payloads)

  }
}