package com.bxkj.jrzp.orderrecord.ui.details

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.jrzp.orderrecord.CourseOrderRxCode
import com.bxkj.jrzp.orderrecord.data.CoursesOrderData
import com.bxkj.jrzp.orderrecord.ui.review.CourseReviewNavigation
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.string
import com.bxkj.jrzp.user.databinding.UserActivityCourseOrderDetailsBinding
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.bxkj.share.ShareCommodityParams
import com.bxkj.share.ui.CommodityShareDialog.Builder
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * @Description: 课程详情
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
@Route(path = CourseOrderDetailsNavigation.PATH)
class CourseOrderDetailsActivity :
  BaseDBActivity<UserActivityCourseOrderDetailsBinding, CourseOrderDetailsViewModel>(),
  OnClickListener {

  override fun getViewModelClass(): Class<CourseOrderDetailsViewModel> =
    CourseOrderDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_course_order_details

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeCourseOrderReviewSuccess()

    subscribeViewModelEvent()

    viewModel.start(intent.getIntExtra(CourseOrderDetailsNavigation.EXTRA_ORDER_ID, 0))
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.iv_left) {
        finish()
      }
    }
  }

  private fun subscribeCourseOrderReviewSuccess() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == CourseOrderRxCode.ADD_COURSE_ORDER_REVIEW_SUCCESS) {
            if (it.msg is CoursesOrderData) {
              viewModel.updateCourseOrderReviewState(it.msg as CoursesOrderData)
            }
          }
        }
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.courseOrder.observe(this, Observer { order ->
      if (order.groupBuying()) {
        order.pintuanEdate?.let { timeDiff ->
          val startDiffSecond = timeDiff.toFloat().toInt() * 1000L
          Observable.interval(0, 1, TimeUnit.MILLISECONDS)
            .take(startDiffSecond + 1)
            .map<Long> { aLong -> startDiffSecond - aLong }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : io.reactivex.Observer<Long> {
              override fun onComplete() {
                order.groupBuyFailed()
              }

              override fun onSubscribe(d: Disposable) {
                addDisposable(d)
              }

              override fun onNext(countDown: Long) {
                viewBinding.tvGroupBuyState.text = HtmlUtils.fromHtml(
                  getString(
                    R.string.user_course_order_details_group_buy_count_down_format,
                    order.getGroupRemainingCount(),
                    TimeUtils.millisecondFormat(countDown)
                  )
                )
              }

              override fun onError(e: Throwable) {
              }
            })
        }
      }
    })

    viewModel.toCourseDetailsCommand.observe(this, Observer {
      LearningWebNavigation.navigate(it.type, it.kcid, localUserId).start()
    })

    viewModel.contractServiceCommand.observe(this, Observer {
      SystemUtil.callPhone(this, it)
    })

    viewModel.copyOrderNoCommand.observe(this, Observer {
      SystemUtil.copy(this, "OrderNo", it)
    })

    viewModel.toReviewCommand.observe(this, Observer {
      CourseReviewNavigation.navigate(it).start()
    })

    viewModel.shareCommand.observe(this, Observer {
      Builder()
        .setShareTitle(it.title)
        .setShareContent(it.content)
        .setShareUrl(it.shareUrl)
        .setSharePic(it.pic)
        .setCommissionNotice(it.orderList)
        .setCommodityParams(ShareCommodityParams.from(it.pic, it.name, it.money))
        .setShareTipsTitle(getString(string.user_course_order_share_tips_title))
        .setShareTips(getString(string.user_course_order_share_tips))
        .build().show(supportFragmentManager)
    })
  }
}