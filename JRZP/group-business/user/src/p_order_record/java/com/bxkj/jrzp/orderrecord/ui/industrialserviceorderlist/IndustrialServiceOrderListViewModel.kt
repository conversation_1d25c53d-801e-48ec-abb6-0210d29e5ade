package com.bxkj.jrzp.orderrecord.ui.industrialserviceorderlist

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.orderrecord.data.IndustrialServiceOrderData
import com.bxkj.jrzp.orderrecord.repository.CoursesOrderRepository
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderReviewStatus
import com.bxkj.jrzp.orderrecord.ui.list.OrderPaymentStatus
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/5/8
 */
class IndustrialServiceOrderListViewModel @Inject constructor(
  private val coursesOrderRepository: CoursesOrderRepository
) : BaseViewModel() {

  val industrialServiceOrderListViewModel = RefreshListViewModel()

  private var paymentStatus: Int = OrderPaymentStatus.ALL
  private var commentStatus: Int = CoursesOrderReviewStatus.ALL

  init {
    industrialServiceOrderListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        coursesOrderRepository.getIndustrialServiceOrderList(
          getSelfUserID(),
          paymentStatus,
          commentStatus,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          industrialServiceOrderListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            industrialServiceOrderListViewModel.noMoreData()
          } else {
            industrialServiceOrderListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start(paymentStatus: Int, commentStatus: Int) {
    this.paymentStatus = paymentStatus
    this.commentStatus = commentStatus

    industrialServiceOrderListViewModel.refresh()
  }

  fun updateCourseOrderReviewState(industrialServiceOrder: IndustrialServiceOrderData) {
    industrialServiceOrderListViewModel.replace(industrialServiceOrder)
  }

  fun cancelOrder(clickItem: IndustrialServiceOrderData) {
    viewModelScope.launch {
      showLoading()
      coursesOrderRepository.cancelServiceOrder(getSelfUserID(), clickItem.id)
        .handleResult({
          industrialServiceOrderListViewModel.remove(clickItem)
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }
}