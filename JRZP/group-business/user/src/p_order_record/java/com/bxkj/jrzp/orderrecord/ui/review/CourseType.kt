package com.bxkj.jrzp.orderrecord.ui.review

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
class CourseType {
  companion object {
    const val SKILL = 1
    const val EDUCATION = 2
  }

  @IntDef(SKILL, EDUCATION)
  @Target(VALUE_PARAMETER)
  @Retention(SOURCE)
  annotation class Type
}