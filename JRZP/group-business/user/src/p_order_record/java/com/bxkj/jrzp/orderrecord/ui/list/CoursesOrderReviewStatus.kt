package com.bxkj.jrzp.orderrecord.ui.list

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class CoursesOrderReviewStatus {
  companion object {
    const val ALL = -1
    const val NOT_REVIEW = 0
    const val REVIEWED = 1
  }

  @IntDef(ALL, NOT_REVIEW, REVIEWED)
  @Retention(SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Status
}