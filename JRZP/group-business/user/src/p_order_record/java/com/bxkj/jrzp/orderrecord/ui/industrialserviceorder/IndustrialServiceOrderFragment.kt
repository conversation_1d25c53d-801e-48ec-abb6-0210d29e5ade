package com.bxkj.jrzp.orderrecord.ui.industrialserviceorder

import android.os.Bundle
import android.view.View
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.jrzp.orderrecord.ui.industrialserviceorderlist.IndustrialServiceOrderListFragment
import com.bxkj.jrzp.orderrecord.ui.list.OrderPaymentStatus
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderReviewStatus
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.array
import com.bxkj.jrzp.user.databinding.UserFragmentIndustrialServiceOrderBinding
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * 工业服务订单
 * @author: YangXin
 * @date: 2021/5/8
 */
class IndustrialServiceOrderFragment :
    BaseDBFragment<UserFragmentIndustrialServiceOrderBinding, BaseViewModel>() {

    companion object {
        fun newInstance(): IndustrialServiceOrderFragment {
            return IndustrialServiceOrderFragment()
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_fragment_industrial_service_order

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        setupVpContent()
    }

     override fun initImmersionBar() {
        statusBarManager.statusBarDarkFont(true, 0.4f).init()
    }

    private fun setupVpContent() {
        val contentFragments = arrayListOf(
            IndustrialServiceOrderListFragment.newInstance(OrderPaymentStatus.ALL),
            IndustrialServiceOrderListFragment.newInstance(OrderPaymentStatus.UNPAID),
            IndustrialServiceOrderListFragment.newInstance(OrderPaymentStatus.PAID),
            IndustrialServiceOrderListFragment.newInstance(
                OrderPaymentStatus.ALL,
                CoursesOrderReviewStatus.REVIEWED
            )
        )
        viewBinding.vpContent.offscreenPageLimit = contentFragments.size
        viewBinding.vpContent.adapter = CommonPagerAdapter(childFragmentManager, contentFragments)

        setupCoursesOrderTypeIndicator()
    }

    private fun setupCoursesOrderTypeIndicator() {
        viewBinding.indicatorOrderType.navigator = CommonNavigator(parentActivity).apply {
            isAdjustMode = true
            adapter =
                MagicIndicatorAdapter(resources.getStringArray(array.user_order_status_type)).apply {
                    setOnTabClickListener(object : OnTabClickListener {
                        override fun onTabClicked(v: View, index: Int) {
                            viewBinding.vpContent.currentItem = index
                        }
                    })
                }
        }
        ViewPagerHelper.bind(viewBinding.indicatorOrderType, viewBinding.vpContent)
    }
}