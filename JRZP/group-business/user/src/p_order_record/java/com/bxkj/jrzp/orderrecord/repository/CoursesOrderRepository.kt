package com.bxkj.jrzp.orderrecord.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.orderrecord.api.CoursesOrderApi
import com.bxkj.jrzp.orderrecord.data.CourseOrderGroupBuyShareData
import com.bxkj.jrzp.orderrecord.data.CourseRatingTagData
import com.bxkj.jrzp.orderrecord.data.CoursesOrderData
import com.bxkj.jrzp.orderrecord.data.IndustrialServiceOrderData
import com.bxkj.jrzp.orderrecord.ui.list.OrderPaymentStatus.Status
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderReviewStatus
import com.bxkj.jrzp.orderrecord.ui.review.CourseType.Type
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
class CoursesOrderRepository @Inject constructor(
  private val mCoursesOrderApi: CoursesOrderApi
) : BaseRepo() {

  /**
   * 取消订单支付
   */
  suspend fun cancelServiceOrder(userId: Int, orderId: Int): ReqResponse<Nothing> {
    return httpRequest {
      mCoursesOrderApi.cancelServiceOrder(
        ZPRequestBody().apply {
          put("yp_id", userId)
          put("id", orderId)
        }
      )
    }
  }

  /**
   * 添加服务订单评价
   */
  suspend fun addServiceOrderComment(
    orderId: Int,
    productId: Int,
    score: Int,
    content: String,
    businessId: Int,
    selfUserId: Int,
    serviceType: Int,
    anonymous: Boolean
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoursesOrderApi.addServiceOrderComment(
        ZPRequestBody().apply {
          put("ypid", businessId)
          put("ypid2", selfUserId)
          put("lanmu", serviceType)
          put("orderid", orderId)
          put("proid", productId)
          put("score", score)
          put("content", content)
          put("niming", if (anonymous) 1 else 0)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 工服订单详情
   */
  suspend fun getServiceOrderDetails(
    userId: Int,
    orderId: Int
  ): ReqResponse<IndustrialServiceOrderData> {
    return httpRequest {
      mCoursesOrderApi.getServiceOrderDetails(
        ZPRequestBody().apply {
          put("yp_id", userId)
          put("id", orderId)
        }
      )
    }
  }

  /**
   * 获取工业服务订单列表
   */
  suspend fun getIndustrialServiceOrderList(
    userId: Int,
    paymentStatus: Int,
    commented: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<IndustrialServiceOrderData>> {
    return httpRequest {
      mCoursesOrderApi.getIndustrialServiceOrderList(
        ZPRequestBody().apply {
          put("yp_id", userId)
          put("ispay", paymentStatus)
          put("pingjia", commented)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }
      )
    }
  }

  /**
   * 返回课程订单列表
   */
  suspend fun getOrderList(
    userID: Int,
    coursesType: Int,
    @Status paymentStatus: Int,
    @CoursesOrderReviewStatus.Status reviewStatus: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<CoursesOrderData>> {
    return httpRequest {
      mCoursesOrderApi.getCoursesOrderList(
        mapOf(
          "uid" to userID,
          "type" to coursesType,
          "isPay" to paymentStatus,
          "pingjia" to reviewStatus,
          "pageIndex" to pageIndex,
          "pageSize" to pageSize
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 取消订单
   */
  suspend fun cancelCourseOrder(userId: Int, orderId: Int): ReqResponse<Nothing> {
    return httpRequest {
      mCoursesOrderApi.cancelCourseOrder(
        mapOf(
          "uid" to userId,
          "oid" to orderId
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 获取课程评价标签
   */
  suspend fun getCourseRatingTags(@Type type: Int): ReqResponse<List<CourseRatingTagData>> {
    return httpRequest {
      mCoursesOrderApi.getCourseRatingTag(
        mapOf(
          "type" to type
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 添加评论
   */
  suspend fun addOrderReview(
    userId: Int,
    orderId: Int,
    @Type courseType: Int,
    courseId: Int,
    reviewType: Int,
    star: Int,
    tags: String,
    content: String,
    pics: String,
    anonymous: Boolean
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoursesOrderApi.addOrderReview(
        mapOf(
          "pid" to 0,
          "uid" to userId,
          "oid" to orderId,
          "kcType" to courseType,
          "kcId" to courseId,
          "type" to reviewType,
          "score" to star,
          "labelIds" to tags,
          "content" to content,
          "pic" to pics,
          "niming" to if (anonymous) 1 else 0
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 获取订单详情
   */
  suspend fun getCourseOrderDetails(userId: Int, orderId: Int): ReqResponse<CoursesOrderData> {
    return httpRequest {
      mCoursesOrderApi.getCourseOrderDetails(
        mapOf(
          "uid" to userId,
          "id" to orderId
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 获取拼单分享信息
   */
  suspend fun getGroupBuyShareInfo(orderId: Int): ReqResponse<CourseOrderGroupBuyShareData> {
    return httpRequest {
      mCoursesOrderApi.getGroupBuyShareInfo(
        mapOf("oid" to orderId).paramsEncrypt()
      )
    }
  }
}