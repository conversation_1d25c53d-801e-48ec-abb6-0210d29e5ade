package com.bxkj.jrzp.orderrecord.ui.details

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.AESOperator
import com.bxkj.jrzp.orderrecord.data.CourseOrderGroupBuyShareData
import com.bxkj.jrzp.orderrecord.data.CoursesOrderData
import com.bxkj.jrzp.orderrecord.repository.CoursesOrderRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
class CourseOrderDetailsViewModel @Inject constructor(
  private val mCoursesOrderRepository: CoursesOrderRepository
) : BaseViewModel() {
  val courseOrder = MutableLiveData<CoursesOrderData>()

  val toCourseDetailsCommand =
    LiveEvent<CoursesOrderData>()
  val contractServiceCommand = LiveEvent<String>()
  val copyOrderNoCommand = LiveEvent<String>()
  val toReviewCommand = LiveEvent<CoursesOrderData>()
  val shareCommand =
    LiveEvent<CourseOrderGroupBuyShareData>()

  fun start(orderID: Int) {
    getCourseOrderDetails(orderID)
  }

  private fun getCourseOrderDetails(orderID: Int) {
    viewModelScope.launch {
      mCoursesOrderRepository.getCourseOrderDetails(getSelfUserID(), orderID)
        .handleResult({
          courseOrder.value = it
        })
    }
  }

  fun toCourseDetails() {
    courseOrder.value?.let {
      toCourseDetailsCommand.value = it
    }
  }

  fun contractService() {
    courseOrder.value?.let {
      contractServiceCommand.value = it.kefuPhone
    }
  }

  fun copyOrderNo() {
    courseOrder.value?.let {
      copyOrderNoCommand.value = it.num
    }
  }

  fun toReview() {
    courseOrder.value?.let {
      toReviewCommand.value = it
    }
  }

  fun share() {
    courseOrder.value?.let { order ->
      if (order.groupBuying()) {
        viewModelScope.launch {
          showLoading()
          mCoursesOrderRepository.getGroupBuyShareInfo(order.id)
            .handleResult({ shareInfo ->
              shareInfo?.let {
                if (order.isSkill()) {
                  shareInfo.shareUrl =
                    "https://m.jrzp.com/jineng/kcView.aspx?kcid=${order.kcid}&sort=${order.canshu1}&quid=${order.canshu2}&pintuanTh=${order.num}&oid=${AESOperator.safeEncrypt(
                      order.id.toString()
                    )}"
                } else {
                  shareInfo.shareUrl =
                    "https://m.jrzp.com/xueli/kcView.aspx?kcid=${order.kcid}&sort=${order.canshu1}&quid=${order.canshu2}&profe=${order.canshu3}&pintuanTh=${order.num}&oid=${AESOperator.safeEncrypt(
                      order.id.toString()
                    )}"
                }
                shareCommand.value = shareInfo
              }
            }, {
              showToast(it.errMsg)
            }, {
              hideLoading()
            })
        }
      }
    }
  }

  fun updateCourseOrderReviewState(order: CoursesOrderData) {
    courseOrder.value?.let {
      it.updateReviewState(order.pingjia)
    }
  }
}