package com.bxkj.jrzp.orderrecord.ui.list

import android.app.Activity
import android.content.Intent
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.AESOperator
import com.bxkj.jrzp.orderrecord.data.CourseOrderGroupBuyShareData
import com.bxkj.jrzp.orderrecord.data.CoursesOrderData
import com.bxkj.jrzp.orderrecord.repository.CoursesOrderRepository
import com.bxkj.jrzp.orderrecord.ui.review.CourseReviewNavigation
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class CoursesOrderListViewModel @Inject constructor(
  private val mCoursesOrderRepository: CoursesOrderRepository
) : BaseViewModel() {

  val coursesOrderListViewModel = RefreshListViewModel()

  val shareCommand =
    LiveEvent<CourseOrderGroupBuyShareData>()

  private var mPaymentStatus = OrderPaymentStatus.ALL
  private var mReviewStatus = CoursesOrderReviewStatus.ALL

  init {
    setupCoursesOrderListViewModel()
  }

  private fun setupCoursesOrderListViewModel() {
    coursesOrderListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mCoursesOrderRepository.getOrderList(
          getSelfUserID(),
          0,
          mPaymentStatus,
          mReviewStatus,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          coursesOrderListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            coursesOrderListViewModel.noMoreData()
          } else {
            coursesOrderListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start(paymentStatus: Int?, reviewStatus: Int?) {
    paymentStatus?.let {
      mPaymentStatus = it
    }
    reviewStatus?.let {
      mReviewStatus = it
    }
    coursesOrderListViewModel.refresh()
  }

  fun cancelOrder(orderItem: CoursesOrderData) {
    viewModelScope.launch {
      showLoading()
      mCoursesOrderRepository.cancelCourseOrder(getSelfUserID(), orderItem.id)
        .handleResult({
          if (coursesOrderListViewModel.remove(orderItem) == 0) {
            coursesOrderListViewModel.refresh()
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun share(order: CoursesOrderData) {
    viewModelScope.launch {
      showLoading()
      mCoursesOrderRepository.getGroupBuyShareInfo(order.id)
        .handleResult({ shareInfo ->
          shareInfo?.let {
            if (order.isSkill()) {
              shareInfo.shareUrl =
                "https://m.jrzp.com/jineng/kcView.aspx?kcid=${order.kcid}&sort=${order.canshu1}&quid=${order.canshu2}&pintuanTh=${order.num}&oid=${AESOperator.safeEncrypt(
                  order.id.toString()
                )}"
            } else {
              shareInfo.shareUrl =
                "https://m.jrzp.com/xueli/kcView.aspx?kcid=${order.kcid}&sort=${order.canshu1}&quid=${order.canshu2}&profe=${order.canshu3}&pintuanTh=${order.num}&oid=${AESOperator.safeEncrypt(
                  order.id.toString()
                )}"
            }
            shareCommand.value = shareInfo
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == CoursesOrderListFragment.TO_COURSE_REVIEW_CODE && resultCode == Activity.RESULT_OK) {
      data?.let {
        val reviewOrder =
          it.getParcelableExtra<CoursesOrderData>(CourseReviewNavigation.EXTRA_COURSE_ORDER)
        return coursesOrderListViewModel.replace(reviewOrder)
      }
    }
  }

  fun updateCourseOrderReviewState(order: CoursesOrderData) {
    coursesOrderListViewModel.replace(order)
  }
}