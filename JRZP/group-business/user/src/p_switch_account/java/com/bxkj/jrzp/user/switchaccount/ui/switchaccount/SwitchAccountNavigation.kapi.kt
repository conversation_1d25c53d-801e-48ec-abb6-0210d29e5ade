package com.bxkj.jrzp.user.switchaccount.ui.switchaccount

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/19
 * @version: V1.0
 */
class SwitchAccountNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/switchaccount"

    @JvmStatic
    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}