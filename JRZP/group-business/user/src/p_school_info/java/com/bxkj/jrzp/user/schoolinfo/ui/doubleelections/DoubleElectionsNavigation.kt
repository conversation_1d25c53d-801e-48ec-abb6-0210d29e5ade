package com.bxkj.jrzp.user.schoolinfo.ui.doubleelections

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
class DoubleElectionsNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/doubleelections"


    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}