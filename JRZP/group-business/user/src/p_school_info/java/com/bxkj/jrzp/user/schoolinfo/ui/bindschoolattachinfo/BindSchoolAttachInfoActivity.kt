package com.bxkj.jrzp.user.schoolinfo.ui.bindschoolattachinfo

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityBindSchoolAttachInfoBinding
import com.bxkj.jrzp.user.schoolinfo.data.SchoolAttachInfoData

/**
 * @Description: 绑定学校附加信息
 * @author: YangXin
 * @date: 2020/11/26
 * @version: V1.0
 */
@Route(path = BindSchoolAttachInfoNavigation.PATH)
class BindSchoolAttachInfoActivity :
  BaseDBActivity<UserActivityBindSchoolAttachInfoBinding, BindSchoolAttachInfoViewModel>() {
  override fun getViewModelClass(): Class<BindSchoolAttachInfoViewModel> =
    BindSchoolAttachInfoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_bind_school_attach_info

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    if (getIntentOpenConfirmBack()) {
      listenKeyboardConfirm()
    }

    setupSearchResultList()

    subscribeViewModelEvent()

    handlePageType()

    viewModel.start(
      intent.getIntExtra(
        BindSchoolAttachInfoNavigation.EXTRA_ATTACH_INFO_TYPE,
        BindSchoolAttachInfoNavigation.SCHOOL_DEPARTMENT
      ),
      intent.getStringExtra(BindSchoolAttachInfoNavigation.EXTRA_LAST_SELECT_NAME),
      intent.getIntExtra(BindSchoolAttachInfoNavigation.EXTRA_PARENT_ID, CommonApiConstants.NO_ID)
    )
  }

  private fun getIntentOpenConfirmBack(): Boolean {
    return intent.getBooleanExtra(BindSchoolAttachInfoNavigation.EXTRA_OPEN_CONFIRM_BACK, false)
  }

  private fun listenKeyboardConfirm() {
    viewBinding.etSearchContent.setOnEditorActionListener { v, actionId, event ->
      if (actionId == EditorInfo.IME_ACTION_DONE) {
        backAndResult(
          SchoolAttachInfoData.fromName(
            viewBinding.etSearchContent.text?.toString().getOrDefault()
          )
        )
        return@setOnEditorActionListener true
      }
      return@setOnEditorActionListener false
    }
//    dataBinding.etSearchContent.setOnKeyListener { v, keyCode, event ->
//      if (keyCode == KeyEvent.KEYCODE_ENTER && keyCode == KeyEvent.ACTION_DOWN) {
//        backAndResult(
//          SchoolAttachInfoData.fromName(
//            dataBinding.etSearchContent.text?.toString().safely()
//          )
//        )
//        return@setOnKeyListener true
//      }
//      return@setOnKeyListener false
//    }
  }

  private fun handlePageType() {
    intent.getIntExtra(
      BindSchoolAttachInfoNavigation.EXTRA_ATTACH_INFO_TYPE,
      BindSchoolAttachInfoNavigation.SCHOOL_DEPARTMENT
    ).let {
      when (it) {
        BindSchoolAttachInfoNavigation.SCHOOL -> {
          viewBinding.titleBar.setTitle(getString(R.string.user_bind_school_attach_info_school_page_title))
          viewBinding.etSearchContent.setHint(R.string.user_bind_school_attach_info_school_hint)
        }
        BindSchoolAttachInfoNavigation.SCHOOL_DEPARTMENT -> {
          viewBinding.titleBar.setTitle(getString(R.string.user_bind_school_attach_info_department_page_title))
          viewBinding.etSearchContent.setHint(R.string.user_bind_school_attach_info_department_hint)
        }
        else -> {
          viewBinding.titleBar.setTitle(getString(R.string.user_bind_school_attach_info_permission_page_title))
          viewBinding.etSearchContent.setHint(R.string.user_bind_school_attach_info_permission_hint)
        }
      }
    }
  }

  private fun setupSearchResultList() {
    val searchResultListAdapter = SimpleDBListAdapter<SchoolAttachInfoData>(
      this,
      R.layout.user_recycler_school_attach_info_list_item,
      BR.data
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          backAndResult(data[position])
        }
      })
    }

    viewBinding.includeSearchResult.recyclerContent.layoutManager = LinearLayoutManager(this)
    viewBinding.includeSearchResult.recyclerContent.addItemDecoration(
      LineItemDecoration.Builder()
        .divider(getResDrawable(R.drawable.divider_f4f4f4))
        .margin(dip(12))
        .build()
    )

    viewModel.searchResultListViewModel.setAdapter(searchResultListAdapter)
  }

  private fun subscribeViewModelEvent() {
    viewModel.searchContent.observe(this, Observer {
      viewModel.startSearch()
    })

    viewModel.resultSearchContentCommand.observe(this, Observer {
      backAndResult(SchoolAttachInfoData.fromName(it))
    })
  }

  private fun backAndResult(schoolAttachInfoData: SchoolAttachInfoData) {
    setResult(Activity.RESULT_OK,
      Intent().apply {
        putExtra(BindSchoolAttachInfoNavigation.EXTRA_RESULT_ATTACH_INFO, schoolAttachInfoData)
      })
    finish()
  }

}