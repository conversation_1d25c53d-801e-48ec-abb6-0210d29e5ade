package com.bxkj.jrzp.user.schoolinfo.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ListDTO
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.user.data.CampusTalkEditorData
import com.bxkj.jrzp.user.schoolinfo.data.*
import com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection.EditDoubleElectionParams
import com.bxkj.jrzp.user.schoolinfo.ui.editemployment.EditEmploymentParams
import com.bxkj.personal.data.CompanyDetailsData
import com.bxkj.personal.data.UserNoticeItemData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
interface SchoolInfoApi {

  @POST("/Xuanjianghui/GetListByPage/")
  suspend fun getSeminarList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<CampusTalkData>>

  @POST("/Xuanjianghui/GetMyXuanjianghuiListByPage/")
  suspend fun getCampusTalkListV2(@Body encryptReqParams: EncryptReqParams): BaseResponse<ListDTO<CampusTalkDataV2>>

  @POST("/User/GetSchoolUserCenterTongji/")
  suspend fun getSchoolInfoCount(@Body encryptReqParams: EncryptReqParams): BaseResponse<SchoolInfoCountData>

  @POST("/Gaoxiao/GetGaoxiaoContactInfo/")
  suspend fun getSchoolContractInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<SchoolContractData>

  @POST("/Gaoxiao/EditGaoxiaoContactInfo/")
  suspend fun updateSchoolContractInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Jiuye/GetJiuyeInfo/")
  suspend fun getEmploymentInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<EditEmploymentParams>

  //============================ 双选会 ============================//

  @POST("/News/GetShuangxuanhuiListByPage/")
  suspend fun getDoubleElectionList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<DoubleElectionData>>

  @POST("/News/GetShuangxuanhuiInfo/")
  suspend fun getDoubleElectionDetails(@Body encryptReqParams: EncryptReqParams): BaseResponse<EditDoubleElectionParams>

  @POST("/News/EditShuangxuanhui/")
  suspend fun updateDoubleElection(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/News/AddShuangxuanhui/")
  suspend fun releaseDoubleElection(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Xuanjianghui/EditStatus/")
  suspend fun reviewSeminar(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Yuanxi/GetYuanxiList/")
  suspend fun getDepartmentList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<SchoolAttachInfoData>>

  @POST("/Zhuanye/GetZhuanyeList/")
  suspend fun getProfessionList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<SchoolAttachInfoData>>

  @POST("/Jiuye/AddJiuye/")
  suspend fun updateEmployment(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  //============================ 学校信息 ============================//
  @POST("/Gaoxiao/GetGaoxiaoInfoV2/")
  suspend fun getSchoolInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<SchoolInfoData>

  @POST("/Gaoxiao/AddOrEditGaoxiao/")
  suspend fun updateSchoolInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/GaoxiaoLx/GetGaoxiaoLxList/")
  suspend fun getSchoolTypes(): BaseResponse<List<PickerOptionsData>>

  @POST("/Gaoxiao/ByjGetGaoxiaoListByPage/")
  suspend fun getSchoolList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<SchoolAttachInfoData>>

  //============================ 已报名企业 ============================//
  @POST("/NewsBaoming/GetBaomingCompanyListByPage/")
  suspend fun getDoubleElectionCompany(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<DoubleElectionCompanyData>>

  @POST("/NewsBaoming/SetBaomingState/")
  suspend fun reviewDoubleElectionCompany(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/NewsBaoming/GetBaomingCompanyInfo/")
  suspend fun getDoubleElectionCompanyDetails(@Body encryptReqParams: EncryptReqParams): BaseResponse<CompanyDetailsData>

  @POST("/Xuanjianghui/AddOrEditMyXuanjianghui/")
  suspend fun editSeminar(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST("/Xuanjianghui/GetMyXuanjianghuiInfo/")
  suspend fun getSeminarInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<CampusTalkEditorData>

  //============================== 公招公考 ==============================//
  @POST("/News/GetGongzhaoListByPage/")
  suspend fun getCivilServiceInfoList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<UserNoticeItemData>>
}