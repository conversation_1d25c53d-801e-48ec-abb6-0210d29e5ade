package com.bxkj.jrzp.user.schoolinfo.ui.bindschoolattachinfo

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/26
 * @version: V1.0
 */
class BindSchoolAttachInfoViewModel @Inject constructor(
  private val mSchoolInfoRepository: SchoolInfoRepository
) : BaseViewModel() {

  val searchContent = MutableLiveData<String>()

  val searchResultListViewModel = RefreshListViewModel()

  val showConfirmButton = MutableLiveData<Boolean>().apply { false }

  val resultSearchContentCommand = LiveEvent<String>()

  private var mSchoolInfoType: Int = BindSchoolAttachInfoNavigation.SCHOOL_DEPARTMENT
  private var mParentID: Int = CommonApiConstants.NO_ID
  private var mSearchKeyword: String = ""

  fun start(infoType: Int, lastSelectName: String?, parentID: Int) {
    mSchoolInfoType = infoType
    searchContent.value = lastSelectName
    mParentID = parentID
    setupSearchResultListViewModel()
  }

  fun startSearch() {
    searchContent.value?.let { content ->
      mSearchKeyword = content
      searchResultListViewModel.refresh()
    }
  }

  fun confirm() {
    searchContent.value?.let {
      resultSearchContentCommand.value = it
    } ?: let {
      resultSearchContentCommand.value = ""
    }
  }

  private fun setupSearchResultListViewModel() {
    if (mSchoolInfoType != BindSchoolAttachInfoNavigation.SCHOOL) {
      searchResultListViewModel.refreshLayoutViewModel.enableRefresh(false)
      searchResultListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    }
    searchResultListViewModel.setOnLoadDataListener { currentPage ->
      search(currentPage)
    }
  }

  private fun search(currentPage: Int) {
    viewModelScope.launch {
      when (mSchoolInfoType) {
        BindSchoolAttachInfoNavigation.SCHOOL -> {
          mSchoolInfoRepository.getSchoolList(
            mParentID,
            mSearchKeyword,
            currentPage,
            CommonApiConstants.DEFAULT_PAGE_SIZE
          ).handleResult({
            showConfirmButton.value = false
            searchResultListViewModel.autoAddAll(it)
          }, {
            if (it.isNoDataError) {
              searchResultListViewModel.noMoreData()
            } else {
              searchResultListViewModel.loadError()
            }
          })
        }
        BindSchoolAttachInfoNavigation.SCHOOL_DEPARTMENT -> {
          mSchoolInfoRepository.getDepartmentList(mParentID, mSearchKeyword)
            .handleResult({
              showConfirmButton.value = false
              searchResultListViewModel.reset(it)
            }, {
              showConfirmButton.value = true
            })
        }
        else -> {
          mSchoolInfoRepository.getProfessionList(
            mParentID,
            mSearchKeyword,
            currentPage,
            CommonApiConstants.DEFAULT_PAGE_SIZE
          ).handleResult({
            showConfirmButton.value = false
            searchResultListViewModel.reset(it)
          }, {
            showConfirmButton.value = true
          })
        }
      }
    }
  }
}