package com.bxkj.jrzp.user.schoolinfo.ui.employments

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.userhome.api.UserHomeRepository
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.data.UserNewsItemData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/25
 * @version: V1.0
 */
class EmploymentsViewModel @Inject constructor(
    private val mUserHomeRepository: UserHomeRepository,
    private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

    val employmentListViewModel = RefreshListViewModel()

    init {
        setupEmploymentListViewModel()
    }

    fun refresh() {
        employmentListViewModel.refresh()
    }

    fun deleteEmployment(employment: UserNewsItemData) {
        viewModelScope.launch {
            showLoading()
            mOpenUserRepository.deleteReleasedInfo(
                getSelfUserID(),
                employment.lanmu,
                employment.jiuyeList?.id.getOrDefault()
            ).handleResult({
                showToast(R.string.common_delete_success)
                if (employmentListViewModel.remove(employment) == 0) {
                    employmentListViewModel.refresh()
                }
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    private fun setupEmploymentListViewModel() {
        employmentListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mUserHomeRepository.getSchoolNewsList(
                    getSelfUserID(),
                    getSelfUserID(),
                    UserInfoNavigationData.NAVIGATION_EMPLOYMENT_FACILITATION,
                    2,
                    currentPage,
                    CommonApiConstants.DEFAULT_PAGE_SIZE
                ).handleResult({
                    employmentListViewModel.autoAddAll(it)
                }, {
                    if (it.isNoDataError) {
                        employmentListViewModel.noMoreData()
                    } else {
                        employmentListViewModel.loadError()
                    }
                })
            }
        }
    }
}