package com.bxkj.jrzp.user.schoolinfo.ui.editemployment

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/26
 * @version: V1.0
 */
class EditEmploymentNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/editemployment"

    const val EXTRA_EMPLOYMENT_ID = "EMPLOYMENT_ID"

    fun create(employmentID: Int = CommonApiConstants.NO_ID): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_EMPLOYMENT_ID, employmentID)
    }
  }
}