package com.bxkj.jrzp.user.schoolinfo.ui.civilserviceinfolist

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoRepository
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.personal.data.UserNoticeItemData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/4
 * @version: V1.0
 */
class CivilServiceInfoListViewModel @Inject constructor(
  private val mSchoolInfoRepository: SchoolInfoRepository,
  private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

  val civilServiceInfoListViewModel = RefreshListViewModel()

  init {
    setupCivilServiceInfoListViewModel()
  }

  fun refresh() {
    civilServiceInfoListViewModel.refresh()
  }

  private fun setupCivilServiceInfoListViewModel() {
    civilServiceInfoListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mSchoolInfoRepository.getCivilServiceInfoList(
          getSelfUserID(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          civilServiceInfoListViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            civilServiceInfoListViewModel.noMoreData()
          } else {
            civilServiceInfoListViewModel.loadError()
          }
        })
      }
    }
  }

  fun deleteCivilServiceInfo(notice: UserNoticeItemData) {
    viewModelScope.launch {
      showLoading()
      mOpenUserRepository.deleteReleasedInfo(
        getSelfUserID(),
        UserInfoNavigationData.NAVIGATION_NEWS,
        notice.id
      ).handleResult({
        showToast(R.string.common_delete_success)
        if (civilServiceInfoListViewModel.remove(notice) == 0) {
          civilServiceInfoListViewModel.refresh()
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

}