package com.bxkj.jrzp.user.schoolinfo.data

/**
 * @Description:
 * @author: Yang<PERSON>in
 * @date: 2020/12/7
 * @version: V1.0
 */
data class SchoolContractData(
  var uid: Int,
  var name: String? = "",
  var lxr: String? = "",
  var tel: String = "",
  var mail: String? = "",
  var fax: String? = ""
) {

  companion object {

    fun getDefault(userID: Int): SchoolContractData {
      return SchoolContractData(userID)
    }
  }
}