package com.bxkj.jrzp.user.schoolinfo.data

import com.bxkj.common.util.HtmlUtils

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
data class DoubleElectionData(
  var id: Int,
  var nid: Int,
  var title: String,
  var date: String,
  var count: Int,
  var bmCount: Int,  //报名
  var state: Int,
  var stateMsg: String,
  var jbState: Int
) {

  fun expired(): Boolean {
    return jbState == 1
  }

  /**
   * 审核成功
   */
  fun reviewSuccess(): Boolean {
    return state == 1
  }

  fun getStateText(): String {
    return when (state) {
      0 -> "待审核"
      1 -> "审核成功"
      2 -> "审核失败"
      else -> ""
    }
  }

  fun getColorStateText(): String {
    return when (state) {
      0 -> HtmlUtils.setFontTextColor("#FF7405", "报名审核中")
      1 -> HtmlUtils.setFontTextColor("#4FC182", "报名成功")
      2 -> HtmlUtils.setFontTextColor("#EC535B", "报名失败")
      else -> ""
    }
  }
}