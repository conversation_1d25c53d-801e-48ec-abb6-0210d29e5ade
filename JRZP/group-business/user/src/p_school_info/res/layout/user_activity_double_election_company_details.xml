<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanydetails.DoubleElectionCompanyDetailsViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/user_double_election_company_details_page_title" />

    <include
      android:id="@+id/include_company_details"
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.companyDetailsContentListViewModel}" />

    <View style="@style/Line.Horizontal.Light" />

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="horizontal"
      android:paddingTop="@dimen/dp_10"
      android:paddingBottom="@dimen/dp_10"
      android:visibility="@{viewModel.unReview?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.16sp.ff7647"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_weight="1"
        android:background="@drawable/frame_fe6600_round"
        android:gravity="center"
        android:onClick="@{()->viewModel.reviewDoubleElectionCompany(1)}"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:text="@string/user_double_election_company_details_reject" />

      <TextView
        style="@style/Text.16sp.FFFFFF"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_weight="1"
        android:background="@drawable/bg_fe6600_round"
        android:gravity="center"
        android:onClick="@{()->viewModel.reviewDoubleElectionCompany(2)}"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:text="@string/user_double_election_company_details_accept" />
    </LinearLayout>

  </LinearLayout>
</layout>