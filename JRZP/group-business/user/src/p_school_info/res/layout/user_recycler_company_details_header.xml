<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.CompanyDetailsData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical">

    <include
      android:id="@+id/include_company_info"
      layout="@layout/user_include_company_info"
      app:companyInfo="@{data}" />

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16" />

    <TextView
      style="@style/Text.BigInfoTitle"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_12"
      android:text="@string/user_double_election_company_details_desc" />

    <cn.carbs.android.expandabletextview.library.ExpandableTextView
      android:id="@+id/tv_company_desc"
      style="@style/Text.ContentText"
      android:layout_width="match_parent"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      app:etv_MaxLinesOnShrink="4"
      app:etv_ToExpandHint="@string/user_double_election_company_details_expand"
      app:etv_ToShrinkHint="@string/user_double_election_company_details_shrink" />

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_16" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_contract_info"
      style="@style/match_wrap"
      android:paddingStart="@dimen/dp_16"
      android:paddingEnd="@dimen/dp_12">

      <TextView
        android:id="@+id/tv_contract_text"
        style="@style/Text.BigInfoTitle"
        android:layout_marginTop="@dimen/dp_12"
        android:text="@string/user_double_election_company_details_contract"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_contract"
        style="@style/Text.ContentText"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{@string/user_double_election_company_details_contract_format(data.lxr)}"
        app:layout_constraintEnd_toStartOf="@id/iv_view_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_contract_text" />

      <TextView
        android:id="@+id/tv_contract_phone"
        style="@style/Text.ContentText"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{@string/user_double_election_company_details_contract_phone_format(data.phone)}"
        app:layout_constraintEnd_toStartOf="@id/iv_view_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_contract" />

      <ImageView
        android:id="@+id/iv_view_more"
        style="@style/wrap_wrap"
        android:src="@drawable/common_ic_more"
        app:layout_constraintBottom_toBottomOf="@id/tv_contract_phone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_contract" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_16" />

    <TextView
      android:id="@+id/tv_business_license"
      style="@style/Text.BigInfoTitle"
      android:drawableEnd="@drawable/common_ic_more"
      android:gravity="center_vertical"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_16"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_16"
      android:text="@string/user_double_election_company_details_business_license" />

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_marginStart="@dimen/dp_16" />

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap">

      <TextView
        android:id="@+id/tv_address_text"
        style="@style/Text.BigInfoTitle"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/user_double_election_company_details_address"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_view_route"
        style="@style/Text.12sp.FF7647"
        android:layout_marginEnd="@dimen/dp_12"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:drawableEnd="@drawable/user_ic_double_election_address_location"
        android:drawablePadding="@dimen/dp_4"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/user_double_election_company_details_view_route"
        app:layout_constraintBottom_toBottomOf="@id/tv_address_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_address_text" />

      <ImageView
        android:id="@+id/iv_map_location"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/frame_f4f4f4"
        android:scaleType="fitXY"
        android:src="@drawable/user_ic_company_map_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:135"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_address_text" />

      <TextView
        style="@style/Text.ContentText"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_24"
        android:background="@drawable/bg_ffffff"
        android:paddingStart="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_12"
        android:text="@{@string/user_double_election_company_details_address_format(data.address)}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/iv_map_location"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_map_location" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
      style="@style/Line.Horizontal.Light"
      android:layout_height="@dimen/dp_4" />

    <TextView
      style="@style/Text.BigInfoTitle"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginTop="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12"
      android:text="@string/user_double_election_company_details_job" />

  </LinearLayout>
</layout>