<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.schoolinfo.ui.civilserviceinfolist.CivilServiceInfoListViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/user_civil_service_info_list_release"
      app:title="@string/user_civil_service_info_list_page_title" />

    <include
      android:id="@+id/include_civil_service"
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.civilServiceInfoListViewModel}" />

    <TextView
      android:id="@+id/tv_release"
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_30"
      android:text="@string/user_civil_service_info_list_release" />

  </LinearLayout>
</layout>