package com.bxkj.jrzp.userhome.ui.homepage

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup.LayoutParams
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import cn.jzvd.JzvdStd
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.imageloader.UCropEngine
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.fixImgUrl
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.qmui.QMUIStatusBarHelper
import com.bxkj.common.util.recyclerutil.AlignLeftPagerSnapHelper
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.MoreInfoDialog
import com.bxkj.common.widget.dialog.iconselect.IconSelectDialog
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.friend.data.RecommendFriendItemData
import com.bxkj.jrzp.friend.ui.findfriends.FindFriendsNavigation
import com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceNavigation
import com.bxkj.jrzp.live.room.data.LiveRoomData
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.string
import com.bxkj.jrzp.user.databinding.UserActivityUserHomeBinding
import com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo.EnterpriseVideoNavigation
import com.bxkj.jrzp.user.institutions.ui.institutionsinfo.InstitutionsInfoNavigation
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoNavigation
import com.bxkj.jrzp.user.selectidentity.SelectIdentityNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.ui.usercourses.UserCoursesFragment
import com.bxkj.jrzp.userhome.ui.userjob.UserJobListFragment
import com.bxkj.jrzp.userhome.ui.usernews.UserNewsListFragment
import com.bxkj.jrzp.userhome.ui.userphoto.UserPhotoListFragment
import com.bxkj.personal.ui.activity.fans.FansNavigation
import com.bxkj.personal.ui.activity.gallery.ImageGalleryNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowNavigation
import com.bxkj.personal.ui.activity.userbasicinfo.FillInfoNext.NEXT_AUTHENTICATION
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoNavigation
import com.bxkj.share.ui.ShareBusinessCardDialog
import com.bxkj.share.ui.ShareRecruitmentInfoDialog
import com.bxkj.share.ui.StandardShareDialog
import com.bxkj.support.upload.data.FileItem
import com.bxkj.video.ui.fullscreenplayer.FullScreenPlayerNavigation
import com.google.android.material.appbar.AppBarLayout
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route
import com.yalantis.ucrop.UCrop.Options
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import kotlin.math.abs
import kotlin.math.min

/**
 * @Description: 用户个人主页
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
@Route(path = UserHomeNavigation.PATH)
class UserHomePageActivity :
  BaseDBActivity<UserActivityUserHomeBinding, UserHomeViewModel>(),
  OnClickListener {
  companion object {
    const val TO_SELECT_HEADER_BG = 1
    const val TO_EDIT_USER_INFO = 2

    const val TO_USER_HOME_PAGE_CODE = 3
  }

  private var initTopLayout: Boolean = false

  override fun getViewModelClass(): Class<UserHomeViewModel> = UserHomeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_user_home

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewModel.start(
      intent.getIntExtra(UserHomeNavigation.EXTRA_QUERY_USER_ID, 0),
      intent.getIntExtra(UserHomeNavigation.EXTRA_QUERY_ENTERPRISE_ID, 0),
      intent.getIntExtra(
        UserHomeNavigation.EXTRA_USER_AUTH_TYPE,
        AuthenticationType.QUERY_HIGHER_AUTH,
      ),
    )

    setupTitleBar()

    setupRecommendFriendsList()

    setupLiveNoticeList()
  }

  override fun onResume() {
    super.onResume()
    viewModel.refreshUserHomePageInfo()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.iv_share -> {
          viewModel.getShareInfo(viewBinding.vpContent.currentItem)
        }

        R.id.ll_follow -> {
          MyFollowNavigation
            .navigate(viewModel.getQueryUserId(), false)
            .start()
        }

        R.id.tv_add_friend -> {
          FindFriendsNavigation.navigate().start()
        }

        R.id.tv_view_more_friends -> {
          FindFriendsNavigation.navigate().start()
        }
      }
    }
  }

  private fun setupTitleBar() {
    statusBarManager
      .titleBar(viewBinding.clTitleBar)
      .statusBarDarkFont(true, 0.4f)
      .init()

    viewBinding.clTitleBar.background
      .mutate()
      .alpha = 0

    viewBinding.clTitleBar.viewTreeObserver.addOnGlobalLayoutListener {
      if (!initTopLayout) {
        setupCollapsingToolbarLayoutMinHeight()
        setupAppBarOffsetChangeListener()
      }
    }
  }

  /**
   * 设置推荐好友List
   */
  private fun setupRecommendFriendsList() {
    val recommendFriendsListAdapter =
      SimpleDiffListAdapter(
        R.layout.user_recycler_home_page_recomend_friend_item,
        RecommendFriendItemData.DiffCallback(),
        BR.data,
      ).apply {
        setOnItemClickListener(
          object : SuperItemClickListener {
            override fun onClick(
              v: View,
              position: Int,
            ) {
              getData()?.let {
                val clickItem = it[position]
                if (v.id == R.id.tv_follow) {
                  viewModel.followUser(clickItem)
                } else {
                  UserHomeNavigation.navigate(clickItem.uid).startForResult(
                    this@UserHomePageActivity,
                    TO_USER_HOME_PAGE_CODE,
                  )
                }
              }
            }
          },
          R.id.tv_follow,
        )
      }

    viewBinding.recyclerRecommendFriends.layoutManager =
      LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)

    viewBinding.recyclerRecommendFriends.addItemDecoration(
      LineItemDecoration
        .Builder()
        .divider(ContextCompat.getDrawable(this, R.drawable.divider_8))
        .orientation(LinearLayoutManager.HORIZONTAL)
        .drawHeader(false)
        .drawFoot(false)
        .build(),
    )

    viewBinding.recyclerRecommendFriends.adapter = recommendFriendsListAdapter
  }

  /**
   * 设置直播预告list
   */
  private fun setupLiveNoticeList() {
    val liveNoticeListAdapter =
      object : SimpleDiffListAdapter<LiveRoomData>(
        R.layout.user_recycler_live_notice_item,
        LiveRoomData.DiffCallBack(),
        BR.data,
      ) {
        override fun bind(
          holder: SuperViewHolder,
          item: LiveRoomData,
          position: Int,
        ) {
          getData()?.let {
            val layoutParams = holder.itemView.layoutParams
            if (it.size == 1) {
              layoutParams.width = LayoutParams.MATCH_PARENT
            } else {
              layoutParams.width = DensityUtils.dp2px(this@UserHomePageActivity, 285f)
            }
            holder.itemView.layoutParams = layoutParams
          }
          super.bind(holder, item, position)
        }
      }.apply {
        setOnItemClickListener(
          object : SuperItemClickListener {
            override fun onClick(
              v: View,
              position: Int,
            ) {
              getData()?.let {
                val clickItem = it[position]
                LiveAudienceNavigation
                  .navigate(
                    LiveRoomData.get(
                      clickItem.id,
                      clickItem.roomName,
                    ),
                  ).withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                  .start(this@UserHomePageActivity)
              }
            }
          },
        )
      }

    viewBinding.recyclerLiveList.layoutManager =
      LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
    viewBinding.recyclerLiveList.addItemDecoration(
      LineItemDecoration
        .Builder()
        .divider(ContextCompat.getDrawable(this, R.drawable.divider_12))
        .orientation(LinearLayoutManager.HORIZONTAL)
        .drawHeader(true)
        .drawFoot(true)
        .build(),
    )
    AlignLeftPagerSnapHelper().attachToRecyclerView(viewBinding.recyclerLiveList)

    viewBinding.recyclerLiveList.adapter = liveNoticeListAdapter
  }

  private fun setupAppBarOffsetChangeListener() {
    initTopLayout = true
    val finalHeight = dip(44f)

    val userNameShowStatusSwitchHeight =
      viewBinding.tvName.top + viewBinding.tvName.height - viewBinding.ctlUserInfoGroup.minimumHeight

    viewBinding.ablHeader.addOnOffsetChangedListener(
      AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
        val absOffset = abs(verticalOffset)

        if (absOffset >= userNameShowStatusSwitchHeight) {
          if (!viewBinding.tvTitleBarName.isShown) {
            viewBinding.tvTitleBarName.visibility = View.VISIBLE
          }
        } else {
          if (viewBinding.tvTitleBarName.isShown) {
            viewBinding.tvTitleBarName.visibility = View.GONE
          }
        }

        val progress = min(1f, (absOffset / finalHeight.toFloat()))
        if (progress >= 0.6f) {
          QMUIStatusBarHelper.setStatusBarLightMode(this)
          viewBinding.ivBack.setImageResource(R.drawable.user_ic_user_home_back_black)
          viewBinding.ivShare.setImageResource(R.drawable.user_ic_user_home_share_black)
          viewBinding.ivShareIdCard.visibility = View.GONE
        } else {
          QMUIStatusBarHelper.setStatusBarDarkMode(this)
          viewBinding.ivBack.setImageResource(R.drawable.user_ic_user_home_back_white)
          viewBinding.ivShare.setImageResource(R.drawable.user_ic_user_home_share_white)
          viewBinding.ivShareIdCard.visibility = View.VISIBLE
        }
        viewBinding.clTitleBar.background
          .mutate()
          .alpha =
          (255 * progress).toInt()
      },
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.editHeaderBgCommand.observe(
      this,
      Observer {
        toSelectHeaderBg()
      },
    )

    viewModel.viewBigAvatarCommand.observe(
      this,
      Observer {
        ImageGalleryNavigation.navigate(arrayListOf(FileItem.fromUrl(it)), 0).start()
      },
    )

    viewModel.toSelectCompanyLogo.observe(
      this,
      EventObserver {
        PermissionUtils.requestPermission(
          this,
          getString(R.string.permission_tips_title),
          getString(R.string.permission_select_img_tips),
          object : PermissionUtils.OnRequestResultListener {
            override fun onRequestSuccess() {
              PictureSelector
                .create(this@UserHomePageActivity)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.getInstance())
                .setSandboxFileEngine(SandboxFileEngine.getInstance())
                .setCompressEngine(ImageCompressEngine.getInstance())
                .setSelectionMode(SelectModeConfig.SINGLE)
                .isDirectReturnSingle(true)
                .setImageSpanCount(4)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            }

            override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
              showToast(getString(R.string.cancel))
            }
          },
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE,
        )
      },
    )

    viewModel.showSelectShareTypeCommand.observe(
      this,
      Observer {
        showSelectShareTypeDialog()
      },
    )

    viewModel.contractUser.observe(
      this,
      Observer { phoneNumber ->
        ActionDialog
          .Builder()
          .setTitle(getString(R.string.user_home_page_contract_tips))
          .setContent(getString(R.string.user_home_page_contract_phone_format, phoneNumber))
          .setCopyText(phoneNumber)
          .setConfirmText(getString(R.string.user_home_page_contract))
          .setOnConfirmClickListener {
            SystemUtil.callPhone(this, phoneNumber)
          }.build()
          .show(supportFragmentManager)
      },
    )

    viewModel.viewAllUserDescCommand.observe(
      this,
      Observer {
        MoreInfoDialog(this, getString(R.string.user_home_page_desc), it).show()
      },
    )

    viewModel.toUserFansPageCommand.observe(
      this,
      Observer {
        if (it.dwID > 0) {
          FansNavigation
            .navigate(
              it.dwID,
              FansNavigation.QUERY_TYPE_ENTERPRISE,
            ).start()
        } else {
          FansNavigation
            .navigate(
              it.userID,
              FansNavigation.QUERY_TYPE_PERSONAL,
            ).start()
        }
      },
    )

    viewModel.toSelectAuthTypeCommand.observe(
      this,
      Observer {
        SelectIdentityNavigation.navigate().start()
        // SelectAuthTypeNavigation.navigate().start()
      },
    )

    viewModel.userNewsNavigation.observe(
      this,
      Observer {
        setupNewsNavigationAndContentFragments(it)
      },
    )

    viewModel.toEditUserInfoCommand.observe(
      this,
      Observer {
        when (it) {
          AuthenticationType.PERSONAL -> {
            MicroResumeInfoNavigation.create().startForResult(this, TO_EDIT_USER_INFO)
          }

          AuthenticationType.ENTERPRISE, AuthenticationType.HR -> {
            BusinessBasicInfoNavigation.navigate().startForResult(this, TO_EDIT_USER_INFO)
          }

          AuthenticationType.SCHOOL -> {
            SchoolInfoNavigation.navigate().startForResult(this, TO_EDIT_USER_INFO)
          }

          AuthenticationType.INSTITUTIONS -> {
            InstitutionsInfoNavigation.create().startForResult(this, TO_EDIT_USER_INFO)
          }

          AuthenticationType.SELF_EMPLOYED -> {
            IDCardValidationNavigation.create().startForResult(this, TO_EDIT_USER_INFO)
          }

          else -> {
            MicroResumeInfoNavigation.create().startForResult(this, TO_EDIT_USER_INFO)
          }
        }
      },
    )

    viewModel.followStatus.observe(
      this,
      Observer {
        setResult(
          UserHomeNavigation.RESULT_FOLLOW_STATUS_CHANGE,
          intent.apply {
            putExtra(UserHomeNavigation.EXTRA_FOLLOW_STATUS, it)
          },
        )
      },
    )

    viewModel.continueUncompletedAuthEvent.observe(
      this,
      Observer { authType ->
        when (authType) {
          AuthenticationType.PERSONAL -> {
            UserBasicInfoNavigation.create(nextStep = NEXT_AUTHENTICATION).start()
          }

          AuthenticationType.SCHOOL -> {
            SchoolInfoNavigation.navigate(SchoolInfoNavigation.NEXT_STEP_AUTH).start()
          }

          AuthenticationType.INSTITUTIONS -> {
            InstitutionsInfoNavigation.create(InstitutionsInfoNavigation.NEXT_AUTH).start()
          }

          AuthenticationType.SELF_EMPLOYED -> {
            IDCardValidationNavigation.create().start()
          }

          else -> {
            BusinessBasicInfoNavigation.navigate(true, authType).start()
          }
        }
      },
    )

    viewModel.showNormalShareCommand.observe(
      this,
      Observer {
        StandardShareDialog
          .Builder()
          .setShareTitle(it.title)
          .setShareMomentTitle(it.title2)
          .setShareContent(it.content)
          .setSharePic(it.logo)
          .setShareUrl(
            "${viewModel.getShareUrl()}&sortid=${
              viewModel.getTabIDByIndex(
                viewBinding.vpContent.currentItem,
              )
            }",
          ).build()
          .show(supportFragmentManager)
      },
    )

    viewModel.showBusinessCardCommand.observe(
      this,
      Observer {
        ShareBusinessCardDialog
          .Builder()
          .showJobCount(AuthenticationType.isHROrEnterprise(viewModel.getUserAuthType()))
          .setJobCount(it.relCount)
          .setUserAuthTagImg(it.memberLevelIcon)
          .setShareUrl(
            "${viewModel.getShareUrl()}&sortid=${
              viewModel.getTabIDByIndex(
                viewBinding.vpContent.currentItem,
              )
            }&fx=android",
          ).setUserName(it.name)
          .setUserAvatar(it.photo)
          .setUserDesc(it.introduction)
          .setUserCount(it.actionCount, it.likesCount, it.fansCount)
          .build()
          .show(supportFragmentManager)
      },
    )

    viewModel.toPlayVideoCommand.observe(
      this,
      Observer {
        FullScreenPlayerNavigation.create(it.fixImgUrl()).start()
      },
    )

    viewModel.toSelectVideoCommand.observe(
      this,
      Observer {
        EnterpriseVideoNavigation.create().start()
      },
    )
  }

  private fun showSelectShareTypeDialog() {
    IconSelectDialog
      .Builder()
      .addItem(
        R.drawable.user_ic_share_business_card,
        getString(R.string.user_home_page_share_business_card),
      ).addItem(
        R.drawable.user_ic_share_recruitment,
        getString(R.string.user_home_page_share_recruitment),
      ).setOnItemClickListener(
        object : SuperItemClickListener {
          override fun onClick(
            v: View,
            position: Int,
          ) {
            if (position == 0) {
              viewModel.shareBusinessCard()
            } else {
              ShareRecruitmentInfoDialog
                .Builder()
                .setShareUrl(
                  "${viewModel.getShareUrl()}&sortid=${
                    viewModel.getTabIDByIndex(
                      viewBinding.vpContent.currentItem,
                    )
                  }&fx=android",
                )
                .recruitmentInfoUrl("http://m.jrzp.com/page/createJianzhang.aspx?uid=${viewModel.getQueryUserId()}")
                .build()
                .show(supportFragmentManager)
            }
          }
        },
      ).build()
      .show(supportFragmentManager)
  }

  private fun setupNewsNavigationAndContentFragments(userInfoNavigation: List<UserInfoNavigationData>) {
    setupNewsContentViewPager(userInfoNavigation)

    setupUserNewsTypeIndicator(userInfoNavigation.map { it.name }.toTypedArray())

    handleTargetTab(userInfoNavigation)
  }

  private fun handleTargetTab(userInfoNavigation: List<UserInfoNavigationData>) {
    viewBinding.vpContent.setCurrentItem(
      userInfoNavigation
        .indexOfFirst {
          it.id == getIntentTargetTabId()
        }.getOrDefault(),
      false,
    )
  }

  private fun getIntentTargetTabId(): Int =
    intent.getIntExtra(
      UserHomeNavigation.EXTRA_TARGET_TAB,
      UserInfoNavigationData.NAVIGATION_ALL,
    )

  private fun toSelectHeaderBg() {
    PermissionUtils.requestPermission(
      this,
      getString(string.permission_tips_title),
      getString(string.permission_select_img_tips),
      object : OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector
            .create(this@UserHomePageActivity)
            .openGallery(SelectMimeType.ofImage())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .isDirectReturnSingle(true)
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setCropEngine(
              UCropEngine(
                Options().apply {
                  withAspectRatio(375f, 160f)
                },
              ),
            ).setImageEngine(GlideEngine.getInstance())
            .setImageSpanCount(4)
            .forResult(TO_SELECT_HEADER_BG)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE,
    )
  }

  private fun setupCollapsingToolbarLayoutMinHeight() {
    statusBarManager.barParams.titleBarView?.let {
      viewBinding.ctlUserInfoGroup.minimumHeight = it.height
    }
  }

  private fun setupNewsContentViewPager(userInfoNavigation: List<UserInfoNavigationData>) {
    viewBinding.vpContent.isSaveEnabled = false
    viewBinding.vpContent.adapter =
      object : FragmentStatePagerAdapter(supportFragmentManager) {
        override fun getCount(): Int = userInfoNavigation.size

        override fun getItem(position: Int): Fragment {
          val navigationInfo = userInfoNavigation[position]
          return when (navigationInfo.id) {
            UserInfoNavigationData.NAVIGATION_PHOTO -> {
              UserPhotoListFragment.newInstance(
                viewModel.getQueryUserId(),
                viewModel.getQueryInstitutionsId(),
                viewModel.getUserAuthType(),
              )
            }

            UserInfoNavigationData.NAVIGATION_JOB -> {
              UserJobListFragment.newInstance(viewModel.getQueryUserId())
            }

            UserInfoNavigationData.NAVIGATION_COURSES -> {
              UserCoursesFragment.newInstance(viewModel.getQueryUserId())
            }

            else -> {
              UserNewsListFragment.newInstance(
                viewModel.getQueryUserId(),
                viewModel.getQueryInstitutionsId(),
                navigationInfo.id,
                viewModel.getUserAuthType(),
              )
            }
          }
        }
      }
  }

  private fun setupUserNewsTypeIndicator(indicatorTitles: Array<String?>) {
    val indicatorAdapter =
      UserHomeNoticeIndicatorAdapter(indicatorTitles)
        .apply {
          setOnTabClickListener(
            object : OnTabClickListener {
              override fun onTabClicked(
                v: View,
                index: Int,
              ) {
                viewBinding.vpContent.currentItem = index
              }
            },
          )
        }
    val layoutParams =
      viewBinding.indicatorUserNoticeType.layoutParams
    if (indicatorTitles.size <= 4) {
      layoutParams.width = DensityUtils.dp2px(this, 80f) * indicatorTitles.size
    } else {
      layoutParams.width = LayoutParams.MATCH_PARENT
    }
    viewBinding.indicatorUserNoticeType.layoutParams = layoutParams
    viewBinding.indicatorUserNoticeType.navigator =
      CommonNavigator(this).apply {
        adapter = indicatorAdapter
        isAdjustMode = indicatorTitles.size <= 4
      }

    ViewPagerHelper.bind(viewBinding.indicatorUserNoticeType, viewBinding.vpContent)
  }

  override fun onActivityResult(
    requestCode: Int,
    resultCode: Int,
    data: Intent?,
  ) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    super.onDestroy()
    JzvdStd.releaseAllVideos()
  }
}
