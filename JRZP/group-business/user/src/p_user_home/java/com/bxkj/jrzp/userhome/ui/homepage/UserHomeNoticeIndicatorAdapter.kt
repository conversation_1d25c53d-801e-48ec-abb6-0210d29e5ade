package com.bxkj.jrzp.userhome.ui.homepage

import android.content.Context
import android.graphics.Typeface
import androidx.core.content.ContextCompat
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.util.DensityUtils
import com.bxkj.jrzp.user.R
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.SimplePagerTitleView

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/17
 * @version: V1.0
 */
class UserHomeNoticeIndicatorAdapter constructor(titles: Array<String?>) :
  MagicIndicatorAdapter(titles) {

  override fun getTitleView(context: Context, index: Int): IPagerTitleView {
    return object : SimplePagerTitleView(context) {
      override fun onEnter(
        index: Int,
        totalCount: Int,
        enterPercent: Float,
        leftToRight: Boolean
      ) {
        typeface = Typeface.DEFAULT_BOLD
      }

      override fun onLeave(
        index: Int,
        totalCount: Int,
        leavePercent: Float,
        leftToRight: Boolean
      ) {
        typeface = Typeface.DEFAULT
      }
    }.apply<SimplePagerTitleView> {
      textSize = 16f
      normalColor = ContextCompat.getColor(context, R.color.cl_333333)
      selectedColor = ContextCompat.getColor(context, R.color.cl_333333)
      if (getTitles().size > 4) {
        setPadding(DensityUtils.dp2px(context, 18f), 0, DensityUtils.dp2px(context, 18f), 0)
      } else {
        setPadding(DensityUtils.dp2px(context, 8f), 0, DensityUtils.dp2px(context, 8f), 0)
      }
      setOnClickListener {
        getOnTabClickListener()?.onTabClicked(this, index)
      }
      text = getTitles()[index]
    }
  }

  override fun getIndicator(context: Context): IPagerIndicator {
    return LinePagerIndicator(context).apply {
      setColors(ContextCompat.getColor(context, R.color.cl_ff7405))
      mode = LinePagerIndicator.MODE_EXACTLY
      lineWidth = DensityUtils.dp2px(context, 32f).toFloat()
      lineHeight = DensityUtils.dp2px(context, 2f).toFloat()
    }
  }
}