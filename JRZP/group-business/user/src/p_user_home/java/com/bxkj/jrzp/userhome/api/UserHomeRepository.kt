package com.bxkj.jrzp.userhome.api

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.data.UserNewsItemData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/18
 * @version: V1.0
 */
class UserHomeRepository @Inject constructor(
  private val mUserHomeApi: UserHomeApi
) : BaseRepo() {

  suspend fun getInstitutionsNewList(
    queryUserId: Int,
    queryInstitutionsID: Int,
    selfUserID: Int,
    newsType: Int,
    showType: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<UserNewsItemData>> {
    return httpRequest {
      mUserHomeApi.getInstitutionsNewsList(
        ZPRequestBody().apply {
          put("uid", queryUserId)
          put("dwid", queryInstitutionsID)
          put("curUid", selfUserID)
          put("lanmu", newsType)
          put("showType", showType)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户发布资讯
   */
  suspend fun getSchoolNewsList(
    queryUserId: Int,
    myUserId: Int,
    newsType: Int,
    showType: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<UserNewsItemData>> {
    return httpRequest {
      mUserHomeApi.getSchoolNewsList(
        ZPRequestBody().apply {
          put("uid", queryUserId)
          put("curUid", myUserId)
          put("lanmu", newsType)
          put("showType", showType)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户发布资讯
   */
  suspend fun getPersonalNewsList(
    queryUserId: Int,
    myUserId: Int,
    newsType: Int,
    showType: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<UserNewsItemData>> {
    return httpRequest {
      mUserHomeApi.getPersonalNewsList(
        ZPRequestBody().apply {
          put("uid", queryUserId)
          put("curUid", myUserId)
          put("lanmu", newsType)
          put("showType", showType)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取企业资讯列表
   */
  suspend fun getEnterpriseNewsList(
    queryUserId: Int,
    myUserId: Int,
    newsType: Int,
    showType: Int,
    pageIndex: Int,
    pageSize: Int,
    jobType: Int = 0
  ): ReqResponse<List<UserNewsItemData>> {
    return httpRequest {
      mUserHomeApi.getEnterpriseNewsList(
        ZPRequestBody().apply {
          put("uid", queryUserId)
          put("curUid", myUserId)
          put("lanmu", newsType)
          put("showType", showType)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
          put("jnid", jobType)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 上传用户背景图
   */
  suspend fun editUserHomeHeaderBg(
    userId: Int,
    authType: Int,
    pic: String,
    picId: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mUserHomeApi.editUserHomeHeaderBg(
        ZPRequestBody().apply {
          put("uid", userId)
          put("type", authType)
          put("pic", pic)
          put("picId", picId)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取个人用户资讯导航信息
   */
  suspend fun getPersonalInfoNavigation(
    queryUserID: Int,
    selfUserID: Int
  ): ReqResponse<List<UserInfoNavigationData>> {
    return httpRequest {
      mUserHomeApi.getPersonalHomeInfoNavigation(
        ZPRequestBody().apply {
          put("uid", queryUserID)
          put("curUid", selfUserID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取企业用户资讯导航信息
   */
  suspend fun getEnterpriseInfoNavigation(
    queryUserID: Int,
    selfUserID: Int
  ): ReqResponse<List<UserInfoNavigationData>> {
    return httpRequest {
      mUserHomeApi.getEnterpriseHomeInfoNavigation(
        ZPRequestBody().apply {
          put("uid", queryUserID)
          put("curUid", selfUserID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取学校用户导航
   */
  suspend fun getSchoolInfoNavigation(
    queryUserID: Int,
    currentUserID: Int
  ): ReqResponse<List<UserInfoNavigationData>> {
    return httpRequest {
      mUserHomeApi.getSchoolHomeInfoNavigation(
        ZPRequestBody().apply {
          put("uid", queryUserID)
          put("curUid", currentUserID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取事业单位用户导航
   */
  suspend fun getInstitutionsNavigation(
    queryUserId: Int,
    queryInstitutionsID: Int,
    selfUserID: Int
  ): ReqResponse<List<UserInfoNavigationData>> {
    return httpRequest {
      mUserHomeApi.getInstitutionsNavigation(
        ZPRequestBody().apply {
          put("uid", queryUserId)
          put("dwid", queryInstitutionsID)
          put("curUid", selfUserID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户主页分享信息
   */
  suspend fun getUserHomePageShareInfo(
    userID: Int,
    dwID: Int,
    infoType: Int,
    authType: Int
  ): ReqResponse<ShareInfoData> {
    return httpRequest {
      mUserHomeApi.getUserHomePageShareInfo(
        ZPRequestBody().apply {
          put("uid", userID)
          put("dwID", dwID)
          put("type", infoType)
          put("rzType", authType)
          put("appType", 1)
        }.paramsEncrypt()
      )
    }
  }
}