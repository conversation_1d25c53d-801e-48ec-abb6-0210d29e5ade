package com.bxkj.jrzp.userhome.ui.userphoto

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.util.Pair
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.fixImgUrl
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserFragmentPhotoListBinding
import com.bxkj.jrzp.userhome.data.UserNewsItemData
import com.bxkj.jrzp.userhome.data.UserPhotoItemData
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsNavigation
import com.bxkj.personal.ui.activity.gznews.NewsDetailsNavigation
import com.bxkj.personal.ui.activity.momentdetails.MomentDetailsNavigation
import com.sanjindev.mui.ui.ImageViewerActivity

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/21
 * @version: V1.0
 */
class UserPhotoListFragment :
    BaseDBFragment<UserFragmentPhotoListBinding, UserPhotoListViewModel>() {

    companion object {
        const val EXTRA_QUERY_USER_ID = "QUERY_USER_ID"
        const val EXTRA_QUERY_INSTITUTIONS_ID = "QUERY_INSTITUTIONS_ID"
        const val EXTRA_USER_AUTH_TYPE = "AUTH_TYPE"

        fun newInstance(queryUserId: Int, queryInstitutionsID: Int, userAuthType: Int): Fragment {
            return UserPhotoListFragment().apply {
                arguments = bundleOf(
                    EXTRA_QUERY_USER_ID to queryUserId,
                    EXTRA_QUERY_INSTITUTIONS_ID to queryInstitutionsID,
                    EXTRA_USER_AUTH_TYPE to userAuthType
                )
            }
        }
    }

    override fun getViewModelClass(): Class<UserPhotoListViewModel> =
        UserPhotoListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_fragment_photo_list

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupUserPhotoListAdapter()

        viewModel.start(
            arguments?.getInt(EXTRA_QUERY_USER_ID),
            arguments?.getInt(EXTRA_QUERY_INSTITUTIONS_ID).getOrDefault(),
            arguments?.getInt(EXTRA_USER_AUTH_TYPE)
        )
    }

    private fun setupUserPhotoListAdapter() {
        val userPhotoListAdapter = object : SimpleDBListAdapter<UserNewsItemData>(
            parentActivity,
            R.layout.user_recycler_user_photo_item
        ) {
            override fun convert(
                holder: SuperViewHolder,
                viewType: Int,
                item: UserNewsItemData,
                position: Int
            ) {
                holder.bind(BR.data, item.picList)
            }
        }.apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    val clickItem = data[position].picList
                    clickItem?.let {
                        when {
                            clickItem.isCompanyProductPic() -> {
                                openGallery(
                                    v.findViewById<ImageView>(R.id.iv_pic),
                                    data,
                                    data[position]
                                )
                            }
                            clickItem.isCompanyStylePic() -> {
                                openGallery(
                                    v.findViewById<ImageView>(R.id.iv_pic),
                                    data,
                                    data[position]
                                )
                            }
                            clickItem.isNewsPic() -> {
                                NewsDetailsNavigation.navigation(clickItem.glid)
                            }
                            clickItem.isSchoolPic() -> {

                            }
                            clickItem.isMomentPic() -> {
                                MomentDetailsNavigation.navigate(clickItem.glid).start()
                            }
                            clickItem.isQAPic() -> {
                                AnswerDetailsNavigation.navigate(
                                    clickItem.toWendaTitle,
                                    clickItem.glid
                                ).start()
                            }
                            else -> {

                            }
                        }
                    }
                }
            })
        }

        viewBinding.includeUserPhotoList.recyclerContent.layoutManager =
            GridLayoutManager(parentActivity, 3)
        viewBinding.includeUserPhotoList.recyclerContent.addItemDecoration(
            GridItemDecoration(
                ContextCompat.getDrawable(parentActivity, R.drawable.divider_2)
            )
        )

        viewModel.userPhotoListViewModel.setAdapter(userPhotoListAdapter)
    }

    private fun openGallery(view: View, data: List<UserNewsItemData>, clickItem: UserNewsItemData) {
        val tempPicList = ArrayList<UserPhotoItemData>()
        val viewImageUrls = ArrayList<String>()
        for (i in data.indices) {
            val item = data[i]
            item.picList?.let {
                if (it.isCompanyProductPic() || it.isCompanyStylePic()) {
                    tempPicList.add(it)
                    viewImageUrls.add(it.pic.getOrDefault().fixImgUrl())
                }
            }
        }
        val convertPosition = tempPicList.indexOf(clickItem.picList)

        startActivity(
            ImageViewerActivity.newIntent(
                requireContext(),
                viewImageUrls,
                tempPicList.indexOf(clickItem.picList)
            ),
            ActivityOptionsCompat.makeSceneTransitionAnimation(
                parentActivity,
                Pair(view, viewImageUrls[convertPosition])
            ).toBundle()
        )
//        ImageGalleryNavigation.navigate(
//            tempPicList,
//            tempPicList.indexOf(clickItem.picList)
//        ).start()
    }
}