<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="isSelf"
      type="Boolean" />

    <variable
      name="data"
      type="com.bxkj.jrzp.userhome.data.UserVideoItemData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_14">

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="horizontal"
      android:paddingStart="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_14">

      <TextView
        android:id="@+id/tv_title"
        style="@style/common_Text.18sp.333333"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_weight="1"
        android:text="@{data.content}" />


      <FrameLayout style="@style/wrap_wrap">

        <ImageView
          android:layout_width="@dimen/user_home_page_video_item_cover_width"
          android:layout_height="@dimen/user_home_page_video_item_cover_height"
          bind:loadRadiusImg="@{data.sppic}" />

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:src="@drawable/ic_play_video" />
      </FrameLayout>

    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:layout_height="@dimen/common_dp_32"
      android:layout_gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        style="@style/Text.12sp.999999"
        android:layout_marginStart="@dimen/dp_14"
        android:text="@{@string/user_home_video_item_play_count_format(data.sjview)}" />

      <TextView
        style="@style/Text.12sp.999999"
        android:layout_marginStart="@dimen/dp_14"
        android:text="@{@string/user_home_video_item_comment_format(data.plCount)}" />

      <TextView
        style="@style/Text.12sp.999999"
        android:layout_marginStart="@dimen/dp_14"
        android:text="@{data.sjdate}" />

      <Space
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1" />

      <ImageView
        android:id="@+id/iv_more_function"
        style="@style/wrap_wrap"
        android:layout_width="@dimen/common_dp_32"
        android:layout_height="@dimen/common_dp_32"
        android:layout_marginEnd="@dimen/dp_14"
        android:scaleType="centerInside"
        android:src="@drawable/user_ic_more_function"
        android:visibility="@{isSelf?View.VISIBLE:View.GONE}" />

    </LinearLayout>

  </LinearLayout>
</layout>