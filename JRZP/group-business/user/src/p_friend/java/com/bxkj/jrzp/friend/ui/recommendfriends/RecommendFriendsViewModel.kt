package com.bxkj.jrzp.friend.ui.recommendfriends

import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.bxkj.common.adapter.multitypeadapter.ListHeaderPlaceholder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.friend.data.RecommendFriendItemData
import com.bxkj.jrzp.user.FollowType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.user.repository.UserRepository
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/12
 * @version: V1.0
 */
class RecommendFriendsViewModel @Inject constructor(
  private val mUserRepository: UserRepository,
  private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

  val recommendFriendsListViewModel = RefreshListViewModel()

  val followSuccessEvent = LiveEvent<Void>()

  private var mRecommendFriendIDs: String = ""

  init {
    setupRecommendFriendList()
  }

  fun start() {
    recommendFriendsListViewModel.refresh()
  }

  fun followUser(friend: RecommendFriendItemData) {
    viewModelScope.launch {
      mOpenUserRepository.follow(getSelfUserID(), FollowType.FOLLOW_USER, friend.uid)
        .handleResult({
          followSuccessEvent.call()
          friend.follow()
        }, {
          if (it.errCode == 10002) {
            friend.unfollow()
          } else {
            showToast(it.errMsg)
          }
        })
    }
  }

  private fun setupRecommendFriendList() {
    recommendFriendsListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mUserRepository.getRecommendFriendsList(
          getSelfUserID(),
          mRecommendFriendIDs,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          it?.let {
            recommendFriendsListViewModel.autoAddAll(it.data)
            if (currentPage == 1) {
              mRecommendFriendIDs = it.uids
              recommendFriendsListViewModel.add(0, ListHeaderPlaceholder.get())
            }
          } ?: let {
            recommendFriendsListViewModel.noMoreData()
          }
        }, {
          if (it.isNoDataError) {
            recommendFriendsListViewModel.noMoreData()
          } else {
            recommendFriendsListViewModel.loadError()
          }
        })
      }
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == RecommendFriendsFragment.TO_USER_HOME_PAGE_CODE) {
      if (resultCode == UserHomeNavigation.RESULT_FOLLOW_STATUS_CHANGE && (data != null)) {
        val userID = data.getIntExtra(UserHomeNavigation.EXTRA_QUERY_USER_ID, 0)
        val followStatus = data.getBooleanExtra(UserHomeNavigation.EXTRA_FOLLOW_STATUS, false)
        syncUserFollowStatus(userID, followStatus)
      }
    }
  }

  private fun syncUserFollowStatus(userID: Int, followStatus: Boolean) {
    recommendFriendsListViewModel.data.forEach {
      it?.let {
        if (it is RecommendFriendItemData) {
          if (it.uid == userID) {
            if (followStatus) {
              it.follow()
            } else {
              it.unfollow()
            }
          }
        }
      }
    }
  }
}