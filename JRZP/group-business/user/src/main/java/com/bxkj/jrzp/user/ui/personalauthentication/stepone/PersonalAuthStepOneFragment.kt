package com.bxkj.jrzp.user.ui.personalauthentication.stepone

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.string
import com.bxkj.jrzp.user.databinding.UserFragmentPersonalAuthStepOneBinding
import com.bxkj.jrzp.user.ui.personalauthentication.PersonalAuthenticationActivity
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig

/**
 * @Description: 个人认证第一步
 * @author:45457
 * @date: 2020/9/15
 * @version: V1.0
 */
class PersonalAuthStepOneFragment :
  BaseDBFragment<UserFragmentPersonalAuthStepOneBinding, PersonalAuthStepOneViewModel>(),
  OnClickListener {

  companion object {

    const val TO_SELECT_ID_CARD_POSITIVE_CODE = 1
    const val TO_SELECT_ID_CARD_NEGATIVE_CODE = 2

    fun newInstance(): Fragment {
      return PersonalAuthStepOneFragment()
    }
  }

  override fun getViewModelClass(): Class<PersonalAuthStepOneViewModel> =
    PersonalAuthStepOneViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_fragment_personal_auth_step_one

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_ID_card_positive -> {
          toSelectPic(TO_SELECT_ID_CARD_POSITIVE_CODE)
        }

        R.id.iv_ID_card_negative -> {
          toSelectPic(TO_SELECT_ID_CARD_NEGATIVE_CODE)
        }

        R.id.tv_to_next -> {
          if (parentActivity is PersonalAuthenticationActivity) {
            (parentActivity as PersonalAuthenticationActivity).toNext()
          }
        }
      }
    }
  }

  private fun toSelectPic(selectCode: Int) {
    PermissionUtils.requestPermission(
      parentActivity,
      getString(string.permission_tips_title),
      getString(string.permission_select_img_tips),
      object : OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector.create(this@PersonalAuthStepOneFragment)
            .openGallery(SelectMimeType.ofImage())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setImageEngine(GlideEngine.getInstance())
            .setImageSpanCount(4)
            .forResult(selectCode)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  private fun subscribeViewModelEvent() {
    viewModel.idCardPicPositiveUrl.observe(this, Observer {
      it?.let {
        if (parentActivity is PersonalAuthenticationActivity) {
          (parentActivity as PersonalAuthenticationActivity).setIDCardPositive(it)
        }
        ImageLoader.loadImage(
          this,
          GlideLoadConfig.Builder().url(CommonApiConstants.BASE_JRZP_IMG_URL + it.url)
            .into(viewBinding.ivIDCardPositive).build()
        )
      } ?: let {
        viewBinding.ivIDCardPositive.setImageResource(R.drawable.user_ic_authentication_id_card_placeholder_one)
      }
    })

    viewModel.idCardPicNegativeUrl.observe(this, Observer {
      it?.let {
        if (parentActivity is PersonalAuthenticationActivity) {
          (parentActivity as PersonalAuthenticationActivity).setIDCardNegative(it)
        }
        ImageLoader.loadImage(
          this,
          GlideLoadConfig.Builder().url(CommonApiConstants.BASE_JRZP_IMG_URL + it.url)
            .into(viewBinding.ivIDCardNegative).build()
        )
      } ?: let {
        viewBinding.ivIDCardNegative.setImageResource(R.drawable.user_ic_authentication_id_card_placeholder_two)
      }
    })
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }
}