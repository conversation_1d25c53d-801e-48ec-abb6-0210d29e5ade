package com.bxkj.jrzp.user.ui.jobproveauth

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/28
 * @version: V1.0
 */
class JobProveAuthNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/jobproveauth"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}