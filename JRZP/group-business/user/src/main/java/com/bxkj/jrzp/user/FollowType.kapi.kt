package com.bxkj.jrzp.user

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class FollowType {
  companion object {
    const val FOLLOW_ORG = 3
    const val FOLLOW_USER = 5
  }

  @IntDef(FOLLOW_ORG, FOLLOW_USER)
  @Retention(SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Type
}