package com.bxkj.jrzp.user

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description: 账户类型
 * @author:45457
 * @date: 2020/8/1
 * @version: V1.0
 */
class AccountType {

  companion object {
    const val ALIPAY_TYPE = 1
    const val BANK_CARD_TYPE = 2
  }

  @IntDef(ALIPAY_TYPE, BANK_CARD_TYPE)
  @Retention(SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Type
}