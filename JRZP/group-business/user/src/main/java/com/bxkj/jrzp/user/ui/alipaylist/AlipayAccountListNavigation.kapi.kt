package com.bxkj.jrzp.user.ui.alipaylist

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/3
 * @version: V1.0
 */
class AlipayAccountListNavigation {
  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/alipayaccountlist"

    const val EXTRA_RESULT_ALIPAY_ACCOUNT = "RESULT_ALIPAY_ACCOUNT"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}