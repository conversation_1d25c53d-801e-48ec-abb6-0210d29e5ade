package com.bxkj.jrzp.user.ui.idcardvalidation

import android.os.Bundle
import android.view.View
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityIdcardValidationBinding
import com.bxkj.personal.ui.activity.main.PersonalMainNavigation
import com.therouter.router.Route

/**
 * author:Sanjin
 * date:2025/5/19
 **/
@Route(path = IDCardValidationNavigation.PATH)
class IDCardValidationActivity :
  BaseDBActivity<UserActivityIdcardValidationBinding, IDCardValidationViewModel>() {

  private val extraNextStep by lazy {
    intent.getIntExtra(
      IDCardValidationNavigation.EXTRA_NEXT_STEP,
      IDCardValidationNavigation.NEXT_STEP_FINISH
    )
  }

  override fun getViewModelClass(): Class<IDCardValidationViewModel> =
    IDCardValidationViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_idcard_validation

  override fun initPage(savedInstanceState: Bundle?) {
    // 设置数据绑定
    viewBinding.viewModel = viewModel

    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.errMsg.observe(this, {
      if (it.isNullOrEmpty()) {
        viewBinding.tvErrMsg.visibility = View.GONE
      } else {
        viewBinding.tvErrMsg.visibility = View.VISIBLE
        viewBinding.tvErrMsg.text = it
      }
    })

    viewModel.lockIDCardInfoCommand.observe(this, EventObserver {
      viewBinding.etName.apply {
        setContentEnabled(false)
        setContentTextColor(getResColor(R.color.cl_888888))
      }
      viewBinding.etIdCardNumber.apply {
        setContentEnabled(false)
        setContentTextColor(getResColor(R.color.cl_888888))
      }
    })

    viewModel.showFailedMsgEvent.observe(this, EventObserver {
      TipsDialog()
        .setContent(it)
        .show(supportFragmentManager)
    })

    viewModel.showErrMsgAndFinishEvent.observe(this, EventObserver {
      TipsDialog()
        .setContent(getString(R.string.user_idcard_validation_load_err_tips))
        .setOnConfirmClickListener {
          finish()
        }
        .show(supportFragmentManager)
    })

    viewModel.successEvent.observe(this, EventObserver {
      if (extraNextStep == IDCardValidationNavigation.NEXT_STEP_FINISH) {
        RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_CERTIFICATION_SUBMIT_SUCCESS))
      } else {
        PersonalMainNavigation.navigate().start()
      }
      setResult(RESULT_OK)
      finish()
    })
  }
}