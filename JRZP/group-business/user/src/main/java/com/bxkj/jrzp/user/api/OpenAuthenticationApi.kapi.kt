package com.bxkj.jrzp.user.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.jrzp.user.data.UserAuthStatusData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/18
 * @version: V1.0
 */
interface OpenAuthenticationApi {

  @POST("/BusinessLicense/GetBusinessLicenseStatusV2/")
  suspend fun getUserAuthStatus(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<UserAuthStatusData>>
}