<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.institutions.ui.institutionsinfo.InstitutionsInfoViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/user_institutions_info_page_title" />

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_logo"
      android:layout_width="@dimen/dp_70"
      android:layout_height="@dimen/dp_70"
      android:layout_marginTop="@dimen/dp_24"
      android:onClick="@{onClickListener}"
      bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgUrl="@{viewModel.institutionsInfo.formatLogo}"
      app:shapeAppearance="@style/roundedCornerImageStyle.Avatar" />

    <TextView
      style="@style/Text.14sp.999999"
      android:layout_marginTop="@dimen/dp_12"
      android:text="@string/user_institutions_info_logo" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      android:layout_marginTop="@dimen/dp_24"
      app:yui_content="@={viewModel.institutionsInfo.name}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/user_institutions_info_name" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      android:onClick="@{()->viewModel.showInstitutionsTypePicker()}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.institutionsInfo.typeName}"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/user_institutions_info_type" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      android:onClick="@{()->viewModel.showAreaPicker()}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.institutionsInfo.formatAddress}"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/user_institutions_info_area" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      android:onClick="@{()->viewModel.toEditDetailsAddress()}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.institutionsInfo.address}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="text"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/user_institutions_info_address" />

    <Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      android:id="@+id/tv_next_step"
      style="@style/Button.Basic.Round"
      android:layout_marginStart="@dimen/dp_30"
      android:layout_marginEnd="@dimen/dp_30"
      android:layout_marginBottom="@dimen/dp_18"
      android:onClick="@{()->viewModel.confirm()}" />
  </LinearLayout>
</layout>