package com.bxkj.jrzp.user.institutions.ui.institutionsinfo

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 *
 * @author: YangXin
 * @date: 2021/1/18
 */
class InstitutionsInfoNavigation {
  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/institutionsinfo"

    const val EXTRA_NEXT_STEP = "NEXT_STEP"

    const val NEXT_BACK = 1
    const val NEXT_AUTH = 2

    fun create(nextStep: Int = NEXT_BACK): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_NEXT_STEP, nextStep)
    }
  }
}