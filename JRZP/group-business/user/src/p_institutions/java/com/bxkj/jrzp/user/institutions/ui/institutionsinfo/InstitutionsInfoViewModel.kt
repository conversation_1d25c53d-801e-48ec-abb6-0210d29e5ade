package com.bxkj.jrzp.user.institutions.ui.institutionsinfo

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.AreaOptionsData
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.data.InstitutionsInfoData
import com.bxkj.jrzp.user.repository.UserRepository
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/1/18
 */
class InstitutionsInfoViewModel @Inject constructor(
    private val mUserRepository: UserRepository,
    private val mUploadRepository: UploadRepository
) : BaseViewModel() {

    val institutionsInfo = MutableLiveData<InstitutionsInfoData>()

    //更新事业单位信息成功
    val updateInstitutionsInfoSuccessEvent =
        LiveEvent<InstitutionsInfoData>()

    //显示地区选择器
    val showAreaSelectPickerCommand = LiveEvent<InstitutionsInfoData>()

    //编辑单位详细资料
    val toEditDetailsAddressCommand = LiveEvent<String>()

    //分类选项
    val institutionsTypeOptions = MutableLiveData<List<PickerOptionsData>>()

    init {
        getInstitutionsInfo()
    }

    private fun getInstitutionsInfo() {
        viewModelScope.launch {
            showLoading()
            mUserRepository.getInstitutionsInfo()
                .handleResult({
                    institutionsInfo.value = it
                }, {
                    institutionsInfo.value = InstitutionsInfoData()
                    if (!it.isNoDataError) {
                        showToast(it.errMsg)
                    }
                }, {
                    hideLoading()
                })
        }
    }

    fun showInstitutionsTypePicker() {
        institutionsTypeOptions.value?.let {
            institutionsTypeOptions.value = it
        } ?: let {
            viewModelScope.launch {
                showLoading()
                mUserRepository.getInstitutionsTypeList()
                    .handleResult({
                        institutionsTypeOptions.value = it
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun showAreaPicker() {
        institutionsInfo.value?.let {
            showAreaSelectPickerCommand.value = it
        }
    }

    fun setSelectedType(pickerOptionsData: PickerOptionsData) {
        institutionsInfo.value?.let {
            it.type = pickerOptionsData.id
            it.updateTypeName(pickerOptionsData.name)
        }
    }

    fun getSelectedType(): String? {
        return institutionsInfo.value?.typeName
    }

    fun confirm() {
        institutionsInfo.value?.let { institutionsInfo ->
            showLoading()
            viewModelScope.launch {
                mUserRepository.updateInstitutionsInfo(institutionsInfo)
                    .handleResult({
                        updateInstitutionsInfoSuccessEvent.value = institutionsInfo
                    }, {
                        if (it.errCode == 10002) {
                            updateInstitutionsInfoSuccessEvent.value = institutionsInfo
                        } else {
                            showToast(it.errMsg)
                        }
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun setupProvinceAndCity(
        province: AreaOptionsData,
        city: AreaOptionsData,
        district: AreaOptionsData
    ) {
        institutionsInfo.value?.let {
            it.updateAddressInfo(
                province.id,
                province.name,
                city.id,
                city.name,
                district.id,
                district.name
            )
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            PictureConfig.CHOOSE_REQUEST -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val imgPath = data.getSelectedFirstMediaPath()
                    uploadLogo(imgPath)
                }
            }
            InstitutionsInfoActivity.TO_EDIT_DETAILS_ADDRESS_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val resultText =
                        data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT).getOrDefault()
                    institutionsInfo.value?.let {
                        it.updateDetailsAddress(resultText)
                    }
                }
            }
        }

    }

    private fun uploadLogo(imgPath: String) {
        showLoading()
        viewModelScope.launch {
            mUploadRepository.uploadFileV3(
                imgPath,
                UploadFileRequestParams.getImageUploadParams(
                    getSelfUserID(),
                    UploadFileRequestParams.PATH_NAME_COMPANY_LOGO
                )
            ).handleResult({
                it?.let {
                    institutionsInfo.value?.let { info ->
                        info.updateLogo(it.url)
                    }
                }
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    /**
     * 编辑单位详细地址
     */
    fun toEditDetailsAddress() {
        institutionsInfo.value?.let {
            toEditDetailsAddressCommand.value = it.address.getOrDefault()
        }
    }

}