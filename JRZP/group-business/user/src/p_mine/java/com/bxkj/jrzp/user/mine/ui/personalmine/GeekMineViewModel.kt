package com.bxkj.jrzp.user.mine.ui.personalmine

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.support.db.AppDatabase
import com.bxkj.jrzp.support.db.entry.UserLoginHistory
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenAuthenticationRepository
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.data.ResumeScoreData
import com.bxkj.jrzp.user.mine.UserFunctionItem
import com.bxkj.jrzp.user.mine.data.ServiceItemData
import com.bxkj.jrzp.user.mine.data.UserHomeData
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.user.repository.UserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.mine
 * @Description: 我的
 * <AUTHOR>
 * @date 2020/2/10
 * @version V1.0
 */
class GeekMineViewModel @Inject constructor(
  private val mUserRepository: UserRepository,
  private val _openUserRepository: OpenUserRepository,
  private val mOpenAuthenticationRepository: OpenAuthenticationRepository,
  private val mAppDatabase: AppDatabase
) : BaseViewModel() {

  val userInfo = MutableLiveData<UserHomeData>()
  val resumeScoreInfo = MutableLiveData<ResumeScoreData>()
  val collectionJobCount = MutableLiveData<String>()
  val inviteToDeliveryCount = MutableLiveData<String>()
  val whoSewMeCount = MutableLiveData<String>()
  val interviewCount = MutableLiveData<String>()
  val servicesInfo = MutableLiveData<ServiceItemData>()

  val toMyResumeDetailsCommand = MutableLiveData<VMEvent<Unit>>()

  val toCreateResumeCommand = MutableLiveData<VMEvent<Unit>>()

  //显示简历未完善提示
  val showNoResumeTipsCommand = MutableLiveData<VMEvent<Unit>>()

  //广告链接
  val adImageUrl = MutableLiveData<String>()

  //是否认证
  val isAuth = MutableLiveData<Boolean>().apply { value = false }

  val userOptions = MutableLiveData<List<UserFunctionItem>>()

  val callServicesPhoneCommand = MutableLiveData<VMEvent<String>>()
  val jumpToWechatCommand = MutableLiveData<VMEvent<String>>()

  val editResumeText = MediatorLiveData<String>().apply {
    addSource(resumeScoreInfo) {
      value = when {
        it.score == 100 -> {
          "编辑简历"
        }

        it.score.getOrDefault(0) <= 30 -> {
          "创建简历"
        }

        else -> {
          "完善简历"
        }
      }
    }
  }

  init {
    viewModelScope.launch {
      _openUserRepository.getUserServicesInfo()
        .handleResult({
          if (!it.isNullOrEmpty()) {
            servicesInfo.value = it[0]
          }
        })
    }

    initUserOptions()
  }

  fun refresh() {
    getResumeSoreInfo()
    getAdInfo()
    getUserInfo()
    getUserAboutCount()
    checkUserAuthStatus()
  }

  fun callServicesPhone() {
    servicesInfo.value?.let {
      callServicesPhoneCommand.value = VMEvent(it.kefuTel)
    }
  }

  fun jumpToWechat() {
    servicesInfo.value?.let {
      jumpToWechatCommand.value = VMEvent(it.wechatNum)
    }
  }

  private fun getResumeSoreInfo() {
    viewModelScope.launch {
      _openUserRepository.getResumeScore()
        .handleResult({
          resumeScoreInfo.value = it
        }, {
          showToast(it.errMsg)
        })
    }
  }

  private fun checkUserAuthStatus() {
    viewModelScope.launch {
      mOpenAuthenticationRepository.getUserAuthStatus(getSelfUserID())
        .handleResult({
          if (!it.isNullOrEmpty()) {
            it[0].let { currentAuthStatus ->
              if (AuthenticationType.isPersonal(currentAuthStatus.type) && currentAuthStatus.authSuccess()) {
                isAuth.value = true
              }
            }
          }
        })
    }
  }

  fun toMyResumeDetailsPreCheck() {
    resumeScoreInfo.value?.let {
      if (it.score.getOrDefault() > 30) {
        toMyResumeDetailsCommand.value = VMEvent(Unit)
      } else {
        toCreateResumeCommand.value = VMEvent(Unit)
      }
    }
  }

  fun refreshResume() {
    viewModelScope.launch {
      showLoading()
      mUserRepository.refreshResume()
        .handleResult({
          showToast("刷新成功")
        }, {
          if (it.isNoDataError) {
            showNoResumeTipsCommand.value = VMEvent(Unit)
          } else {
            showToast(it.errMsg)
          }
        }, {
          hideLoading()
        })
    }
    // userInfo.value?.let {
    //   if (CheckUtils.isNullOrEmpty(it.name)) {
    //     toCreateResumeCommand.value = VMEvent(Unit)
    //   } else {
    //     toUserHomeCommand.call()
    //   }
    // }
  }

  private fun initUserOptions() {
    userOptions.value = arrayListOf(
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_MY_RESUME,
      //   R.drawable.user_ic_personal_mine_resume,
      //   "我的简历"
      // ),
      UserFunctionItem.get(
        UserFunctionItem.OP_PERSONAL_VIP,
        R.drawable.user_ic_personal_vip,
        "求职会员"
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_FIND_JOB_BY_MAP,
        R.drawable.user_ic_personal_mine_nearby_job,
        "附近职位"
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_SEARCH_JOB,
        R.drawable.user_ic_personal_mine_search,
        "职位搜索"
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_MY_ORDER,
        R.drawable.user_ic_mine_order,
        "我的订单"
      ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_MY_FAVORITES,
      //   R.drawable.user_ic_personal_mine_favorites,
      //   "我的收藏"
      // ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_MY_COMMENT,
      //   R.drawable.user_ic_personal_mine_comment,
      //   "我的评论"
      // ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_MY_LIKE,
      //   R.drawable.user_ic_personal_mine_like,
      //   "我的点赞"
      // ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_MY_VIDEO,
      //   R.drawable.user_ic_personal_mine_video,
      //   "我的视频"
      // ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_SCHOOL_RECRUIT_DELIVERY_RECORD,
      //   R.drawable.user_ic_school_recruit,
      //   "校招投递"
      // ),
      UserFunctionItem.get(
        UserFunctionItem.OP_SWITCH_IDENTITY,
        R.drawable.user_ic_mine_switch_role,
        "我要招人"
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_HELP,
        R.drawable.user_ic_personal_mine_support,
        "帮助与反馈"
      )
    )
  }

  private fun getAdInfo() {
    viewModelScope.launch {
      mUserRepository.getAdImgUrl()
        .handleResult({
          adImageUrl.value = it
        })
    }
  }

  private fun getUserAboutCount() {
    viewModelScope.launch {
      mUserRepository.getUserCollectionJobCount(getSelfUserID())
        .handleResult({
          collectionJobCount.value = it
        })

      mUserRepository.getInviteToDeliveryCount(getSelfUserID(), -1)
        .handleResult({
          inviteToDeliveryCount.value = it ?: "0"
        })

      mUserRepository.getSewMeCompanyCount(getSelfUserID())
        .handleResult({
          it?.let {
            whoSewMeCount.value = it.get("shuikanlewoCount").toString()
            interviewCount.value = it.get("interviewCount").toString()
          }
        })
    }
  }

  private fun getUserInfo() {
    viewModelScope.launch {
      mUserRepository.getUserHomeInfo(
        getSelfUserID(),
        CommonApiConstants.NO_ID,
        getSelfUserID()
      ).handleResult({
        it?.let { userInfoResult ->
          if (userInfoResult.isSalesPersonal) {
            userOptions.value?.let { options ->
              if (options.first().opId != UserFunctionItem.OP_WORKBENCH) {
                userOptions.value =
                  arrayListOf(
                    UserFunctionItem.get(
                      UserFunctionItem.OP_WORKBENCH,
                      R.drawable.user_ic_workbench,
                      "工作台"
                    )
                  ) + options
              }
            }
          }
          //更新登录记录信息
          mAppDatabase.userLoginHistoryDao()
            .insert(
              UserLoginHistory(
                userInfoResult.userID,
                UserUtils.getUserToken(),
                userInfoResult.photo,
                userInfoResult.name,
                AuthenticationType.PERSONAL
              )
            )
        }
        userInfo.value = it
      }, {
        userInfo.value = UserHomeData.getEmpty()
        if (it.isNetworkError) {
//            showToast(it.errMsg)
        }
      })
    }
  }
}