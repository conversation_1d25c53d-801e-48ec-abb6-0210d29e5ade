package com.bxkj.jrzp.user.mine.ui.institutionsmine

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 *
 * @author: YangXin
 * @date: 2021/1/20
 */
class InstitutionsMineNavigation {

  companion object {
    const val PATH = "${UserConstants.DIRECTORY}/institutionsmine"

    fun fragment(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}