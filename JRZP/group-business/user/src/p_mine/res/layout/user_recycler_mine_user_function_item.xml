<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.user.mine.UserFunctionItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_horizontal|bottom"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_op_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_18"
            android:src="@{data.opIcon}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_top"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="top"
            app:constraint_referenced_ids="iv_op_icon" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_right"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="right"
            app:constraint_referenced_ids="iv_op_icon" />

        <TextView
            style="@style/Text.12sp.333333"
            android:layout_marginBottom="@dimen/dp_18"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@{data.opText}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_op_icon" />

        <ImageView
            android:id="@+id/iv_op_tag"
            style="@style/wrap_wrap"
            app:layout_constraintBottom_toBottomOf="@id/barrier_top"
            app:layout_constraintLeft_toRightOf="@id/barrier_right"
            app:layout_constraintTop_toTopOf="@id/barrier_top" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>