<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.CampusTalkType" />

    <variable
      name="data"
      type="com.bxkj.jrzp.user.schoolinfo.data.CampusTalkDataV2" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_8"
    android:paddingEnd="@dimen/dp_14"
    android:paddingBottom="@dimen/dp_8">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.NormalInfoTitle"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_14"
      android:ellipsize="end"
      android:lines="2"
      android:text="@{data.showTitle}"
      app:layout_constraintEnd_toStartOf="@id/iv_logo"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_address"
      style="@style/Text.Tertiary"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_2"
      android:layout_marginEnd="@dimen/dp_16"
      android:drawableStart="@drawable/ic_address"
      android:drawablePadding="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.cityName}"
      app:layout_constraintEnd_toStartOf="@id/iv_logo"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <ImageView
      android:id="@+id/iv_logo"
      android:layout_width="0dp"
      android:layout_height="0dp"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginBottom="@dimen/dp_8"
      bind:imgRadius="@{@dimen/dp_4}"
      bind:imgUrl="@{data.picUrl}"
      app:layout_constraintBottom_toTopOf="@id/tv_delete"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      style="@style/wrap_wrap"
      android:src="@drawable/user_ic_video_play"
      android:visibility="@{data.type==CampusTalkType.TYPE_ONLINE?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/iv_logo"
      app:layout_constraintEnd_toEndOf="@id/iv_logo"
      app:layout_constraintStart_toStartOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="@id/iv_logo" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.Tertiary"
      android:layout_marginTop="@dimen/dp_2"
      android:text="@{data.date}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_address" />

    <TextView
      android:id="@+id/tv_delivery_count"
      style="@style/Text.12sp.333333"
      android:layout_marginStart="@dimen/dp_8"
      android:background="@drawable/text_click_underline"
      android:text="@{@string/user_campus_talk_delivery_count_format(data.toudiCount)}"
      android:visibility="@{data.passed()?View.VISIBLE:View.GONE}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_date"
      app:layout_constraintStart_toEndOf="@id/tv_date" />

    <TextView
      android:id="@+id/tv_review_status"
      style="@style/Text.12sp.FF7647"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{data.statusText}"
      android:visibility="@{data.passed()?View.GONE:View.VISIBLE}"

      app:layout_constraintBaseline_toBaselineOf="@id/tv_delivery_count"
      app:layout_constraintStart_toEndOf="@id/tv_delivery_count" />

    <TextView
      android:id="@+id/tv_view_count"
      style="@style/Text.Tertiary"
      android:visibility="@{data.unreviewed?View.GONE:View.VISIBLE}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_review_status"
      app:layout_constraintStart_toEndOf="@id/tv_review_status"
      app:layout_goneMarginStart="@dimen/dp_8" />

    <TextView
      android:id="@+id/tv_edit"
      style="@style/Text.Options"
      android:layout_marginEnd="@dimen/dp_8"
      android:text="@string/user_enterprise_seminar_edit"
      android:visibility="@{data.pendingReview()?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_delete"
      app:layout_constraintEnd_toStartOf="@id/tv_delete"
      app:layout_constraintTop_toTopOf="@id/tv_delete" />

    <TextView
      android:id="@+id/tv_delete"
      style="@style/Text.Options"
      android:layout_marginTop="@dimen/dp_4"
      android:text="@string/user_enterprise_seminar_delete"
      app:layout_constraintBottom_toBottomOf="@id/tv_date"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_date" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>