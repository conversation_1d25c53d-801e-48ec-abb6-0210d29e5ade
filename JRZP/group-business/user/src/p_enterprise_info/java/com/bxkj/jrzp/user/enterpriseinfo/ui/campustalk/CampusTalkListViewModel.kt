package com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoRepository
import com.bxkj.jrzp.user.schoolinfo.data.CampusTalkDataV2
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/14
 * @version: V1.0
 */
class CampusTalkListViewModel @Inject constructor(
    private val mSchoolInfoRepository: SchoolInfoRepository,
    private val mOpenUserRepository: OpenUserRepository
) : BaseViewModel() {

    val enterpriseSeminarListViewModel = RefreshListViewModel()

    val showUpgradeVipCommand = LiveEvent<String>()
    val showErrorTipsCommand = LiveEvent<String>()
    val toReleaseCampusCommand = LiveEvent<Int>()

    private var type: Int = 0

    init {
        setupEnterpriseSeminarListViewModel()
    }

    fun setType(type: Int) {
        this.type = type
    }

    fun refresh() {
        enterpriseSeminarListViewModel.refresh()
    }

    fun deleteSeminar(campusTalkData: CampusTalkDataV2) {
        viewModelScope.launch {
            showLoading()
            mOpenUserRepository.deleteReleasedInfo(
                getSelfUserID(),
                UserInfoNavigationData.NAVIGATION_SEMINAR,
                campusTalkData.id
            ).handleResult({
                showToast(R.string.common_delete_success)
                if (enterpriseSeminarListViewModel.remove(campusTalkData) == 0) {
                    enterpriseSeminarListViewModel.refresh()
                }
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    fun releasePreCheck() {
        viewModelScope.launch {
            showLoading()
            mOpenUserRepository.releaseCampusTalkPreCheck(type)
                .handleResult({
                    it?.let {
                        toReleaseCampusCommand.value = type
                    } ?: showToast("发布校验失败，请重试")
                }, {
                    if (it.errCode == 30005 || it.errCode == 30007) {
                        showUpgradeVipCommand.value = it.errMsg
                    } else {
                        showErrorTipsCommand.value = it.errMsg
                    }
                }, {
                    hideLoading()
                })
        }
    }

    private fun setupEnterpriseSeminarListViewModel() {
        enterpriseSeminarListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mSchoolInfoRepository.getCampusTalkListV2(
                    type,
                    getSelfUserID(),
                    false,
                    -1,
                    currentPage,
                    CommonApiConstants.DEFAULT_PAGE_SIZE
                ).handleResult({
                    it?.let {
                        enterpriseSeminarListViewModel.autoAddAll(it.dataList)
                    } ?: let {
                        enterpriseSeminarListViewModel.noMoreData()
                    }
                }, {
                    if (it.isNoDataError) {
                        enterpriseSeminarListViewModel.noMoreData()
                    } else {
                        enterpriseSeminarListViewModel.loadError()
                    }
                })
            }
        }
    }

}