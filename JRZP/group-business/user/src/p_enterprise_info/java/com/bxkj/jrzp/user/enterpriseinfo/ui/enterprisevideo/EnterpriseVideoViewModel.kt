package com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.user.repository.UserRepository
import com.bxkj.video.VideoConstants
import com.bxkj.video.VideoSubType
import com.bxkj.video.VideoType
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.repository.OpenVideoRepository
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.tencent.qcloud.ugckit.basic.UGCKitResult
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/13
 * @version: V1.0
 */
class EnterpriseVideoViewModel @Inject constructor(
    private val mOpenVideoRepository: OpenVideoRepository,
    private val mUserRepository: UserRepository
) : BaseViewModel() {

    val videoListViewModel = RefreshListViewModel()

    //选中项
    val selectedItem = MutableLiveData<List<VideoData>>()

    //拍摄视频
    val toAddVideoCommand = LiveEvent<Void>()

    //剪辑视频
    val toVideoCutCommand = LiveEvent<String>()

    //添加关联成功
    val addLinkSuccess = LiveEvent<Void>()

    init {
        setupVideoListViewModel()
    }

    private fun setupVideoListViewModel() {
        videoListViewModel.refreshLayoutViewModel.enableRefresh(false)
        videoListViewModel.setOnLoadDataListener { currentPage ->
            viewModelScope.launch {
                mOpenVideoRepository.getUserVideoList(
                    getSelfUserID(),
                    getSelfUserID(),
                    AppConstants.USER_TYPE_COMPANY,
                    VideoSubType.VIDEO_LIST_SUB_USER_RELEASE,
                    currentPage,
                    CommonApiConstants.DEFAULT_PAGE_SIZE,
                    1
                ).handleResult({
                    if (currentPage == 1) {
                        it?.let {
                            val tempList = ArrayList(it)
                            tempList.add(0, OnlineVideoData.fromAddItem())
                            videoListViewModel.reset(tempList)
                        }
                    } else {
                        videoListViewModel.addAll(it)
                    }
                })
            }
        }
    }

    fun start() {
        videoListViewModel.refresh()
    }

    /**
     * 确认选中
     */
    fun confirmSelected() {
        selectedItem.value?.let { videoList ->
            viewModelScope.launch {
                showLoading()
                mUserRepository.setupEnterpriseVideo(getSelfUserID(), videoList[0].id)
                    .handleResult({
                        addLinkSuccess.call()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    /**
     * 设置选中项
     */
    fun setSelectedList(selectedVideoData: ArrayList<VideoData>) {
        this.selectedItem.value = selectedVideoData
    }

    /**
     * 添加视频
     */
    fun toAddVideo() {
        toAddVideoCommand.call()
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            EnterpriseVideoActivity.TO_TAKE_VIDEO_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    data.getParcelableExtra<UGCKitResult>(VideoConstants.EXTRA_RESULT_VIDEO_INFO)
                        ?.let {
                            publishVideo(it)
                        } ?: showToast("获取视频信息失败")
                }
            }
            PictureConfig.CHOOSE_REQUEST -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    toVideoCutCommand.value = data.getSelectedFirstMediaPath()
                }
            }
            EnterpriseVideoActivity.TO_VIDEO_CUT_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    data.getParcelableExtra<UGCKitResult>(VideoConstants.EXTRA_RESULT_VIDEO_INFO)
                        ?.let {
                            publishVideo(it)
                        } ?: showToast("获取视频信息失败")
                }
            }
        }
    }

    /**
     * 发布视频
     */
    private fun publishVideo(videoInfo: UGCKitResult) {
        viewModelScope.launch {
            showLoading()
            mOpenVideoRepository.publishVideoByPath(
                getSelfUserID(),
                VideoType.VIDEO_TYPE_RECRUIT,
                videoInfo.coverPath,
                videoInfo.outputPath,
                videoInfo.width,
                videoInfo.height
            ).handleResult({
                RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_PUBLISH_VIDEO_SUCCESS))
                videoListViewModel.refresh()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

}