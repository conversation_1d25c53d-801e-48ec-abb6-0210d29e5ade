package com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair

import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityEnterpriseJobFairBinding
import com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair.doubleelection.EnterpriseDoubleElectionFragment

/**
 * @Description: 企业宣讲会
 * @author: YangXin
 * @date: 2020/12/4
 * @version: V1.0
 */
@Route(path = EnterpriseJobFairNavigation.PATH)
class EnterpriseJobFairActivity :
    BaseDBActivity<UserActivityEnterpriseJobFairBinding, EnterpriseJobFairViewModel>() {

    override fun getViewModelClass(): Class<EnterpriseJobFairViewModel> =
        EnterpriseJobFairViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_activity_enterprise_job_fair

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupPageContent()

    }

    private fun setupPageContent() {

        supportFragmentManager.beginTransaction()
            .add(R.id.fl_content, EnterpriseDoubleElectionFragment.newInstance()).commit()

//        viewBinding.indicatorJobFairType.navigator = CommonNavigator(this).apply {
//            adapter = CommonIndicatorAdapter(getResArray(R.array.user_enterprise_job_fair_type))
//                .apply {
//                    setOnTabClickListener(object : OnTabClickListener {
//                        override fun onTabClicked(v: View, index: Int) {
//                            viewBinding.vpContent.currentItem = index
//                        }
//                    })
//                }
//            isAdjustMode = true
//        }
//
//        viewBinding.vpContent.adapter = CommonPagerAdapter(
//            supportFragmentManager,
//            arrayListOf(
//                EnterpriseSeminarFragment.newInstance(),
//                EnterpriseDoubleElectionFragment.newInstance()
//            )
//        )
//
//        ViewPagerHelper.bind(viewBinding.indicatorJobFairType, viewBinding.vpContent)
    }

}