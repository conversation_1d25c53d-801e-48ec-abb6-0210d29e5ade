package com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo

import android.content.Context
import android.widget.ImageView
import com.bxkj.common.adapter.CommonDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.video.data.VideoData

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/13
 * @version: V1.0
 */
class EnterpriseVideosListAdapter constructor(
  context: Context,
  private val viewModel: EnterpriseVideoViewModel
) : CommonDBListAdapter<VideoData>(context, R.layout.user_recycler_video_select_item, BR.data) {

  private var mSelectPosition = 0

  override fun convert(holder: SuperViewHolder, viewType: Int, item: VideoData, position: Int) {
    super.convert(holder, viewType, item, position)
    val ivSelect = holder.findViewById<ImageView>(R.id.iv_select)
    ivSelect.isSelected = (mSelectPosition == position)
    ivSelect.setOnClickListener {
      if (mSelectPosition != position) {
        mSelectPosition = position
        viewModel.setSelectedList(arrayListOf(item))
        notifyDataSetChanged()
      }
    }
  }

  override fun reset(items: MutableList<VideoData>?) {
    super.reset(items)
    items?.indices?.forEach { index ->
      if (items[index].isQixuan == 1) {
        mSelectPosition = index
        viewModel.setSelectedList(arrayListOf(items[index]))
        notifyDataSetChanged()
      }
    }
  }
}